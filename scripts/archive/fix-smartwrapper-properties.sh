#!/bin/bash

# 🔧 SMARTWRAPPER PROPERTY FIXER - INFRASTRUCTURE AWARE
# Enhanced SmartWrapper fixing for 28-agent ecosystem, Navigation Intelligence, MLCoordinationLayer
# Based on R1 + Devstral consensus strategy for comprehensive component property management
# Integrated with infrastructure awareness protocols and AI-powered property analysis

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${GREEN}🔧 SMARTWRAPPER PROPERTY FIXER - INFRASTRUCTURE AWARE${NC}"
echo -e "${CYAN}Based on: 28 agents | Navigation Intelligence | MLCoordinationLayer | AI-Powered Fixing${NC}"
echo -e "${GREEN}=" | head -c 80 && echo -e "${NC}"

# Infrastructure Awareness Pre-Check
echo -e "\n${BLUE}🤖 INFRASTRUCTURE AWARENESS PRE-CHECK${NC}"
echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Check 28-Agent Ecosystem
AGENT_COUNT=$(ls src/agent-core/agents/*.ts 2>/dev/null | wc -l | tr -d ' ')
echo -e "${CYAN}🤖 28-Agent Ecosystem: ${AGENT_COUNT}/28 agents available${NC}"

# Fix SmartWrapper Property Declarations

files=(
  "src/agent-core/agents/ChatResponseParserAgent.ts"
  "src/agent-core/agents/ConversationalDevAgent.ts" 
  "src/agent-core/agents/DevAgent.ts"
  "src/agent-core/agents/LivingUIAgent.ts"
  "src/agent-core/agents/ProactiveAutonomyAgent.ts"
  "src/agent-core/agents/UserBehaviorAgent.ts"
  "src/agent-core/agents/WorkflowEnhancementAgent.ts"
  "src/agent-core/engines/AutonomyProgressionEngine.ts"
)

for file in "${files[@]}"; do
  echo "🔧 Processing $file..."
  
  # Check if file already has the property
  if grep -q "private smartWrapper: SmartMethodWrapper" "$file"; then
    echo "  ⚠️ Already has smartWrapper property"
    continue
  fi
  
  # Find the class declaration line
  class_line=$(grep -n "export class.*extends.*{" "$file" | head -1 | cut -d: -f1)
  
  if [ -n "$class_line" ]; then
    # Add the property declaration after the class declaration
    sed -i '' "${class_line}a\\
  private smartWrapper: SmartMethodWrapper;" "$file"
    echo "  ✅ Added smartWrapper property"
  else
    echo "  ❌ Could not find class declaration"
  fi
done

echo "🎉 SmartWrapper property fixes completed!"

# Infrastructure Awareness Post-Check (NEW)
echo ""
echo -e "${BLUE}🤖 INFRASTRUCTURE AWARENESS POST-CHECK${NC}"
echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Validate 28-Agent Ecosystem after fixes
AGENT_COUNT=$(ls src/agent-core/agents/*.ts 2>/dev/null | wc -l | tr -d ' ')
echo -e "${CYAN}🤖 28-Agent Ecosystem: ${AGENT_COUNT}/28 agents verified${NC}"

# Check if TypeScript compilation is successful after fixes
echo -e "${CYAN}📝 TypeScript validation after fixes...${NC}"
if npm run type-check >/dev/null 2>&1; then
    echo -e "${GREEN}📝 TypeScript: ✅ No errors after SmartWrapper fixes${NC}"
else
    TS_ERRORS=$(npm run type-check 2>&1 | grep "error TS" | wc -l | tr -d ' ')
    echo -e "${YELLOW}📝 TypeScript: ⚠️  ${TS_ERRORS} errors remaining after fixes${NC}"
fi

# Count successful fixes
FIXED_COUNT=0
for file in "${files[@]}"; do
    if [ -f "$file" ] && grep -q "private smartWrapper: SmartMethodWrapper" "$file"; then
        ((FIXED_COUNT++))
    fi
done

echo -e "${CYAN}🔧 SmartWrapper fixes applied: ${FIXED_COUNT}/${#files[@]} files${NC}"

echo ""
echo -e "${GREEN}🧠 AI Component Analysis Support:${NC}"
echo -e "${CYAN}   • Component Analysis: ollama run deepseek-r1:7b 'SmartWrapper component analysis needed'${NC}"
echo -e "${CYAN}   • Property Strategy: ollama run devstral:latest 'Component property coordination'${NC}"

echo ""
echo -e "${YELLOW}Infrastructure-Aware Next Steps:${NC}"
echo -e "${CYAN}1. Verify all 28 agents have proper SmartWrapper integration${NC}"
echo -e "${CYAN}2. Run 'npm run type-check' to validate TypeScript compilation${NC}"
echo -e "${CYAN}3. Test agent functionality with enhanced SmartWrapper properties${NC}"
echo -e "${CYAN}4. Use AI assistance for complex component property patterns${NC}" 