#!/usr/bin/env node

/**
 * 🧹 COMPREHENSIVE MODEL REFERENCE CLEANUP SCRIPT
 * Removes ALL deepseek-r1:8b references and replaces with deepseek-r1:8b
 * Ensures consistent model usage across all files
 */

const fs = require('fs');
const path = require('path');

// Model Reference Cleanup Rules
const CLEANUP_RULES = {
  // Fix deepseek-r1 version references
  deepseekR1: [
    { from: /deepseek-r1:8b/g, to: 'deepseek-r1:8b' },
    { from: /deepseek-r1:8b/g, to: 'deepseek-r1:8b' },
    { from: /'deepseek-r1:8b'/g, to: "'deepseek-r1:8b'" },
    { from: /"deepseek-r1:8b"/g, to: '"deepseek-r1:8b"' },
    { from: /R1 \(deepseek-r1:8b\)/g, to: 'R1 (deepseek-r1:8b)' },
    { from: /deepseek-r1:8b \(4\.7 GB\)/g, to: 'deepseek-r1:8b (5.2 GB)' },
    { from: /deepseek-r1:8b \(local\)/g, to: 'deepseek-r1:8b (local)' }
  ],
  
  // Update model listing patterns
  modelLists: [
    { from: /Only deepseek-r1:8b and devstral:latest \(local\)/g, to: 'Only deepseek-r1:8b and devstral:latest (local)' },
    { from: /deepseek-r1:8b, devstral:latest/g, to: 'deepseek-r1:8b, devstral:latest' },
    { from: /deepseek-r1:8b and devstral:latest/g, to: 'deepseek-r1:8b and devstral:latest' },
    { from: /Only deepseek-r1:8b \(local\), Strategic Analysis \(deepseek-r1:8b\) \(chat interface\)/g, to: 'Only deepseek-r1:8b and devstral:latest (local)' }
  ],
  
  // Update AI model references in text
  textReferences: [
    { from: /R1 \(deepseek-r1:8b\) strategic analysis/g, to: 'R1 (deepseek-r1:8b) strategic analysis' },
    { from: /deepseek-r1:8b for strategic/g, to: 'deepseek-r1:8b for strategic' },
    { from: /using deepseek-r1:8b/g, to: 'using deepseek-r1:8b' },
    { from: /with deepseek-r1:8b/g, to: 'with deepseek-r1:8b' }
  ]
};

// Files to process (excluding node_modules and .git)
const EXCLUDE_DIRS = ['node_modules', '.git', '.next', 'dist', 'build'];

function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  const validExtensions = ['.ts', '.tsx', '.js', '.jsx', '.md', '.json', '.mjs'];
  return validExtensions.includes(ext);
}

function processDirectory(dirPath) {
  const items = fs.readdirSync(dirPath);
  const results = [];
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      if (!EXCLUDE_DIRS.includes(item)) {
        results.push(...processDirectory(itemPath));
      }
    } else if (shouldProcessFile(itemPath)) {
      results.push(itemPath);
    }
  }
  
  return results;
}

function cleanupFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let hasChanges = false;
    const changes = [];
    
    // Apply all cleanup rules
    Object.entries(CLEANUP_RULES).forEach(([category, rules]) => {
      rules.forEach(rule => {
        const originalContent = content;
        content = content.replace(rule.from, rule.to);
        
        if (content !== originalContent) {
          hasChanges = true;
          const matches = (originalContent.match(rule.from) || []).length;
          changes.push(`${category}: ${matches} replacements`);
        }
      });
    });
    
    if (hasChanges) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ Cleaned: ${filePath}`);
      changes.forEach(change => console.log(`   • ${change}`));
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

// Main execution
async function main() {
  console.log('🧹 FIXING ALL DEEPSEEK-R1:7B → DEEPSEEK-R1:8B REFERENCES...\n');
  
  const startTime = Date.now();
  const files = processDirectory('.');
  
  console.log(`📁 Found ${files.length} files to process\n`);
  
  let cleanedCount = 0;
  let totalFiles = 0;
  
  for (const file of files) {
    totalFiles++;
    if (cleanupFile(file)) {
      cleanedCount++;
    }
  }
  
  const duration = Date.now() - startTime;
  
  console.log('\n🎯 CLEANUP COMPLETE!');
  console.log(`📊 Results:`);
  console.log(`   • Files processed: ${totalFiles}`);
  console.log(`   • Files cleaned: ${cleanedCount}`);
  console.log(`   • Duration: ${duration}ms`);
  console.log('\n✅ ALL MODEL REFERENCES NOW CORRECTLY USE:');
  console.log('   • deepseek-r1:8b (Strategic Analysis)');
  console.log('   • devstral:latest (Coordination)');
}

// Run the cleanup
main().catch(console.error); 