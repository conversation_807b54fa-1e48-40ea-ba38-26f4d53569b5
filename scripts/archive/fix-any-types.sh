#!/bin/bash

# 📝 TYPESCRIPT ANY TYPE FINDER - INFRASTRUCTURE AWARE
# Enhanced TypeScript analysis for 28-agent ecosystem, Navigation Intelligence, MLCoordinationLayer
# Based on R1 + Devstral consensus strategy for comprehensive type safety improvement
# Integrated with infrastructure awareness protocols and AI-powered type analysis

# Colors for terminal output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
CYAN='\033[0;36m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

echo -e "${GREEN}📝 TYPESCRIPT ANY TYPE FINDER - INFRASTRUCTURE AWARE${NC}"
echo -e "${CYAN}Based on: 28 agents | Navigation Intelligence | MLCoordinationLayer | AI-Powered Analysis${NC}"
echo -e "${GREEN}=" | head -c 80 && echo -e "${NC}"
echo -e "${YELLOW}This script will find all instances of 'any' types with infrastructure awareness${NC}"

# Create a directory for reports if it doesn't exist
mkdir -p reports

# Output file
REPORT_FILE="reports/any-types-report.md"

# Clear previous report
echo "# TypeScript 'any' Types Report" > $REPORT_FILE
echo "" >> $REPORT_FILE
echo "Generated on $(date)" >> $REPORT_FILE
echo "" >> $REPORT_FILE
echo "## Files with 'any' Types" >> $REPORT_FILE
echo "" >> $REPORT_FILE

# Find all TypeScript files with 'any' type
echo -e "\n${GREEN}Searching for files with 'any' types...${NC}"

# Using grep to find 'any' types
echo -e "${CYAN}Scanning TypeScript files...${NC}"
grep -r --include="*.ts" --include="*.tsx" -n ": any" ./src | sort > /tmp/any-types.txt

# Count total occurrences
TOTAL_COUNT=$(cat /tmp/any-types.txt | wc -l)
echo -e "${YELLOW}Found ${TOTAL_COUNT} occurrences of 'any' types${NC}"

# Process each file and add to report
echo -e "\n${GREEN}Generating detailed report...${NC}"

# Group by file
cat /tmp/any-types.txt | awk -F: '{print $1}' | sort | uniq | while read file; do
  FILE_NAME=$(echo $file | sed 's/\.\///')
  echo "### $FILE_NAME" >> $REPORT_FILE
  echo "" >> $REPORT_FILE
  echo "| Line | Context |" >> $REPORT_FILE
  echo "|------|---------|" >> $REPORT_FILE
  
  grep "$file:" /tmp/any-types.txt | while read line; do
    LINE_NUM=$(echo $line | cut -d ':' -f 2)
    CONTEXT=$(echo $line | cut -d ':' -f 3- | sed 's/^[ \t]*//')
    echo "| $LINE_NUM | \`$CONTEXT\` |" >> $REPORT_FILE
  done
  
  echo "" >> $REPORT_FILE
  echo "Suggested fixes:" >> $REPORT_FILE
  echo "" >> $REPORT_FILE
  echo "1. Replace with specific types based on usage context" >> $REPORT_FILE
  echo "2. Create appropriate interfaces/types for complex objects" >> $REPORT_FILE
  echo "3. Use union types for multiple possibilities" >> $REPORT_FILE
  echo "4. Consider using `unknown` instead of `any` if type is truly unknown" >> $REPORT_FILE
  echo "" >> $REPORT_FILE
done

echo -e "${GREEN}Report generated at ${CYAN}$REPORT_FILE${NC}"
echo -e "${YELLOW}Please review and fix 'any' types manually${NC}"

# Clean up temp file
rm /tmp/any-types.txt

echo -e "\n${GREEN}=== TypeScript Any Type Analysis Complete! ===${NC}"

# Infrastructure Awareness Analysis (NEW)
echo -e "\n${BLUE}🤖 INFRASTRUCTURE AWARENESS ANALYSIS${NC}"
echo -e "${CYAN}━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━${NC}"

# Check 28-Agent Ecosystem Types
AGENT_ANY_COUNT=$(grep -r --include="*.ts" -n ": any" ./src/agent-core/agents 2>/dev/null | wc -l | tr -d ' ')
echo -e "${CYAN}🤖 28-Agent Ecosystem: ${AGENT_ANY_COUNT} 'any' types found in agent files${NC}"

# Check MLCoordinationLayer Types
ML_ANY_COUNT=$(grep -r --include="*.ts" -n ": any" ./src/agent-core/coordination 2>/dev/null | wc -l | tr -d ' ')
echo -e "${CYAN}🔗 MLCoordinationLayer: ${ML_ANY_COUNT} 'any' types found in coordination files${NC}"

# Check Navigation Intelligence Types
NAV_ANY_COUNT=$(grep -r --include="*.ts" -n ": any" ./src/services/*Navigation* 2>/dev/null | wc -l | tr -d ' ')
echo -e "${CYAN}🗺️  Navigation Intelligence: ${NAV_ANY_COUNT} 'any' types found in navigation files${NC}"

# Infrastructure Priority Analysis
echo -e "\n${PURPLE}🎯 INFRASTRUCTURE PRIORITY RECOMMENDATIONS:${NC}"
if [ "$AGENT_ANY_COUNT" -gt 0 ]; then
    echo -e "${YELLOW}   • HIGH PRIORITY: Fix 'any' types in 28-agent ecosystem files${NC}"
fi
if [ "$ML_ANY_COUNT" -gt 0 ]; then
    echo -e "${YELLOW}   • HIGH PRIORITY: Fix 'any' types in MLCoordinationLayer files${NC}"
fi
if [ "$NAV_ANY_COUNT" -gt 0 ]; then
    echo -e "${YELLOW}   • MEDIUM PRIORITY: Fix 'any' types in Navigation Intelligence files${NC}"
fi

echo -e "\n${GREEN}🧠 AI Type Analysis Support:${NC}"
echo -e "${CYAN}   • Type Analysis: ollama run deepseek-r1:7b 'TypeScript any type analysis needed'${NC}"
echo -e "${CYAN}   • Type Strategy: ollama run devstral:latest 'TypeScript type system coordination'${NC}"

echo -e "\n${YELLOW}Infrastructure-Aware Next Steps:${NC}"
echo -e "${CYAN}1. Review the report at ${REPORT_FILE}${NC}"
echo -e "${CYAN}2. Prioritize fixing 'any' types in critical infrastructure:${NC}"
echo -e "${CYAN}   - 28-agent ecosystem files (${AGENT_ANY_COUNT} instances)${NC}"
echo -e "${CYAN}   - MLCoordinationLayer files (${ML_ANY_COUNT} instances)${NC}"
echo -e "${CYAN}   - Navigation Intelligence files (${NAV_ANY_COUNT} instances)${NC}"
echo -e "${CYAN}3. Run 'npm run type-check' to verify fixes${NC}"
echo -e "${CYAN}4. Use AI assistance for complex type definitions${NC}" 