#!/usr/bin/env node

/**
 * Agent Verification Script
 * Comprehensive analysis of all 28 agents in the ecosystem
 */

const fs = require('fs');
const path = require('path');

const AGENTS_DIR = path.join(__dirname, '../src/agent-core/agents');

// Expected agent categories and their characteristics
const EXPECTED_CATEGORIES = {
  'Intelligence': ['ClaudeIntelligenceEnhanced'],
  'Development': ['DevAgentIntelligenceEnhanced', 'AutonomousDevAgentIntelligenceEnhanced', 'FeatureDiscoveryAgentIntelligenceEnhanced'],
  'Testing': ['TestAgentIntelligenceEnhanced'],
  'Security': ['SecurityAgentIntelligenceEnhanced'],
  'Operations': ['OpsAgentIntelligenceEnhanced', 'ThermalManagementAgentIntelligenceEnhanced'],
  'Monitoring': ['ErrorMonitoringAgentIntelligenceEnhanced', 'PerformanceMonitoringAgentIntelligenceEnhanced', 'UserBehaviorAnalysisAgentIntelligenceEnhanced', 'ProcessMonitoringAgentIntelligenceEnhanced'],
  'Creative': ['UIAgentIntelligenceEnhanced', 'LivingInterfaceAgentIntelligenceEnhanced'],
  'Communication': ['ChatParserAgentIntelligenceEnhanced', 'ConversationalDevelopmentAgentIntelligenceEnhanced', 'NotificationAgentIntelligenceEnhanced'],
  'Coordination': ['ProactiveAutonomyAgentIntelligenceEnhanced', 'WorkflowOptimizationAgentIntelligenceEnhanced', 'ConfigurationAgentIntelligenceEnhanced', 'ResourceAllocationAgentIntelligenceEnhanced', 'PriorityManagementAgentIntelligenceEnhanced', 'TaskProgressionAgentIntelligenceEnhanced', 'StrategicPlanningAgentIntelligenceEnhanced', 'DataIntelligenceAgentIntelligenceEnhanced', 'CollectiveIntelligenceAgentIntelligenceEnhanced', 'IntelligencePathwayAgentIntelligenceEnhanced']
};

async function verifyAgents() {
  console.log('🔍 Starting comprehensive 28-agent verification...\n');

  try {
    // Read all agent files
    const files = fs.readdirSync(AGENTS_DIR);
    const agentFiles = files.filter(file => file.endsWith('IntelligenceEnhanced.ts'));
    
    console.log(`📊 Found ${agentFiles.length} IntelligenceEnhanced agent files`);
    console.log(`🎯 Expected: 28 agents\n`);

    if (agentFiles.length !== 28) {
      console.log(`❌ MISMATCH: Expected 28 agents, found ${agentFiles.length}`);
    } else {
      console.log(`✅ CORRECT: Found exactly 28 agents`);
    }

    // Analyze each agent
    const agentAnalysis = [];
    let totalLines = 0;
    let totalSize = 0;
    const categories = {};

    for (const file of agentFiles) {
      const filePath = path.join(AGENTS_DIR, file);
      const content = fs.readFileSync(filePath, 'utf-8');
      const stats = fs.statSync(filePath);
      
      const lines = content.split('\n').length;
      totalLines += lines;
      totalSize += stats.size;

      // Extract agent class name
      const classMatch = content.match(/export class (\w+(?:Agent|Engine)(?:IntelligenceEnhanced)?)\s+extends/);
      const agentName = classMatch ? classMatch[1] : file.replace('.ts', '');

      // Determine category
      const category = determineCategory(agentName);
      if (!categories[category]) categories[category] = [];
      categories[category].push(agentName);

      // Extract business value
      const businessValueMatch = content.match(/BUSINESS VALUE:\s*\$([0-9.]+[MK]?)/);
      const businessValue = businessValueMatch ? businessValueMatch[1] : 'Unknown';

      // Extract intelligence level
      const intelligenceLevelMatch = content.match(/INTELLIGENCE LEVEL:\s*(\w+)/);
      const intelligenceLevel = intelligenceLevelMatch ? intelligenceLevelMatch[1] : 'Unknown';

      // Extract complexity
      const complexityMatch = content.match(/COMPLEXITY:\s*([0-9]+\+?\s*interfaces)/);
      const complexity = complexityMatch ? complexityMatch[1] : 'Unknown';

      // Check for ML systems
      const mlSystems = [];
      if (content.includes('PATTERN_RECOGNIZER')) mlSystems.push('Pattern Recognition');
      if (content.includes('ADAPTIVE_OPTIMIZER')) mlSystems.push('Adaptive Optimization');
      if (content.includes('PREDICTIVE_ANALYZER')) mlSystems.push('Predictive Analysis');
      if (content.includes('SELF_LEARNING_SYSTEM')) mlSystems.push('Self Learning');

      // Check for coordination capabilities
      const hasCoordination = content.includes('CrossAgent') || content.includes('Coordination');
      const hasMLCoordination = content.includes('MLCoordinationLayer');
      const hasIntelligenceAware = content.includes('IntelligenceAware');

      agentAnalysis.push({
        file,
        agentName,
        category,
        lines,
        size: stats.size,
        businessValue,
        intelligenceLevel,
        complexity,
        mlSystems,
        hasCoordination,
        hasMLCoordination,
        hasIntelligenceAware
      });
    }

    // Display results
    console.log('\n📋 AGENT ANALYSIS SUMMARY');
    console.log('=' .repeat(50));
    console.log(`Total Agents: ${agentFiles.length}`);
    console.log(`Total Lines of Code: ${totalLines.toLocaleString()}`);
    console.log(`Total Size: ${(totalSize / 1024 / 1024).toFixed(2)} MB`);
    console.log(`Average Lines per Agent: ${Math.round(totalLines / agentFiles.length)}`);

    console.log('\n🏷️ CATEGORY DISTRIBUTION');
    console.log('=' .repeat(50));
    for (const [category, agents] of Object.entries(categories)) {
      console.log(`${category}: ${agents.length} agents`);
      agents.forEach(agent => console.log(`  - ${agent}`));
    }

    console.log('\n🧠 INTELLIGENCE LEVELS');
    console.log('=' .repeat(50));
    const intelligenceLevels = {};
    agentAnalysis.forEach(agent => {
      if (!intelligenceLevels[agent.intelligenceLevel]) {
        intelligenceLevels[agent.intelligenceLevel] = 0;
      }
      intelligenceLevels[agent.intelligenceLevel]++;
    });
    for (const [level, count] of Object.entries(intelligenceLevels)) {
      console.log(`${level}: ${count} agents`);
    }

    console.log('\n🤖 ML SYSTEM INTEGRATION');
    console.log('=' .repeat(50));
    const mlStats = {
      withML: agentAnalysis.filter(a => a.mlSystems.length > 0).length,
      withCoordination: agentAnalysis.filter(a => a.hasCoordination).length,
      withMLCoordination: agentAnalysis.filter(a => a.hasMLCoordination).length,
      withIntelligenceAware: agentAnalysis.filter(a => a.hasIntelligenceAware).length
    };
    
    console.log(`Agents with ML Systems: ${mlStats.withML}/${agentFiles.length}`);
    console.log(`Agents with Coordination: ${mlStats.withCoordination}/${agentFiles.length}`);
    console.log(`Agents with ML Coordination: ${mlStats.withMLCoordination}/${agentFiles.length}`);
    console.log(`Agents with Intelligence-Aware: ${mlStats.withIntelligenceAware}/${agentFiles.length}`);

    console.log('\n💰 BUSINESS VALUE ANALYSIS');
    console.log('=' .repeat(50));
    const businessValues = {};
    agentAnalysis.forEach(agent => {
      if (!businessValues[agent.businessValue]) {
        businessValues[agent.businessValue] = 0;
      }
      businessValues[agent.businessValue]++;
    });
    for (const [value, count] of Object.entries(businessValues)) {
      console.log(`${value}: ${count} agents`);
    }

    // Architecture validation
    console.log('\n🏗️ ARCHITECTURE VALIDATION');
    console.log('=' .repeat(50));
    
    const validationResults = {
      allExtendsAgent: agentAnalysis.every(a => {
        const content = fs.readFileSync(path.join(AGENTS_DIR, a.file), 'utf-8');
        return content.includes('extends Agent') || content.includes('extends BaseAgent');
      }),
      allHaveProcessRequest: agentAnalysis.every(a => {
        const content = fs.readFileSync(path.join(AGENTS_DIR, a.file), 'utf-8');
        return content.includes('processRequest');
      }),
      allHaveIntelligenceProfile: agentAnalysis.filter(a => {
        const content = fs.readFileSync(path.join(AGENTS_DIR, a.file), 'utf-8');
        return content.includes('AgentIntelligenceProfile');
      }).length
    };

    console.log(`✅ All agents extend base Agent class: ${validationResults.allExtendsAgent}`);
    console.log(`✅ All agents implement processRequest: ${validationResults.allHaveProcessRequest}`);
    console.log(`📊 Agents with Intelligence Profiles: ${validationResults.allHaveIntelligenceProfile}/${agentFiles.length}`);

    // Final assessment
    console.log('\n🎯 FINAL ASSESSMENT');
    console.log('=' .repeat(50));
    
    const isComplete = agentFiles.length === 28 && 
                      validationResults.allExtendsAgent && 
                      validationResults.allHaveProcessRequest &&
                      mlStats.withIntelligenceAware > 20;

    if (isComplete) {
      console.log('✅ 28-AGENT ECOSYSTEM: FULLY IMPLEMENTED AND VALIDATED');
      console.log('🚀 Ready for production deployment');
    } else {
      console.log('⚠️  28-AGENT ECOSYSTEM: NEEDS ATTENTION');
      console.log('🔧 Some components require completion');
    }

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

function determineCategory(agentName) {
  const name = agentName.toLowerCase();
  
  if (name.includes('test') || name.includes('quality')) return 'Testing';
  if (name.includes('dev') || name.includes('feature') || name.includes('implementation')) return 'Development';
  if (name.includes('security') || name.includes('auth')) return 'Security';
  if (name.includes('ops') || name.includes('thermal') || name.includes('deploy')) return 'Operations';
  if (name.includes('monitor') || name.includes('performance') || name.includes('error') || name.includes('behavior') || name.includes('process')) return 'Monitoring';
  if (name.includes('ui') || name.includes('interface') || name.includes('living')) return 'Creative';
  if (name.includes('chat') || name.includes('conversational') || name.includes('notification') || name.includes('communication')) return 'Communication';
  if (name.includes('claude') || name.includes('intelligence') && !name.includes('collective')) return 'Intelligence';
  
  return 'Coordination';
}

// Run verification
verifyAgents().catch(console.error);
