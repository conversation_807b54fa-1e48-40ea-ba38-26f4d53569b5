{"profiles": [{"agentId": "test-agent", "tier": "PROBATION", "performanceMetrics": {"successRate": 0, "averageResponseTime": 0, "resourceEfficiency": 50, "problemResolutionCount": 0, "systemImprovementContributions": 0, "errorRate": 0, "resourceWasteLevel": 0, "userSatisfactionScore": 50, "collaborationEffectiveness": 50}, "localAIAccess": {"accessLevel": "restricted", "maxRequestsPerHour": 3, "maxRequestsPerDay": 10, "priorityLevel": "low", "allowedModels": ["deepseek-r1:8b"], "maxPromptLength": 500, "thermalBudgetAllocation": 5, "lastAccessTime": "1970-01-01T00:00:00.000Z", "totalRequests": 0, "successfulRequests": 0}, "problemSolvingRecord": {"realProblemsIdentified": 0, "realProblemsSolved": 0, "systemImprovementsImplemented": 0, "bugsPrevented": 0, "performanceOptimizationsDelivered": 0, "costSavingsGenerated": 0, "innovativeSolutionsProposed": 0, "collaborativeSuccesses": 0}, "systemIntegrationQuality": {"integrationScore": 40, "apiCompatibility": 50, "errorHandlingQuality": 50, "resourceManagementCompliance": 50, "documentationQuality": 50, "codeQuality": 50, "testCoverage": 0, "securityCompliance": 50}, "qualityGates": [{"gateId": "test-agent_performance_baseline", "name": "Performance Baseline", "description": "Achieve minimum performance standards", "criteria": [{"criterion": "Success Rate", "target": 85, "current": 0, "trend": "stable", "weight": 0.3}, {"criterion": "Error Rate", "target": 5, "current": 0, "trend": "stable", "weight": 0.2}, {"criterion": "Resource Efficiency", "target": 80, "current": 50, "trend": "stable", "weight": 0.3}, {"criterion": "Response Time", "target": 2000, "current": 0, "trend": "stable", "weight": 0.2}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:53.047Z", "blockingIssues": [], "recommendations": []}, {"gateId": "test-agent_problem_solving_validation", "name": "Problem Solving Validation", "description": "Demonstrate real problem-solving capabilities", "criteria": [{"criterion": "Problems Identified", "target": 3, "current": 0, "trend": "stable", "weight": 0.25}, {"criterion": "Problems Solved", "target": 2, "current": 0, "trend": "stable", "weight": 0.35}, {"criterion": "System Improvements", "target": 1, "current": 0, "trend": "stable", "weight": 0.4}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:53.047Z", "blockingIssues": [], "recommendations": []}, {"gateId": "test-agent_system_integration", "name": "System Integration Quality", "description": "Maintain high-quality system integration", "criteria": [{"criterion": "API Compatibility", "target": 90, "current": 50, "trend": "stable", "weight": 0.3}, {"criterion": "Erro<PERSON>", "target": 85, "current": 50, "trend": "stable", "weight": 0.25}, {"criterion": "Documentation Quality", "target": 80, "current": 50, "trend": "stable", "weight": 0.2}, {"criterion": "Security Compliance", "target": 95, "current": 50, "trend": "stable", "weight": 0.25}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:53.047Z", "blockingIssues": [], "recommendations": []}], "lastReview": "2025-06-09T07:45:53.047Z", "nextReviewDue": "2025-06-16T07:45:53.047Z", "improvementPlan": {"planId": "test-agent_initial_plan", "objectives": [{"objective": "Pass performance baseline quality gate", "priority": "high", "targetMetric": "Success Rate", "currentValue": 0, "targetValue": 85, "deadline": "2025-06-16T07:45:53.047Z", "actions": ["Complete 10 successful operations", "Maintain error rate below 5%", "Optimize response time"]}, {"objective": "Demonstrate real problem-solving value", "priority": "high", "targetMetric": "Problems Solved", "currentValue": 0, "targetValue": 2, "deadline": "2025-06-23T07:45:53.047Z", "actions": ["Identify actual system issues", "Implement verifiable improvements", "Document impact and value created"]}], "timeline": "14 days initial evaluation", "milestones": [{"milestone": "First successful operation", "dueDate": "2025-06-10T07:45:53.047Z", "status": "pending", "deliverables": ["Successful task completion", "Performance metrics logged"], "completionCriteria": ["Success rate > 0%", "No critical errors"]}], "resourceRequirements": ["Limited LocalAI access", "Monitoring tools", "Documentation templates"], "successCriteria": ["Pass all initial quality gates", "Demonstrate measurable value creation", "Maintain compliance with governance standards"]}}, {"agentId": "duplicate-agent", "tier": "PROBATION", "performanceMetrics": {"successRate": 0, "averageResponseTime": 0, "resourceEfficiency": 50, "problemResolutionCount": 0, "systemImprovementContributions": 0, "errorRate": 0, "resourceWasteLevel": 0, "userSatisfactionScore": 50, "collaborationEffectiveness": 50}, "localAIAccess": {"accessLevel": "restricted", "maxRequestsPerHour": 3, "maxRequestsPerDay": 10, "priorityLevel": "low", "allowedModels": ["deepseek-r1:8b"], "maxPromptLength": 500, "thermalBudgetAllocation": 5, "lastAccessTime": "1970-01-01T00:00:00.000Z", "totalRequests": 0, "successfulRequests": 0}, "problemSolvingRecord": {"realProblemsIdentified": 0, "realProblemsSolved": 0, "systemImprovementsImplemented": 0, "bugsPrevented": 0, "performanceOptimizationsDelivered": 0, "costSavingsGenerated": 0, "innovativeSolutionsProposed": 0, "collaborativeSuccesses": 0}, "systemIntegrationQuality": {"integrationScore": 40, "apiCompatibility": 50, "errorHandlingQuality": 50, "resourceManagementCompliance": 50, "documentationQuality": 50, "codeQuality": 50, "testCoverage": 0, "securityCompliance": 50}, "qualityGates": [{"gateId": "duplicate-agent_performance_baseline", "name": "Performance Baseline", "description": "Achieve minimum performance standards", "criteria": [{"criterion": "Success Rate", "target": 85, "current": 0, "trend": "stable", "weight": 0.3}, {"criterion": "Error Rate", "target": 5, "current": 0, "trend": "stable", "weight": 0.2}, {"criterion": "Resource Efficiency", "target": 80, "current": 50, "trend": "stable", "weight": 0.3}, {"criterion": "Response Time", "target": 2000, "current": 0, "trend": "stable", "weight": 0.2}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:53.055Z", "blockingIssues": [], "recommendations": []}, {"gateId": "duplicate-agent_problem_solving_validation", "name": "Problem Solving Validation", "description": "Demonstrate real problem-solving capabilities", "criteria": [{"criterion": "Problems Identified", "target": 3, "current": 0, "trend": "stable", "weight": 0.25}, {"criterion": "Problems Solved", "target": 2, "current": 0, "trend": "stable", "weight": 0.35}, {"criterion": "System Improvements", "target": 1, "current": 0, "trend": "stable", "weight": 0.4}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:53.055Z", "blockingIssues": [], "recommendations": []}, {"gateId": "duplicate-agent_system_integration", "name": "System Integration Quality", "description": "Maintain high-quality system integration", "criteria": [{"criterion": "API Compatibility", "target": 90, "current": 50, "trend": "stable", "weight": 0.3}, {"criterion": "Erro<PERSON>", "target": 85, "current": 50, "trend": "stable", "weight": 0.25}, {"criterion": "Documentation Quality", "target": 80, "current": 50, "trend": "stable", "weight": 0.2}, {"criterion": "Security Compliance", "target": 95, "current": 50, "trend": "stable", "weight": 0.25}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:53.055Z", "blockingIssues": [], "recommendations": []}], "lastReview": "2025-06-09T07:45:53.055Z", "nextReviewDue": "2025-06-16T07:45:53.055Z", "improvementPlan": {"planId": "duplicate-agent_initial_plan", "objectives": [{"objective": "Pass performance baseline quality gate", "priority": "high", "targetMetric": "Success Rate", "currentValue": 0, "targetValue": 85, "deadline": "2025-06-16T07:45:53.055Z", "actions": ["Complete 10 successful operations", "Maintain error rate below 5%", "Optimize response time"]}, {"objective": "Demonstrate real problem-solving value", "priority": "high", "targetMetric": "Problems Solved", "currentValue": 0, "targetValue": 2, "deadline": "2025-06-23T07:45:53.056Z", "actions": ["Identify actual system issues", "Implement verifiable improvements", "Document impact and value created"]}], "timeline": "14 days initial evaluation", "milestones": [{"milestone": "First successful operation", "dueDate": "2025-06-10T07:45:53.056Z", "status": "pending", "deliverables": ["Successful task completion", "Performance metrics logged"], "completionCriteria": ["Success rate > 0%", "No critical errors"]}], "resourceRequirements": ["Limited LocalAI access", "Monitoring tools", "Documentation templates"], "successCriteria": ["Pass all initial quality gates", "Demonstrate measurable value creation", "Maintain compliance with governance standards"]}}, {"agentId": "restricted-agent", "tier": "PROBATION", "performanceMetrics": {"successRate": 0, "averageResponseTime": 0, "resourceEfficiency": 50, "problemResolutionCount": 0, "systemImprovementContributions": 0, "errorRate": 0, "resourceWasteLevel": 0, "userSatisfactionScore": 50, "collaborationEffectiveness": 50}, "localAIAccess": {"accessLevel": "restricted", "maxRequestsPerHour": 3, "maxRequestsPerDay": 10, "priorityLevel": "low", "allowedModels": ["deepseek-r1:8b"], "maxPromptLength": 500, "thermalBudgetAllocation": 5, "lastAccessTime": "2025-06-09T07:45:53.060Z", "totalRequests": 1, "successfulRequests": 0}, "problemSolvingRecord": {"realProblemsIdentified": 0, "realProblemsSolved": 0, "systemImprovementsImplemented": 0, "bugsPrevented": 0, "performanceOptimizationsDelivered": 0, "costSavingsGenerated": 0, "innovativeSolutionsProposed": 0, "collaborativeSuccesses": 0}, "systemIntegrationQuality": {"integrationScore": 40, "apiCompatibility": 50, "errorHandlingQuality": 50, "resourceManagementCompliance": 50, "documentationQuality": 50, "codeQuality": 50, "testCoverage": 0, "securityCompliance": 50}, "qualityGates": [{"gateId": "restricted-agent_performance_baseline", "name": "Performance Baseline", "description": "Achieve minimum performance standards", "criteria": [{"criterion": "Success Rate", "target": 85, "current": 0, "trend": "stable", "weight": 0.3}, {"criterion": "Error Rate", "target": 5, "current": 0, "trend": "stable", "weight": 0.2}, {"criterion": "Resource Efficiency", "target": 80, "current": 50, "trend": "stable", "weight": 0.3}, {"criterion": "Response Time", "target": 2000, "current": 0, "trend": "stable", "weight": 0.2}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:53.057Z", "blockingIssues": [], "recommendations": []}, {"gateId": "restricted-agent_problem_solving_validation", "name": "Problem Solving Validation", "description": "Demonstrate real problem-solving capabilities", "criteria": [{"criterion": "Problems Identified", "target": 3, "current": 0, "trend": "stable", "weight": 0.25}, {"criterion": "Problems Solved", "target": 2, "current": 0, "trend": "stable", "weight": 0.35}, {"criterion": "System Improvements", "target": 1, "current": 0, "trend": "stable", "weight": 0.4}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:53.057Z", "blockingIssues": [], "recommendations": []}, {"gateId": "restricted-agent_system_integration", "name": "System Integration Quality", "description": "Maintain high-quality system integration", "criteria": [{"criterion": "API Compatibility", "target": 90, "current": 50, "trend": "stable", "weight": 0.3}, {"criterion": "Erro<PERSON>", "target": 85, "current": 50, "trend": "stable", "weight": 0.25}, {"criterion": "Documentation Quality", "target": 80, "current": 50, "trend": "stable", "weight": 0.2}, {"criterion": "Security Compliance", "target": 95, "current": 50, "trend": "stable", "weight": 0.25}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:53.057Z", "blockingIssues": [], "recommendations": []}], "lastReview": "2025-06-09T07:45:53.057Z", "nextReviewDue": "2025-06-16T07:45:53.057Z", "improvementPlan": {"planId": "restricted-agent_initial_plan", "objectives": [{"objective": "Pass performance baseline quality gate", "priority": "high", "targetMetric": "Success Rate", "currentValue": 0, "targetValue": 85, "deadline": "2025-06-16T07:45:53.057Z", "actions": ["Complete 10 successful operations", "Maintain error rate below 5%", "Optimize response time"]}, {"objective": "Demonstrate real problem-solving value", "priority": "high", "targetMetric": "Problems Solved", "currentValue": 0, "targetValue": 2, "deadline": "2025-06-23T07:45:53.057Z", "actions": ["Identify actual system issues", "Implement verifiable improvements", "Document impact and value created"]}], "timeline": "14 days initial evaluation", "milestones": [{"milestone": "First successful operation", "dueDate": "2025-06-10T07:45:53.057Z", "status": "pending", "deliverables": ["Successful task completion", "Performance metrics logged"], "completionCriteria": ["Success rate > 0%", "No critical errors"]}], "resourceRequirements": ["Limited LocalAI access", "Monitoring tools", "Documentation templates"], "successCriteria": ["Pass all initial quality gates", "Demonstrate measurable value creation", "Maintain compliance with governance standards"]}}, {"agentId": "performance-agent", "tier": "PROBATION", "performanceMetrics": {"successRate": 0, "averageResponseTime": 0, "resourceEfficiency": 50, "problemResolutionCount": 0, "systemImprovementContributions": 0, "errorRate": 0, "resourceWasteLevel": 0, "userSatisfactionScore": 50, "collaborationEffectiveness": 50}, "localAIAccess": {"accessLevel": "restricted", "maxRequestsPerHour": 3, "maxRequestsPerDay": 10, "priorityLevel": "low", "allowedModels": ["deepseek-r1:8b"], "maxPromptLength": 500, "thermalBudgetAllocation": 5, "lastAccessTime": "1970-01-01T00:00:00.000Z", "totalRequests": 0, "successfulRequests": 0}, "problemSolvingRecord": {"realProblemsIdentified": 0, "realProblemsSolved": 0, "systemImprovementsImplemented": 0, "bugsPrevented": 0, "performanceOptimizationsDelivered": 0, "costSavingsGenerated": 0, "innovativeSolutionsProposed": 0, "collaborativeSuccesses": 0}, "systemIntegrationQuality": {"integrationScore": 40, "apiCompatibility": 50, "errorHandlingQuality": 50, "resourceManagementCompliance": 50, "documentationQuality": 50, "codeQuality": 50, "testCoverage": 0, "securityCompliance": 50}, "qualityGates": [{"gateId": "performance-agent_performance_baseline", "name": "Performance Baseline", "description": "Achieve minimum performance standards", "criteria": [{"criterion": "Success Rate", "target": 85, "current": 0, "trend": "stable", "weight": 0.3}, {"criterion": "Error Rate", "target": 5, "current": 0, "trend": "stable", "weight": 0.2}, {"criterion": "Resource Efficiency", "target": 80, "current": 50, "trend": "stable", "weight": 0.3}, {"criterion": "Response Time", "target": 2000, "current": 0, "trend": "stable", "weight": 0.2}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:58.061Z", "blockingIssues": [], "recommendations": []}, {"gateId": "performance-agent_problem_solving_validation", "name": "Problem Solving Validation", "description": "Demonstrate real problem-solving capabilities", "criteria": [{"criterion": "Problems Identified", "target": 3, "current": 0, "trend": "stable", "weight": 0.25}, {"criterion": "Problems Solved", "target": 2, "current": 0, "trend": "stable", "weight": 0.35}, {"criterion": "System Improvements", "target": 1, "current": 0, "trend": "stable", "weight": 0.4}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:58.061Z", "blockingIssues": [], "recommendations": []}, {"gateId": "performance-agent_system_integration", "name": "System Integration Quality", "description": "Maintain high-quality system integration", "criteria": [{"criterion": "API Compatibility", "target": 90, "current": 50, "trend": "stable", "weight": 0.3}, {"criterion": "Erro<PERSON>", "target": 85, "current": 50, "trend": "stable", "weight": 0.25}, {"criterion": "Documentation Quality", "target": 80, "current": 50, "trend": "stable", "weight": 0.2}, {"criterion": "Security Compliance", "target": 95, "current": 50, "trend": "stable", "weight": 0.25}], "status": "pending", "lastEvaluation": "1970-01-01T00:00:00.000Z", "nextEvaluation": "2025-06-10T07:45:58.061Z", "blockingIssues": [], "recommendations": []}], "lastReview": "2025-06-09T07:45:58.061Z", "nextReviewDue": "2025-06-16T07:45:58.061Z", "improvementPlan": {"planId": "performance-agent_initial_plan", "objectives": [{"objective": "Pass performance baseline quality gate", "priority": "high", "targetMetric": "Success Rate", "currentValue": 0, "targetValue": 85, "deadline": "2025-06-16T07:45:58.061Z", "actions": ["Complete 10 successful operations", "Maintain error rate below 5%", "Optimize response time"]}, {"objective": "Demonstrate real problem-solving value", "priority": "high", "targetMetric": "Problems Solved", "currentValue": 0, "targetValue": 2, "deadline": "2025-06-23T07:45:58.061Z", "actions": ["Identify actual system issues", "Implement verifiable improvements", "Document impact and value created"]}], "timeline": "14 days initial evaluation", "milestones": [{"milestone": "First successful operation", "dueDate": "2025-06-10T07:45:58.061Z", "status": "pending", "deliverables": ["Successful task completion", "Performance metrics logged"], "completionCriteria": ["Success rate > 0%", "No critical errors"]}], "resourceRequirements": ["Limited LocalAI access", "Monitoring tools", "Documentation templates"], "successCriteria": ["Pass all initial quality gates", "Demonstrate measurable value creation", "Maintain compliance with governance standards"]}}], "lastSaved": "2025-06-09T07:45:58.061Z"}