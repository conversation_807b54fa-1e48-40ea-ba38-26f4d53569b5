{"name": "creAItive", "version": "0.9.0", "description": "CreAItive: An innovative AI-powered creative platform with autonomous agent systems and real-time collaboration tools. Built with Next.js 14 using Real-First Development methodology over 15 days in May 2025 (May 19 - June 2, 2025).", "keywords": ["creative", "ai", "autonomous", "agents", "platform", "collaboration"], "author": "CreAItive Team", "license": "MIT", "private": true, "scripts": {"dev": "next dev", "dev:next": "next dev", "dev:socket": "node server/socketServer.js", "dev:websocket": "node src/server/websocket-server.js", "dev:all": "concurrently \"npm run dev:next\" \"npm run dev:socket\"", "dev:realtime": "concurrently \"npm run dev:next\" \"npm run dev:websocket\"", "dev:kill": "chmod +x scripts/kill-dev.sh && scripts/kill-dev.sh", "unified": "node scripts/unified-workflow-commands.js", "unified:dev": "bash scripts/unified-dev.sh", "unified:build": "bash scripts/unified-build.sh", "unified:deploy": "bash scripts/unified-deploy.sh", "unified:maintenance": "bash scripts/unified-maintenance.sh", "unified:daily": "bash scripts/unified-daily.sh", "unified:agents": "bash scripts/unified-agents.sh", "unified:test": "bash scripts/unified-test.sh", "unified:emergency": "bash scripts/unified-emergency.sh", "unified:dashboard": "bash scripts/unified-dashboard.sh", "unified:docs": "bash scripts/unified-docs.sh", "unified:accuracy": "bash scripts/unified-accuracy.sh", "enhanced-error-monitor": "bash scripts/enhanced-error-monitor.sh", "infrastructure-health-check": "bash scripts/enhanced-error-monitor.sh", "predictive-error-analysis": "bash scripts/enhanced-error-monitor.sh --ai-analysis", "unified:project-status": "bash scripts/unified-project-status.sh", "unified:roadmap-review": "bash scripts/unified-roadmap-review.sh", "unified:task-management": "bash scripts/unified-task-management.sh", "log-cleanup": "node scripts/log-management-control.js", "log-cleanup-emergency": "node scripts/log-management-control.js --emergency", "log-monitor": "node scripts/automated-log-monitor.js", "log-status": "echo '📊 Checking log status...' && find .ai-inbox .ai-chat-bridge .claude-bridge -name '*.json' 2>/dev/null | wc -l && echo 'Total log files found'", "agent-queue-control": "echo '🚦 Agent queue management - checking scheduling system' && node -e \"console.log('Agent queue control activated - see IntelligentAIResourceManager for scheduling')\"", "configure-agent-queue": "node scripts/configure-agent-queue-control.js", "configure-agent-queue-production": "node scripts/configure-agent-queue-control.js --production", "agent-queue-status": "node scripts/configure-agent-queue-control.js --status", "agent-queue-monitor": "node scripts/agent-queue-monitor.js", "full-log-control": "npm run log-cleanup && npm run configure-agent-queue && echo '✅ Complete log management system configured for testing phase'", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "ANALYZE=true npm run build", "analyze:server": "BUNDLE_ANALYZE=server npm run build", "analyze:browser": "BUNDLE_ANALYZE=browser npm run build", "perf": "npm run analyze && echo 'Bundle analysis complete! Check .next/analyze/'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:real-claude-integration": "node scripts/test-real-claude-integration.js", "security-audit": "node scripts/security-audit.js", "security-check": "bash scripts/security-check.sh", "security-full": "bash scripts/security-full.sh", "type-check": "tsc --noEmit", "type-check-production": "tsc --project tsconfig.production.json", "type-check-tests": "tsc --project tsconfig.test.json", "format": "prettier --write \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{js,jsx,ts,tsx,json,css,md}\"", "validate-env": "node scripts/validate-real-first-config.js", "real-first-check": "npm run validate-env && npm run test:real-claude-integration", "prepare": "husky install", "fix-docs-warnings": "node scripts/fix-documentation-warnings.js", "bulk-fix-warnings": "node scripts/bulk-fix-remaining-warnings.js", "organize-docs-comprehensive": "node scripts/organize-docs-comprehensive.js", "visual-map": "node scripts/agent-ecosystem-visual-mapper.js", "visual-map-generate": "node scripts/agent-ecosystem-visual-mapper.js generate", "visual-map-update": "node scripts/agent-ecosystem-visual-mapper.js update", "update-memory-bank": "node scripts/update-memory-bank.js", "check-docs-consistency": "bash scripts/check-docs-consistency.sh", "memory-check": "npm run update-memory-bank && npm run check-docs-consistency", "request-ai-consensus": "echo '🤖 Requesting R1+Devstral consensus...' && ollama run deepseek-r1:8b 'Consensus needed: Review enhanced maintenance findings' && ollama run devstral:latest 'Coordination needed: Validate maintenance recommendations'", "daily-verify": "bash scripts/daily-system-check.sh", "agent-count": "curl -s http://localhost:3000/api/agents/status | jq '.data.agentCount'", "agent-health": "curl -s http://localhost:3000/api/agents/orchestrator | jq '.data.agents_managed'", "feature-check": "curl -s http://localhost:3000/api/agents/feature-discovery | jq '.data.discoveredFeatures'", "system-status": "curl -s http://localhost:3000/api/agents/status | jq '.data | {agentCount, autonomyLevel, activeAgents}'", "weekly-audit": "npm run security-full && npm run memory-check", "cursor-verify": "bash scripts/cursor-verify-with-agents.sh", "agent-respond": "node scripts/process-agent-requests.js", "agent-status": "node scripts/quick-agent-status.js", "develop-agent-intelligence": "node scripts/agent-intelligence-development.js", "develop-agents": "node scripts/agent-intelligence-development.js", "validate-readme-system": "node scripts/readme-system-validator.js", "emergency-fix": "bash scripts/emergency-fix.sh", "check-terminal-spam": "node scripts/check-terminal-spam.js", "check-activity-optimization": "node scripts/check-activity-optimization.js", "agent-calibration-summary": "node scripts/agent-calibration-summary.js", "enhanced-system-status": "curl -s http://localhost:3000/api/governance/enhanced-system-status | jq '.'", "register-active-agents": "node scripts/register-active-agents.js", "emergency-memory-cleanup": "node --expose-gc scripts/emergency-memory-cleanup.js", "setup-test-infrastructure": "node scripts/setup-test-infrastructure.js", "setup-dev": "bash scripts/daily-dev-setup.sh", "test:quantum": "jest tests/quantum --verbose", "test:mcp": "jest tests/mcp --verbose", "test:parallel-agents": "jest tests/parallel-agents --verbose", "test:scaling": "jest tests/scaling --verbose", "test:thermal": "jest tests/thermal --verbose", "migrate-agents": "node scripts/migrate-all-agents.js", "migrate-agents-full": "node scripts/migrate-all-agents-comprehensive.js", "stress-test-ai": "node tests/ai-enhancement-stress-test.js", "test-ai-safe": "npm run stress-test-ai && npm run security-check", "test-ai-safety": "node tests/quick-ai-safety-test.js", "test-all-safety": "npm run test-ai-safety && npm run security-check", "test-comprehensive": "node tests/comprehensive-ai-system-test.js", "test-full-system": "npm run test-comprehensive && npm run security-check", "monitor": "node scripts/continuous-ai-monitor.js", "test-agents-live": "node scripts/test-agents-live.js", "check-interface-conflicts": "node scripts/check-interface-conflicts.js", "fix-interface-conflicts": "node scripts/fix-interface-conflicts.js", "fix-interface-conflicts-incremental": "node scripts/fix-interface-conflicts-incremental.js", "phase1-fix-tests": "npm run type-check-tests", "phase2-fix-interfaces": "npm run type-check-production && npm run check-interface-conflicts", "dual-phase-validation": "npm run phase1-fix-tests && npm run phase2-fix-interfaces && npm run type-check && npm run build", "emergency-rollback": "git stash && echo 'Emergency rollback: all changes stashed'", "pre-build-checks": "npm run check-interface-conflicts && npm run type-check && npm run lint", "analyze-agent-architecture": "node scripts/agent-architecture-analyzer.js", "extract-success-patterns": "node scripts/extract-success-patterns.js", "verify-refactoring-revolution": "node scripts/agent-refactoring-verification-suite.js", "enhanced-phase-strategy": "node scripts/enhanced-phase-strategy.js", "continuous-progress-tracker": "node scripts/continuous-progress-tracker.js", "enhanced-agent-extractor": "node scripts/enhanced-agent-extractor.js", "automated-docs-verification": "node scripts/automated-documentation-verification.js", "docs-reality-check": "node scripts/automated-documentation-verification.js", "verify-docs-accuracy": "npm run automated-docs-verification && npm run organize-docs-comprehensive", "agent-development-tracking": "node scripts/agent-development-tracking-system.js", "agent-validation-dashboard": "node scripts/agent-development-tracking-system.js", "m2-max-optimize": "bash scripts/m2-max-ollama-optimizer.sh", "m2-max-test": "bash scripts/m2-max-ollama-optimizer.sh && echo '🎯 M2 Max optimization (Metal limitations aware) complete'", "m2-max-realistic": "echo '⚠️ Running REALISTIC M2 Max performance test accounting for Metal bf16 limitations...' && bash scripts/m2-max-ollama-optimizer.sh", "ollama-health": "ollama ps && echo '' && ollama list && echo '' && system_profiler SPHardwareDataType | grep -E '(Model|Chip|Memory)'", "task-manager:create-coordination-phase-0": "node scripts/task-manager-coordination.js --phase=0", "task-manager:setup-parallel-queues": "node scripts/task-manager-coordination.js --setup-queues", "task-manager:validate-ai-consensus": "node scripts/task-manager-coordination.js --validate-consensus", "task-manager:start-parallel-phase-1A-1B": "node scripts/task-manager-coordination.js --phase=1-parallel", "task-manager:monitor-concurrent-progress": "node scripts/task-manager-coordination.js --monitor", "task-manager:coordination-dashboard": "node scripts/task-manager-coordination.js --dashboard", "agent-mesh:demo": "node -e \"console.log('🚀 Starting Agent Mesh <PERSON>mo...'); require('./src/agent-core/mesh/AgentMeshManager.ts').AgentMeshManager.getInstance().runMeshDemo()\"", "coordination:phase3-test": "node -e \"console.log('🧪 Starting Phase 3 Integration Testing...'); const { IntelligenceCoordinationTesting } = require('./src/agent-core/testing/IntelligenceCoordinationTesting.ts'); IntelligenceCoordinationTesting.getInstance().runPhase3IntegrationTests().then(health => console.log('✅ Phase 3 Complete:', health)).catch(err => console.error('❌ Phase 3 Failed:', err))\"", "coordination:test-status": "node -e \"const { IntelligenceCoordinationTesting } = require('./src/agent-core/testing/IntelligenceCoordinationTesting.ts'); console.log('📊 Testing Status:', IntelligenceCoordinationTesting.getInstance().getTestingStatus())\"", "coordination:validate-system": "npm run coordination:phase3-test", "orchestration:status": "echo '🔍 Checking orchestration system status...' && curl -s http://localhost:3000/api/orchestration/metrics | jq '.architecture' 2>/dev/null || echo 'Dev server not running'", "utils:fix-common-errors": "echo '⚠️ This utility is currently disabled due to faulty patterns - see docs/🔧-utilities/fixes/fix-common-errors.js'", "utils:start-dev-clean": "bash docs/🔧-utilities/scripts/start-dev-clean.sh", "utils:start-dev": "bash docs/🔧-utilities/scripts/start-dev.sh", "utils:collaboration-server": "node docs/🔧-utilities/servers/server.js", "utils:list-all": "echo '🔧 Available utility scripts:' && find docs/🔧-utilities -name '*.js' -o -name '*.sh' | sort", "script-enhancement-roadmap": "bash scripts/script-enhancement-roadmap.sh", "script-ecosystem-audit": "bash scripts/script-ecosystem-validator.sh", "enhance-critical-scripts": "echo '⚡ Phase 2: Enhancing 4 critical scripts' && npm run script-ecosystem-audit", "enhance-sprint-1": "echo '🏃 Phase 3 Sprint 1: Enhancing 23 high-priority scripts'", "validate-enhancements": "npm run script-ecosystem-audit && npm run type-check", "cleanup-model-references": "node scripts/cleanup-model-references.js", "reset-test-data": "node -e \"console.log('🧪 Resetting test data...'); const { TestAccountManager } = require('./src/systems/testing/TestAccountManager.tsx'); TestAccountManager.getInstance().resetTestData(); console.log('✅ Test data reset complete');\""}, "dependencies": {"@anthropic-ai/sdk": "^0.51.0", "@apollo/client": "^3.13.8", "@pinecone-database/pinecone": "^6.0.0", "@radix-ui/react-primitive": "^2.1.2", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-tabs": "^1.1.11", "@reduxjs/toolkit": "^2.8.2", "@svgr/webpack": "^8.1.0", "@types/bcrypt": "^5.0.2", "@types/d3": "^7.4.3", "@types/node-fetch": "^2.6.12", "@types/redux-persist": "^4.0.0", "@types/ws": "^8.18.1", "axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "chalk": "^5.4.1", "chart.js": "^4.4.9", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "concurrently": "^9.1.2", "critters": "^0.0.23", "d3": "^7.9.0", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "glob": "^11.0.2", "graphql": "^16.11.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.511.0", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "nanoid": "^5.1.5", "next": "15.3.2", "next-auth": "^4.24.11", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "openai": "^4.100.0", "prom-client": "^15.1.3", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "redux-persist": "^6.0.0", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "uuid": "^11.1.0", "ws": "^8.18.2", "zod": "^3.25.30"}, "devDependencies": {"@eslint/eslintrc": "^3", "@next/bundle-analyzer": "^14.2.29", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/postcss": "^4.1.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/bcryptjs": "^2.4.6", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "js-yaml": "^4.1.0", "lint-staged": "^16.0.0", "msw": "^2.8.4", "postcss": "^8", "postcss-nesting": "^13.0.1", "prettier": "^3.5.3", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "tsx": "^4.19.4", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.2"}}