import jwt from 'jsonwebtoken';
import { NextRequest } from 'next/server';

// JWT Secret - In production, use environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'creAItive-observation-secret-key';

export interface AuthUser {
  id: string;
  email: string;
  name: string;
  role: 'observer' | 'admin_observer' | 'developer_observer';
  permissions: string[];
}

export interface AuthRequest extends NextRequest {
  user?: AuthUser;
}

/**
 * Verify JWT token and extract user information
 */
export function verifyToken(token: string): AuthUser | null {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as any;
    
    return {
      id: decoded.userId,
      email: decoded.email,
      name: decoded.name || 'Observer',
      role: decoded.role,
      permissions: decoded.permissions || []
    };
  } catch (error) {
    return null;
  }
}

/**
 * Extract token from request (cookie or Authorization header)
 */
export function extractToken(request: NextRequest): string | null {
  // Try cookie first (more secure)
  const cookieToken = request.cookies.get('auth-token')?.value;
  if (cookieToken) {
    return cookieToken;
  }

  // Fallback to Authorization header
  const authHeader = request.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

/**
 * Check if user has required permission
 */
export function hasPermission(user: AuthUser, permission: string): boolean {
  return user.permissions.includes(permission);
}

/**
 * Check if user has any of the required permissions
 */
export function hasAnyPermission(user: AuthUser, permissions: string[]): boolean {
  return permissions.some(permission => user.permissions.includes(permission));
}

/**
 * Check if user has all required permissions
 */
export function hasAllPermissions(user: AuthUser, permissions: string[]): boolean {
  return permissions.every(permission => user.permissions.includes(permission));
}

/**
 * Middleware function to authenticate requests
 */
export function authenticateRequest(request: NextRequest): AuthUser | null {
  const token = extractToken(request);
  
  if (!token) {
    return null;
  }

  return verifyToken(token);
}

/**
 * Permission definitions for different observation levels
 */
export const PERMISSIONS = {
  // Basic observation permissions
  OBSERVE_AI_DECISIONS: 'observe_ai_decisions',
  VIEW_AGENT_STATUS: 'view_agent_status',
  MONITOR_PERFORMANCE: 'monitor_performance',
  
  // Advanced observation permissions
  EXPORT_OBSERVATION_DATA: 'export_observation_data',
  VIEW_SYSTEM_INTERNALS: 'view_system_internals',
  
  // Developer permissions
  DEBUG_LOGS: 'debug_logs',
  ADVANCED_ANALYTICS: 'advanced_analytics',
  
  // Restricted actions (always forbidden for observation-only platform)
  CONTROL_AGENTS: 'control_agents', // Never granted
  MODIFY_CONFIGURATIONS: 'modify_configurations', // Never granted
  INTERVENE_IN_DECISIONS: 'intervene_in_decisions', // Never granted
  STOP_AUTONOMOUS_OPERATIONS: 'stop_autonomous_operations' // Never granted
} as const;

/**
 * Role-based permission sets
 */
export const ROLE_PERMISSIONS = {
  observer: [
    PERMISSIONS.OBSERVE_AI_DECISIONS,
    PERMISSIONS.VIEW_AGENT_STATUS,
    PERMISSIONS.MONITOR_PERFORMANCE
  ],
  admin_observer: [
    PERMISSIONS.OBSERVE_AI_DECISIONS,
    PERMISSIONS.VIEW_AGENT_STATUS,
    PERMISSIONS.MONITOR_PERFORMANCE,
    PERMISSIONS.EXPORT_OBSERVATION_DATA,
    PERMISSIONS.VIEW_SYSTEM_INTERNALS
  ],
  developer_observer: [
    PERMISSIONS.OBSERVE_AI_DECISIONS,
    PERMISSIONS.VIEW_AGENT_STATUS,
    PERMISSIONS.MONITOR_PERFORMANCE,
    PERMISSIONS.EXPORT_OBSERVATION_DATA,
    PERMISSIONS.VIEW_SYSTEM_INTERNALS,
    PERMISSIONS.DEBUG_LOGS,
    PERMISSIONS.ADVANCED_ANALYTICS
  ]
} as const;
