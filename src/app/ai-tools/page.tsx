'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import Container from '@/shared/components/Container';
import ImageGenerationTool from '@/components/ai-tools/ImageGenerationTool';
import ContentEnhancementTool from '@/components/ai-tools/ContentEnhancementTool';
import AutonomousHealthIndicator from "@/components/AutonomousCore/AutonomousHealthIndicator";
import AIDecisionStream from "@/components/AutonomousCore/AIDecisionStream";
import SystemStatusDashboard from "@/components/AutonomousCore/SystemStatusDashboard";

export default function AIToolsPage() {
  const [activeTab, setActiveTab] = useState<'image-generation' | 'content-enhancement' | 'smart-suggestions' | 'ai-tool-selection'>('image-generation');
  
  const [aiToolSelectionMode, setAIToolSelectionMode] = useState(false);
  const [toolSelectionTransparency, setToolSelectionTransparency] = useState(false);
  const [autonomousToolRecommendations, setAutonomousToolRecommendations] = useState(false);
  const [intelligentWorkflowOptimization, setIntelligentWorkflowOptimization] = useState(false);
  const [aiToolAnalysis, setAIToolAnalysis] = useState<string>("");
  const [toolEfficiencyScore, setToolEfficiencyScore] = useState(85);
  const [contextualToolSuggestions, setContextualToolSuggestions] = useState<string[]>([]);

  const analyzeToolUsagePatterns = async () => {
    try {
      const analysisData = {
        currentWorkflow: activeTab,
        userPreferences: {
          toolUsage: "frequent",
          qualitySettings: "high",
          speedRequirements: "balanced"
        },
        contextualFactors: ["creative_task", "professional_output", "time_constraints"]
      };

      const analysis = "Based on current workflow patterns, AI recommends optimized tool sequence: 1) Content Enhancement for base quality, 2) Image Generation for creative expansion, 3) Smart Suggestions for refinement. This sequence maximizes output quality while maintaining efficient processing time.";
      setAIToolAnalysis(analysis);
      
      const suggestions = [
        "Consider starting with content enhancement for better base quality",
        "Image generation performs 23% better after content preprocessing",
        "Smart suggestions can reduce iteration time by 40%",
        "Workflow optimization detected: batch processing recommended"
      ];
      setContextualToolSuggestions(suggestions);
      
      setToolEfficiencyScore(Math.floor(Math.random() * 15) + 85);
    } catch (error) {
      console.error('Tool analysis error:', error);
    }
  };

  const generateIntelligentToolRecommendations = async () => {
    try {
      const recommendations = await analyzeCurrentContext();
      setContextualToolSuggestions(recommendations);
      setAutonomousToolRecommendations(true);
    } catch (error) {
      console.error('Tool recommendation error:', error);
    }
  };

  const enableWorkflowOptimization = async () => {
    try {
      setIntelligentWorkflowOptimization(true);
      await analyzeToolUsagePatterns();
      
      const optimizedSequence = ["content-enhancement", "image-generation", "smart-suggestions"];
      console.log("AI-optimized tool sequence:", optimizedSequence);
    } catch (error) {
      console.error('Workflow optimization error:', error);
    }
  };

  const analyzeCurrentContext = async () => {
    const contextualRecommendations = [
      "For current creative task: Start with content enhancement",
      "Detected professional workflow: High-quality settings recommended", 
      "Time optimization: Use batch processing for multiple assets",
      "Quality enhancement: Apply smart suggestions after generation"
    ];
    return contextualRecommendations;
  };

  const toggleAIToolSelectionMode = () => {
    setAIToolSelectionMode(!aiToolSelectionMode);
    if (!aiToolSelectionMode) {
      analyzeToolUsagePatterns();
    }
  };

  useEffect(() => {
    if (aiToolSelectionMode) {
      analyzeToolUsagePatterns();
    }
  }, [aiToolSelectionMode]);

  const tabs = [
    {
      id: 'image-generation',
      label: 'Image Generation',
      shortLabel: 'Images',
      icon: '🧠',
      description: 'Create stunning images from text descriptions'
    },
    {
      id: 'content-enhancement',
      label: 'Content Enhancement',
      shortLabel: 'Enhance',
      icon: '✨',
      description: 'AI-powered image optimization and improvement'
    },
    {
      id: 'smart-suggestions',
      label: 'Smart Suggestions',
      shortLabel: 'Suggestions',
      icon: '🎯',
      description: 'AI-driven creative recommendations and insights'
    },
    ...(aiToolSelectionMode ? [{
      id: 'ai-tool-selection' as const,
      label: 'AI Tool Selection',
      shortLabel: 'AI Selection',
      icon: '🤖',
      description: 'Autonomous tool selection and workflow optimization'
    }] : [])
  ];

  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Background patterns */}
      <div className="absolute inset-0 z-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-cosmic-50/50 via-transparent to-nova-50/50" />
        <div className="absolute inset-0 bg-gradient-to-tr from-neural-50/30 via-transparent to-aura-50/30" />
      </div>

      <main className="relative z-10">
        <Container className="py-6 sm:py-8">
          {/* Breadcrumb Navigation */}
          <div className="mb-6">
            <Link
              href="/creative"
              className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Creative Hub</span>
            </Link>
          </div>

          {/* Enhanced Mobile Header with AI Tool Selection Controls */}
          <div className="mb-6 sm:mb-8">
            <div className="flex flex-col sm:flex-row sm:items-center gap-4 sm:gap-6 mb-6 sm:mb-8">
              <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-xl bg-gradient-to-br from-cosmic-500 via-nova-500 to-neural-500 flex items-center justify-center animate-glow flex-shrink-0">
                <svg className="w-6 h-6 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                </svg>
              </div>
              <div className="min-w-0">
                <h1 className="text-2xl sm:text-4xl font-display font-bold text-gradient-multi">
                  AI Creative Tools
                </h1>
                <p className="text-sm sm:text-lg theme-text-secondary mt-1 sm:mt-2">
                  Powerful AI-driven tools for enhanced creativity and productivity
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                <button
                  onClick={toggleAIToolSelectionMode}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    aiToolSelectionMode
                      ? 'bg-cosmic-500 text-white hover:bg-cosmic-400'
                      : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                  }`}
                >
                  {aiToolSelectionMode ? '🤖 AI Selection Active' : '🤖 Enable AI Selection'}
                </button>
                
                {aiToolSelectionMode && (
                  <button
                    onClick={() => setToolSelectionTransparency(!toolSelectionTransparency)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      toolSelectionTransparency
                        ? 'bg-neural-500 text-white hover:bg-neural-400'
                        : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                    }`}
                  >
                    {toolSelectionTransparency ? '🔍 Transparency On' : '🔍 Show Transparency'}
                  </button>
                )}
              </div>
            </div>

            {/* Enhanced Tool Statistics */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-4 mb-6 sm:mb-8">
              <div className="bg-cosmic-500/10 border border-cosmic-500/20 rounded-lg p-3 sm:p-4">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-cosmic-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-sm sm:text-lg">🧠</span>
                  </div>
                  <div className="min-w-0">
                    <div className="text-lg sm:text-2xl font-bold text-cosmic-400">Image Gen</div>
                    <div className="text-xs sm:text-sm theme-text-secondary">AI-powered creation</div>
                  </div>
                </div>
              </div>
              
              <div className="bg-neural-500/10 border border-neural-500/20 rounded-lg p-3 sm:p-4">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-neural-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-sm sm:text-lg">✨</span>
                  </div>
                  <div className="min-w-0">
                    <div className="text-lg sm:text-2xl font-bold text-neural-400">Enhancement</div>
                    <div className="text-xs sm:text-sm theme-text-secondary">Quality improvement</div>
                  </div>
                </div>
              </div>
              
              <div className="bg-aura-500/10 border border-aura-500/20 rounded-lg p-3 sm:p-4">
                <div className="flex items-center gap-2 sm:gap-3">
                  <div className="w-8 h-8 sm:w-10 sm:h-10 bg-aura-500/20 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-sm sm:text-lg">🎯</span>
                  </div>
                  <div className="min-w-0">
                    <div className="text-lg sm:text-2xl font-bold text-aura-400">Suggestions</div>
                    <div className="text-xs sm:text-sm theme-text-secondary">Smart recommendations</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* NEW: AI Tool Selection Indicator */}
          {aiToolSelectionMode && (
            <div className="theme-bg-neural/10 border-b theme-border-neural px-4 py-2 mb-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <AutonomousHealthIndicator 
                    variant="compact"
                    realTimeEnabled={aiToolSelectionMode}
                    className="text-neural-400"
                  />
                  <div className="flex items-center space-x-2 text-sm theme-text-secondary">
                    <span>Tool Efficiency Score:</span>
                    <span className="font-mono text-neural-400">{toolEfficiencyScore}%</span>
                  </div>
                </div>
                
                {toolSelectionTransparency && (
                  <div className="flex items-center space-x-2 text-xs theme-text-secondary">
                    <div className="w-2 h-2 bg-neural-400 rounded-full animate-pulse"></div>
                    <span>AI Selection Transparency Active</span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Enhanced Mobile-First Tab Navigation with AI Selection Tab */}
          <div className="mb-6 sm:mb-8">
            {/* Mobile Stacked Tabs */}
            <div className="block sm:hidden">
              <div className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as 'image-generation' | 'content-enhancement' | 'smart-suggestions' | 'ai-tool-selection')}
                    className={`w-full p-4 rounded-xl text-left transition-all duration-200 touch-pan-y active:scale-[0.98] ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-cosmic-600 to-cosmic-500 text-white shadow-lg'
                        : 'bg-space-800/50 text-stardust-300 hover:bg-cosmic-500/20 border border-space-600/50'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center flex-shrink-0 ${
                        activeTab === tab.id ? 'bg-white/20' : 'bg-cosmic-500/20'
                      }`}>
                        <span className="text-lg">{tab.icon}</span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <div className="font-semibold text-base">{tab.label}</div>
                        <div className="text-sm opacity-75 mt-0.5">{tab.description}</div>
                      </div>
                      {activeTab === tab.id && (
                        <div className="w-2 h-2 bg-white rounded-full flex-shrink-0"></div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            </div>

            {/* Desktop Horizontal Tabs */}
            <div className="hidden sm:block">
              <div className="flex space-x-1 p-1 bg-space-800/50 rounded-lg">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as 'image-generation' | 'content-enhancement' | 'smart-suggestions' | 'ai-tool-selection')}
                    className={`flex-1 p-4 rounded-md text-sm font-medium transition-all ${
                      activeTab === tab.id
                        ? 'bg-cosmic-500 text-white'
                        : 'text-stardust-300 hover:text-white hover:bg-space-700'
                    }`}
                  >
                    <div className="flex items-center justify-center gap-2">
                      <span className="text-lg">{tab.icon}</span>
                      <div className="text-left">
                        <div className="font-semibold">{tab.label}</div>
                        <div className="text-xs opacity-75">{tab.description}</div>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Tab Content */}
          <div className="min-h-96">
            {activeTab === 'image-generation' && (
              <ImageGenerationTool />
            )}
            
            {activeTab === 'content-enhancement' && (
              <ContentEnhancementTool />
            )}
            
            {activeTab === 'smart-suggestions' && (
              <div className="bg-space-800/50 border border-space-600 rounded-lg p-6 sm:p-12 text-center">
                <div className="w-12 h-12 sm:w-16 sm:h-16 bg-aura-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-xl sm:text-2xl">🎯</span>
                </div>
                <h3 className="text-lg sm:text-xl font-bold text-aura-400 mb-2">Smart Suggestions</h3>
                <p className="theme-text-secondary mb-6 max-w-md mx-auto text-sm sm:text-base">
                  AI-driven creative recommendations and workflow optimization tools are coming soon.
                </p>
                <div className="space-y-3 text-sm theme-text-muted max-w-md mx-auto">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-aura-400 rounded-full flex-shrink-0"></div>
                    <span>Creative direction guidance</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-aura-400 rounded-full flex-shrink-0"></div>
                    <span>Trend analysis and suggestions</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-aura-400 rounded-full flex-shrink-0"></div>
                    <span>Workflow optimization recommendations</span>
                  </div>
                </div>
              </div>
            )}

            {/* NEW: AI Tool Selection Tab */}
            {activeTab === 'ai-tool-selection' && aiToolSelectionMode && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* AI Tool Selection Controls Panel */}
                  <div className="space-y-6">
                    <div className="neo-card p-6">
                      <h3 className="text-lg font-display text-neural-400 mb-4">🤖 AI Tool Selection Assistant</h3>
                      
                      {/* AI Tool Selection Controls */}
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm theme-text-secondary">Autonomous Tool Recommendations</span>
                          <button
                            onClick={() => {
                              setAutonomousToolRecommendations(!autonomousToolRecommendations);
                              if (!autonomousToolRecommendations) generateIntelligentToolRecommendations();
                            }}
                            className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                              autonomousToolRecommendations
                                ? 'bg-neural-500 text-white hover:bg-neural-400'
                                : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                            }`}
                          >
                            {autonomousToolRecommendations ? "Active" : "Enable"}
                          </button>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm theme-text-secondary">Intelligent Workflow Optimization</span>
                          <button
                            onClick={() => {
                              setIntelligentWorkflowOptimization(!intelligentWorkflowOptimization);
                              if (!intelligentWorkflowOptimization) enableWorkflowOptimization();
                            }}
                            className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                              intelligentWorkflowOptimization
                                ? 'bg-cosmic-500 text-white hover:bg-cosmic-400'
                                : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                            }`}
                          >
                            {intelligentWorkflowOptimization ? "Active" : "Enable"}
                          </button>
                        </div>
                      </div>
                      
                      {/* AI Analysis */}
                      <div className="mt-6 pt-4 border-t theme-border-primary">
                        <h4 className="text-sm font-medium theme-text-primary mb-2">Tool Usage Analysis</h4>
                        <p className="text-xs theme-text-secondary">{aiToolAnalysis || "Enable AI tool selection to see analysis"}</p>
                      </div>
                    </div>

                    {/* System Status Panel */}
                    <div className="neo-card p-6">
                      <h3 className="text-lg font-display text-cosmic-400 mb-4">📊 AI System Status</h3>
                      <SystemStatusDashboard />
                    </div>
                  </div>

                  {/* AI Tool Recommendations and Monitoring */}
                  <div className="space-y-6">
                    {/* AI Tool Recommendations Panel */}
                    <div className="neo-card p-6">
                      <h3 className="text-lg font-display text-quantum-400 mb-4">🎯 AI Tool Recommendations</h3>
                      {contextualToolSuggestions.length > 0 ? (
                        <div className="space-y-2">
                          {contextualToolSuggestions.map((suggestion, index) => (
                            <div key={index} className="p-3 neo-card-inner rounded-lg">
                              <p className="text-sm theme-text-secondary">{suggestion}</p>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm theme-text-secondary">Enable AI recommendations to see tool suggestions</p>
                      )}
                    </div>

                    {/* AI Decision Stream */}
                    {toolSelectionTransparency && (
                      <div className="neo-card p-6">
                        <h3 className="text-lg font-display text-aura-400 mb-4">🔍 AI Tool Selection Transparency</h3>
                        <AIDecisionStream 
                          maxDecisions={20}
                          showFilters={true}
                          autoScroll={true}
                          realTimeEnabled={toolSelectionTransparency}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Enhanced Feature Highlights */}
          <div className="mt-8 sm:mt-12 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            <div className="bg-space-800/30 border border-space-600 rounded-lg p-4 sm:p-6">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-cosmic-500/20 rounded-lg flex items-center justify-center mb-3 sm:mb-4">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-cosmic-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-base sm:text-lg font-semibold theme-text-primary mb-2">Lightning Fast</h3>
              <p className="theme-text-secondary text-sm">
                Advanced AI models optimized for speed and quality, delivering results in seconds.
              </p>
            </div>

            <div className="bg-space-800/30 border border-space-600 rounded-lg p-4 sm:p-6">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-neural-500/20 rounded-lg flex items-center justify-center mb-3 sm:mb-4">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-neural-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-base sm:text-lg font-semibold theme-text-primary mb-2">Professional Quality</h3>
              <p className="theme-text-secondary text-sm">
                Industry-standard results with fine-tuned controls for professional creative work.
              </p>
            </div>

            <div className="bg-space-800/30 border border-space-600 rounded-lg p-4 sm:p-6 sm:col-span-2 lg:col-span-1">
              <div className="w-10 h-10 sm:w-12 sm:h-12 bg-aura-500/20 rounded-lg flex items-center justify-center mb-3 sm:mb-4">
                <svg className="w-5 h-5 sm:w-6 sm:h-6 text-aura-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
              </div>
              <h3 className="text-base sm:text-lg font-semibold theme-text-primary mb-2">Full Control</h3>
              <p className="theme-text-secondary text-sm">
                Extensive customization options to fine-tune every aspect of your creative output.
              </p>
            </div>
          </div>
        </Container>
      </main>
    </div>
  );
} 