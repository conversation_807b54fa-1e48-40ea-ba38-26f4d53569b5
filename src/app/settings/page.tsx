"use client";

import React from 'react';
import Link from 'next/link';
import { useAuthContext } from '@/contexts/AuthContext';
import { 
  Palette, 
  User, 
  Shield, 
  Bell, 
  Monitor, 
  Accessibility, 
  Download,
  Settings as SettingsIcon,
  ChevronRight
} from 'lucide-react';

export default function SettingsPage() {
  const { user } = useAuthContext();

  const settingsCategories = [
    {
      title: 'Appearance',
      description: 'Customize your visual experience',
      icon: Palette,
      href: '/settings/theme',
      color: 'cosmic',
      features: ['Theme customization', 'Color schemes', 'AI theme generation', 'Visual effects']
    },
    {
      title: 'Account',
      description: 'Manage your account and profile',
      icon: User,
      href: '/settings/account',
      color: 'nova',
      features: ['Profile settings', 'Email preferences', 'Password security', 'Account data']
    },
    {
      title: 'Privacy & Security',
      description: 'Control your privacy and security settings',
      icon: Shield,
      href: '/settings/privacy',
      color: 'neural',
      features: ['Data privacy', 'Security settings', 'Two-factor auth', 'Session management']
    },
    {
      title: 'Notifications',
      description: 'Manage your notification preferences',
      icon: Bell,
      href: '/settings/notifications',
      color: 'quantum',
      features: ['Email notifications', 'Push notifications', 'AI alerts', 'System updates']
    },
    {
      title: 'Display & Accessibility',
      description: 'Accessibility and display options',
      icon: Accessibility,
      href: '/settings/accessibility',
      color: 'aura',
      features: ['High contrast', 'Large text', 'Reduced motion', 'Screen reader support']
    },
    {
      title: 'Data & Export',
      description: 'Manage your data and exports',
      icon: Download,
      href: '/settings/data',
      color: 'stardust',
      features: ['Export data', 'Import settings', 'Backup preferences', 'Data portability']
    }
  ];

  return (
    <div className="min-h-screen bg-space-900 pt-20">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-2xl flex items-center justify-center">
              <SettingsIcon className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-cosmic-400 via-nova-400 to-neural-400 bg-clip-text text-transparent">
              Settings
            </h1>
          </div>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
            Customize your CreAItive experience. Manage your account, preferences, and platform settings.
          </p>

          {user && (
            <div className="bg-space-800/50 border border-cosmic-500/30 rounded-lg p-4 max-w-2xl mx-auto mb-8">
              <p className="text-cosmic-300 text-sm">
                👋 <strong>Welcome, {user.name || user.email?.split('@')[0]}!</strong> 
                <span className="text-white/70 ml-2">
                  Your settings are automatically saved and synced across all your devices.
                </span>
              </p>
            </div>
          )}

          {!user && (
            <div className="bg-cosmic-500/10 border border-cosmic-500/30 rounded-lg p-4 max-w-2xl mx-auto mb-8">
              <p className="text-cosmic-300 text-sm">
                🔒 <strong>Login Required:</strong> 
                <Link href="/login" className="text-cosmic-400 hover:text-cosmic-300 underline ml-1">
                  Sign in to access personalized settings
                </Link>
              </p>
            </div>
          )}
        </div>

        {/* Settings Categories */}
        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-6">
            {settingsCategories.map((category, index) => {
              const Icon = category.icon;
              const isAvailable = category.href === '/settings/theme' || user;
              
              return (
                <div key={category.href} className="relative">
                  {isAvailable ? (
                    <Link href={category.href}>
                      <div className={`bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:border-${category.color}-500/50 transition-all duration-300 hover:scale-105 cursor-pointer group`}>
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-4">
                            <div className={`w-12 h-12 bg-gradient-to-br from-${category.color}-500 to-${category.color}-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                              <Icon className="w-6 h-6 text-white" />
                            </div>
                            <div>
                              <h3 className="text-lg font-semibold text-white group-hover:text-cosmic-300 transition-colors">
                                {category.title}
                              </h3>
                              <p className="text-white/70 text-sm">{category.description}</p>
                            </div>
                          </div>
                          <ChevronRight className="w-5 h-5 text-white/40 group-hover:text-white/80 transition-colors" />
                        </div>
                        
                        <div className="space-y-2">
                          {category.features.map((feature, featureIndex) => (
                            <div key={featureIndex} className="flex items-center gap-2 text-xs text-white/60">
                              <div className={`w-1.5 h-1.5 bg-${category.color}-400 rounded-full`} />
                              {feature}
                            </div>
                          ))}
                        </div>
                      </div>
                    </Link>
                  ) : (
                    <div className={`bg-space-800/30 backdrop-blur-sm border border-white/5 rounded-xl p-6 opacity-60 cursor-not-allowed`}>
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-4">
                          <div className={`w-12 h-12 bg-gradient-to-br from-${category.color}-500/50 to-${category.color}-600/50 rounded-lg flex items-center justify-center`}>
                            <Icon className="w-6 h-6 text-white/50" />
                          </div>
                          <div>
                            <h3 className="text-lg font-semibold text-white/50">
                              {category.title}
                            </h3>
                            <p className="text-white/40 text-sm">{category.description}</p>
                          </div>
                        </div>
                        <div className="bg-space-700 text-white/60 text-xs px-2 py-1 rounded">
                          Login Required
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        {category.features.map((feature, featureIndex) => (
                          <div key={featureIndex} className="flex items-center gap-2 text-xs text-white/40">
                            <div className={`w-1.5 h-1.5 bg-${category.color}-400/50 rounded-full`} />
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="max-w-4xl mx-auto mt-12">
          <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6">Quick Actions</h2>
            
            <div className="grid md:grid-cols-3 gap-4">
              <Link 
                href="/settings/theme"
                className="bg-cosmic-500/10 hover:bg-cosmic-500/20 border border-cosmic-500/30 rounded-lg p-4 transition-colors group"
              >
                <div className="flex items-center gap-3">
                  <Palette className="w-5 h-5 text-cosmic-400" />
                  <div>
                    <div className="text-white font-medium">Customize Theme</div>
                    <div className="text-white/60 text-xs">Change colors instantly</div>
                  </div>
                </div>
              </Link>
              
              {user && (
                <>
                  <button className="bg-nova-500/10 hover:bg-nova-500/20 border border-nova-500/30 rounded-lg p-4 transition-colors group">
                    <div className="flex items-center gap-3">
                      <Download className="w-5 h-5 text-nova-400" />
                      <div>
                        <div className="text-white font-medium">Export Data</div>
                        <div className="text-white/60 text-xs">Download your data</div>
                      </div>
                    </div>
                  </button>
                  
                  <button className="bg-neural-500/10 hover:bg-neural-500/20 border border-neural-500/30 rounded-lg p-4 transition-colors group">
                    <div className="flex items-center gap-3">
                      <Shield className="w-5 h-5 text-neural-400" />
                      <div>
                        <div className="text-white font-medium">Privacy Check</div>
                        <div className="text-white/60 text-xs">Review privacy settings</div>
                      </div>
                    </div>
                  </button>
                </>
              )}
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="max-w-4xl mx-auto mt-12 text-center">
          <div className="bg-space-800/30 border border-white/10 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Need Help?</h3>
            <p className="text-white/70 text-sm mb-4">
              Check our documentation or contact support for assistance with settings.
            </p>
            <div className="flex justify-center gap-4">
              <Link 
                href="/docs"
                className="text-cosmic-400 hover:text-cosmic-300 text-sm underline"
              >
                Documentation
              </Link>
              <Link 
                href="/support"
                className="text-cosmic-400 hover:text-cosmic-300 text-sm underline"
              >
                Contact Support
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
