"use client";

import React, { useState, useEffect } from 'react';
import { useDynamicTheme } from '@/systems/theme/DynamicThemeController';
import { useUserPreferences } from '@/systems/user/UserPreferencesManager';
import { useAuthContext } from '@/contexts/AuthContext';
import { Palette, Wand2, Save, Download, Upload, Sparkles, Eye, Monitor, Smartphone } from 'lucide-react';
import Link from 'next/link';

export default function ThemeSettingsPage() {
  const { user } = useAuthContext();
  const {
    currentSchema,
    blueprints,
    applyTheme,
    applyBlueprint,
    generateAITheme,
    createCustomBlueprint
  } = useDynamicTheme();

  const {
    themes: savedThemes,
    saveTheme,
    deleteTheme: deleteSavedTheme,
    setDefaultTheme,
    exportPreferences,
    importPreferences,
    isLoggedIn
  } = useUserPreferences();

  const [activeTab, setActiveTab] = useState<'customize' | 'blueprints' | 'ai' | 'effects' | 'saved'>('customize');
  const [customColors, setCustomColors] = useState(currentSchema.colors);
  const [customEffects, setCustomEffects] = useState(currentSchema.effects);
  const [aiPrompt, setAiPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [themeName, setThemeName] = useState('');
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop');

  // Update custom colors when schema changes
  useEffect(() => {
    setCustomColors(currentSchema.colors);
    setCustomEffects(currentSchema.effects);
  }, [currentSchema]);

  // APPLY CUSTOM COLORS
  const applyCustomColors = () => {
    const newSchema = {
      ...currentSchema,
      name: themeName || 'Custom Theme',
      colors: customColors,
      effects: customEffects,
      gradients: {
        primary: `linear-gradient(135deg, ${customColors.cosmic} 0%, ${customColors.nova} 100%)`,
        secondary: `linear-gradient(135deg, ${customColors.neural} 0%, ${customColors.aura} 100%)`,
        accent: `linear-gradient(135deg, ${customColors.quantum} 0%, ${customColors.cosmic} 100%)`
      }
    };
    applyTheme(newSchema);
  };

  // SAVE THEME TO USER PREFERENCES
  const saveThemeToPreferences = async () => {
    if (!user) return;

    const name = themeName || `Custom Theme ${savedThemes.length + 1}`;
    const themeData = {
      name,
      schema: {
        ...currentSchema,
        name,
        colors: customColors,
        effects: customEffects
      },
      isDefault: savedThemes.length === 0
    };

    try {
      await saveTheme(themeData);
      setThemeName('');
      alert('Theme saved to your preferences!');
    } catch (error) {
      alert('Failed to save theme. Please try again.');
    }
  };

  // GENERATE AI THEME
  const handleAIGeneration = async () => {
    if (!aiPrompt.trim()) return;
    
    setIsGenerating(true);
    try {
      await generateAITheme(aiPrompt);
      setAiPrompt('');
    } catch (error) {
      console.error('AI theme generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // EXPORT PREFERENCES
  const exportUserPreferences = () => {
    const preferencesData = exportPreferences();
    const blob = new Blob([preferencesData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `theme-preferences-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  // IMPORT PREFERENCES
  const importUserPreferences = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const preferencesData = e.target?.result as string;
        const success = await importPreferences(preferencesData);
        if (success) {
          alert('Preferences imported successfully!');
        } else {
          alert('Failed to import preferences. Please check the file format.');
        }
      } catch (error) {
        alert('Invalid preferences file');
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="min-h-screen bg-space-900 pt-20">
      <div className="container mx-auto px-6 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-6">
            <div className="w-16 h-16 bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-2xl flex items-center justify-center">
              <Palette className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-cosmic-400 via-nova-400 to-neural-400 bg-clip-text text-transparent">
              Theme Preferences
            </h1>
          </div>
          
          <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
            Customize your visual experience. Create, save, and manage your personal themes.
            Your preferences are saved to your account and sync across all devices.
          </p>

          {!user && (
            <div className="bg-cosmic-500/10 border border-cosmic-500/30 rounded-lg p-4 max-w-2xl mx-auto mb-8">
              <p className="text-cosmic-300 text-sm">
                🔒 <strong>Login Required:</strong> 
                <Link href="/login" className="text-cosmic-400 hover:text-cosmic-300 underline ml-1">
                  Login to save your theme preferences
                </Link>
              </p>
            </div>
          )}
        </div>

        {/* Preview Controls */}
        <div className="flex items-center justify-center gap-4 mb-8">
          <div className="flex items-center gap-2 bg-space-800 rounded-lg p-1">
            <button
              onClick={() => setPreviewMode('desktop')}
              className={`flex items-center gap-2 px-3 py-2 rounded text-sm transition-colors ${
                previewMode === 'desktop' 
                  ? 'bg-cosmic-500 text-white' 
                  : 'text-white/60 hover:text-white'
              }`}
            >
              <Monitor className="w-4 h-4" />
              Desktop
            </button>
            <button
              onClick={() => setPreviewMode('mobile')}
              className={`flex items-center gap-2 px-3 py-2 rounded text-sm transition-colors ${
                previewMode === 'mobile' 
                  ? 'bg-cosmic-500 text-white' 
                  : 'text-white/60 hover:text-white'
              }`}
            >
              <Smartphone className="w-4 h-4" />
              Mobile
            </button>
          </div>
          
          <div className="text-sm text-white/60">
            Current: <span className="text-cosmic-300 font-medium">{currentSchema.name}</span>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex justify-center mb-8">
          <div className="flex bg-space-800 rounded-lg p-1">
            {[
              { id: 'customize', label: 'Customize', icon: '🎨' },
              { id: 'blueprints', label: 'Blueprints', icon: '📋' },
              { id: 'ai', label: 'AI Generate', icon: '🤖' },
              { id: 'effects', label: 'Effects', icon: '✨' },
              { id: 'saved', label: 'My Themes', icon: '💾' }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 px-4 py-2 rounded font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-cosmic-500 text-white'
                    : 'text-white/60 hover:text-white'
                }`}
              >
                <span>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content Area */}
        <div className="max-w-4xl mx-auto">
          {/* Customize Tab */}
          {activeTab === 'customize' && (
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Custom Color Scheme</h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-6">
                  {Object.entries(customColors).map(([colorName, colorValue]) => (
                    <div key={colorName} className="space-y-3">
                      <label className="text-white font-medium capitalize flex items-center gap-2">
                        <div 
                          className="w-4 h-4 rounded border border-white/20"
                          style={{ backgroundColor: colorValue }}
                        />
                        {colorName}
                        <span className="text-xs text-white/60">
                          ({colorName === 'cosmic' ? 'Intelligence/Systems' : 
                             colorName === 'nova' ? 'Success/Active' :
                             colorName === 'quantum' ? 'Processing/Analysis' :
                             colorName === 'neural' ? 'AI Features' :
                             colorName === 'aura' ? 'Community/Social' :
                             colorName === 'stardust' ? 'Secondary Elements' : 'Background'})
                        </span>
                      </label>
                      <div className="flex items-center gap-3">
                        <input
                          type="color"
                          value={colorValue}
                          onChange={(e) => setCustomColors(prev => ({
                            ...prev,
                            [colorName]: e.target.value
                          }))}
                          className="w-12 h-12 rounded-lg border border-white/20 cursor-pointer"
                        />
                        <input
                          type="text"
                          value={colorValue}
                          onChange={(e) => setCustomColors(prev => ({
                            ...prev,
                            [colorName]: e.target.value
                          }))}
                          className="flex-1 bg-space-700 text-white px-3 py-2 rounded-lg border border-white/20"
                        />
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="space-y-6">
                  <h3 className="text-lg font-semibold text-white">Live Preview</h3>
                  <div className="bg-space-700 rounded-lg p-6 space-y-4">
                    <div 
                      className="h-12 rounded-lg flex items-center justify-center text-white font-medium"
                      style={{ backgroundColor: customColors.cosmic }}
                    >
                      Primary Button (Cosmic)
                    </div>
                    <div 
                      className="h-10 rounded-lg flex items-center justify-center text-white"
                      style={{ backgroundColor: customColors.nova }}
                    >
                      Success Button (Nova)
                    </div>
                    <div 
                      className="h-8 rounded flex items-center justify-center text-white text-sm"
                      style={{ backgroundColor: customColors.quantum }}
                    >
                      Warning (Quantum)
                    </div>
                    <div 
                      className="h-8 rounded flex items-center justify-center text-white text-sm"
                      style={{ backgroundColor: customColors.neural }}
                    >
                      AI Feature (Neural)
                    </div>
                    <div 
                      className="h-6 rounded flex items-center justify-center text-white text-xs"
                      style={{ backgroundColor: customColors.aura }}
                    >
                      Community (Aura)
                    </div>
                  </div>
                  
                  {user && (
                    <div className="space-y-3">
                      <input
                        type="text"
                        placeholder="Theme name (optional)"
                        value={themeName}
                        onChange={(e) => setThemeName(e.target.value)}
                        className="w-full bg-space-700 text-white px-3 py-2 rounded-lg border border-white/20"
                      />
                      <button
                        onClick={saveThemeToPreferences}
                        className="w-full bg-nova-500 hover:bg-nova-600 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
                      >
                        <Save className="w-5 h-5" />
                        Save to My Themes
                      </button>
                    </div>
                  )}
                  
                  <button
                    onClick={applyCustomColors}
                    className="w-full bg-cosmic-500 hover:bg-cosmic-600 text-white py-3 px-4 rounded-lg font-medium transition-colors"
                  >
                    Apply Theme
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Blueprints Tab */}
          {activeTab === 'blueprints' && (
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Theme Blueprints</h2>
              <p className="text-white/70 mb-8">Pre-designed themes for different use cases and personalities</p>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                {blueprints.map(blueprint => (
                  <div
                    key={blueprint.id}
                    className="bg-space-700 rounded-lg p-6 cursor-pointer hover:bg-space-600 transition-all duration-300 hover:scale-105"
                    onClick={() => applyBlueprint(blueprint.id)}
                  >
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-white font-semibold">{blueprint.name}</h3>
                      <span className="text-xs text-white/60 bg-space-600 px-2 py-1 rounded capitalize">
                        {blueprint.category}
                      </span>
                    </div>

                    <p className="text-white/70 text-sm mb-4">{blueprint.schema.description}</p>

                    <div className="flex gap-1 mb-4">
                      {Object.values(blueprint.schema.colors).slice(0, 6).map((color, index) => (
                        <div
                          key={index}
                          className="w-6 h-6 rounded"
                          style={{ backgroundColor: color }}
                          title={Object.keys(blueprint.schema.colors)[index]}
                        />
                      ))}
                    </div>

                    <div className="flex items-center justify-between text-xs text-white/60">
                      <span>Effects: {Object.values(blueprint.schema.effects).filter(Boolean).length}/3</span>
                      <span>Click to apply</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* AI Generate Tab */}
          {activeTab === 'ai' && (
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">AI Theme Generator</h2>
              <p className="text-white/70 mb-8">Describe your ideal theme and let AI create it for you</p>

              <div className="max-w-2xl mx-auto space-y-6">
                <div className="space-y-3">
                  <label className="text-white font-medium">Describe Your Theme</label>
                  <textarea
                    value={aiPrompt}
                    onChange={(e) => setAiPrompt(e.target.value)}
                    placeholder="e.g., 'Ocean-inspired with blue tones and gentle animations' or 'Dark cyberpunk with neon accents and glow effects'"
                    className="w-full bg-space-700 text-white px-4 py-3 rounded-lg border border-white/20 resize-none h-32"
                  />
                </div>

                <button
                  onClick={handleAIGeneration}
                  disabled={!aiPrompt.trim() || isGenerating}
                  className="w-full bg-gradient-to-r from-cosmic-500 to-neural-500 hover:from-cosmic-600 hover:to-neural-600 text-white py-4 px-6 rounded-lg font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3"
                >
                  {isGenerating ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      Generating Your Theme...
                    </>
                  ) : (
                    <>
                      <Wand2 className="w-5 h-5" />
                      Generate AI Theme
                    </>
                  )}
                </button>

                <div className="bg-space-700 rounded-lg p-6">
                  <h3 className="text-white font-medium mb-4">💡 Example Prompts</h3>
                  <div className="grid md:grid-cols-2 gap-4 text-sm">
                    {[
                      "Professional blue corporate theme with minimal effects",
                      "Warm sunset colors with glow effects for creativity",
                      "Dark cyberpunk theme with neon green accents",
                      "Minimal grayscale theme for focus and productivity",
                      "Vibrant gaming theme with particles and animations",
                      "Medical clean white and blue theme",
                      "Forest green nature-inspired theme",
                      "Royal purple and gold luxury theme"
                    ].map((example, index) => (
                      <button
                        key={index}
                        onClick={() => setAiPrompt(example)}
                        className="text-left p-3 bg-space-600 hover:bg-space-500 rounded text-white/80 hover:text-white transition-colors"
                      >
                        "{example}"
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Effects Tab */}
          {activeTab === 'effects' && (
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Visual Effects</h2>
              <p className="text-white/70 mb-8">Control animations, glow effects, and visual enhancements</p>

              <div className="max-w-2xl mx-auto space-y-8">
                {[
                  {
                    key: 'glow',
                    label: 'Glow Effects',
                    description: 'Subtle glow around interactive elements and buttons',
                    icon: '✨'
                  },
                  {
                    key: 'animations',
                    label: 'Smooth Animations',
                    description: 'Smooth transitions and micro-interactions throughout the interface',
                    icon: '🎭'
                  },
                  {
                    key: 'particles',
                    label: 'Particle Effects',
                    description: 'Animated background particles for dynamic visual appeal',
                    icon: '🌟'
                  }
                ].map(effect => (
                  <div key={effect.key} className="bg-space-700 rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <span className="text-2xl">{effect.icon}</span>
                        <div>
                          <h3 className="text-white font-semibold">{effect.label}</h3>
                          <p className="text-white/60 text-sm">{effect.description}</p>
                        </div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={customEffects[effect.key as keyof typeof customEffects]}
                          onChange={(e) => {
                            const newEffects = {
                              ...customEffects,
                              [effect.key]: e.target.checked
                            };
                            setCustomEffects(newEffects);

                            const newSchema = {
                              ...currentSchema,
                              effects: newEffects
                            };
                            applyTheme(newSchema);
                          }}
                          className="sr-only"
                        />
                        <div className="w-14 h-8 bg-space-600 rounded-full peer peer-checked:bg-cosmic-500 transition-colors">
                          <div className="w-6 h-6 bg-white rounded-full shadow transform transition-transform peer-checked:translate-x-6 translate-x-1 translate-y-1" />
                        </div>
                      </label>
                    </div>

                    <div className="text-xs text-white/50">
                      {customEffects[effect.key as keyof typeof customEffects] ? 'Enabled' : 'Disabled'}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* My Themes Tab */}
          {activeTab === 'saved' && (
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold text-white">My Saved Themes</h2>
                <div className="flex gap-3">
                  <button
                    onClick={exportUserPreferences}
                    className="bg-neural-500 hover:bg-neural-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2"
                  >
                    <Download className="w-4 h-4" />
                    Export All
                  </button>
                  <label className="bg-aura-500 hover:bg-aura-600 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-2 cursor-pointer">
                    <Upload className="w-4 h-4" />
                    Import Preferences
                    <input
                      type="file"
                      accept=".json"
                      onChange={importUserPreferences}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>

              {!user ? (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">🔒</div>
                  <h3 className="text-xl font-semibold text-white mb-2">Login Required</h3>
                  <p className="text-white/70 mb-6">Sign in to save and manage your personal themes</p>
                  <Link
                    href="/login"
                    className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                  >
                    Login to Continue
                  </Link>
                </div>
              ) : savedThemes.length === 0 ? (
                <div className="text-center py-12">
                  <div className="text-6xl mb-4">🎨</div>
                  <h3 className="text-xl font-semibold text-white mb-2">No Saved Themes</h3>
                  <p className="text-white/70 mb-6">Create and save your first custom theme</p>
                  <button
                    onClick={() => setActiveTab('customize')}
                    className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                  >
                    Create Theme
                  </button>
                </div>
              ) : (
                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {savedThemes.map(theme => (
                    <div key={theme.id} className="bg-space-700 rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-white font-semibold">{theme.name}</h3>
                        {theme.isDefault && (
                          <span className="text-xs bg-nova-500 text-white px-2 py-1 rounded">
                            Default
                          </span>
                        )}
                      </div>

                      <div className="flex gap-1 mb-4">
                        {Object.values(theme.schema.colors).slice(0, 6).map((color: any, index: number) => (
                          <div
                            key={index}
                            className="w-6 h-6 rounded"
                            style={{ backgroundColor: color }}
                          />
                        ))}
                      </div>

                      <div className="text-xs text-white/60 mb-4">
                        Created: {new Date(theme.createdAt).toLocaleDateString()}
                      </div>

                      <div className="flex gap-2">
                        <button
                          onClick={() => applyTheme(theme.schema)}
                          className="flex-1 bg-cosmic-500 hover:bg-cosmic-600 text-white py-2 px-3 rounded text-sm font-medium transition-colors"
                        >
                          Apply
                        </button>
                        <button
                          onClick={() => setDefaultTheme(theme.id)}
                          className="bg-nova-500 hover:bg-nova-600 text-white py-2 px-3 rounded text-sm transition-colors"
                          title="Set as default"
                        >
                          ⭐
                        </button>
                        <button
                          onClick={() => deleteSavedTheme(theme.id)}
                          className="bg-red-500 hover:bg-red-600 text-white py-2 px-3 rounded text-sm transition-colors"
                          title="Delete theme"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
