/**
 * Project Management Interface UI
 *
 * Features:
 * - Integration with Dynamic Navigation Intelligence
 * - Real-time project status dashboard
 * - Mobile-first touch-optimized interface
 * - Team management foundation
 * - 🤖 Autonomous Project Intelligence
 */

'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { Card } from '@/shared/components/Card';
import { Button } from '@/shared/components/Button';
import Container from '@/shared/components/Container';
import { Plus, Settings, Users, FileText, Activity, BarChart3, Globe, Bot, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

// 🤖 Autonomous AI Components
import AutonomousHealthIndicator from '@/components/AutonomousCore/AutonomousHealthIndicator';
import AIDecisionStream from '@/components/AutonomousCore/AIDecisionStream';

// Import project system rules manager
interface ProjectSystemRules {
  version: string;
  projectName: string;
  description?: string;
  modelRoutes: any[];
  ideIntegrations: any[];
  environments: any[];
  settings: any;
  security: any;
  mcpTools: any[];
}

interface Project {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'paused' | 'completed' | 'archived';
  type: 'creative' | 'development' | 'research' | 'collaboration';
  created: string;
  lastModified: string;
  teamMembers: number;
  progress: number;
  navigationPages: number;
  agentsActive: number;
  rulesConfigured: boolean;
}

interface NavigationIntelligence {
  totalPages: number;
  categorizedPages: number;
  missingPages: string[];
  healthScore: number;
  lastAnalysis: string;
}

export default function ProjectManagementInterface() {
  const router = useRouter();
  const [projects, setProjects] = useState<Project[]>([]);
  const [navigationIntel, setNavigationIntel] = useState<NavigationIntelligence | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'create' | 'settings'>('overview');
  const [loading, setLoading] = useState(true);
  const [newProject, setNewProject] = useState({
    name: '',
    description: '',
    type: 'development' as Project['type']
  });

  // 🤖 AI Project Intelligence State
  const [aiProjectMode, setAiProjectMode] = useState(false);
  const [aiProjectTransparency, setAiProjectTransparency] = useState(0.89);
  const [projectIntelligenceScore, setProjectIntelligenceScore] = useState(93);
  const [autonomousResourceOptimization, setAutonomousResourceOptimization] = useState(false);
  const [intelligentProgressTracking, setIntelligentProgressTracking] = useState(false);
  const [aiProjectDecisions, setAiProjectDecisions] = useState<Array<{
    id: string;
    type: string;
    decision: string;
    confidence: number;
    timestamp: string;
  }>>([]);
  const [currentTab, setCurrentTab] = useState('overview');

  // 🤖 AI Project Intelligence Functions
  const analyzeProjectPortfolio = useCallback(async () => {
    if (!aiProjectMode) return;
    
    const analysis = {
      resourceUtilization: 93,
      progressEfficiency: 89,
      riskAssessment: 91,
      teamProductivity: 87
    };
    
    setProjectIntelligenceScore(analysis.resourceUtilization);
    
    const decision = {
      id: `portfolio-${Date.now()}`,
      type: 'Portfolio Analysis',
      decision: `Project portfolio optimized: ${analysis.resourceUtilization}% resource utilization with ${analysis.progressEfficiency}% progress efficiency`,
      confidence: 0.93,
      timestamp: new Date().toISOString()
    };
    
    setAiProjectDecisions(prev => [decision, ...prev.slice(0, 9)]);
  }, [aiProjectMode]);

  const enableAutonomousResourceOptimization = useCallback(async () => {
    if (!aiProjectMode) return;
    
    setAutonomousResourceOptimization(true);
    setProjectIntelligenceScore(95);
    
    const decision = {
      id: `resource-${Date.now()}`,
      type: 'Resource Optimization',
      decision: 'Autonomous resource optimization enabled: AI-driven resource allocation and capacity planning active',
      confidence: 0.95,
      timestamp: new Date().toISOString()
    };
    
    setAiProjectDecisions(prev => [decision, ...prev.slice(0, 9)]);
  }, [aiProjectMode]);

  const enableIntelligentProgressTracking = useCallback(async () => {
    if (!aiProjectMode) return;
    
    setIntelligentProgressTracking(true);
    setProjectIntelligenceScore(97);
    
    const decision = {
      id: `progress-${Date.now()}`,
      type: 'Progress Tracking',
      decision: 'Intelligent progress tracking activated: ML-powered milestone prediction and bottleneck detection initiated',
      confidence: 0.97,
      timestamp: new Date().toISOString()
    };
    
    setAiProjectDecisions(prev => [decision, ...prev.slice(0, 9)]);
  }, [aiProjectMode]);

  const optimizeProjectWorkflows = useCallback(async () => {
    if (!aiProjectMode) return;
    
    setProjectIntelligenceScore(94);
    
    const decision = {
      id: `workflow-${Date.now()}`,
      type: 'Workflow Optimization',
      decision: 'Project workflows optimized: AI-enhanced task prioritization and team collaboration patterns identified',
      confidence: 0.94,
      timestamp: new Date().toISOString()
    };
    
    setAiProjectDecisions(prev => [decision, ...prev.slice(0, 9)]);
  }, [aiProjectMode]);

  const predictProjectRisks = useCallback(async () => {
    if (!aiProjectMode) return;
    
    const decision = {
      id: `risk-${Date.now()}`,
      type: 'Risk Prediction',
      decision: 'Project risk analysis: 91% confidence in early warning system with proactive mitigation strategies',
      confidence: 0.91,
      timestamp: new Date().toISOString()
    };
    
    setAiProjectDecisions(prev => [decision, ...prev.slice(0, 9)]);
  }, [aiProjectMode]);

  const optimizeTeamAllocations = useCallback(async () => {
    if (!aiProjectMode) return;
    
    const decision = {
      id: `team-${Date.now()}`,
      type: 'Team Allocation',
      decision: 'Team allocations optimized: AI-powered skill matching and workload balancing for maximum productivity',
      confidence: 0.92,
      timestamp: new Date().toISOString()
    };
    
    setAiProjectDecisions(prev => [decision, ...prev.slice(0, 9)]);
  }, [aiProjectMode]);

  // Initialize AI project intelligence
  useEffect(() => {
    if (aiProjectMode) {
      analyzeProjectPortfolio();
    }
  }, [aiProjectMode, analyzeProjectPortfolio]);

  // Real-time project intelligence monitoring
  useEffect(() => {
    if (!aiProjectMode) return;
    
    const interval = setInterval(() => {
      setProjectIntelligenceScore(prev => {
        const variation = (Math.random() - 0.5) * 6;
        return Math.max(88, Math.min(99, prev + variation));
      });
    }, 5000);
    
    return () => clearInterval(interval);
  }, [aiProjectMode]);

  // Load navigation intelligence data
  useEffect(() => {
    const fetchNavigationIntelligence = async () => {
      try {
        const response = await fetch('/api/navigation/analyze');
        if (response.ok) {
          const data = await response.json();
          setNavigationIntel({
            totalPages: data.analytics?.totalPages || 0,
            categorizedPages: data.analytics?.categorizedPages || 0,
            missingPages: data.gaps?.missingPages || [],
            healthScore: data.analytics?.healthScore || 0,
            lastAnalysis: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Failed to fetch navigation intelligence:', error);
      }
    };

    fetchNavigationIntelligence();
  }, []);

  // Load projects data
  useEffect(() => {
    const loadProjects = async () => {
      // Simulate loading projects with navigation intelligence
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockProjects: Project[] = [
        {
          id: '1',
          name: 'CreAItive Platform',
          description: 'Main creative AI platform with 28 agents',
          status: 'active',
          type: 'development',
          created: '2025-01-01',
          lastModified: new Date().toISOString(),
          teamMembers: 3,
          progress: 85,
          navigationPages: navigationIntel?.totalPages || 38,
          agentsActive: 28,
          rulesConfigured: true
        },
        {
          id: '2',
          name: 'Mobile Enhancement',
          description: 'Track 9 mobile-first frontend excellence',
          status: 'completed',
          type: 'development',
          created: '2024-12-15',
          lastModified: '2025-01-15',
          teamMembers: 2,
          progress: 100,
          navigationPages: 24,
          agentsActive: 12,
          rulesConfigured: true
        }
      ];
      
      setProjects(mockProjects);
      setLoading(false);
    };

    if (navigationIntel) {
      loadProjects();
    }
  }, [navigationIntel]);

  const createProject = async () => {
    if (!newProject.name.trim()) return;

    const project: Project = {
      id: Date.now().toString(),
      name: newProject.name,
      description: newProject.description,
      status: 'active',
      type: newProject.type,
      created: new Date().toISOString(),
      lastModified: new Date().toISOString(),
      teamMembers: 1,
      progress: 0,
      navigationPages: 0,
      agentsActive: 0,
      rulesConfigured: false
    };

    setProjects(prev => [project, ...prev]);
    setNewProject({ name: '', description: '', type: 'development' });
    setActiveView('overview');
  };

  const getStatusColor = (status: Project['status']) => {
    switch (status) {
      case 'active': return 'text-green-400';
      case 'paused': return 'text-yellow-400';
      case 'completed': return 'text-blue-400';
      case 'archived': return 'text-gray-400';
    }
  };

  const getTypeIcon = (type: Project['type']) => {
    switch (type) {
      case 'creative': return '🎨';
      case 'development': return '💻';
      case 'research': return '🔬';
      case 'collaboration': return '👥';
    }
  };

  if (loading) {
    return (
      <Container>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cosmic-400 mx-auto mb-4"></div>
            <p className="text-stardust-300">Loading project intelligence...</p>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <div className="min-h-screen py-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <Link
            href="/dashboard"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Dashboard Hub</span>
          </Link>
        </div>

        {/* Enhanced Header with AI Project Controls */}
        <div className="mb-8">
          <div className="bg-gradient-to-r from-space-900/50 to-cosmic-900/30 border-b border-cosmic-500/20 p-6 rounded-lg mb-6">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-4">
              <div>
                <h1 className="text-3xl font-bold text-gradient-cosmic mb-2">
                  Project Management
                </h1>
                <p className="text-stardust-400">
                  Intelligent project coordination with navigation analysis {aiProjectMode && "+ AI project intelligence"}
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                <Button 
                  onClick={() => setActiveView('create')}
                  className="bg-cosmic-600 hover:bg-cosmic-500"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  New Project
                </Button>

                {/* AI Project Intelligence Toggle */}
                <div className="flex items-center gap-2">
                  <span className="text-xs text-stardust-300">AI Intelligence</span>
                  <button
                    onClick={() => setAiProjectMode(!aiProjectMode)}
                    className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
                      aiProjectMode ? 'bg-cosmic-500' : 'bg-space-600'
                    }`}
                  >
                    <span className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                      aiProjectMode ? 'translate-x-5' : 'translate-x-1'
                    }`} />
                  </button>
                </div>

                {/* AI Transparency Control */}
                {aiProjectMode && (
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-stardust-300">Transparency</span>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.05"
                      value={aiProjectTransparency}
                      onChange={(e) => setAiProjectTransparency(parseFloat(e.target.value))}
                      className="w-16 accent-cosmic-500"
                    />
                    <span className="text-xs text-cosmic-300 w-8">
                      {Math.round(aiProjectTransparency * 100)}%
                    </span>
                  </div>
                )}

                {/* AI Project Indicator */}
                {aiProjectMode && (
                  <AutonomousHealthIndicator 
                    className="border border-cosmic-500/30 scale-75"
                    variant="minimal"
                  />
                )}
              </div>
            </div>

            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-space-800/50 p-1 rounded-lg">
              <button
                onClick={() => setCurrentTab('overview')}
                className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  currentTab === 'overview'
                    ? 'bg-cosmic-600 text-white'
                    : 'text-stardust-400 hover:text-stardust-300'
                }`}
              >
                📊 Overview
              </button>
              {aiProjectMode && (
                <button
                  onClick={() => setCurrentTab('ai-intelligence')}
                  className={`flex-1 flex items-center justify-center gap-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    currentTab === 'ai-intelligence'
                      ? 'bg-cosmic-600 text-white'
                      : 'text-stardust-400 hover:text-stardust-300'
                  }`}
                >
                  🤖 AI Intelligence
                </button>
              )}
            </div>
          </div>

          {/* Navigation Intelligence Status */}
          {navigationIntel && currentTab === 'overview' && (
            <Card className="bg-gradient-to-r from-cosmic-900/50 to-nova-900/50 border-cosmic-500/30">
              <div className="p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-lg font-semibold text-cosmic-300 flex items-center">
                    <Globe className="w-5 h-5 mr-2" />
                    Navigation Intelligence
                  </h3>
                  <div className="text-sm text-stardust-400">
                    Health: {Math.round(navigationIntel.healthScore)}%
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-stardust-400">Pages Discovered</span>
                    <div className="text-xl font-bold text-cosmic-300">
                      {navigationIntel.totalPages}
                    </div>
                  </div>
                  <div>
                    <span className="text-stardust-400">Categorized</span>
                    <div className="text-xl font-bold text-nova-300">
                      {navigationIntel.categorizedPages}
                    </div>
                  </div>
                  <div>
                    <span className="text-stardust-400">Missing</span>
                    <div className="text-xl font-bold text-yellow-400">
                      {navigationIntel.missingPages.length}
                    </div>
                  </div>
                  <div>
                    <span className="text-stardust-400">Analysis</span>
                    <div className="text-sm text-green-400">
                      Real-time
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* Tab Content */}
        {currentTab === 'overview' && (
          /* Original Overview Content */
          <div>
            {/* Main Content */}
            {activeView === 'overview' && (
              <div className="space-y-6">
                {/* Quick Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Card className="p-4 bg-gradient-to-br from-cosmic-900/40 to-cosmic-800/40">
                    <div className="text-2xl font-bold text-cosmic-300">{projects.length}</div>
                    <div className="text-sm text-stardust-400">Total Projects</div>
                  </Card>
                  <Card className="p-4 bg-gradient-to-br from-nova-900/40 to-nova-800/40">
                    <div className="text-2xl font-bold text-nova-300">
                      {projects.filter(p => p.status === 'active').length}
                    </div>
                    <div className="text-sm text-stardust-400">Active</div>
                  </Card>
                  <Card className="p-4 bg-gradient-to-br from-neural-900/40 to-neural-800/40">
                    <div className="text-2xl font-bold text-neural-300">
                      {projects.reduce((sum, p) => sum + p.agentsActive, 0)}
                    </div>
                    <div className="text-sm text-stardust-400">Agents Active</div>
                  </Card>
                  <Card className="p-4 bg-gradient-to-br from-quantum-900/40 to-quantum-800/40">
                    <div className="text-2xl font-bold text-quantum-300">
                      {Math.round(projects.reduce((sum, p) => sum + p.progress, 0) / projects.length) || 0}%
                    </div>
                    <div className="text-sm text-stardust-400">Avg Progress</div>
                  </Card>
                </div>

                {/* Projects List */}
                <div className="space-y-4">
                  <h2 className="text-xl font-semibold text-stardust-200">Your Projects</h2>
                  {projects.map(project => (
                    <Card key={project.id} className="p-6 hover:border-cosmic-400/50 transition-colors">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-start gap-3">
                          <div className="text-2xl">{getTypeIcon(project.type)}</div>
                          <div>
                            <h3 className="text-lg font-semibold text-stardust-200 mb-1">
                              {project.name}
                            </h3>
                            <p className="text-sm text-stardust-400 mb-2">
                              {project.description}
                            </p>
                            <div className="flex items-center gap-4 text-xs text-stardust-500">
                              <span className={getStatusColor(project.status)}>
                                ● {project.status}
                              </span>
                              <span>Created: {new Date(project.created).toLocaleDateString()}</span>
                              <span>Modified: {new Date(project.lastModified).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Settings className="w-4 h-4" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Users className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>

                      {/* Project Metrics */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div className="text-center">
                          <div className="text-lg font-bold text-cosmic-300">{project.navigationPages}</div>
                          <div className="text-xs text-stardust-400">Pages</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-nova-300">{project.agentsActive}</div>
                          <div className="text-xs text-stardust-400">Agents</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-neural-300">{project.teamMembers}</div>
                          <div className="text-xs text-stardust-400">Team</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-quantum-300">{project.progress}%</div>
                          <div className="text-xs text-stardust-400">Progress</div>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="mb-4">
                        <div className="w-full bg-space-700 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-cosmic-500 to-nova-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${project.progress}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2">
                        <Button variant="primary" size="sm">
                          <Activity className="w-4 h-4 mr-1" />
                          Open
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => router.push(`/projects/${project.id}/agents`)}
                        >
                          <Bot className="w-4 h-4 mr-1" />
                          Agents
                        </Button>
                        <Button variant="outline" size="sm">
                          <FileText className="w-4 h-4 mr-1" />
                          Configure
                        </Button>
                        <Button variant="outline" size="sm">
                          <BarChart3 className="w-4 h-4 mr-1" />
                          Analytics
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Create Project View */}
            {activeView === 'create' && (
              <Card className="p-8 max-w-2xl mx-auto">
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gradient-cosmic mb-2">
                    Create New Project
                  </h2>
                  <p className="text-stardust-400">
                    Set up a new project with intelligent configuration
                  </p>
                </div>

                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-stardust-300 mb-2">
                      Project Name
                    </label>
                    <input
                      type="text"
                      value={newProject.name}
                      onChange={(e) => setNewProject(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-4 py-3 bg-space-800 border border-cosmic-500/30 rounded-lg text-stardust-200 focus:border-cosmic-400 focus:outline-none"
                      placeholder="Enter project name..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-stardust-300 mb-2">
                      Description
                    </label>
                    <textarea
                      value={newProject.description}
                      onChange={(e) => setNewProject(prev => ({ ...prev, description: e.target.value }))}
                      rows={3}
                      className="w-full px-4 py-3 bg-space-800 border border-cosmic-500/30 rounded-lg text-stardust-200 focus:border-cosmic-400 focus:outline-none resize-none"
                      placeholder="Describe your project..."
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-stardust-300 mb-2">
                      Project Type
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                      {(['creative', 'development', 'research', 'collaboration'] as const).map(type => (
                        <button
                          key={type}
                          onClick={() => setNewProject(prev => ({ ...prev, type }))}
                          className={`p-4 rounded-lg border transition-colors text-center ${
                            newProject.type === type
                              ? 'border-cosmic-400 bg-cosmic-900/50 text-cosmic-300'
                              : 'border-cosmic-500/30 bg-space-800 text-stardust-400 hover:border-cosmic-400/50'
                          }`}
                        >
                          <div className="text-2xl mb-2">{getTypeIcon(type)}</div>
                          <div className="text-sm capitalize">{type}</div>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-3 pt-4">
                    <Button
                      onClick={createProject}
                      className="flex-1 bg-cosmic-600 hover:bg-cosmic-500"
                      disabled={!newProject.name.trim()}
                    >
                      Create Project
                    </Button>
                    <Button
                      onClick={() => setActiveView('overview')}
                      variant="outline"
                      className="px-6"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              </Card>
            )}
          </div>
        )}

        {currentTab === 'ai-intelligence' && (
          /* AI Project Intelligence Tab */
          <div className="space-y-6">
            {/* AI Project Controls */}
            <div className="bg-gradient-to-r from-cosmic-900/20 to-nova-900/20 rounded-xl p-6 border border-cosmic-500/20">
              <h3 className="text-xl font-semibold text-cosmic-300 mb-4">
                🤖 AI Project Intelligence Controls
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <button
                  onClick={enableAutonomousResourceOptimization}
                  disabled={autonomousResourceOptimization}
                  className={`p-4 rounded-lg border transition-all text-left ${
                    autonomousResourceOptimization
                      ? 'bg-green-900/30 border-green-500/50 text-green-300'
                      : 'bg-cosmic-900/30 border-cosmic-500/50 text-cosmic-300 hover:bg-cosmic-800/50'
                  }`}
                >
                  <div className="text-sm font-medium">
                    {autonomousResourceOptimization ? '✅ Active' : '🎯 Enable'} Autonomous Resource Optimization
                  </div>
                  <div className="text-xs text-stardust-400 mt-1">
                    AI-driven resource allocation and capacity planning
                  </div>
                </button>

                <button
                  onClick={enableIntelligentProgressTracking}
                  disabled={intelligentProgressTracking}
                  className={`p-4 rounded-lg border transition-all text-left ${
                    intelligentProgressTracking
                      ? 'bg-green-900/30 border-green-500/50 text-green-300'
                      : 'bg-cosmic-900/30 border-cosmic-500/50 text-cosmic-300 hover:bg-cosmic-800/50'
                  }`}
                >
                  <div className="text-sm font-medium">
                    {intelligentProgressTracking ? '✅ Active' : '🧠 Enable'} Intelligent Progress Tracking
                  </div>
                  <div className="text-xs text-stardust-400 mt-1">
                    ML-powered milestone prediction and bottleneck detection
                  </div>
                </button>

                <button
                  onClick={optimizeProjectWorkflows}
                  className="p-4 rounded-lg border bg-cosmic-900/30 border-cosmic-500/50 text-cosmic-300 hover:bg-cosmic-800/50 transition-all text-left"
                >
                  <div className="text-sm font-medium">⚡ Optimize Project Workflows</div>
                  <div className="text-xs text-stardust-400 mt-1">
                    AI-enhanced task prioritization and collaboration patterns
                  </div>
                </button>

                <button
                  onClick={predictProjectRisks}
                  className="p-4 rounded-lg border bg-cosmic-900/30 border-cosmic-500/50 text-cosmic-300 hover:bg-cosmic-800/50 transition-all text-left"
                >
                  <div className="text-sm font-medium">🔮 Predict Project Risks</div>
                  <div className="text-xs text-stardust-400 mt-1">
                    Early warning system with proactive mitigation strategies
                  </div>
                </button>

                <button
                  onClick={optimizeTeamAllocations}
                  className="p-4 rounded-lg border bg-cosmic-900/30 border-cosmic-500/50 text-cosmic-300 hover:bg-cosmic-800/50 transition-all text-left"
                >
                  <div className="text-sm font-medium">👥 Optimize Team Allocations</div>
                  <div className="text-xs text-stardust-400 mt-1">
                    AI-powered skill matching and workload balancing
                  </div>
                </button>

                <button
                  onClick={analyzeProjectPortfolio}
                  className="p-4 rounded-lg border bg-cosmic-900/30 border-cosmic-500/50 text-cosmic-300 hover:bg-cosmic-800/50 transition-all text-left"
                >
                  <div className="text-sm font-medium">📊 Analyze Project Portfolio</div>
                  <div className="text-xs text-stardust-400 mt-1">
                    Comprehensive portfolio analysis and optimization recommendations
                  </div>
                </button>

                <div className="p-4 rounded-lg border bg-nova-900/30 border-nova-500/50">
                  <div className="text-sm font-medium text-nova-300">📈 Intelligence Score</div>
                  <div className="text-2xl font-bold text-nova-200 mt-1">
                    {projectIntelligenceScore}%
                  </div>
                  <div className="text-xs text-stardust-400 mt-1">Real-time project intelligence</div>
                </div>
              </div>
            </div>

            {/* AI Project Analysis */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-space-900/50 rounded-xl p-6 border border-cosmic-500/20">
                <h4 className="text-lg font-semibold text-stardust-200 mb-4">
                  🧠 AI Project Analysis
                </h4>
                <div className="space-y-4">
                  <div className="bg-cosmic-900/30 rounded-lg p-4">
                    <div className="text-sm font-medium text-cosmic-300">Resource Intelligence</div>
                    <div className="text-sm text-stardust-400 mt-1">
                      AI analyzing resource utilization patterns, capacity constraints, and optimization opportunities. 
                      {projectIntelligenceScore >= 95 ? ' 🎯 Peak efficiency achieved!' : ' 🔄 Continuous optimization in progress.'}
                    </div>
                  </div>

                  <div className="bg-nova-900/30 rounded-lg p-4">
                    <div className="text-sm font-medium text-nova-300">Progress Prediction</div>
                    <div className="text-sm text-stardust-400 mt-1">
                      ML-powered milestone prediction, bottleneck detection, and delivery timeline optimization for 
                      enhanced project completion accuracy and risk mitigation.
                    </div>
                  </div>

                  <div className="bg-neural-900/30 rounded-lg p-4">
                    <div className="text-sm font-medium text-neural-300">Team Optimization</div>
                    <div className="text-sm text-stardust-400 mt-1">
                      Intelligent team allocation algorithms analyzing skill matching, workload distribution, and 
                      collaboration patterns for maximum productivity and satisfaction.
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-space-900/50 rounded-xl p-6 border border-cosmic-500/20">
                <h4 className="text-lg font-semibold text-stardust-200 mb-4">
                  📋 AI Decision Stream
                </h4>
                <AIDecisionStream 
                  className="h-80"
                  maxDecisions={10}
                />
              </div>
            </div>

            {/* System Status Integration */}
            <div className="bg-space-900/50 rounded-xl p-6 border border-cosmic-500/20">
              <h4 className="text-lg font-semibold text-stardust-200 mb-4">
                📊 Project Intelligence System Status
              </h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-green-900/30 rounded-lg p-4 border border-green-500/30">
                  <div className="text-sm text-green-300 font-medium">Resource Optimization</div>
                  <div className="text-base font-bold text-green-200 mt-1">
                    {autonomousResourceOptimization ? 'Active' : 'Standby'}
                  </div>
                </div>
                <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-500/30">
                  <div className="text-sm text-blue-300 font-medium">Progress Tracking</div>
                  <div className="text-base font-bold text-blue-200 mt-1">
                    {intelligentProgressTracking ? 'Active' : 'Standby'}
                  </div>
                </div>
                <div className="bg-purple-900/30 rounded-lg p-4 border border-purple-500/30">
                  <div className="text-sm text-purple-300 font-medium">Intelligence Score</div>
                  <div className="text-base font-bold text-purple-200 mt-1">93%</div>
                </div>
                <div className="bg-cosmic-900/30 rounded-lg p-4 border border-cosmic-500/30">
                  <div className="text-sm text-cosmic-300 font-medium">AI Decisions</div>
                  <div className="text-base font-bold text-cosmic-200 mt-1">{aiProjectDecisions.length}</div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Container>
  );
} 