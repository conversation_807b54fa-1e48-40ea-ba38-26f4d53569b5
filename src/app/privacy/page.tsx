'use client';

import React from 'react';
import { Shield, Lock, Eye, Database, Users, FileText } from 'lucide-react';

export default function PrivacyPage() {
  const sections = [
    {
      icon: Database,
      title: 'Information We Collect',
      content: [
        'Account information (name, email, profile details)',
        'Usage data and interaction patterns with AI agents',
        'Content you create and share on the platform',
        'Technical information (IP address, browser type, device info)',
        'Communication records when you contact support'
      ]
    },
    {
      icon: Eye,
      title: 'How We Use Your Information',
      content: [
        'Provide and improve our AI agent services',
        'Personalize your experience and recommendations',
        'Communicate with you about updates and features',
        'Ensure platform security and prevent abuse',
        'Analyze usage patterns to enhance performance'
      ]
    },
    {
      icon: Users,
      title: 'Information Sharing',
      content: [
        'We do not sell your personal information to third parties',
        'Data may be shared with service providers under strict agreements',
        'Legal compliance may require disclosure in specific circumstances',
        'Anonymous, aggregated data may be used for research purposes',
        'You control what content you choose to make public'
      ]
    },
    {
      icon: Lock,
      title: 'Data Security',
      content: [
        'End-to-end encryption for sensitive communications',
        'Regular security audits and vulnerability assessments',
        'Secure data centers with industry-standard protections',
        'Access controls and authentication requirements',
        'Incident response procedures for potential breaches'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-cosmic-500/20 px-4 py-2 rounded-full mb-6">
            <Shield className="w-4 h-4 text-cosmic-400" />
            <span className="text-cosmic-300 text-sm font-medium">Privacy Policy</span>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Your Privacy
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-purple-400 ml-2">
              Matters
            </span>
          </h1>
          
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            We're committed to protecting your privacy and being transparent about 
            how we collect, use, and protect your information.
          </p>
          
          <div className="mt-6 text-white/60">
            <p>Last updated: January 15, 2024</p>
          </div>
        </div>

        {/* Key Principles */}
        <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 mb-16 border border-white/10">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">Our Privacy Principles</h2>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <Shield className="w-8 h-8 text-cosmic-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Transparency</h3>
              <p className="text-white/70 text-sm">
                Clear communication about what data we collect and why
              </p>
            </div>
            
            <div className="text-center">
              <Lock className="w-8 h-8 text-cosmic-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Security</h3>
              <p className="text-white/70 text-sm">
                Industry-leading security measures to protect your data
              </p>
            </div>
            
            <div className="text-center">
              <Users className="w-8 h-8 text-cosmic-400 mx-auto mb-3" />
              <h3 className="text-lg font-semibold text-white mb-2">Control</h3>
              <p className="text-white/70 text-sm">
                You decide what to share and can manage your privacy settings
              </p>
            </div>
          </div>
        </div>

        {/* Privacy Sections */}
        <div className="space-y-8 mb-16">
          {sections.map((section, index) => (
            <div
              key={index}
              className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-cosmic-500/20 rounded-lg">
                  <section.icon className="w-5 h-5 text-cosmic-400" />
                </div>
                <h2 className="text-2xl font-bold text-white">{section.title}</h2>
              </div>
              
              <ul className="space-y-3">
                {section.content.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start gap-3">
                    <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-white/70">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Your Rights */}
        <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 mb-16">
          <div className="flex items-center gap-3 mb-6">
            <FileText className="w-6 h-6 text-cosmic-400" />
            <h2 className="text-2xl font-bold text-white">Your Rights</h2>
          </div>
          
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Access & Portability</h3>
              <p className="text-white/70 mb-4">
                Request a copy of your personal data and export your content at any time.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Correction & Deletion</h3>
              <p className="text-white/70 mb-4">
                Update incorrect information or request deletion of your account and data.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Opt-Out</h3>
              <p className="text-white/70 mb-4">
                Control marketing communications and data processing preferences.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">Complaint</h3>
              <p className="text-white/70 mb-4">
                File complaints with supervisory authorities if you have concerns.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-4">Questions About Privacy?</h3>
            <p className="text-white/70 mb-6 max-w-2xl mx-auto">
              Our privacy team is here to help. Contact us with any questions about 
              this policy or how we handle your data.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-6 py-3 rounded-lg transition-all">
                Contact Privacy Team
              </button>
              <button className="bg-space-700 hover:bg-space-600 text-white px-6 py-3 rounded-lg transition-all">
                Manage Privacy Settings
              </button>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-12 text-center text-white/60 text-sm">
          <p>
            This privacy policy applies to all CreAItive services and may be updated periodically. 
            We'll notify you of significant changes via email or platform notifications.
          </p>
        </div>
      </div>
    </div>
  );
}
