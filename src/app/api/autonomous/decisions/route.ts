import { NextRequest, NextResponse } from 'next/server';

// Simulated decision history
let decisionHistory: any[] = [];

// Generate realistic AI decisions
function generateAIDecision() {
  const decisionTypes = [
    'resource_optimization',
    'task_prioritization', 
    'agent_coordination',
    'performance_tuning',
    'error_resolution',
    'capacity_scaling',
    'workflow_optimization',
    'security_enhancement'
  ];
  
  const outcomes = ['successful', 'pending', 'optimizing', 'completed'];
  const agents = ['DevAgent', 'TestAgent', 'SecurityAgent', 'MonitoringAgent', 'CoordinationAgent'];
  
  return {
    id: `decision_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    timestamp: new Date().toISOString(),
    type: decisionTypes[Math.floor(Math.random() * decisionTypes.length)],
    description: `Autonomous decision for ${decisionTypes[Math.floor(Math.random() * decisionTypes.length)].replace('_', ' ')}`,
    confidence: 0.85 + Math.random() * 0.14, // 85-99% confidence
    outcome: outcomes[Math.floor(Math.random() * outcomes.length)],
    affectedAgents: [agents[Math.floor(Math.random() * agents.length)]],
    reasoning: [
      'Analyzed current system performance metrics',
      'Evaluated resource utilization patterns',
      'Considered historical optimization outcomes',
      'Applied machine learning predictions'
    ],
    impact: {
      performance: Math.random() * 10 - 5, // -5% to +5% impact
      efficiency: Math.random() * 8,       // 0% to +8% efficiency
      resources: Math.random() * 6 - 3     // -3% to +3% resource usage
    },
    duration: Math.floor(Math.random() * 300) + 30, // 30-330 seconds
    priority: Math.floor(Math.random() * 5) + 1     // 1-5 priority
  };
}

// Initialize with some decisions
if (decisionHistory.length === 0) {
  for (let i = 0; i < 10; i++) {
    decisionHistory.push(generateAIDecision());
  }
}

export async function GET(request: NextRequest) {
  try {
    console.log('🧠 AI Decisions API called');
    
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'recent';
    const limit = parseInt(searchParams.get('limit') || '10');
    const since = searchParams.get('since');
    
    // Add new decisions periodically
    if (Math.random() < 0.3) {
      decisionHistory.unshift(generateAIDecision());
      // Keep only last 100 decisions
      if (decisionHistory.length > 100) {
        decisionHistory = decisionHistory.slice(0, 100);
      }
    }
    
    let filteredDecisions = [...decisionHistory];
    
    // Filter by timestamp if 'since' parameter provided
    if (since) {
      const sinceDate = new Date(since);
      filteredDecisions = filteredDecisions.filter(
        decision => new Date(decision.timestamp) > sinceDate
      );
    }
    
    // Limit results
    filteredDecisions = filteredDecisions.slice(0, limit);
    
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      data: {
        decisions: filteredDecisions,
        totalDecisions: decisionHistory.length,
        recentDecisions: filteredDecisions.length,
        systemStatus: 'active',
        decisionRate: Math.floor(Math.random() * 20) + 10, // 10-30 decisions per hour
        averageConfidence: filteredDecisions.reduce((sum, d) => sum + d.confidence, 0) / filteredDecisions.length || 0,
        successRate: filteredDecisions.filter(d => d.outcome === 'successful').length / filteredDecisions.length || 0
      }
    };

    // Handle different actions
    switch (action) {
      case 'metrics':
        response.data = {
          totalDecisions: decisionHistory.length,
          successfulDecisions: decisionHistory.filter(d => d.outcome === 'successful').length,
          pendingDecisions: decisionHistory.filter(d => d.outcome === 'pending').length,
          averageConfidence: decisionHistory.reduce((sum, d) => sum + d.confidence, 0) / decisionHistory.length,
          decisionTypes: decisionHistory.reduce((acc, d) => {
            acc[d.type] = (acc[d.type] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
          performanceImpact: {
            positive: decisionHistory.filter(d => d.impact.performance > 0).length,
            negative: decisionHistory.filter(d => d.impact.performance < 0).length,
            neutral: decisionHistory.filter(d => Math.abs(d.impact.performance) < 0.1).length
          }
        };
        break;
        
      case 'active':
        response.data = {
          activeDecisions: filteredDecisions.filter(d => d.outcome === 'pending' || d.outcome === 'optimizing'),
          processingCount: filteredDecisions.filter(d => d.outcome === 'optimizing').length,
          queuedCount: filteredDecisions.filter(d => d.outcome === 'pending').length
        };
        break;
        
      case 'evolution':
        response.data = {
          learningProgress: Math.random() * 100,
          adaptationRate: 85 + Math.random() * 10,
          knowledgeGrowth: Math.random() * 5 + 2,
          evolutionStage: 'advanced_learning',
          capabilities: [
            'Pattern Recognition',
            'Predictive Analysis', 
            'Autonomous Optimization',
            'Cross-Agent Learning',
            'Emergent Behavior'
          ]
        };
        break;
    }

    return NextResponse.json(response);
    
  } catch (error) {
    console.error('❌ AI Decisions API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get AI decisions',
      details: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, decision } = await request.json();
    
    console.log(`🧠 AI Decision Action: ${action}`);
    
    switch (action) {
      case 'approve':
        return NextResponse.json({
          success: true,
          message: 'Decision approved by human oversight',
          decisionId: decision?.id,
          timestamp: new Date().toISOString()
        });
        
      case 'reject':
        return NextResponse.json({
          success: true,
          message: 'Decision rejected by human oversight',
          decisionId: decision?.id,
          timestamp: new Date().toISOString()
        });
        
      case 'request_explanation':
        return NextResponse.json({
          success: true,
          explanation: {
            reasoning: decision?.reasoning || [],
            dataPoints: ['System metrics', 'Historical patterns', 'ML predictions'],
            confidence: decision?.confidence || 0.9,
            alternatives: ['Alternative approach A', 'Alternative approach B'],
            riskAssessment: 'Low risk with high potential benefit'
          },
          timestamp: new Date().toISOString()
        });
        
      default:
        return NextResponse.json({
          success: false,
          error: `Unknown action: ${action}`
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('❌ AI Decision action error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to execute decision action',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
