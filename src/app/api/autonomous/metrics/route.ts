import { NextRequest, NextResponse } from 'next/server';

// Generate realistic time-series data
function generateTimeSeriesData(points: number = 24) {
  const data = [];
  const now = Date.now();
  
  for (let i = points - 1; i >= 0; i--) {
    const timestamp = new Date(now - (i * 60 * 60 * 1000)); // Hourly data
    data.push({
      timestamp: timestamp.toISOString(),
      performance: 85 + Math.random() * 10 + Math.sin(i / 4) * 5,
      efficiency: 80 + Math.random() * 15 + Math.cos(i / 3) * 3,
      accuracy: 90 + Math.random() * 8 + Math.sin(i / 6) * 2,
      throughput: 1000 + Math.random() * 500 + Math.sin(i / 2) * 200,
      latency: 20 + Math.random() * 10 + Math.cos(i / 5) * 5,
      errorRate: Math.max(0, 2 + Math.random() * 3 - Math.sin(i / 4) * 1.5)
    });
  }
  
  return data;
}

export async function GET(request: NextRequest) {
  try {
    console.log('📊 Autonomous Metrics API called');
    
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'current';
    const timeRange = searchParams.get('timeRange') || '24h';
    const granularity = searchParams.get('granularity') || 'hour';
    
    // Calculate data points based on time range
    let dataPoints = 24; // Default 24 hours
    switch (timeRange) {
      case '1h': dataPoints = 12; break;   // 5-minute intervals
      case '6h': dataPoints = 24; break;   // 15-minute intervals  
      case '24h': dataPoints = 24; break;  // 1-hour intervals
      case '7d': dataPoints = 168; break;  // 1-hour intervals for 7 days
      case '30d': dataPoints = 30; break;  // 1-day intervals for 30 days
    }
    
    const timeSeriesData = generateTimeSeriesData(dataPoints);
    const latest = timeSeriesData[timeSeriesData.length - 1];
    
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      timeRange,
      granularity,
      data: {
        current: {
          performance: latest.performance,
          efficiency: latest.efficiency,
          accuracy: latest.accuracy,
          throughput: latest.throughput,
          latency: latest.latency,
          errorRate: latest.errorRate,
          autonomyLevel: 94.7,
          systemHealth: 'optimal',
          activeAgents: 26,
          totalDecisions: 1247,
          successRate: 96.8
        },
        historical: timeSeriesData,
        aggregated: {
          averagePerformance: timeSeriesData.reduce((sum, d) => sum + d.performance, 0) / timeSeriesData.length,
          averageEfficiency: timeSeriesData.reduce((sum, d) => sum + d.efficiency, 0) / timeSeriesData.length,
          averageAccuracy: timeSeriesData.reduce((sum, d) => sum + d.accuracy, 0) / timeSeriesData.length,
          peakThroughput: Math.max(...timeSeriesData.map(d => d.throughput)),
          minLatency: Math.min(...timeSeriesData.map(d => d.latency)),
          averageErrorRate: timeSeriesData.reduce((sum, d) => sum + d.errorRate, 0) / timeSeriesData.length
        },
        trends: {
          performance: calculateTrend(timeSeriesData.map(d => d.performance)),
          efficiency: calculateTrend(timeSeriesData.map(d => d.efficiency)),
          accuracy: calculateTrend(timeSeriesData.map(d => d.accuracy)),
          throughput: calculateTrend(timeSeriesData.map(d => d.throughput)),
          latency: calculateTrend(timeSeriesData.map(d => d.latency)),
          errorRate: calculateTrend(timeSeriesData.map(d => d.errorRate))
        }
      }
    };

    // Handle different actions
    switch (action) {
      case 'performance':
        (response.data as any) = {
          systemPerformance: {
            overall: latest.performance,
            components: {
              mlCoordination: 92.3,
              agentCommunication: 89.7,
              decisionEngine: 95.1,
              learningSystem: 87.9,
              safetyMonitoring: 98.2
            },
            bottlenecks: [
              { component: 'learningSystem', impact: 'low', recommendation: 'Increase learning rate' }
            ],
            optimizations: [
              { type: 'cache_optimization', impact: '+3.2% performance' },
              { type: 'algorithm_tuning', impact: '+1.8% efficiency' }
            ]
          }
        };
        break;

      case 'agents':
        (response.data as any) = {
          agentMetrics: {
            totalAgents: 28,
            activeAgents: 26,
            idleAgents: 2,
            averageLoad: 67.3,
            communicationRate: 45.7, // messages per second
            consensusTime: 234.5,    // milliseconds
            coordinationEfficiency: 91.2,
            individualPerformance: Array.from({ length: 28 }, (_, i) => ({
              agentId: `agent_${i + 1}`,
              name: `Agent${i + 1}`,
              performance: 80 + Math.random() * 20,
              load: Math.random() * 100,
              status: Math.random() > 0.1 ? 'active' : 'idle',
              lastActivity: new Date(Date.now() - Math.random() * 3600000).toISOString()
            }))
          }
        };
        break;

      case 'learning':
        (response.data as any) = {
          learningMetrics: {
            knowledgeBase: {
              size: '2.3TB',
              growth: '+127MB/day',
              patterns: 15847,
              newPatterns: 23
            },
            adaptationRate: 89.3,
            learningVelocity: 92.1,
            knowledgeRetention: 94.7,
            crossAgentLearning: {
              sharedKnowledge: 78.9,
              collaborativeLearning: 85.2,
              knowledgeTransfer: 91.4
            },
            recentLearnings: [
              'Optimized resource allocation patterns',
              'Improved error prediction algorithms',
              'Enhanced inter-agent communication protocols'
            ]
          }
        };
        break;

      case 'security':
        (response.data as any) = {
          securityMetrics: {
            threatLevel: 'low',
            securityScore: 96.8,
            activeThreats: 0,
            blockedAttempts: 12,
            securityEvents: [
              { type: 'authentication_success', count: 1247 },
              { type: 'authorization_check', count: 3456 },
              { type: 'suspicious_activity', count: 3 }
            ],
            safetyProtocols: {
              active: 15,
              triggered: 0,
              lastCheck: new Date().toISOString()
            }
          }
        };
        break;
    }

    return NextResponse.json(response);
    
  } catch (error) {
    console.error('❌ Autonomous Metrics API error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get autonomous metrics',
      details: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Helper function to calculate trend
function calculateTrend(values: number[]): 'improving' | 'declining' | 'stable' {
  if (values.length < 2) return 'stable';
  
  const recent = values.slice(-5); // Last 5 values
  const older = values.slice(-10, -5); // Previous 5 values
  
  if (recent.length === 0 || older.length === 0) return 'stable';
  
  const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
  const olderAvg = older.reduce((sum, val) => sum + val, 0) / older.length;
  
  const change = ((recentAvg - olderAvg) / olderAvg) * 100;
  
  if (change > 2) return 'improving';
  if (change < -2) return 'declining';
  return 'stable';
}

export async function POST(request: NextRequest) {
  try {
    const { action, parameters } = await request.json();
    
    console.log(`📊 Metrics Action: ${action}`);
    
    switch (action) {
      case 'reset_metrics':
        return NextResponse.json({
          success: true,
          message: 'Metrics reset successfully',
          timestamp: new Date().toISOString()
        });
        
      case 'export_data':
        return NextResponse.json({
          success: true,
          exportUrl: '/api/autonomous/metrics/export',
          format: parameters?.format || 'json',
          timestamp: new Date().toISOString()
        });
        
      default:
        return NextResponse.json({
          success: false,
          error: `Unknown action: ${action}`
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('❌ Metrics action error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to execute metrics action',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
