import { NextRequest, NextResponse } from 'next/server';
import { 
  getSystemMetrics, 
  getHistoricalMetrics, 
  getSystemHealth,
  formatBytes, 
  formatUptime,
  getProcessMonitoring 
} from '@/utils/systemMonitoring';

export async function GET(request: NextRequest) {
  try {
    console.log('📊 Getting real-time system metrics...');
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const includeHistory = searchParams.get('history') === 'true';
    const historyLimit = parseInt(searchParams.get('limit') || '50');
    const format = searchParams.get('format') || 'detailed';
    
    // Get real system metrics
    const systemMetrics = await getSystemMetrics();
    const systemHealth = getSystemHealth(systemMetrics);
    const processData = getProcessMonitoring();
    
    // Get historical data if requested
    let historicalData: any[] = [];
    if (includeHistory) {
      historicalData = getHistoricalMetrics(undefined, undefined, historyLimit);
    }
    
    // Format response based on requested format
    if (format === 'compact') {
      return NextResponse.json({
        success: true,
        timestamp: systemMetrics.timestamp,
        metrics: {
          cpu: Math.round(systemMetrics.cpu.usage * 100) / 100,
          memory: Math.round(systemMetrics.memory.usagePercentage * 100) / 100,
          disk: Math.round(systemMetrics.disk.usagePercentage * 100) / 100,
          health: systemHealth.status,
          score: systemHealth.score
        },
        realData: true
      });
    }
    
    // Detailed format (default)
    const response = {
      success: true,
      timestamp: systemMetrics.timestamp,
      data: {
        system: {
          health: {
            status: systemHealth.status,
            score: systemHealth.score,
            issues: systemHealth.issues
          },
          cpu: {
            usage: Math.round(systemMetrics.cpu.usage * 100) / 100,
            cores: systemMetrics.cpu.cores,
            model: systemMetrics.cpu.model,
            speed: systemMetrics.cpu.speed,
            loadAverage: systemMetrics.cpu.loadAverage.map(load => Math.round(load * 100) / 100),
            status: systemMetrics.cpu.usage > 80 ? 'critical' : systemMetrics.cpu.usage > 60 ? 'warning' : 'normal'
          },
          memory: {
            total: systemMetrics.memory.total,
            totalFormatted: formatBytes(systemMetrics.memory.total),
            used: systemMetrics.memory.used,
            usedFormatted: formatBytes(systemMetrics.memory.used),
            free: systemMetrics.memory.free,
            freeFormatted: formatBytes(systemMetrics.memory.free),
            available: systemMetrics.memory.available,
            availableFormatted: formatBytes(systemMetrics.memory.available),
            usage: Math.round(systemMetrics.memory.usagePercentage * 100) / 100,
            status: systemMetrics.memory.usagePercentage > 90 ? 'critical' : systemMetrics.memory.usagePercentage > 80 ? 'warning' : 'normal'
          },
          disk: {
            total: systemMetrics.disk.total,
            totalFormatted: formatBytes(systemMetrics.disk.total),
            used: systemMetrics.disk.used,
            usedFormatted: formatBytes(systemMetrics.disk.used),
            free: systemMetrics.disk.free,
            freeFormatted: formatBytes(systemMetrics.disk.free),
            usage: Math.round(systemMetrics.disk.usagePercentage * 100) / 100,
            status: systemMetrics.disk.usagePercentage > 95 ? 'critical' : systemMetrics.disk.usagePercentage > 85 ? 'warning' : 'normal'
          },
          network: {
            interfaces: systemMetrics.network.interfaces.map(iface => ({
              name: iface.name,
              address: iface.address,
              family: iface.family,
              internal: iface.internal,
              mac: iface.mac
            })),
            activeConnections: systemMetrics.network.activeConnections,
            externalInterfaces: systemMetrics.network.interfaces.filter(iface => !iface.internal).length,
            status: 'normal'
          },
          process: {
            pid: systemMetrics.process.pid,
            uptime: systemMetrics.process.uptime,
            uptimeFormatted: formatUptime(systemMetrics.process.uptime),
            memory: {
              rss: systemMetrics.process.memoryUsage.rss,
              rssFormatted: formatBytes(systemMetrics.process.memoryUsage.rss),
              heapTotal: systemMetrics.process.memoryUsage.heapTotal,
              heapTotalFormatted: formatBytes(systemMetrics.process.memoryUsage.heapTotal),
              heapUsed: systemMetrics.process.memoryUsage.heapUsed,
              heapUsedFormatted: formatBytes(systemMetrics.process.memoryUsage.heapUsed),
              external: systemMetrics.process.memoryUsage.external,
              externalFormatted: formatBytes(systemMetrics.process.memoryUsage.external)
            },
            status: 'running'
          },
          platform: {
            os: systemMetrics.system.platform,
            arch: systemMetrics.system.arch,
            hostname: systemMetrics.system.hostname,
            uptime: systemMetrics.system.uptime,
            uptimeFormatted: formatUptime(systemMetrics.system.uptime)
          }
        },
        processes: processData,
        historical: includeHistory ? {
          dataPoints: historicalData.length,
          timeRange: historicalData.length > 0 ? {
            start: historicalData[0]?.timestamp,
            end: historicalData[historicalData.length - 1]?.timestamp
          } : null,
          data: historicalData
        } : null
      },
      realData: true,
      monitoringVersion: '1.0.0'
    };
    
    console.log(`✅ Real-time metrics: CPU ${systemMetrics.cpu.usage.toFixed(1)}%, Memory ${systemMetrics.memory.usagePercentage.toFixed(1)}%, Health: ${systemHealth.status}`);
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('❌ Real-time metrics failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Real-time metrics collection failed',
      details: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString(),
      realData: false
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { action, parameters } = await request.json();
    
    switch (action) {
      case 'get_health_summary':
        const metrics = await getSystemMetrics();
        const health = getSystemHealth(metrics);
        return NextResponse.json({
          success: true,
          data: {
            status: health.status,
            score: health.score,
            issues: health.issues,
            timestamp: metrics.timestamp
          }
        });
        
      case 'get_historical_trends':
        const limit = parameters?.limit || 100;
        const startTime = parameters?.startTime;
        const endTime = parameters?.endTime;
        const historical = getHistoricalMetrics(startTime, endTime, limit);
        
        return NextResponse.json({
          success: true,
          data: {
            dataPoints: historical.length,
            timeRange: historical.length > 0 ? {
              start: historical[0]?.timestamp,
              end: historical[historical.length - 1]?.timestamp
            } : null,
            metrics: historical
          }
        });
        
      default:
        return NextResponse.json({
          success: false,
          error: `Unknown action: ${action}`
        }, { status: 400 });
    }
    
  } catch (error) {
    console.error('❌ Real-time metrics action failed:', error);
    return NextResponse.json({
      success: false,
      error: 'Action failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
