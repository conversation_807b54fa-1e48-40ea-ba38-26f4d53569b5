/**
 * 🔗 NAVIGATION CONNECTIONS API
 * Detailed analysis of suggested navigation connections
 */

import { NextRequest, NextResponse } from 'next/server';
import { dynamicNavigationService } from '@/services/DynamicNavigationService';

export async function GET(request: NextRequest) {
  try {
    console.log('🔗 Analyzing navigation connections...');
    
    // Get the navigation analysis
    const analysis = await dynamicNavigationService.generateNavigationAnalysis();

    // Extract connection suggestions with details
    const connectionDetails = analysis.suggestedConnections.map(suggestion => {
      return {
        from: suggestion.from,
        to: suggestion.to,
        reason: suggestion.reason,
        type: 'cross-reference',
        priority: 'medium',
        description: `Connect ${suggestion.from} to ${suggestion.to}: ${suggestion.reason}`,
        implementation: `Add navigation link from ${suggestion.from} to ${suggestion.to}`,
        impact: 'Medium',
        category: 'Navigation',
        pages_affected: [suggestion.from, suggestion.to],
        estimated_effort: 'Low'
      };
    });
    
    // Group connections by type
    const connectionsByType = {
      'cross-hub-links': [],
      'breadcrumb-navigation': [],
      'related-pages': [],
      'quick-access': [],
      'contextual-navigation': [],
      'hub-integration': [],
      'other': []
    };
    
    connectionDetails.forEach(connection => {
      const type = connection.type || 'other';
      if (connectionsByType[type]) {
        connectionsByType[type].push(connection);
      } else {
        connectionsByType['other'].push(connection);
      }
    });
    
    // Priority analysis
    const priorityBreakdown = {
      high: connectionDetails.filter(c => c.priority === 'high').length,
      medium: connectionDetails.filter(c => c.priority === 'medium').length,
      low: connectionDetails.filter(c => c.priority === 'low').length
    };
    
    const response = {
      success: true,
      connections: {
        total: connectionDetails.length,
        breakdown: connectionsByType,
        priority: priorityBreakdown,
        details: connectionDetails,
        summary: {
          most_common_type: Object.entries(connectionsByType)
            .sort(([,a], [,b]) => b.length - a.length)[0]?.[0] || 'none',
          high_priority_count: priorityBreakdown.high,
          implementation_ready: connectionDetails.filter(c => 
            c.estimated_effort === 'Low' || c.estimated_effort === 'Medium'
          ).length
        }
      }
    };
    
    console.log('🔗 Connection Analysis Results:');
    console.log(`📊 Total connections: ${connectionDetails.length}`);
    console.log(`🔥 High priority: ${priorityBreakdown.high}`);
    console.log(`📋 By type:`, Object.entries(connectionsByType).map(([type, items]) => 
      `${type}: ${items.length}`
    ).join(', '));
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('❌ Connection analysis failed:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Connection analysis failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
