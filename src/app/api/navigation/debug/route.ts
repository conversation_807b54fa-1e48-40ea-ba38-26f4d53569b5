/**
 * 🔍 NAVIGATION DEBUG API
 * Detailed analysis of navigation categorization and missing pages
 */

import { NextRequest, NextResponse } from 'next/server';
import { dynamicNavigationService } from '@/services/DynamicNavigationService';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Starting detailed navigation debug analysis...');
    
    // Get all discovered pages
    const pages = await dynamicNavigationService.discoverAllPages();
    
    // Analyze categorization
    const categorization: Record<string, string[]> = {
      'core-platform': [],
      'intelligence-hub': [],
      'agent-hub': [],
      'creative-hub': [],
      'dashboard-hub': [],
      'uncategorized': []
    };
    
    const expectedPages = {
      'core-platform': ['/', '/login', '/register'],
      'intelligence-hub': ['/intelligence', '/intelligence-analytics', '/omniscient', '/models'],
      'agent-hub': ['/agents', '/agent-ecosystem', '/swarm', '/orchestration', '/autonomous-core-demo'],
      'creative-hub': ['/creative', '/canvas', '/ai-tools', '/gallery', '/marketplace', '/voice'],
      'dashboard-hub': ['/dashboard', '/monitoring', '/chat', '/community', '/tasks', '/profile']
    };
    
    // Categorize each page
    pages.forEach(page => {
      const category = page.suggestedCategory || 'uncategorized';
      if (categorization[category]) {
        categorization[category].push(page.route);
      } else {
        categorization['uncategorized'].push(page.route);
      }
    });
    
    // Find missing pages
    const missingPages: Record<string, string[]> = {};
    const foundPages: Record<string, string[]> = {};
    
    Object.entries(expectedPages).forEach(([category, expected]) => {
      const actual = categorization[category] || [];
      const missing = expected.filter(route => !actual.includes(route));
      const found = expected.filter(route => actual.includes(route));
      
      missingPages[category] = missing;
      foundPages[category] = found;
    });
    
    // Check if pages exist but are miscategorized
    const allRoutes = pages.map(p => p.route);
    const miscategorized: Record<string, Array<{route: string; expectedCategory: string; actualCategory: string}>> = {};
    
    Object.entries(expectedPages).forEach(([category, expected]) => {
      expected.forEach(route => {
        if (allRoutes.includes(route)) {
          const actualCategory = pages.find(p => p.route === route)?.suggestedCategory;
          if (actualCategory !== category) {
            if (!miscategorized[category]) miscategorized[category] = [];
            miscategorized[category].push({
              route,
              expectedCategory: category,
              actualCategory: actualCategory || 'uncategorized'
            });
          }
        }
      });
    });
    
    const response = {
      success: true,
      debug: {
        totalPages: pages.length,
        categorization,
        expectedPages,
        missingPages,
        foundPages,
        miscategorized,
        allRoutes: allRoutes.sort(),
        pageDetails: pages.map(p => ({
          route: p.route,
          suggestedCategory: p.suggestedCategory,
          name: p.name,
          description: p.description
        }))
      }
    };
    
    console.log('🔍 Debug Analysis Results:');
    console.log('📊 Total pages:', pages.length);
    console.log('📋 Categorization:', categorization);
    console.log('❌ Missing pages:', missingPages);
    console.log('🔄 Miscategorized:', miscategorized);
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('❌ Navigation debug failed:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Navigation debug failed',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
