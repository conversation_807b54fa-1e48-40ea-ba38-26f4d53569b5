import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// JWT Secret - In production, use environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'creAItive-observation-secret-key';
const JWT_EXPIRES_IN = '24h';

// Mock user database - same as observation route
const MOCK_USERS = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'Demo Observer',
    role: 'observer',
    permissions: ['observe_ai_decisions', 'view_agent_status', 'monitor_performance']
  },
  {
    id: '2', 
    email: '<EMAIL>',
    name: 'Admin Observer',
    role: 'admin_observer',
    permissions: ['observe_ai_decisions', 'view_agent_status', 'monitor_performance', 'export_observation_data', 'view_system_internals']
  },
  {
    id: '3',
    email: '<EMAIL>', 
    name: 'Developer Observer',
    role: 'developer_observer',
    permissions: ['observe_ai_decisions', 'view_agent_status', 'monitor_performance', 'export_observation_data', 'view_system_internals', 'debug_logs', 'advanced_analytics']
  }
];

export async function POST(request: NextRequest) {
  try {
    // Get current token from cookie or header
    const currentToken = request.cookies.get('auth-token')?.value || 
                        request.headers.get('authorization')?.replace('Bearer ', '');

    if (!currentToken) {
      return NextResponse.json(
        { success: false, error: 'No token provided for refresh' },
        { status: 401 }
      );
    }

    // Verify current token (allow expired tokens for refresh)
    let decoded: any;
    try {
      decoded = jwt.verify(currentToken, JWT_SECRET);
    } catch (error: any) {
      // If token is expired, try to decode without verification to get user info
      if (error.name === 'TokenExpiredError') {
        decoded = jwt.decode(currentToken);
      } else {
        return NextResponse.json(
          { success: false, error: 'Invalid token' },
          { status: 401 }
        );
      }
    }

    if (!decoded || !decoded.userId) {
      return NextResponse.json(
        { success: false, error: 'Invalid token payload' },
        { status: 401 }
      );
    }

    // Find user
    const user = MOCK_USERS.find(u => u.id === decoded.userId);
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 401 }
      );
    }

    // Generate new JWT token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };

    const newToken = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

    // Create response with new token
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        permissions: user.permissions
      },
      token: newToken,
      expiresIn: JWT_EXPIRES_IN,
      refreshed: true
    });

    // Set new secure HTTP-only cookie
    response.cookies.set('auth-token', newToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      path: '/'
    });

    return response;

  } catch (error) {
    console.error('Token refresh error:', error);
    return NextResponse.json(
      { success: false, error: 'Token refresh failed' },
      { status: 500 }
    );
  }
}
