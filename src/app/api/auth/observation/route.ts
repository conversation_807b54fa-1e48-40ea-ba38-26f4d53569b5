import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';

// JWT Secret - In production, use environment variable
const JWT_SECRET = process.env.JWT_SECRET || 'creAItive-observation-secret-key';
const JWT_EXPIRES_IN = '24h';

// Mock user database - In production, use real database
const MOCK_USERS = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'demo123', // In production, use bcrypt hashed passwords
    name: 'Demo Observer',
    role: 'observer',
    permissions: ['observe_ai_decisions', 'view_agent_status', 'monitor_performance']
  },
  {
    id: '2', 
    email: '<EMAIL>',
    password: 'admin123',
    name: 'Admin Observer',
    role: 'admin_observer',
    permissions: ['observe_ai_decisions', 'view_agent_status', 'monitor_performance', 'export_observation_data', 'view_system_internals']
  },
  {
    id: '3',
    email: '<EMAIL>', 
    password: 'dev123',
    name: 'Developer Observer',
    role: 'developer_observer',
    permissions: ['observe_ai_decisions', 'view_agent_status', 'monitor_performance', 'export_observation_data', 'view_system_internals', 'debug_logs', 'advanced_analytics']
  }
];

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { success: false, error: 'Email and password are required' },
        { status: 400 }
      );
    }

    // Find user in mock database
    const user = MOCK_USERS.find(u => u.email.toLowerCase() === email.toLowerCase());
    
    if (!user || user.password !== password) {
      return NextResponse.json(
        { success: false, error: 'Invalid credentials' },
        { status: 401 }
      );
    }

    // Generate JWT token
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };

    const token = jwt.sign(tokenPayload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });

    // Create response with secure cookie
    const response = NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        permissions: user.permissions
      },
      token,
      expiresIn: JWT_EXPIRES_IN
    });

    // Set secure HTTP-only cookie
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      path: '/'
    });

    return response;

  } catch (error) {
    console.error('Authentication error:', error);
    return NextResponse.json(
      { success: false, error: 'Authentication failed' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Verify current token
    const token = request.cookies.get('auth-token')?.value || 
                  request.headers.get('authorization')?.replace('Bearer ', '');

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const decoded = jwt.verify(token, JWT_SECRET) as any;
    
    // Find user
    const user = MOCK_USERS.find(u => u.id === decoded.userId);
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        permissions: user.permissions
      },
      tokenValid: true
    });

  } catch (error) {
    return NextResponse.json(
      { success: false, error: 'Invalid token' },
      { status: 401 }
    );
  }
}
