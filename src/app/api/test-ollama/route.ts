/**
 * 🧪 Test Ollama Integration
 * Simple endpoint to test real AI connectivity
 */

import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Testing Ollama integration...');
    
    const { searchParams } = new URL(request.url);
    const model = searchParams.get('model') || 'devstral:latest';
    const prompt = searchParams.get('prompt') || 'Hello! Please respond with just "AI is working" and nothing else.';
    
    // Test with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Timeout')), 15000); // 15 second timeout
    });
    
    const startTime = Date.now();
    
    console.log(`🤖 Testing ${model} with prompt: "${prompt}"`);
    
    const ollamaPromise = execAsync(`ollama run ${model} "${prompt}"`);
    
    const { stdout: response } = await Promise.race([ollamaPromise, timeoutPromise]) as any;
    
    const executionTime = Date.now() - startTime;
    
    // Clean response
    const cleanResponse = response
      .replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
    
    console.log(`✅ Ollama test successful in ${executionTime}ms`);
    
    return NextResponse.json({
      success: true,
      data: {
        model,
        prompt,
        response: cleanResponse,
        executionTime,
        timestamp: new Date().toISOString(),
        status: 'working'
      }
    });
    
  } catch (error) {
    console.error('❌ Ollama test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: {
        model: 'unknown',
        prompt: 'test failed',
        response: 'Ollama service unavailable',
        executionTime: 0,
        timestamp: new Date().toISOString(),
        status: 'error'
      }
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { model = 'devstral:latest', prompt = 'Test message' } = await request.json();
    
    console.log(`🧪 POST test with ${model}: "${prompt}"`);
    
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Timeout')), 15000);
    });
    
    const startTime = Date.now();
    const ollamaPromise = execAsync(`ollama run ${model} "${prompt}"`);
    
    const { stdout: response } = await Promise.race([ollamaPromise, timeoutPromise]) as any;
    const executionTime = Date.now() - startTime;
    
    const cleanResponse = response
      .replace(/[⠙⠹⠸⠼⠴⠦⠧⠇⠏⠋]/g, '')
      .replace(/\s+/g, ' ')
      .trim();
    
    return NextResponse.json({
      success: true,
      data: {
        model,
        prompt,
        response: cleanResponse,
        executionTime,
        timestamp: new Date().toISOString(),
        status: 'working'
      }
    });
    
  } catch (error) {
    console.error('❌ Ollama POST test failed:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
