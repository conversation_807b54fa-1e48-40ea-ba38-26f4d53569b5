'use client';

import React, { useState } from 'react';
import { Mail, Phone, MapPin, Send, MessageSquare, Users, Zap } from 'lucide-react';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    type: 'general'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log('Form submitted:', formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const contactTypes = [
    { value: 'general', label: 'General Inquiry' },
    { value: 'support', label: 'Technical Support' },
    { value: 'partnership', label: 'Partnership' },
    { value: 'press', label: 'Press & Media' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Get in
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-purple-400 ml-2">
              Touch
            </span>
          </h1>
          <p className="text-xl text-white/70 max-w-2xl mx-auto">
            Have questions about CreAItive? We'd love to hear from you. 
            Send us a message and we'll respond as soon as possible.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6">Send us a message</h2>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="block text-white/70 text-sm font-medium mb-2">
                    Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-space-700/50 border border-white/10 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-cosmic-500/50"
                    placeholder="Your name"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-white/70 text-sm font-medium mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-3 bg-space-700/50 border border-white/10 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-cosmic-500/50"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="type" className="block text-white/70 text-sm font-medium mb-2">
                  Inquiry Type
                </label>
                <select
                  id="type"
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  className="w-full px-4 py-3 bg-space-700/50 border border-white/10 rounded-lg text-white focus:outline-none focus:border-cosmic-500/50"
                >
                  {contactTypes.map((type) => (
                    <option key={type.value} value={type.value} className="bg-space-800">
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label htmlFor="subject" className="block text-white/70 text-sm font-medium mb-2">
                  Subject
                </label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  value={formData.subject}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-3 bg-space-700/50 border border-white/10 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-cosmic-500/50"
                  placeholder="What's this about?"
                />
              </div>
              
              <div>
                <label htmlFor="message" className="block text-white/70 text-sm font-medium mb-2">
                  Message
                </label>
                <textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  required
                  rows={6}
                  className="w-full px-4 py-3 bg-space-700/50 border border-white/10 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-cosmic-500/50 resize-none"
                  placeholder="Tell us more about your inquiry..."
                />
              </div>
              
              <button
                type="submit"
                className="w-full flex items-center justify-center gap-2 bg-cosmic-500 hover:bg-cosmic-600 text-white px-6 py-3 rounded-lg transition-all"
              >
                <Send className="w-4 h-4" />
                Send Message
              </button>
            </form>
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            {/* Contact Methods */}
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Other ways to reach us</h2>
              
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-cosmic-500/20 rounded-lg">
                    <Mail className="w-5 h-5 text-cosmic-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium mb-1">Email</h3>
                    <p className="text-white/60"><EMAIL></p>
                    <p className="text-white/40 text-sm">We'll respond within 24 hours</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-cosmic-500/20 rounded-lg">
                    <MessageSquare className="w-5 h-5 text-cosmic-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium mb-1">Live Chat</h3>
                    <p className="text-white/60">Available in the app</p>
                    <p className="text-white/40 text-sm">Monday - Friday, 9 AM - 6 PM PST</p>
                  </div>
                </div>
                
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-cosmic-500/20 rounded-lg">
                    <Users className="w-5 h-5 text-cosmic-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-medium mb-1">Community</h3>
                    <p className="text-white/60">Join our Discord</p>
                    <p className="text-white/40 text-sm">Connect with other users and get help</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Links */}
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <h2 className="text-2xl font-bold text-white mb-6">Quick Links</h2>
              
              <div className="space-y-4">
                <a
                  href="/community"
                  className="flex items-center gap-3 text-white/70 hover:text-cosmic-400 transition-colors"
                >
                  <Users className="w-4 h-4" />
                  Community Forum
                </a>
                <a
                  href="/explore"
                  className="flex items-center gap-3 text-white/70 hover:text-cosmic-400 transition-colors"
                >
                  <Zap className="w-4 h-4" />
                  Documentation
                </a>
                <a
                  href="/about"
                  className="flex items-center gap-3 text-white/70 hover:text-cosmic-400 transition-colors"
                >
                  <MapPin className="w-4 h-4" />
                  About Us
                </a>
              </div>
            </div>

            {/* Response Time */}
            <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-xl p-6 border border-white/10">
              <h3 className="text-lg font-semibold text-white mb-2">Response Time</h3>
              <p className="text-white/70 text-sm">
                We typically respond to all inquiries within 24 hours during business days. 
                For urgent technical issues, please use the live chat feature in the app.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
