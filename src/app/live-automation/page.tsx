'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export default function LiveAutomationPage() {
  return (
    <div className="min-h-screen interface-standard pt-20">
      {/* Breadcrumb Navigation */}
      <div className="px-6 py-4">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </Link>
      </div>

      {/* Hero Section */}
      <section className="py-20 px-6 relative overflow-hidden">
        <div className="max-w-6xl mx-auto text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-8 text-gradient-cosmic priority-element priority-critical text-adaptive">
            ⚡ Live Demo
          </h1>
          <p className="text-xl md:text-2xl mb-12 text-stardust-light priority-element priority-high text-adaptive">
            Real-time automation and live system demonstrations
          </p>
          <div className="flex items-center justify-center gap-2 mb-8">
            <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
            <span className="text-white font-medium">Live Systems Running</span>
          </div>
        </div>
        
        {/* Enhanced Background */}
        <div className="absolute inset-0 smart-glass performance-layer gpu-accelerated">
          <div className="absolute top-20 left-20 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-20 right-20 w-96 h-96 bg-cosmic-500/20 rounded-full blur-3xl"></div>
        </div>
      </section>

      {/* Live Demo Systems */}
      <section className="py-20 px-6 interface-complex">
        <div className="max-w-6xl mx-auto">
          <div className="grid-adaptive">
            {/* Real-time Processing */}
            <div className="card-smart performance-layer p-8">
              <div className="progressive-trigger priority-element priority-high" aria-expanded="false">
                <div className="flex items-center gap-3 mb-3">
                  <div className="w-3 h-3 bg-white rounded-full animate-pulse"></div>
                  <h3 className="text-xl font-semibold text-adaptive">Real-time Processing</h3>
                </div>
              </div>
              <div className="progressive-container progressive-collapsed">
                <p className="text-stardust-light mb-4 text-adaptive priority-element priority-medium">
                  Live data processing and analysis in action
                </p>
                <div className="space-y-2 text-sm priority-element priority-low">
                  <div className="flex justify-between">
                    <span>Processing Rate:</span>
                    <span className="text-white">1,247 ops/sec</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Latency:</span>
                    <span className="text-emerald-400">0.3ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Throughput:</span>
                    <span className="text-cosmic-400">99.9%</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Automation Workflows */}
            <div className="card-smart performance-layer p-8">
              <div className="progressive-trigger priority-element priority-high" aria-expanded="false">
                <span className="text-3xl mb-2">🔄</span>
                <h3 className="text-xl font-semibold mb-3 text-adaptive">Automation Workflows</h3>
              </div>
              <div className="progressive-container progressive-collapsed">
                <p className="text-stardust-light mb-4 text-adaptive priority-element priority-medium">
                  Autonomous task execution and workflow management
                </p>
                <ul className="space-y-2 text-sm priority-element priority-low">
                  <li>• Task scheduling and execution</li>
                  <li>• Error handling and recovery</li>
                  <li>• Resource optimization</li>
                  <li>• Performance monitoring</li>
                </ul>
              </div>
            </div>

            {/* Interactive Demos */}
            <div className="card-smart performance-layer p-8">
              <div className="progressive-trigger priority-element priority-high" aria-expanded="false">
                <span className="text-3xl mb-2">🎮</span>
                <h3 className="text-xl font-semibold mb-3 text-adaptive">Interactive Demos</h3>
              </div>
              <div className="progressive-container progressive-collapsed">
                <p className="text-stardust-light mb-4 text-adaptive priority-element priority-medium">
                  Try the systems yourself with live interactions
                </p>
                <div className="space-y-3">
                  <button className="w-full neo-button neo-button-primary text-left">
                    🎨 Try Canvas Tools
                  </button>
                  <button className="w-full neo-button neo-button-outline text-left">
                    🤖 Chat with AI Agents
                  </button>
                  <button className="w-full neo-button neo-button-ghost text-left">
                    ⚡ Test Automation
                  </button>
                </div>
              </div>
            </div>

            {/* System Monitoring */}
            <div className="card-smart performance-layer p-8">
              <div className="progressive-trigger priority-element priority-high" aria-expanded="false">
                <span className="text-3xl mb-2">📊</span>
                <h3 className="text-xl font-semibold mb-3 text-adaptive">System Monitoring</h3>
              </div>
              <div className="progressive-container progressive-collapsed">
                <p className="text-stardust-light mb-4 text-adaptive priority-element priority-medium">
                  Live system health and performance metrics
                </p>
                <div className="space-y-2 text-sm priority-element priority-low">
                  <div className="flex justify-between">
                    <span>CPU Usage:</span>
                    <span className="text-emerald-400">12%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Memory:</span>
                    <span className="text-cosmic-400">2.1GB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Network I/O:</span>
                    <span className="text-nova-400">45 MB/s</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Live Events */}
            <div className="card-smart performance-layer p-8">
              <div className="progressive-trigger priority-element priority-high" aria-expanded="false">
                <span className="text-3xl mb-2">📡</span>
                <h3 className="text-xl font-semibold mb-3 text-adaptive">Live Events</h3>
              </div>
              <div className="progressive-container progressive-collapsed">
                <p className="text-stardust-light mb-4 text-adaptive priority-element priority-medium">
                  Real-time system events and activities
                </p>
                <div className="space-y-2 text-xs priority-element priority-low max-h-32 overflow-y-auto">
                  <div className="flex justify-between p-2 bg-space-800/30 rounded">
                    <span>Agent task completed</span>
                    <span className="text-emerald-400">12:34:56</span>
                  </div>
                  <div className="flex justify-between p-2 bg-space-800/30 rounded">
                    <span>System optimization applied</span>
                    <span className="text-cosmic-400">12:34:45</span>
                  </div>
                  <div className="flex justify-between p-2 bg-space-800/30 rounded">
                    <span>User interaction processed</span>
                    <span className="text-nova-400">12:34:32</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Demo Status */}
            <div className="card-smart performance-layer p-8 text-center">
              <span className="text-4xl mb-4 block">🔴</span>
              <h3 className="text-xl font-semibold mb-4 text-adaptive priority-element priority-high">
                Demo Status
              </h3>
              <div className="text-2xl font-bold text-white mb-2">LIVE</div>
              <p className="text-stardust-light text-adaptive priority-element priority-medium">
                All systems operational and demonstrating
              </p>
            </div>

            {/* Live Feed */}
            <div className="card-smart performance-layer p-8 col-span-full">
              <div className="progressive-trigger priority-element priority-high" aria-expanded="false">
                <span className="text-3xl mb-2">📺</span>
                <h3 className="text-xl font-semibold mb-3 text-adaptive">Live System Feed</h3>
              </div>
              <div className="progressive-container progressive-collapsed">
                <div className="bg-space-900 p-6 rounded-lg border border-cosmic-500/20">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                    <span className="text-white font-mono text-sm">LIVE SYSTEM OUTPUT</span>
                  </div>
                  <div className="font-mono text-xs space-y-1 max-h-40 overflow-y-auto">
                    <div className="text-emerald-400">[12:34:56] ✅ Agent UIAgent: Task completed successfully</div>
                    <div className="text-cosmic-400">[12:34:54] 🔄 System: Auto-optimization cycle initiated</div>
                    <div className="text-nova-400">[12:34:52] 📊 Monitor: Performance metrics updated</div>
                    <div className="text-white">[12:34:50] ⚡ Demo: User interaction processed</div>
                    <div className="text-quantum-400">[12:34:48] 🧠 AI: Intelligent decision made</div>
                    <div className="text-emerald-400">[12:34:46] ✅ Test: All systems operational</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
} 