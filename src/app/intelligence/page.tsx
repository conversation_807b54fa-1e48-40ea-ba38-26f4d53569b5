"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Brain, Activity, BarChart3, Zap, Eye, Settings, ArrowRight, TrendingUp } from 'lucide-react';

export default function IntelligencePage() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [liveMetrics, setLiveMetrics] = useState({
    aiDecisions: 1247,
    systemHealth: 98,
    activeModels: 5,
    intelligenceLevel: 94
  });

  // Simulate live data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setLiveMetrics(prev => ({
        aiDecisions: prev.aiDecisions + Math.floor(Math.random() * 3),
        systemHealth: 95 + Math.floor(Math.random() * 5),
        activeModels: 4 + Math.floor(Math.random() * 3),
        intelligenceLevel: 90 + Math.floor(Math.random() * 8)
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const intelligenceFeatures = [
    {
      title: "AI Analytics Dashboard",
      href: "/intelligence-analytics",
      icon: BarChart3,
      description: "Real-time AI performance metrics and insights",
      status: "Live",
      color: "cosmic"
    },
    {
      title: "Autonomous Core Demo",
      href: "/autonomous-core-demo", 
      icon: Brain,
      description: "Watch autonomous AI systems in action",
      status: "Active",
      color: "nova"
    },
    {
      title: "Autonomous Observation",
      href: "/autonomous-observation",
      icon: Eye,
      description: "Monitor autonomous system behavior",
      status: "Monitoring",
      color: "neural"
    },
    {
      title: "AI Models Management",
      href: "/models",
      icon: Settings,
      description: "Configure and manage AI models",
      status: "Ready",
      color: "aura"
    },
    {
      title: "Live AI Dashboard",
      href: "/live-ai-dashboard",
      icon: Activity,
      description: "Real-time AI system monitoring",
      status: "Streaming",
      color: "quantum"
    },
    {
      title: "Omniscient AI",
      href: "/omniscient",
      icon: Zap,
      description: "Advanced AI capabilities and insights",
      status: "Enhanced",
      color: "cosmic"
    }
  ];

  return (
    <div className="min-h-screen bg-space-900 pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-cosmic-500/20 via-space-900 to-nova-500/20" />
        <div className="absolute inset-0 bg-gradient-to-tr from-neural-500/10 via-transparent to-aura-500/10" />
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-2xl flex items-center justify-center">
                <Brain className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-cosmic-400 via-nova-400 to-neural-400 bg-clip-text text-transparent">
                Intelligence Hub
              </h1>
            </div>
            
            <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
              {isLoggedIn 
                ? "Control and monitor your AI intelligence systems with real-time analytics and advanced controls."
                : "Experience the power of autonomous AI intelligence. Watch live AI decisions and system analytics."
              }
            </p>

            {!isLoggedIn && (
              <div className="bg-cosmic-500/10 border border-cosmic-500/30 rounded-lg p-4 max-w-2xl mx-auto mb-8">
                <p className="text-cosmic-300 text-sm">
                  🔍 <strong>Observer Mode:</strong> You're viewing live AI intelligence data. 
                  <Link href="/login" className="text-cosmic-400 hover:text-cosmic-300 underline ml-1">
                    Login for full control access
                  </Link>
                </p>
              </div>
            )}
          </div>

          {/* Live Metrics Dashboard */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-space-800/50 backdrop-blur-sm border border-cosmic-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-cosmic-400 mb-2">{liveMetrics.aiDecisions.toLocaleString()}</div>
              <div className="text-white/60 text-sm">AI Decisions Made</div>
              <div className="w-2 h-2 bg-nova-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-nova-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-nova-400 mb-2">{liveMetrics.systemHealth}%</div>
              <div className="text-white/60 text-sm">System Health</div>
              <div className="w-2 h-2 bg-nova-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-neural-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-neural-400 mb-2">{liveMetrics.activeModels}</div>
              <div className="text-white/60 text-sm">Active AI Models</div>
              <div className="w-2 h-2 bg-nova-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-aura-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-aura-400 mb-2">{liveMetrics.intelligenceLevel}%</div>
              <div className="text-white/60 text-sm">Intelligence Level</div>
              <div className="w-2 h-2 bg-nova-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Intelligence Features Grid */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">AI Intelligence Systems</h2>
            <p className="text-white/70 max-w-2xl mx-auto">
              Access powerful AI analytics, autonomous systems, and intelligence monitoring tools
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {intelligenceFeatures.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Link
                  key={feature.href}
                  href={feature.href}
                  className="group bg-space-800/30 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:border-cosmic-500/50 transition-all duration-300 hover:scale-105"
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-br from-${feature.color}-500 to-${feature.color}-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white group-hover:text-cosmic-300 transition-colors">
                        {feature.title}
                      </h3>
                      <div className="flex items-center gap-2 mt-1">
                        <div className={`w-2 h-2 bg-nova-400 rounded-full animate-pulse`}></div>
                        <span className="text-xs text-nova-400">{feature.status}</span>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-white/70 text-sm mb-4 group-hover:text-white/90 transition-colors">
                    {feature.description}
                  </p>
                  
                  <div className="flex items-center text-cosmic-400 text-sm group-hover:text-cosmic-300 transition-colors">
                    <span>Access System</span>
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16">
        <div className="container mx-auto px-6 text-center">
          <div className="bg-gradient-to-r from-cosmic-500/10 to-nova-500/10 border border-cosmic-500/30 rounded-2xl p-8 max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-white mb-4">
              {isLoggedIn ? "Manage Your AI Intelligence" : "Ready to Control AI Intelligence?"}
            </h2>
            <p className="text-white/70 mb-6">
              {isLoggedIn 
                ? "Access advanced AI controls, configure models, and monitor system performance."
                : "Login to access full AI intelligence controls and advanced analytics."
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {isLoggedIn ? (
                <>
                  <Link href="/dashboard" className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <BarChart3 className="w-5 h-5" />
                    Open Dashboard Hub
                  </Link>
                  <Link href="/agents" className="bg-space-700 hover:bg-space-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Manage AI Agents
                  </Link>
                </>
              ) : (
                <>
                  <Link href="/login" className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Brain className="w-5 h-5" />
                    Login for Full Access
                  </Link>
                  <Link href="/agents" className="bg-space-700 hover:bg-space-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Eye className="w-5 h-5" />
                    View Agent Hub
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
