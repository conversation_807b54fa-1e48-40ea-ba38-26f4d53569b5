"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Palette, Image, Zap, Mic, ShoppingCart, Sparkles, ArrowRight, Plus, Eye } from 'lucide-react';

export default function CreativePage() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [liveStats, setLiveStats] = useState({
    creationsToday: 342,
    activeCreators: 89,
    aiToolsUsed: 156,
    marketplaceItems: 2847
  });

  // Simulate live data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setLiveStats(prev => ({
        creationsToday: prev.creationsToday + Math.floor(Math.random() * 2),
        activeCreators: 85 + Math.floor(Math.random() * 10),
        aiToolsUsed: prev.aiToolsUsed + Math.floor(Math.random() * 3),
        marketplaceItems: prev.marketplaceItems + Math.floor(Math.random() * 2)
      }));
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  const creativeTools = [
    {
      title: "AI Canvas",
      href: "/canvas",
      icon: Palette,
      description: "Primary creative workspace with AI-powered design tools",
      status: "Ready",
      color: "nova",
      isPrimary: true
    },
    {
      title: "Gallery & Assets",
      href: "/gallery",
      icon: Image,
      description: "Browse AI-generated content and manage your assets",
      status: "Live",
      color: "cosmic"
    },
    {
      title: "AI Creative Tools",
      href: "/ai-tools",
      icon: Zap,
      description: "Advanced AI utilities for content creation",
      status: "Enhanced",
      color: "neural"
    },
    {
      title: "Voice Interface",
      href: "/voice",
      icon: Mic,
      description: "Voice-powered creative commands and generation",
      status: "Active",
      color: "aura"
    },
    {
      title: "Marketplace",
      href: "/marketplace",
      icon: ShoppingCart,
      description: "Trade and discover creative assets globally",
      status: "Trading",
      color: "quantum"
    },
    {
      title: "AI Models",
      href: "/models",
      icon: Sparkles,
      description: "Creative AI models and generation engines",
      status: "Optimized",
      color: "cosmic"
    }
  ];

  const recentCreations = [
    { title: "Abstract AI Art", type: "Image", creator: "AI Canvas" },
    { title: "Voice Synthesis", type: "Audio", creator: "Voice AI" },
    { title: "Dynamic Logo", type: "Design", creator: "AI Tools" },
    { title: "3D Visualization", type: "3D", creator: "AI Canvas" }
  ];

  return (
    <div className="min-h-screen bg-space-900 pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-nova-500/20 via-space-900 to-aura-500/20" />
        <div className="absolute inset-0 bg-gradient-to-tr from-cosmic-500/10 via-transparent to-neural-500/10" />
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-nova-500 to-aura-500 rounded-2xl flex items-center justify-center">
                <Palette className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-nova-400 via-aura-400 to-cosmic-400 bg-clip-text text-transparent">
                Creative Hub
              </h1>
            </div>
            
            <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
              {isLoggedIn 
                ? "Access your complete creative suite with AI-powered tools, personal projects, and marketplace."
                : "Explore AI-powered creativity tools and see what's possible with artificial intelligence."
              }
            </p>

            {!isLoggedIn && (
              <div className="bg-nova-500/10 border border-nova-500/30 rounded-lg p-4 max-w-2xl mx-auto mb-8">
                <p className="text-nova-300 text-sm">
                  🎨 <strong>Preview Mode:</strong> You're viewing creative capabilities. 
                  <Link href="/login" className="text-nova-400 hover:text-nova-300 underline ml-1">
                    Login for full creative access
                  </Link>
                </p>
              </div>
            )}
          </div>

          {/* Live Creative Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-space-800/50 backdrop-blur-sm border border-nova-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-nova-400 mb-2">{liveStats.creationsToday.toLocaleString()}</div>
              <div className="text-white/60 text-sm">Creations Today</div>
              <div className="w-2 h-2 bg-green-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-aura-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-aura-400 mb-2">{liveStats.activeCreators}</div>
              <div className="text-white/60 text-sm">Active Creators</div>
              <div className="w-2 h-2 bg-green-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-cosmic-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-cosmic-400 mb-2">{liveStats.aiToolsUsed.toLocaleString()}</div>
              <div className="text-white/60 text-sm">AI Tools Used</div>
              <div className="w-2 h-2 bg-green-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-neural-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-neural-400 mb-2">{liveStats.marketplaceItems.toLocaleString()}</div>
              <div className="text-white/60 text-sm">Marketplace Items</div>
              <div className="w-2 h-2 bg-green-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Creative Tools Grid */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Creative Tools & Workspace</h2>
            <p className="text-white/70 max-w-2xl mx-auto">
              Powerful AI-enhanced creative tools for unlimited artistic expression
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {creativeTools.map((tool, index) => {
              const Icon = tool.icon;
              return (
                <Link
                  key={tool.href}
                  href={tool.href}
                  className={`group bg-space-800/30 backdrop-blur-sm border rounded-xl p-6 transition-all duration-300 hover:scale-105 ${
                    tool.isPrimary 
                      ? 'border-nova-500/50 bg-nova-500/5' 
                      : 'border-white/10 hover:border-nova-500/50'
                  }`}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-br from-${tool.color}-500 to-${tool.color}-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white group-hover:text-nova-300 transition-colors">
                        {tool.title}
                        {tool.isPrimary && <span className="text-xs bg-nova-500/20 text-nova-400 px-2 py-1 rounded-full ml-2">Primary</span>}
                      </h3>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-xs text-green-400">{tool.status}</span>
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-white/70 text-sm mb-4 group-hover:text-white/90 transition-colors">
                    {tool.description}
                  </p>
                  
                  <div className="flex items-center text-nova-400 text-sm group-hover:text-nova-300 transition-colors">
                    <span>{isLoggedIn ? 'Open Tool' : 'Preview Tool'}</span>
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Recent Creations Preview */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Recent Creations</h2>
            <p className="text-white/70">Live feed of AI-generated content from the platform</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {recentCreations.map((creation, index) => (
              <div key={index} className="bg-space-800/30 backdrop-blur-sm border border-white/10 rounded-xl p-4 hover:border-nova-500/50 transition-colors">
                <div className="aspect-square bg-gradient-to-br from-nova-500/20 to-aura-500/20 rounded-lg mb-3 flex items-center justify-center">
                  <Sparkles className="w-8 h-8 text-white/60" />
                </div>
                <h3 className="text-white font-medium text-sm mb-1">{creation.title}</h3>
                <div className="flex items-center justify-between text-xs text-white/60">
                  <span>{creation.type}</span>
                  <span>{creation.creator}</span>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center">
            <Link href="/gallery" className="inline-flex items-center gap-2 text-nova-400 hover:text-nova-300 transition-colors">
              <span>View Full Gallery</span>
              <ArrowRight className="w-4 h-4" />
            </Link>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16">
        <div className="container mx-auto px-6 text-center">
          <div className="bg-gradient-to-r from-nova-500/10 to-aura-500/10 border border-nova-500/30 rounded-2xl p-8 max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-white mb-4">
              {isLoggedIn ? "Start Creating Now" : "Ready to Create with AI?"}
            </h2>
            <p className="text-white/70 mb-6">
              {isLoggedIn 
                ? "Access your full creative suite and start building amazing content with AI assistance."
                : "Login to access the complete creative workspace and save your projects."
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {isLoggedIn ? (
                <>
                  <Link href="/canvas" className="bg-nova-500 hover:bg-nova-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Palette className="w-5 h-5" />
                    Open AI Canvas
                  </Link>
                  <Link href="/gallery" className="bg-space-700 hover:bg-space-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Image className="w-5 h-5" />
                    Browse Gallery
                  </Link>
                </>
              ) : (
                <>
                  <Link href="/login" className="bg-nova-500 hover:bg-nova-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Plus className="w-5 h-5" />
                    Login to Create
                  </Link>
                  <Link href="/canvas" className="bg-space-700 hover:bg-space-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Eye className="w-5 h-5" />
                    Try Canvas Demo
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
