"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";
import Container from "@/shared/components/Container";
import PostComposer from "@/features/community/components/PostComposer";
import PostCard from "@/features/community/components/PostCard";
import ActivityFeed from "@/features/community/components/ActivityFeed";
import TabGroup from "@/shared/components/TabGroup";
import { useCommunityPosts } from "@/hooks/useCommunityPosts";
import { useCommunityActivity } from "@/hooks/useCommunityActivity";

export default function CommunityPage() {
  const sessionData = useSession();
  const { data: session, status } = sessionData || { data: null, status: "loading" };
  const router = useRouter();
  const [activeTab, setActiveTab] = useState("feed");

  // Use custom hooks for fetching posts and activity
  const { 
    posts, 
    isLoading: postsLoading, 
    hasMore: hasMorePosts, 
    fetchPosts, 
    loadMore: loadMorePosts,
    refresh: refreshPosts
  } = useCommunityPosts();

  const {
    activity,
    isLoading: activityLoading,
    hasMore: hasMoreActivity,
    fetchActivity,
    loadMore: loadMoreActivity,
    refresh: refreshActivity
  } = useCommunityActivity();

  // Check authentication and load initial data
  useEffect(() => {
    if (status === "unauthenticated") {
      // Redirect to login page if not authenticated
      router.push("/login?redirect=/community");
    } else if (status === "authenticated") {
      // Fetch data based on active tab
      if (activeTab === "feed") {
        fetchPosts();
      } else if (activeTab === "activity") {
        fetchActivity();
      }
    }
  }, [status, router, activeTab, fetchPosts, fetchActivity]);

  // Handle tab change
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    
    // Load data for the selected tab if not already loaded
    if (tab === "feed") {
      refreshPosts();
    } else if (tab === "activity") {
      refreshActivity();
    }
  };

  // Determine which loading state to use based on active tab
  const isLoading = activeTab === "feed" ? postsLoading : activityLoading;

  // Tabs for the community page
  const tabs = [
    { id: "feed", label: "Feed" },
    { id: "activity", label: "Activity" },
  ];

  if (status === "loading") {
    return (
      <Container>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-pulse text-cosmic-600">Loading...</div>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <div className="max-w-4xl mx-auto py-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <Link
            href="/dashboard"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Dashboard Hub</span>
          </Link>
        </div>

        <h1 className="text-3xl font-orbitron font-bold text-gradient-cosmic mb-8">
          Community
        </h1>
        
        <TabGroup 
          tabs={tabs} 
          activeTab={activeTab} 
          onChange={handleTabChange} 
        />
        
        <div className="mt-6">
          {activeTab === "feed" && (
            <div className="space-y-6">
              {session?.user && (
                <PostComposer 
                  user={session.user} 
                  onPostCreated={refreshPosts} 
                />
              )}
              
              {posts.length > 0 ? (
                <div className="space-y-4">
                  {posts.map(post => (
                    <PostCard key={post._id} post={post} />
                  ))}
                  
                  {hasMorePosts && (
                    <div className="flex justify-center mt-6">
                      <button
                        onClick={loadMorePosts}
                        disabled={postsLoading}
                        className="px-4 py-2 bg-glass rounded-full text-stardust-300 hover:bg-glass-light transition duration-300 disabled:opacity-50"
                      >
                        {postsLoading ? "Loading..." : "Load More"}
                      </button>
                    </div>
                  )}
                </div>
              ) : postsLoading ? (
                <div className="text-center py-10 bg-glass rounded-xl">
                  <p className="text-stardust-300">Loading posts...</p>
                </div>
              ) : (
                <div className="text-center py-10 bg-glass rounded-xl">
                  <p className="text-stardust-300">
                    No posts yet. Be the first to share something!
                  </p>
                </div>
              )}
            </div>
          )}
          
          {activeTab === "activity" && (
            <ActivityFeed 
              activity={activity} 
              isLoading={activityLoading} 
              hasMore={hasMoreActivity} 
              onLoadMore={loadMoreActivity} 
            />
          )}
        </div>
      </div>
    </Container>
  );
} 