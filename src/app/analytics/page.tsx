/**
 * Analytics Page
 * Advanced analytics dashboard with data visualization
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import Container from '@/shared/components/Container';
import AdvancedAnalyticsDashboard from '@/components/analytics/AdvancedAnalyticsDashboard';

export default function AnalyticsPage() {
  return (
    <Container>
      <div className="min-h-screen py-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <Link
            href="/dashboard"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Dashboard Hub</span>
          </Link>
        </div>

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gradient-cosmic mb-2">
            Analytics Dashboard
          </h1>
          <p className="text-stardust-400">
            Advanced analytics and data visualization for comprehensive insights
          </p>
        </div>

        <AdvancedAnalyticsDashboard
          timeRange="7d"
          autoRefresh={true}
        />
      </div>
    </Container>
  );
}