import { Metada<PERSON> } from 'next';
import Link from 'next/link';
import Container from '@/shared/components/Container';
import UnifiedIntelligenceAnalytics from '@/components/AutonomousCore/UnifiedIntelligenceAnalytics';
import { ArrowLeft } from 'lucide-react';

export const metadata: Metadata = {
  title: 'Intelligence Analytics | CreAItive',
  description: 'Unified intelligence analytics dashboard showing complete frontend logic tree mapping with TIER 3 autonomous system integration',
  keywords: ['intelligence analytics', 'frontend logic tree', 'autonomous systems', 'TIER 3', 'agent coordination'],
};

/**
 * 🧠 INTELLIGENCE ANALYTICS PAGE
 * 
 * Complete frontend logic tree mapping dashboard that provides:
 * - TIER 3 autonomous system status and integration
 * - 47-page frontend architecture visualization 
 * - Cross-section coordination monitoring
 * - Agent ecosystem performance analytics
 * - Real-time intelligence capabilities assessment
 * - Performance metrics and autonomous operations tracking
 * 
 * This page serves as the central intelligence command center for observing
 * and analyzing the entire system's autonomous capabilities and frontend architecture.
 */
export default function IntelligenceAnalyticsPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-space-900">
      <Container className="py-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <Link
            href="/intelligence"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Intelligence Hub</span>
          </Link>
        </div>

        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="h-12 w-12 bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-lg flex items-center justify-center">
              <span className="text-2xl">🧠</span>
            </div>
            <div>
              <h1 className="text-4xl font-bold bg-gradient-to-r from-cosmic-400 via-nova-400 to-neural-400 bg-clip-text text-transparent">
                Intelligence Analytics
              </h1>
              <p className="text-stardust-300 text-lg">
                Complete frontend logic tree mapping with TIER 3 autonomous integration
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 p-6 bg-glass border border-cosmic-500/20 rounded-xl">
            <div className="text-center">
              <div className="text-2xl font-bold text-cosmic-400">TIER 3</div>
              <div className="text-sm text-stardust-400">Autonomous System</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-nova-400">67+ Pages</div>
              <div className="text-sm text-stardust-400">Frontend Architecture</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-neural-400">28 Agents</div>
              <div className="text-sm text-stardust-400">Autonomous Ecosystem</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-nova-400">100%</div>
              <div className="text-sm text-stardust-400">System Integration</div>
            </div>
          </div>
        </div>

        {/* Main Intelligence Analytics Dashboard */}
        <UnifiedIntelligenceAnalytics />
      </Container>
    </div>
  );
} 