"use client";

import { useEffect, useState, useRef, ChangeEvent } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuthContext } from "@/contexts/AuthContext";
import { AnimationWrapper } from "@/shared/components";
import Button from "@/shared/components/Button/Button";
import Card from "@/shared/components/Card/Card";
import Container from "@/shared/components/Container";
import Heading from "@/shared/components/Heading";
import Input from "@/shared/components/Input";
import Select from "@/shared/components/Select";
import { PerformanceMonitor, LazyWrapper } from "@/shared/components/Performance";
import { ProfileData, getProfile, updateProfile, uploadAvatar } from "@/services/profileService";
import Image from "next/image";
import {
  FiEdit3, FiSettings, FiUsers, FiHeart, FiBookmark, FiGrid, FiList,
  FiShare2, FiMapPin, FiLink, FiCalendar, FiEye, FiMessageCircle,
  FiUserPlus, FiUserCheck, FiBell, FiMail, FiCamera, FiSliders,
  FiStar, FiTrendingUp, FiActivity, FiFilter, FiSearch, FiSave, FiX, FiAward, FiCoffee, FiArrowLeft
} from "react-icons/fi";

const DEFAULT_AVATAR = "/images/default-avatar.png";

// Mock user activity data
const ACTIVITY_DATA = [
  {
    id: 1,
    type: 'like',
    content: 'Liked "Digital Landscape Art"',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    user: { name: 'Alex Chen', avatar: '/api/placeholder/32x32?id=user1' }
  },
  {
    id: 2,
    type: 'follow',
    content: 'Started following Sarah Design',
    timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
    user: { name: 'Sarah Design', avatar: '/api/placeholder/32x32?id=user2' }
  },
  {
    id: 3,
    type: 'project',
    content: 'Published "Abstract Motion Graphics"',
    timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    user: { name: 'Current User', avatar: '/api/placeholder/32x32?id=user3' }
  },
  {
    id: 4,
    type: 'comment',
    content: 'Commented on "AI Art Revolution"',
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    user: { name: 'Mike Studio', avatar: '/api/placeholder/32x32?id=user4' }
  }
];

// Mock portfolio projects
const PORTFOLIO_PROJECTS = Array.from({ length: 12 }, (_, i) => ({
  id: i + 1,
  title: `Creative Project ${i + 1}`,
  description: `An innovative project showcasing ${['digital art', 'photography', '3D modeling', 'illustration'][i % 4]} skills.`,
  image: `/api/placeholder/300x200?id=project-${i + 1}`,
  likes: Math.floor(Math.random() * 500) + 50,
  views: Math.floor(Math.random() * 2000) + 100,
  comments: Math.floor(Math.random() * 50) + 5,
  category: ['Digital Art', 'Photography', '3D Art', 'Illustration'][i % 4],
  featured: Math.random() > 0.7,
  createdAt: new Date(Date.now() - Math.random() * 10000000000)
}));

// Profile customization themes
const PROFILE_THEMES = [
  { id: 'cosmic', name: 'Cosmic', primary: '#6E7AFF', accent: '#FF78F7', background: 'from-cosmic-500/20 to-nova-500/20' },
  { id: 'neural', name: 'Neural', primary: '#A1F5FF', accent: '#F1E0FF', background: 'from-neural-500/20 to-quantum-500/20' },
  { id: 'quantum', name: 'Quantum', primary: '#F1E0FF', accent: '#FFC366', background: 'from-quantum-500/20 to-aura-500/20' },
  { id: 'aura', name: 'Aura', primary: '#FFC366', accent: '#6E7AFF', background: 'from-aura-500/20 to-cosmic-500/20' },
  { id: 'nova', name: 'Nova', primary: '#FF78F7', accent: '#A1F5FF', background: 'from-nova-500/20 to-neural-500/20' }
];

export default function ProfilePage() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading } = useAuthContext();
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'posts' | 'projects' | 'likes' | 'activity' | 'settings'>('projects');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isFollowing, setIsFollowing] = useState(false);
  const [selectedTheme, setSelectedTheme] = useState('cosmic');
  const [showNotificationSettings, setShowNotificationSettings] = useState(false);
  const [notificationSettings, setNotificationSettings] = useState({
    email: true,
    push: true,
    likes: true,
    comments: true,
    follows: true,
    mentions: true
  });
  const [searchQuery, setSearchQuery] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [isAuthenticated, isLoading, router]);

  // Fetch profile data when component mounts
  useEffect(() => {
    if (isAuthenticated) {
      fetchProfile();
    }
  }, [isAuthenticated]);

  const fetchProfile = async () => {
    try {
      setError(null);
      const profileData = await getProfile();
      setProfile(profileData);
    } catch (error) {
      console.error("Error fetching profile:", error);
      setError("Failed to load profile. Please try again.");
    }
  };

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setProfile((prev) => {
      if (!prev) return prev;
      return { ...prev, [name]: value };
    });
  };

  const handleInputValueChange = (name: string) => (value: string) => {
    setProfile((prev) => {
      if (!prev) return prev;
      return { ...prev, [name]: value };
    });
  };

  const handleSocialInputValueChange = (name: string) => (value: string) => {
    setProfile((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        socialLinks: {
          ...prev.socialLinks,
          [name]: value,
        },
      };
    });
  };

  const handleSocialLinkChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfile((prev) => {
      if (!prev) return prev;
      return {
        ...prev,
        socialLinks: {
          ...prev.socialLinks,
          [name]: value,
        },
      };
    });
  };

  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = async (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      setIsUploading(true);
      setError(null);
      const avatarUrl = await uploadAvatar(file);
      setProfile((prev) => {
        if (!prev) return prev;
        return { ...prev, avatarUrl };
      });
    } catch (error) {
      console.error("Error uploading avatar:", error);
      setError("Failed to upload avatar. Please try again.");
    } finally {
      setIsUploading(false);
    }
  };

  const handleSaveProfile = async () => {
    if (!profile) return;

    try {
      setIsSaving(true);
      setError(null);
      const updatedProfile = await updateProfile(profile);
      setProfile(updatedProfile);
      setIsEditing(false);
    } catch (error) {
      console.error("Error saving profile:", error);
      setError("Failed to save profile. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const handleFollow = () => {
    setIsFollowing(!isFollowing);
  };

  const handleThemeChange = (themeId: string) => {
    setSelectedTheme(themeId);
  };

  const handleNotificationChange = (setting: string, enabled: boolean) => {
    setNotificationSettings(prev => ({
      ...prev,
      [setting]: enabled
    }));
  };

  // Filter portfolio projects based on search
  const filteredProjects = PORTFOLIO_PROJECTS.filter(project =>
    project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Filter activity based on search
  const filteredActivity = ACTIVITY_DATA.filter(activity =>
    activity.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
    activity.user.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-adaptive">
        <div className="relative">
          <div className="w-16 h-16 border-4 border-cosmic-500/20 rounded-full"></div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-t-cosmic-500 rounded-full animate-spin"></div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  const currentTheme = PROFILE_THEMES.find(theme => theme.id === selectedTheme) || PROFILE_THEMES[0];

  return (
    // TEMPORARILY DISABLED: PerformanceMonitor causing infinite re-renders
    // <PerformanceMonitor 
    //   componentName="Profile" 
    //   threshold={100}
    //   enableRenderTracking={true}
    //   showDebugInfo={false}
    // >
      <div className="min-h-screen bg-adaptive">
        {/* Enhanced Background with Theme Support */}
        <div className="absolute inset-0 z-0 opacity-20">
          <div className={`absolute inset-0 bg-gradient-to-br ${currentTheme.background}`} />
          <div className="absolute inset-0 bg-gradient-to-tr from-space-900/50 via-transparent to-space-900/50" />
          {/* Floating orbs for visual depth */}
          <div className="absolute top-20 left-20 w-32 h-32 bg-cosmic-400/10 rounded-full blur-2xl animate-float" />
          <div className="absolute bottom-20 right-20 w-48 h-48 bg-nova-400/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
        </div>

        <Container className="relative z-10 py-8">
          {/* Breadcrumb Navigation */}
          <div className="mb-6">
            <Link
              href="/dashboard"
              className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
            >
              <FiArrowLeft className="w-4 h-4" />
              <span>Back to Dashboard Hub</span>
            </Link>
          </div>

          {/* Enhanced Cover/Banner Section */}
          <AnimationWrapper>
            <Card variant="elevated" className="overflow-hidden mb-8">
              {/* Cover Image with Theme */}
              <div className={`h-48 md:h-64 bg-gradient-to-r ${currentTheme.background} relative`}>
                <div className="absolute inset-0 bg-black/20" />
                {isEditing && (
                  <div className="absolute top-4 right-4">
                    <Button variant="ghost" size="sm" className="bg-black/50 text-white hover:bg-black/70">
                      <FiCamera className="w-4 h-4 mr-2" />
                      Change Cover
                    </Button>
                  </div>
                )}
              </div>

              {/* Profile Header */}
              <div className="px-6 py-6 -mt-16 relative">
                <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-6">
                  {/* Avatar and Basic Info */}
                  <div className="flex flex-col md:flex-row md:items-end gap-6">
                    {/* Enhanced Avatar */}
                    <div className="relative">
                      <div
                        className={`w-32 h-32 rounded-full border-4 border-white shadow-xl bg-space-700 overflow-hidden ${
                          isEditing ? "cursor-pointer hover:opacity-80" : ""
                        }`}
                        onClick={isEditing ? handleAvatarClick : undefined}
                        style={{ borderColor: currentTheme.primary }}
                      >
                        {isUploading ? (
                          <div className="h-full w-full flex items-center justify-center">
                            <div className="w-8 h-8 border-2 border-t-transparent border-cosmic-500 rounded-full animate-spin"></div>
                          </div>
                        ) : (
                          <Image
                            src={profile?.avatarUrl || DEFAULT_AVATAR}
                            alt="Profile avatar"
                            width={128}
                            height={128}
                            className="h-full w-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src = DEFAULT_AVATAR;
                            }}
                          />
                        )}
                      </div>
                      {isEditing && (
                        <>
                          <input
                            ref={fileInputRef}
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={handleFileChange}
                          />
                          <div className="absolute bottom-2 right-2 bg-cosmic-500 rounded-full p-2 shadow-lg">
                            <FiCamera className="w-4 h-4 text-white" />
                          </div>
                        </>
                      )}
                      {/* Online Status Indicator */}
                      <div className="absolute bottom-4 right-4 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>

                    {/* User Info */}
                    <div className="text-center md:text-left">
                      <Heading level={1} size="2xl" className="text-stardust-200 mb-2">
                        {profile?.name || user?.name || 'User Name'}
                      </Heading>
                      <p className="text-stardust-400 mb-2">{profile?.email || user?.email}</p>
                      {profile?.bio && (
                        <p className="text-stardust-300 max-w-md">{profile.bio}</p>
                      )}
                      
                      {/* Enhanced Stats */}
                      <div className="flex flex-wrap gap-6 mt-4 text-sm">
                        <div className="text-center">
                          <div className="font-semibold text-stardust-200">1.2k</div>
                          <div className="text-stardust-500">Followers</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-stardust-200">489</div>
                          <div className="text-stardust-500">Following</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-stardust-200">24</div>
                          <div className="text-stardust-500">Projects</div>
                        </div>
                        <div className="text-center">
                          <div className="font-semibold text-stardust-200">15.3k</div>
                          <div className="text-stardust-500">Likes</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-wrap gap-3">
                    {!isEditing ? (
                      <>
                        <Button variant="primary" onClick={handleEditToggle}>
                          <FiEdit3 className="w-4 h-4 mr-2" />
                          Edit Profile
                        </Button>
                        <Button variant="outline" onClick={() => setActiveTab('settings')}>
                          <FiSettings className="w-4 h-4 mr-2" />
                          Settings
                        </Button>
                        <Button variant="outline">
                          <FiShare2 className="w-4 h-4 mr-2" />
                          Share
                        </Button>
                      </>
                    ) : (
                      <>
                        <Button
                          variant="ghost"
                          onClick={() => {
                            setIsEditing(false);
                            fetchProfile();
                          }}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="primary"
                          onClick={handleSaveProfile}
                          isLoading={isSaving}
                        >
                          Save Changes
                        </Button>
                      </>
                    )}
                  </div>
                </div>

                {/* Error Display */}
                {error && (
                  <div className="mt-4 bg-red-500/20 border border-red-500/50 text-red-300 px-4 py-3 rounded-lg">
                    {error}
                  </div>
                )}
              </div>
            </Card>
          </AnimationWrapper>

          {/* Enhanced Navigation Tabs */}
          <AnimationWrapper>
            <Card variant="glass" className="mb-8">
              <div className="p-1">
                <div className="flex flex-wrap gap-1">
                  {[
                    { id: 'projects', label: 'Projects', icon: FiGrid, count: PORTFOLIO_PROJECTS.length },
                    { id: 'posts', label: 'Posts', icon: FiList, count: 12 },
                    { id: 'likes', label: 'Likes', icon: FiHeart, count: 89 },
                    { id: 'activity', label: 'Activity', icon: FiActivity, count: ACTIVITY_DATA.length },
                    { id: 'settings', label: 'Settings', icon: FiSettings }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`flex items-center gap-2 px-4 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'bg-cosmic-500 text-white shadow-cosmic/50'
                          : 'text-stardust-400 hover:text-stardust-200 hover:bg-space-700/50'
                      }`}
                    >
                      <tab.icon className="w-4 h-4" />
                      {tab.label}
                      {tab.count !== undefined && (
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          activeTab === tab.id ? 'bg-white/20' : 'bg-space-600'
                        }`}>
                          {tab.count}
                        </span>
                      )}
                    </button>
                  ))}
                </div>
              </div>
            </Card>
          </AnimationWrapper>

          {/* Enhanced Content Area */}
          <AnimationWrapper>
            <div className="space-y-6">
              {/* Projects Tab */}
              {activeTab === 'projects' && (
                <>
                  {/* Search and Filter Bar */}
                  <Card variant="glass" className="mb-6">
                    <div className="p-4">
                      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
                        <div className="flex-grow max-w-md">
                          <Input
                            variant="glass"
                            placeholder="Search projects..."
                            value={searchQuery}
                            onChange={setSearchQuery}
                            leftIcon={<FiSearch className="text-stardust-400" />}
                            fullWidth
                          />
                        </div>
                        <div className="flex gap-2">
                          <Select
                            variant="glass"
                            value="all"
                            onChange={() => {}}
                            options={[
                              { value: 'all', label: 'All Categories' },
                              { value: 'digital-art', label: 'Digital Art' },
                              { value: 'photography', label: 'Photography' },
                              { value: '3d-art', label: '3D Art' },
                              { value: 'illustration', label: 'Illustration' }
                            ]}
                          />
                          <div className="flex rounded-lg bg-space-800 p-1">
                            <button
                              onClick={() => setViewMode('grid')}
                              className={`p-2 rounded-md transition-colors ${
                                viewMode === 'grid' 
                                  ? 'bg-cosmic-500 text-white' 
                                  : 'text-stardust-400 hover:text-stardust-200'
                              }`}
                            >
                              <FiGrid className="w-4 h-4" />
                            </button>
                            <button
                              onClick={() => setViewMode('list')}
                              className={`p-2 rounded-md transition-colors ${
                                viewMode === 'list' 
                                  ? 'bg-cosmic-500 text-white' 
                                  : 'text-stardust-400 hover:text-stardust-200'
                              }`}
                            >
                              <FiList className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Projects Grid */}
                  <div className={
                    viewMode === 'grid' 
                      ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                      : "space-y-4"
                  }>
                    {filteredProjects.map((project, index) => (
                      <Card variant="elevated" hover="glow" className="overflow-hidden h-full group cursor-pointer">
                        {viewMode === 'grid' ? (
                          <>
                            {/* Grid View */}
                            <div className="relative h-48 overflow-hidden">
                              <Image
                                src={project.image}
                                alt={project.title}
                                fill
                                className="object-cover transition-transform duration-300 group-hover:scale-105"
                              />
                              {project.featured && (
                                <div className="absolute top-3 left-3">
                                  <span className="px-2 py-1 text-xs rounded-full bg-cosmic-500 text-white font-medium">
                                    Featured
                                  </span>
                                </div>
                              )}
                              <div className="absolute top-3 right-3">
                                <span className="px-2 py-1 text-xs rounded-full bg-space-900/80 backdrop-blur-sm text-stardust-300 border border-space-700">
                                  {project.category}
                                </span>
                              </div>
                            </div>
                            <div className="p-5">
                              <h3 className="text-lg font-semibold text-stardust-200 mb-2 group-hover:text-cosmic-300 transition-colors">
                                {project.title}
                              </h3>
                              <p className="text-stardust-400 text-sm mb-4 line-clamp-2">
                                {project.description}
                              </p>
                              <div className="flex items-center justify-between text-xs text-stardust-500">
                                <div className="flex items-center gap-4">
                                  <span className="flex items-center gap-1">
                                    <FiHeart className="w-3 h-3" />
                                    {project.likes}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <FiEye className="w-3 h-3" />
                                    {project.views}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <FiMessageCircle className="w-3 h-3" />
                                    {project.comments}
                                  </span>
                                </div>
                                <span>{project.createdAt.toLocaleDateString()}</span>
                              </div>
                            </div>
                          </>
                        ) : (
                          <>
                            {/* List View */}
                            <div className="flex gap-4 p-5">
                              <div className="relative w-24 h-24 flex-shrink-0 overflow-hidden rounded-lg">
                                <Image
                                  src={project.image}
                                  alt={project.title}
                                  fill
                                  className="object-cover"
                                />
                              </div>
                              <div className="flex-grow">
                                <div className="flex items-start justify-between mb-2">
                                  <h3 className="text-lg font-semibold text-stardust-200 group-hover:text-cosmic-300 transition-colors">
                                    {project.title}
                                  </h3>
                                  {project.featured && (
                                    <span className="px-2 py-1 text-xs rounded-full bg-cosmic-500 text-white font-medium">
                                      Featured
                                    </span>
                                  )}
                                </div>
                                <p className="text-stardust-400 text-sm mb-3 line-clamp-2">
                                  {project.description}
                                </p>
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center gap-4 text-xs text-stardust-500">
                                    <span className="flex items-center gap-1">
                                      <FiHeart className="w-3 h-3" />
                                      {project.likes}
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <FiEye className="w-3 h-3" />
                                      {project.views}
                                    </span>
                                    <span className="flex items-center gap-1">
                                      <FiMessageCircle className="w-3 h-3" />
                                      {project.comments}
                                    </span>
                                  </div>
                                  <span className="text-xs text-stardust-500">{project.category}</span>
                                </div>
                              </div>
                            </div>
                          </>
                        )}
                      </Card>
                    ))}
                  </div>
                </>
              )}

              {/* Activity Tab */}
              {activeTab === 'activity' && (
                <Card variant="elevated" className="p-6">
                  <div className="flex items-center justify-between mb-6">
                    <Heading level={2} size="lg" className="text-stardust-200">
                      Recent Activity
                    </Heading>
                    <Button variant="ghost" size="sm">
                      <FiFilter className="w-4 h-4 mr-2" />
                      Filter
                    </Button>
                  </div>

                  <div className="space-y-4">
                    {filteredActivity.map((activity, index) => (
                      <Card variant="elevated" className="p-4 rounded-lg bg-space-800/50 hover:bg-space-800 transition-colors">
                        <div className="flex items-start gap-4">
                          <Image
                            src={activity.user.avatar}
                            alt={activity.user.name}
                            width={40}
                            height={40}
                            className="rounded-full"
                          />
                          <div className="flex-grow">
                            <p className="text-stardust-300 mb-1">
                              <span className="font-medium">{activity.user.name}</span>{' '}
                              {activity.content}
                            </p>
                            <p className="text-stardust-500 text-sm">
                              {activity.timestamp.toLocaleDateString()} at{' '}
                              {activity.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                            </p>
                          </div>
                          <div className={`p-2 rounded-full ${
                            activity.type === 'like' ? 'bg-red-500/20 text-red-400' :
                            activity.type === 'follow' ? 'bg-blue-500/20 text-blue-400' :
                            activity.type === 'project' ? 'bg-green-500/20 text-green-400' :
                            'bg-purple-500/20 text-purple-400'
                          }`}>
                            {activity.type === 'like' && <FiHeart className="w-4 h-4" />}
                            {activity.type === 'follow' && <FiUsers className="w-4 h-4" />}
                            {activity.type === 'project' && <FiStar className="w-4 h-4" />}
                            {activity.type === 'comment' && <FiMessageCircle className="w-4 h-4" />}
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </Card>
              )}

              {/* Settings Tab */}
              {activeTab === 'settings' && (
                <Card variant="elevated" className="p-6">
                  <Heading level={3} size="lg" className="text-stardust-200 mb-4 flex items-center">
                    <FiSliders className="w-5 h-5 mr-2" />
                    Profile Customization
                  </Heading>
                  
                  <div className="space-y-4">
                    <div>
                      <label className="block text-stardust-300 mb-2 font-medium">Profile Theme</label>
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-3">
                        {PROFILE_THEMES.map((theme) => (
                          <button
                            key={theme.id}
                            onClick={() => handleThemeChange(theme.id)}
                            className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                              selectedTheme === theme.id 
                                ? 'border-cosmic-500 bg-cosmic-500/10' 
                                : 'border-space-600 hover:border-space-500'
                            }`}
                          >
                            <div 
                              className="w-full h-8 rounded mb-2"
                              style={{ background: `linear-gradient(135deg, ${theme.primary}, ${theme.accent})` }}
                            />
                            <span className="text-stardust-300 text-sm font-medium">{theme.name}</span>
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              )}

              {/* Other tabs (posts, likes) can be implemented similarly */}
              {(activeTab === 'posts' || activeTab === 'likes') && (
                <Card variant="elevated" className="p-12 text-center">
                  <div className="text-6xl mb-4">🚧</div>
                  <Heading level={3} size="lg" className="text-stardust-300 mb-2">
                    Coming Soon
                  </Heading>
                  <p className="text-stardust-400">
                    The {activeTab} section is being built with amazing features.
                  </p>
                </Card>
              )}
            </div>
          </AnimationWrapper>
        </Container>
      </div>
    // </PerformanceMonitor>
  );
} 