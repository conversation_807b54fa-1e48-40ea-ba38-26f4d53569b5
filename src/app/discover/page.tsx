"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Container from "@/shared/components/Container";
import Heading from "@/shared/components/Heading";
import Button from "@/shared/components/Button/Button";
import { FiCompass, FiShuffle, FiHeart, FiBookmark, FiRefreshCw, FiArrowLeft } from "react-icons/fi";
import TrendingGrid from "@/features/discovery/components/TrendingGrid";
import CategoryGrid from "@/features/discovery/components/CategoryGrid";
import RecommendationGrid from "@/features/discovery/components/RecommendationGrid";
import { trendingService } from "@/features/discovery/services/trendingService";

export default function DiscoverPage() {
  const [refreshKey, setRefreshKey] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [discoveryMode, setDiscoveryMode] = useState<'random' | 'curated' | 'trending'>('curated');

  // Mock user data (would come from auth in real app)
  const mockUserId = "user123";
  const userInterests = ["digital art", "illustration", "animation"];

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    setTimeout(() => {
      setRefreshKey(prev => prev + 1);
      setIsRefreshing(false);
    }, 1000);
  };

  const discoveryModes = [
    {
      id: 'curated',
      name: 'Curated',
      description: 'Handpicked content based on your interests',
      icon: FiHeart,
    },
    {
      id: 'random',
      name: 'Random',
      description: 'Surprise me with something unexpected',
      icon: FiShuffle,
    },
    {
      id: 'trending',
      name: 'Trending',
      description: "What's hot right now",
      icon: FiCompass,
    },
  ];

  return (
    <Container className="py-16">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <FiArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </Link>
      </div>

      {/* Page Header */}
      <div className="text-center mb-12">
        <Heading level={1} gradient size="3xl" align="center" className="mb-4">
          <FiCompass className="inline mr-2" size={48} />
          Discover
        </Heading>
        <p className="text-stardust-400 max-w-2xl mx-auto mb-8">
          Explore the unknown and find your next creative inspiration. Let our discovery engine 
          guide you to amazing content you never knew existed.
        </p>

        {/* Discovery Mode Selection */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
          <div className="flex rounded-lg overflow-hidden border border-space-700">
            {discoveryModes.map(mode => (
              <button
                key={mode.id}
                className={`px-6 py-3 text-sm flex items-center gap-2 transition-all duration-300 ${
                  discoveryMode === mode.id 
                    ? 'bg-cosmic-600 text-white shadow-cosmic' 
                    : 'bg-space-800 text-stardust-400 hover:bg-space-700'
                }`}
                onClick={() => setDiscoveryMode(mode.id as any)}
                title={mode.description}
              >
                <mode.icon size={16} />
                {mode.name}
              </button>
            ))}
          </div>

          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center gap-2"
          >
            <FiRefreshCw className={isRefreshing ? 'animate-spin' : ''} size={16} />
            Refresh
          </Button>
        </div>

        <p className="text-stardust-500 text-sm">
          {discoveryModes.find(m => m.id === discoveryMode)?.description}
        </p>
      </div>

      {/* Discovery Content based on mode */}
      <div key={refreshKey}>
        {discoveryMode === 'curated' && (
          <>
            {/* Personalized Recommendations */}
            <div className="mb-20">
              <RecommendationGrid
                userId={mockUserId}
                title="Recommended For You"
                subtitle="Content curated based on your interests and activity"
                interests={userInterests}
                limit={8}
                variant="personalized"
              />
            </div>

            {/* Similar to Your Interests */}
            <div className="mb-20">
              <RecommendationGrid
                userId={mockUserId}
                title="Similar to Your Interests"
                subtitle="More content like what you love"
                interests={userInterests}
                limit={6}
                variant="personalized"
              />
            </div>

            {/* Explore New Categories */}
            <div className="mb-20">
              <CategoryGrid 
                title="Explore New Territories" 
                subtitle="Branch out and discover new creative disciplines"
                limit={8}
              />
            </div>
          </>
        )}

        {discoveryMode === 'random' && (
          <>
            {/* Random Discovery */}
            <div className="mb-20">
              <div className="text-center mb-8">
                <Heading level={2} size="2xl" className="text-stardust-200 mb-2">
                  <FiShuffle className="inline mr-2" />
                  Random Discovery
                </Heading>
                <p className="text-stardust-400">
                  A completely random selection from across the platform
                </p>
              </div>
              <TrendingGrid 
                title=""
                subtitle=""
                limit={12}
                showViewAll={false}
              />
            </div>

            {/* Random Categories */}
            <div className="mb-20">
              <CategoryGrid 
                title="Random Categories" 
                subtitle="Explore something completely different"
                limit={6}
              />
            </div>
          </>
        )}

        {discoveryMode === 'trending' && (
          <>
            {/* What's Hot */}
            <div className="mb-20">
              <TrendingGrid 
                title="What's Hot Right Now" 
                subtitle="The most popular content across all categories"
                limit={12}
              />
            </div>

            {/* Trending Categories */}
            <div className="mb-20">
              <CategoryGrid 
                title="Trending Categories" 
                subtitle="The most active creative disciplines"
                featuredOnly={true}
                limit={8}
              />
            </div>
          </>
        )}
      </div>

      {/* Daily Discovery Challenge */}
      <div className="mb-20">
        <div className="neo-card p-8 text-center bg-gradient-to-br from-neural-900/20 via-quantum-900/20 to-aura-900/20">
          <FiBookmark size={48} className="mx-auto text-neural-400 mb-4" />
          <Heading level={2} size="xl" className="text-stardust-200 mb-3">
            Daily Discovery Challenge
          </Heading>
          <p className="text-stardust-400 max-w-2xl mx-auto mb-6">
            Today's challenge: Explore content from at least 3 different categories you've never visited before. 
            Expand your creative horizons and discover new inspiration!
          </p>
          <div className="flex flex-wrap justify-center gap-3">
            <span className="px-3 py-1 bg-neural-600/20 text-neural-300 rounded-full text-sm">
              Architecture
            </span>
            <span className="px-3 py-1 bg-quantum-600/20 text-quantum-300 rounded-full text-sm">
              Generative Art
            </span>
            <span className="px-3 py-1 bg-aura-600/20 text-aura-300 rounded-full text-sm">
              Mixed Reality
            </span>
          </div>
        </div>
      </div>

      {/* Discovery Stats */}
      <div className="mb-20">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="neo-card p-6 text-center">
            <div className="text-3xl font-bold text-cosmic-400 mb-2">1,247</div>
            <div className="text-stardust-300">Items Discovered</div>
            <div className="text-stardust-500 text-sm">This month</div>
          </div>
          <div className="neo-card p-6 text-center">
            <div className="text-3xl font-bold text-nova-400 mb-2">23</div>
            <div className="text-stardust-300">New Artists Found</div>
            <div className="text-stardust-500 text-sm">This week</div>
          </div>
          <div className="neo-card p-6 text-center">
            <div className="text-3xl font-bold text-neural-400 mb-2">8</div>
            <div className="text-stardust-300">Categories Explored</div>
            <div className="text-stardust-500 text-sm">Personal best</div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="text-center py-16 bg-gradient-to-br from-quantum-900/20 via-neural-900/20 to-aura-900/20 rounded-2xl">
        <Heading level={2} size="2xl" className="text-stardust-200 mb-4">
          Keep Exploring
        </Heading>
        <p className="text-stardust-400 max-w-xl mx-auto mb-8">
          The creative universe is infinite. There's always something new to discover.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Button variant="primary" size="lg" onClick={handleRefresh}>
            Discover More
          </Button>
          <Link href="/explore">
            <Button variant="outline" size="lg">
              Back to Explore
            </Button>
          </Link>
        </div>
      </div>
    </Container>
  );
} 