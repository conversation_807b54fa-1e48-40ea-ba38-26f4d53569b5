'use client';

import React, { useState, useCallback } from 'react';
import { Upload, File, Image, Video, Music, X, CheckCircle, AlertCircle } from 'lucide-react';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  status: 'uploading' | 'completed' | 'error';
  progress: number;
}

export default function UploadPage() {
  const [files, setFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const droppedFiles = Array.from(e.dataTransfer.files);
    handleFiles(droppedFiles);
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const selectedFiles = Array.from(e.target.files);
      handleFiles(selectedFiles);
    }
  };

  const handleFiles = (fileList: File[]) => {
    const newFiles: UploadedFile[] = fileList.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      status: 'uploading',
      progress: 0
    }));

    setFiles(prev => [...prev, ...newFiles]);

    // Simulate upload progress
    newFiles.forEach(file => {
      simulateUpload(file.id);
    });
  };

  const simulateUpload = (fileId: string) => {
    const interval = setInterval(() => {
      setFiles(prev => prev.map(file => {
        if (file.id === fileId) {
          const newProgress = Math.min(file.progress + Math.random() * 20, 100);
          const status = newProgress === 100 ? 'completed' : 'uploading';
          return { ...file, progress: newProgress, status };
        }
        return file;
      }));
    }, 500);

    setTimeout(() => {
      clearInterval(interval);
      setFiles(prev => prev.map(file => 
        file.id === fileId ? { ...file, progress: 100, status: 'completed' } : file
      ));
    }, 3000);
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type: string) => {
    if (type.startsWith('image/')) return Image;
    if (type.startsWith('video/')) return Video;
    if (type.startsWith('audio/')) return Music;
    return File;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Upload
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-purple-400 ml-2">
              Files
            </span>
          </h1>
          <p className="text-xl text-white/70 max-w-2xl mx-auto">
            Upload your files to process with our AI agents. Supports images, videos, audio, and documents.
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          {/* Upload Area */}
          <div
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            className={`relative border-2 border-dashed rounded-2xl p-12 text-center transition-all ${
              isDragOver
                ? 'border-cosmic-500 bg-cosmic-500/10'
                : 'border-white/20 hover:border-white/40'
            }`}
          >
            <div className="max-w-md mx-auto">
              <Upload className="w-16 h-16 text-cosmic-400 mx-auto mb-6" />
              
              <h3 className="text-2xl font-bold text-white mb-4">
                Drop files here or click to upload
              </h3>
              
              <p className="text-white/60 mb-6">
                Support for images, videos, audio files, and documents up to 100MB each
              </p>
              
              <input
                type="file"
                multiple
                onChange={handleFileInput}
                className="hidden"
                id="file-upload"
                accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
              />
              
              <label
                htmlFor="file-upload"
                className="inline-flex items-center gap-2 bg-cosmic-500 hover:bg-cosmic-600 text-white px-6 py-3 rounded-lg cursor-pointer transition-all"
              >
                <Upload className="w-4 h-4" />
                Choose Files
              </label>
            </div>
          </div>

          {/* File List */}
          {files.length > 0 && (
            <div className="mt-8">
              <h3 className="text-xl font-bold text-white mb-6">Uploaded Files</h3>
              
              <div className="space-y-4">
                {files.map((file) => {
                  const FileIcon = getFileIcon(file.type);
                  
                  return (
                    <div
                      key={file.id}
                      className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-4"
                    >
                      <div className="flex items-center gap-4">
                        <div className="p-2 bg-cosmic-500/20 rounded-lg">
                          <FileIcon className="w-5 h-5 text-cosmic-400" />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="text-white font-medium truncate">{file.name}</h4>
                            <div className="flex items-center gap-2">
                              {file.status === 'completed' && (
                                <CheckCircle className="w-4 h-4 text-green-400" />
                              )}
                              {file.status === 'error' && (
                                <AlertCircle className="w-4 h-4 text-red-400" />
                              )}
                              <button
                                onClick={() => removeFile(file.id)}
                                className="text-white/40 hover:text-white transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                          
                          <div className="flex items-center justify-between text-sm text-white/60">
                            <span>{formatFileSize(file.size)}</span>
                            <span>
                              {file.status === 'uploading' && `${Math.round(file.progress)}%`}
                              {file.status === 'completed' && 'Completed'}
                              {file.status === 'error' && 'Error'}
                            </span>
                          </div>
                          
                          {file.status === 'uploading' && (
                            <div className="mt-2">
                              <div className="w-full bg-space-700 rounded-full h-2">
                                <div
                                  className="bg-cosmic-500 h-2 rounded-full transition-all duration-300"
                                  style={{ width: `${file.progress}%` }}
                                />
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Supported Formats */}
          <div className="mt-12 grid md:grid-cols-4 gap-6">
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center">
              <Image className="w-8 h-8 text-cosmic-400 mx-auto mb-3" />
              <h4 className="text-white font-medium mb-2">Images</h4>
              <p className="text-white/60 text-sm">JPG, PNG, GIF, WebP</p>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center">
              <Video className="w-8 h-8 text-cosmic-400 mx-auto mb-3" />
              <h4 className="text-white font-medium mb-2">Videos</h4>
              <p className="text-white/60 text-sm">MP4, MOV, AVI, WebM</p>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center">
              <Music className="w-8 h-8 text-cosmic-400 mx-auto mb-3" />
              <h4 className="text-white font-medium mb-2">Audio</h4>
              <p className="text-white/60 text-sm">MP3, WAV, FLAC, AAC</p>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center">
              <File className="w-8 h-8 text-cosmic-400 mx-auto mb-3" />
              <h4 className="text-white font-medium mb-2">Documents</h4>
              <p className="text-white/60 text-sm">PDF, DOC, DOCX, TXT</p>
            </div>
          </div>

          {/* Processing Options */}
          {files.some(f => f.status === 'completed') && (
            <div className="mt-12 bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 border border-white/10">
              <h3 className="text-2xl font-bold text-white mb-4 text-center">
                Ready to Process
              </h3>
              <p className="text-white/70 text-center mb-6">
                Your files are uploaded and ready to be processed by our AI agents.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-6 py-3 rounded-lg transition-all">
                  Process with AI
                </button>
                <button className="bg-space-700 hover:bg-space-600 text-white px-6 py-3 rounded-lg transition-all">
                  Save to Gallery
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
