'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { io, Socket } from 'socket.io-client';

interface LiveAIDecision {
  id: string;
  agentId: string;
  model: string;
  response: string;
  confidence: number;
  timestamp: string;
  executionTime: number;
  reasoning: string[];
}

interface SystemMetrics {
  totalAgents: number;
  activeDecisions: number;
  systemHealth: number;
  averageResponseTime: number;
  modelDistribution: Record<string, number>;
}

export default function LiveAIDashboard() {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [liveDecisions, setLiveDecisions] = useState<LiveAIDecision[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [connectionStatus, setConnectionStatus] = useState('Disconnecting...');
  const [requestingDecision, setRequestingDecision] = useState(false);
  const decisionsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initialize Socket.IO connection
    const newSocket = io('http://localhost:3000', {
      transports: ['websocket', 'polling']
    });

    newSocket.on('connect', () => {
      console.log('🔌 Connected to real-time AI stream');
      setIsConnected(true);
      setConnectionStatus('Connected to Live AI Stream');
      
      // Subscribe to AI decision updates
      newSocket.emit('subscribe', ['decisions', 'metrics', 'all']);
      
      // Request initial data
      newSocket.emit('request_metrics');
      newSocket.emit('request_recent_decisions', 10);
    });

    newSocket.on('disconnect', () => {
      console.log('🔌 Disconnected from AI stream');
      setIsConnected(false);
      setConnectionStatus('Disconnected');
    });

    // Listen for real-time AI decisions
    newSocket.on('real_time_update', (message: any) => {
      console.log('📡 Real-time update:', message);
      
      if (message.type === 'decision') {
        const decision: LiveAIDecision = {
          id: message.data.id,
          agentId: message.data.agentId,
          model: message.data.model,
          response: message.data.response,
          confidence: message.data.confidence,
          timestamp: message.data.timestamp,
          executionTime: message.data.executionTime,
          reasoning: message.data.reasoning || []
        };
        
        setLiveDecisions(prev => [decision, ...prev.slice(0, 19)]); // Keep last 20
        
        // Auto-scroll to latest decision
        setTimeout(() => {
          if (decisionsRef.current) {
            decisionsRef.current.scrollTop = 0;
          }
        }, 100);
      }
      
      if (message.type === 'metrics') {
        setSystemMetrics(message.data);
      }
    });

    // Listen for initial data
    newSocket.on('initial_data', (data: any) => {
      console.log('📊 Initial data received:', data);
      if (data.metrics) {
        setSystemMetrics(data.metrics);
      }
      if (data.recentDecisions) {
        setLiveDecisions(data.recentDecisions);
      }
    });

    // Listen for decision responses
    newSocket.on('agent_decision_response', (response: any) => {
      console.log('🤖 Agent decision response:', response);
      setRequestingDecision(false);
      
      if (response.success) {
        // Decision will come through real_time_update
        setConnectionStatus('✅ Real AI Decision Completed!');
      } else {
        setConnectionStatus(`❌ Decision Failed: ${response.error}`);
      }
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, []);

  const requestRealAIDecision = () => {
    if (!socket || !isConnected) {
      alert('Not connected to AI stream');
      return;
    }

    const agentId = 'DevAgentIntelligenceEnhanced';
    const prompt = `Quick system status check. Respond with current time and a brief system assessment.`;
    
    setRequestingDecision(true);
    setConnectionStatus('🤖 Requesting Real AI Decision...');
    
    socket.emit('request_agent_decision', {
      agentId,
      prompt,
      context: { priority: 'high', source: 'live_dashboard' }
    });
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getHealthColor = (health: number) => {
    if (health >= 80) return 'text-nova-400';
    if (health >= 60) return 'text-quantum-400';
    return 'text-red-400';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <Link
            href="/intelligence"
            className="inline-flex items-center gap-2 text-cosmic-300 hover:text-cosmic-200 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Intelligence Hub</span>
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            🤖 Live AI Dashboard
          </h1>
          <p className="text-xl text-cosmic-200">
            Real-time autonomous AI decision streaming
          </p>
          <div className={`text-lg mt-2 ${isConnected ? 'text-nova-400' : 'text-red-400'}`}>
            {connectionStatus}
          </div>
        </div>

        {/* System Metrics */}
        {systemMetrics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">Total Agents</h3>
              <div className="text-3xl font-bold text-cosmic-400">{systemMetrics.totalAgents}</div>
            </div>
            
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">Active Decisions</h3>
              <div className="text-3xl font-bold text-neural-400">{systemMetrics.activeDecisions}</div>
            </div>
            
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">System Health</h3>
              <div className={`text-3xl font-bold ${getHealthColor(systemMetrics.systemHealth)}`}>
                {systemMetrics.systemHealth.toFixed(1)}%
              </div>
            </div>
            
            <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">Avg Response</h3>
              <div className="text-3xl font-bold text-quantum-400">
                {(systemMetrics.averageResponseTime / 1000).toFixed(1)}s
              </div>
            </div>
          </div>
        )}

        {/* Controls */}
        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6 mb-8">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">Real-Time AI Controls</h2>
            <button
              onClick={requestRealAIDecision}
              disabled={!isConnected || requestingDecision}
              className="bg-neural-600 hover:bg-neural-700 disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
            >
              {requestingDecision ? '🤖 AI Processing...' : '🔥 Request Real AI Decision'}
            </button>
          </div>
          <p className="text-cosmic-200 mt-2">
            Click to request a real AI decision from DevAgentIntelligenceEnhanced using actual Ollama models
          </p>
        </div>

        {/* Live Decisions Stream */}
        <div className="bg-white/10 backdrop-blur-lg rounded-xl p-6">
          <h2 className="text-2xl font-bold text-white mb-6">🔴 Live AI Decisions Stream</h2>
          
          <div 
            ref={decisionsRef}
            className="space-y-4 max-h-96 overflow-y-auto"
          >
            {liveDecisions.length === 0 ? (
              <div className="text-center text-gray-400 py-8">
                <div className="animate-pulse">
                  🤖 Waiting for real AI decisions...
                </div>
                <p className="text-sm mt-2">
                  Real AI decisions will appear here as they happen
                </p>
              </div>
            ) : (
              liveDecisions.map((decision) => (
                <div key={decision.id} className="bg-black/30 rounded-lg p-4 border-l-4 border-purple-500">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <span className="text-neural-400 font-semibold">{decision.agentId}</span>
                      <span className="text-cosmic-400 text-sm">{decision.model}</span>
                      <span className="text-nova-400 text-sm">{decision.confidence}% confidence</span>
                    </div>
                    <div className="text-gray-400 text-sm">
                      {formatTime(decision.timestamp)} ({decision.executionTime}ms)
                    </div>
                  </div>
                  
                  <div className="text-white mb-2">
                    {decision.response.substring(0, 200)}
                    {decision.response.length > 200 && '...'}
                  </div>
                  
                  {decision.reasoning.length > 0 && (
                    <div className="text-gray-300 text-sm">
                      <strong>Reasoning:</strong> {decision.reasoning.slice(0, 2).join(', ')}
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
