/**
 * Autonomous Observation Demo Page
 * Phase 1: Frontend preparation for autonomous AI transparency layer
 * 
 * Demonstrates:
 * - Enhanced AgentCard with autonomous capabilities
 * - AutonomousObservationComponent dashboard
 * - Real-time status simulation (WebSocket preparation)
 * - Mobile-first responsive design
 * - 28-agent ecosystem observation
 */

import React from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { Metadata } from 'next';
import AutonomousObservationComponent from '@/components/AutonomousObservation/AutonomousObservationComponent';

export const metadata: Metadata = {
  title: 'Autonomous AI Observation Dashboard | CreAItive',
  description: 'Real-time observation and control interface for 28 IntelligenceEnhanced autonomous agents',
  keywords: ['autonomous AI', 'agent monitoring', 'real-time dashboard', 'AI observation', 'agent control']
};

export default function AutonomousObservationPage() {
  return (
    <div className="min-h-screen bg-space-dark">
      {/* Breadcrumb Navigation */}
      <div className="bg-glass-light border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <Link
            href="/intelligence"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Intelligence Hub</span>
          </Link>
        </div>
      </div>

      {/* Page Header */}
      <div className="bg-glass-light border-b border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white">Autonomous AI Observation</h1>
              <p className="mt-2 text-gray-400">
                Real-time monitoring and control of 28 IntelligenceEnhanced agents
              </p>
            </div>
            <div className="mt-4 md:mt-0 flex items-center gap-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-nova-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-400">Live System</span>
              </div>
              <div className="px-3 py-1 bg-nova-600/20 text-nova-400 rounded-full text-sm">
                Active
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Live System Information Banner */}
        <div className="mb-8 bg-gradient-to-r from-green-600/10 to-blue-600/10 border border-green-500/20 rounded-lg p-6">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-nova-500/20 rounded-full flex items-center justify-center">
                <span className="text-nova-400 text-sm font-bold">🤖</span>
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-2">Live Autonomous AI Observation</h3>
              <p className="text-gray-300 text-sm mb-4">
                Real-time monitoring and control of 28 autonomous AI agents with live decision streaming,
                performance metrics, and intelligent coordination through the MLCoordinationLayer.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-nova-400 rounded-full"></div>
                  <span className="text-gray-400">Enhanced AgentCard with autonomous metrics</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-nova-400 rounded-full"></div>
                  <span className="text-gray-400">Real-time status indicators prepared</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-nova-400 rounded-full"></div>
                  <span className="text-gray-400">WebSocket architecture ready</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-nova-400 rounded-full"></div>
                  <span className="text-gray-400">Mobile-first responsive design</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-nova-400 rounded-full"></div>
                  <span className="text-gray-400">Agent action controls functional</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-nova-400 rounded-full"></div>
                  <span className="text-gray-400">Filtering and sorting operational</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Autonomous Observation Dashboard */}
        <AutonomousObservationComponent 
          className="space-y-6"
          showDetailedMetrics={true}
          enableActions={true}
          autoRefresh={true}
          refreshInterval={3000}
        />

        {/* Phase 2 Preview Banner */}
        <div className="mt-12 bg-gradient-to-r from-purple-600/10 to-cyan-600/10 border border-purple-500/20 rounded-lg p-6">
          <div className="flex items-start gap-4">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-neural-500/20 rounded-full flex items-center justify-center">
                <span className="text-neural-400 text-sm font-bold">2</span>
              </div>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-2">Phase 2: Backend Integration (Upcoming)</h3>
              <p className="text-gray-300 text-sm mb-4">
                The next phase will integrate real autonomous AI systems with live WebSocket connections, 
                MLCoordinationLayer APIs, and genuine agent decision-making capabilities.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-quantum-400 rounded-full"></div>
                  <span className="text-gray-400">Real WebSocket connections to 28 agents</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-quantum-400 rounded-full"></div>
                  <span className="text-gray-400">Live MLCoordinationLayer integration</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-quantum-400 rounded-full"></div>
                  <span className="text-gray-400">Autonomous decision streaming</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-quantum-400 rounded-full"></div>
                  <span className="text-gray-400">Real-time agent control commands</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t border-gray-700 bg-glass-light">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between text-sm text-gray-400">
            <div>
              <p>Autonomous AI Observation Dashboard - Phase 1 Complete</p>
              <p className="mt-1">Enhanced with R1 + Devstral AI coordination strategies</p>
            </div>
            <div className="mt-4 md:mt-0">
              <p>Ready for Phase 2 autonomous AI integration</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 