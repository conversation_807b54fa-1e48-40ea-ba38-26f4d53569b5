/**
 * 🧠 NAVIGATION ANALYSIS DEMO PAGE
 * Showcase the dynamic navigation system that recognizes missing pages and suggests organization
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { AlertTriangle, CheckCircle, RefreshCw, Brain, Search, Zap, ArrowLeft } from 'lucide-react';
import { NavigationAnalysisIndicator } from '@/components/Navigation/NavigationAnalysisIndicator';

export default function NavigationAnalysisPage() {
  const [rawAnalysisData, setRawAnalysisData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);

  const loadDetailedAnalysis = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/navigation/analyze');
      const result = await response.json();
      setRawAnalysisData(result);
    } catch (error) {
      console.error('❌ Failed to load analysis:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadDetailedAnalysis();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-space-900">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Breadcrumb Navigation */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <Brain className="w-8 h-8 text-cosmic-400" />
            <h1 className="text-4xl font-bold text-gradient-cosmic">
              Dynamic Navigation Intelligence
            </h1>
          </div>
          <p className="text-lg text-white/70 max-w-3xl mx-auto">
            Our intelligent system automatically discovers pages, detects missing functionality, 
            and suggests optimal organization for your 80+ page platform.
          </p>
        </div>

        {/* Live Analysis Section */}
        <div className="grid gap-8 mb-8">
          <div className="bg-space-900/50 border border-cosmic-500/20 rounded-xl p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold text-white flex items-center gap-3">
                <Search className="w-6 h-6 text-cosmic-400" />
                Live Navigation Analysis
              </h2>
              <button
                onClick={loadDetailedAnalysis}
                disabled={isLoading}
                className="flex items-center gap-2 px-4 py-2 bg-cosmic-500/10 hover:bg-cosmic-500/20 
                         border border-cosmic-500/20 rounded-lg transition-colors"
              >
                <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
                <span>Refresh Analysis</span>
              </button>
            </div>
            
            {/* Embedded Full Analysis */}
            <NavigationAnalysisIndicator showDetails />
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <div className="bg-space-900/50 border border-cosmic-500/20 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <Search className="w-6 h-6 text-cosmic-400" />
              <h3 className="text-lg font-semibold text-white">Automatic Page Discovery</h3>
            </div>
            <p className="text-white/70 mb-4">
              Scans your filesystem to discover all Next.js pages automatically, analyzing content and structure.
            </p>
            <ul className="text-sm text-white/60 space-y-2">
              <li>• Recursive directory scanning</li>
              <li>• Content analysis & categorization</li>
              <li>• Import dependency tracking</li>
              <li>• Component usage detection</li>
            </ul>
          </div>

          <div className="bg-space-900/50 border border-cosmic-500/20 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-yellow-400" />
              <h3 className="text-lg font-semibold text-white">Missing Page Detection</h3>
            </div>
            <p className="text-white/70 mb-4">
              Intelligently identifies pages that should exist based on your modules and existing functionality.
            </p>
            <ul className="text-sm text-white/60 space-y-2">
              <li>• Expected page analysis</li>
              <li>• Module-based suggestions</li>
              <li>• Dependency gap detection</li>
              <li>• Connection recommendations</li>
            </ul>
          </div>

          <div className="bg-space-900/50 border border-cosmic-500/20 rounded-xl p-6">
            <div className="flex items-center gap-3 mb-4">
              <Zap className="w-6 h-6 text-nova-400" />
              <h3 className="text-lg font-semibold text-white">Smart Organization</h3>
            </div>
            <p className="text-white/70 mb-4">
              Suggests logical groupings and organization improvements based on content analysis.
            </p>
            <ul className="text-sm text-white/60 space-y-2">
              <li>• Intelligent categorization</li>
              <li>• Orphaned page detection</li>
              <li>• Completeness scoring</li>
              <li>• Navigation optimization</li>
            </ul>
          </div>
        </div>

        {/* Raw Data Section */}
        {rawAnalysisData && (
          <div className="bg-space-900/50 border border-cosmic-500/20 rounded-xl p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-3">
              <Brain className="w-5 h-5 text-cosmic-400" />
              Raw Analysis Data
            </h3>
            <div className="bg-space-800/50 border border-cosmic-500/10 rounded-lg p-4 overflow-auto max-h-96">
              <pre className="text-sm text-white/80 font-mono">
                {JSON.stringify(rawAnalysisData, null, 2)}
              </pre>
            </div>
          </div>
        )}

        {/* Demo Actions */}
        <div className="text-center mt-8">
          <div className="bg-cosmic-500/10 border border-cosmic-500/20 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-3">
              🚀 Demo Actions
            </h3>
            <p className="text-white/70 mb-4">
              Try these actions to see the dynamic navigation system in action:
            </p>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div className="bg-space-800/30 rounded-lg p-3">
                <div className="font-medium text-cosmic-400 mb-1">1. Add a New Page</div>
                <div className="text-white/60">
                  Create a new page file and watch it automatically appear in navigation analysis
                </div>
              </div>
              <div className="bg-space-800/30 rounded-lg p-3">
                <div className="font-medium text-nova-400 mb-1">2. Check Mobile Navigation</div>
                <div className="text-white/60">
                  Open mobile navigation to see compact analysis indicator in the footer
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 