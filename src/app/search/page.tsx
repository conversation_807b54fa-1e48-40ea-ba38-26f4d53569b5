"use client";

import { useState, useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import Image from "next/image";
import Button from "@/shared/components/Button/Button";
import Card from "@/shared/components/Card/Card";
import Heading from "@/shared/components/Heading";
import Container from "@/shared/components/Container";
import Input from "@/shared/components/Input";
import Select from "@/shared/components/Select";
import { PerformanceMonitor, LazyWrapper } from "@/shared/components/Performance";
import { AnimationWrapper } from "@/shared/components";
import { searchService } from "@/features/discovery/services/searchService";
import { SearchParams, SearchResult } from "@/features/discovery/types";
import { FiSearch, FiFilter, FiX, FiChevronDown, FiChevronUp, FiClock, FiTrendingUp, FiStar, FiEye, FiHeart, FiShare2, FiBookmark, FiArrowLeft } from "react-icons/fi";

// Categories for filtering
const CATEGORIES = [
  { id: "all", name: "All Categories" },
  { id: "digital-art", name: "Digital Art" },
  { id: "illustration", name: "Illustration" },
  { id: "music", name: "Music" },
  { id: "writing", name: "Writing" },
  { id: "3d", name: "3D Models" },
  { id: "animation", name: "Animation" },
  { id: "photography", name: "Photography" },
  { id: "game-dev", name: "Game Dev" },
  { id: "ai-art", name: "AI Art" },
  { id: "mixed-reality", name: "Mixed Reality" },
  { id: "experimental", name: "Experimental" },
];

// Media types for filtering
const MEDIA_TYPES = [
  { id: "project", name: "Projects" },
  { id: "profile", name: "Profiles" },
  { id: "post", name: "Posts" },
  { id: "media", name: "Media" },
];

// Enhanced Sort options with more advanced capabilities
const SORT_OPTIONS = [
  { id: "relevant", name: "Most Relevant", icon: FiSearch },
  { id: "recent", name: "Most Recent", icon: FiClock },
  { id: "popular", name: "Most Popular", icon: FiTrendingUp },
  { id: "trending", name: "Trending Now", icon: FiStar },
  { id: "views", name: "Most Viewed", icon: FiEye },
  { id: "likes", name: "Most Liked", icon: FiHeart },
];

// Enhanced Popularity filters
const POPULARITY_OPTIONS = [
  { id: "any", name: "Any Popularity" },
  { id: "viral", name: "Viral (10k+ views)" },
  { id: "high", name: "High (1k+ views)" },
  { id: "medium", name: "Medium (100+ views)" },
  { id: "emerging", name: "Emerging (< 100 views)" },
];

// Time range filters (NEW)
const TIME_RANGE_OPTIONS = [
  { id: "any", name: "Any Time" },
  { id: "today", name: "Today" },
  { id: "week", name: "This Week" },
  { id: "month", name: "This Month" },
  { id: "year", name: "This Year" },
];

function SearchPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Search state
  const [query, setQuery] = useState(searchParams?.get("q") || "");
  const [results, setResults] = useState<SearchResult[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [isTimeout, setIsTimeout] = useState(false);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [searchStartTime, setSearchStartTime] = useState<number | null>(null);
  
  // Enhanced Filters state
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedMediaTypes, setSelectedMediaTypes] = useState<string[]>([]);
  const [sortOption, setSortOption] = useState("relevant");
  const [popularity, setPopularity] = useState("any");
  const [timeRange, setTimeRange] = useState("any");
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  
  // Enhanced search timeout and caching
  const SEARCH_TIMEOUT = 8000; // 8 seconds
  const [searchCache, setSearchCache] = useState<Map<string, any>>(new Map());

  // Handle search query input
  const handleQueryChange = (value: string) => {
    setQuery(value);
  };

  // Toggle category selection
  const toggleCategory = (categoryId: string) => {
    if (categoryId === "all") {
      setSelectedCategories([]);
      return;
    }
    
    setSelectedCategories(prevSelected => {
      if (prevSelected.includes(categoryId)) {
        return prevSelected.filter(id => id !== categoryId);
      } else {
        return [...prevSelected, categoryId];
      }
    });
  };

  // Toggle media type selection
  const toggleMediaType = (typeId: string) => {
    setSelectedMediaTypes(prevSelected => {
      if (prevSelected.includes(typeId)) {
        return prevSelected.filter(id => id !== typeId);
      } else {
        return [...prevSelected, typeId];
      }
    });
  };

  // Enhanced search with timeout and caching
  const performSearch = async (resetPage = true) => {
    if (resetPage) {
      setPage(1);
    }
    
    setIsLoading(true);
    setIsTimeout(false);
    setSearchStartTime(Date.now());
    
    // Create cache key
    const cacheKey = JSON.stringify({
      query,
      selectedCategories,
      selectedMediaTypes,
      sortOption,
      popularity,
      timeRange,
      page: resetPage ? 1 : page
    });
    
    // Check cache first
    if (searchCache.has(cacheKey)) {
      const cachedResult = searchCache.get(cacheKey);
      setResults(resetPage ? cachedResult.results : prev => [...prev, ...cachedResult.results]);
      setTotal(cachedResult.total);
      setHasMore(cachedResult.hasMore);
      setPage(cachedResult.page);
      setIsLoading(false);
      return;
    }
    
    // Set timeout for search
    const timeoutId = setTimeout(() => {
      setIsTimeout(true);
      setIsLoading(false);
    }, SEARCH_TIMEOUT);
    
    try {
      // Convert selected categories to proper format
      const categoryFilters = selectedCategories.map(
        id => CATEGORIES.find(c => c.id === id)?.name || ""
      ).filter(name => name !== "");
      
      // Build enhanced search params
      const searchParams: SearchParams = {
        query: query,
        filters: {
          categories: categoryFilters.length > 0 ? categoryFilters : undefined,
          mediaType: selectedMediaTypes.length > 0 ? selectedMediaTypes : undefined,
          popularity: popularity !== "any" ? popularity as any : undefined,
          timeRange: timeRange !== "any" ? timeRange as 'today' | 'week' | 'month' | 'year' : undefined,
        },
        sort: sortOption as any,
        page: resetPage ? 1 : page,
        limit: 12,
      };
      
      // Call search service
      const response = await searchService.search(searchParams);
      
      // Clear timeout on successful response
      clearTimeout(timeoutId);
      
      // Cache the result
      setSearchCache(prev => new Map(prev.set(cacheKey, response)));
      
      if (resetPage) {
        setResults(response.results);
      } else {
        setResults(prev => [...prev, ...response.results]);
      }
      
      setTotal(response.total);
      setHasMore(response.hasMore);
      setPage(response.page);
      
      // Update URL to reflect search state
      const urlParams = new URLSearchParams();
      if (query) urlParams.set("q", query);
      if (sortOption !== "relevant") urlParams.set("sort", sortOption);
      if (selectedCategories.length > 0) urlParams.set("categories", selectedCategories.join(","));
      if (selectedMediaTypes.length > 0) urlParams.set("types", selectedMediaTypes.join(","));
      if (popularity !== "any") urlParams.set("popularity", popularity);
      if (timeRange !== "any") urlParams.set("timeRange", timeRange);
      
      const newUrl = `${window.location.pathname}?${urlParams.toString()}`;
      window.history.replaceState({}, "", newUrl);
      
    } catch (error) {
      clearTimeout(timeoutId);
      console.error("Search error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    performSearch();
  };

  // Load more results
  const loadMore = () => {
    setPage(prevPage => prevPage + 1);
    performSearch(false);
  };

  // Enhanced reset filters
  const resetFilters = () => {
    setSelectedCategories([]);
    setSelectedMediaTypes([]);
    setSortOption("relevant");
    setPopularity("any");
    setTimeRange("any");
    setShowAdvancedFilters(false);
    setSearchCache(new Map()); // Clear cache on reset
  };

  // Retry search after timeout
  const retrySearch = () => {
    setIsTimeout(false);
    performSearch();
  };

  // Initial search when page loads
  useEffect(() => {
    if (searchParams?.get("q")) {
      // Parse URL parameters
      const urlQuery = searchParams.get("q") || "";
      const urlSort = searchParams.get("sort") || "relevant";
      const urlCategories = searchParams.get("categories")?.split(",") || [];
      const urlTypes = searchParams.get("types")?.split(",") || [];
      const urlPopularity = searchParams.get("popularity") || "any";
      const urlTimeRange = searchParams.get("timeRange") || "any";
      
      // Set state from URL parameters
      setQuery(urlQuery);
      setSortOption(urlSort);
      setSelectedCategories(urlCategories);
      setSelectedMediaTypes(urlTypes);
      setPopularity(urlPopularity);
      setTimeRange(urlTimeRange);
      
      // Perform initial search
      performSearch();
    }
  }, []);

  return (
    <Container className="py-16">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <FiArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </Link>
      </div>

      {/* TEMPORARILY DISABLED: PerformanceMonitor causing infinite re-renders */}
      {/* <PerformanceMonitor
        componentName="SearchPage"
        threshold={150}
        enableRenderTracking={true}
        showDebugInfo={false}
      > */}

      <AnimationWrapper animation="fadeIn" duration={600}>
        <div className="text-center mb-10">
          <h1 className="heading-1 mb-responsive-sm text-gradient-cosmic">
            Search the Universe
          </h1>
          <p className="body-large text-white/80 max-w-3xl mx-auto">
            Find projects, creators, and inspiration across the creative universe with advanced search and filtering
          </p>
        </div>
      </AnimationWrapper>

      <AnimationWrapper animation="slideUp" duration={600} delay={200}>
        <Card variant="elevated" className="mb-8">
          <form onSubmit={handleSearch} className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-grow">
                <Input
                  variant="glass"
                  placeholder="Search by keywords, tags, or creator name"
                  value={query}
                  onChange={handleQueryChange}
                  fullWidth
                  leftIcon={<FiSearch className="text-stardust-400" />}
                />
              </div>
              
              <div className="flex flex-col sm:flex-row gap-2">
                <Button 
                  variant="primary" 
                  type="submit"
                  disabled={isLoading}
                  className="w-full sm:w-auto"
                >
                  {isLoading ? (
                    <span className="flex items-center">
                      <span className="animate-spin h-4 w-4 mr-2 border-2 border-t-transparent border-white rounded-full"></span>
                      Searching...
                    </span>
                  ) : (
                    'Search'
                  )}
                </Button>
                
                <Button 
                  variant="outline" 
                  type="button"
                  onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className="w-full sm:w-auto"
                >
                  <FiFilter className="mr-2" />
                  Filters
                  {showAdvancedFilters ? <FiChevronUp className="ml-2" /> : <FiChevronDown className="ml-2" />}
                </Button>
              </div>
            </div>
            
            <AnimationWrapper>
              {showAdvancedFilters && (
                <div 
                  className="mt-6 pt-6 border-t border-space-700"
                >
                  <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-4 gap-4">
                    <Heading level={3} size="lg" className="text-stardust-200">
                      Advanced Filters
                    </Heading>
                    
                    <div className="flex gap-2">
                      <span className="text-sm text-stardust-400">
                        {selectedCategories.length + selectedMediaTypes.length} filters active
                      </span>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={resetFilters}
                        className="text-stardust-400 hover:text-stardust-200"
                      >
                        <FiX className="mr-1" /> Reset All
                      </Button>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                    <div>
                      <label className="block text-stardust-300 mb-2 font-medium">Categories</label>
                      <div className="space-y-2 max-h-40 overflow-y-auto custom-scrollbar pr-2">
                        {CATEGORIES.map(category => (
                          <div key={category.id} className="flex items-center">
                            <input
                              type="checkbox"
                              id={`category-${category.id}`}
                              checked={category.id === "all" ? selectedCategories.length === 0 : selectedCategories.includes(category.id)}
                              onChange={() => toggleCategory(category.id)}
                              className="neo-checkbox h-4 w-4"
                            />
                            <label htmlFor={`category-${category.id}`} className="ml-2 text-stardust-400 text-sm cursor-pointer hover:text-stardust-200 transition-colors">
                              {category.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-stardust-300 mb-2 font-medium">Content Types</label>
                      <div className="space-y-2">
                        {MEDIA_TYPES.map(type => (
                          <div key={type.id} className="flex items-center">
                            <input
                              type="checkbox"
                              id={`type-${type.id}`}
                              checked={selectedMediaTypes.includes(type.id)}
                              onChange={() => toggleMediaType(type.id)}
                              className="neo-checkbox h-4 w-4"
                            />
                            <label htmlFor={`type-${type.id}`} className="ml-2 text-stardust-400 text-sm cursor-pointer hover:text-stardust-200 transition-colors">
                              {type.name}
                            </label>
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-stardust-300 mb-2 font-medium">Sort By</label>
                      <Select
                        variant="glass"
                        value={sortOption}
                        onChange={(e) => setSortOption(e.target.value)}
                        options={SORT_OPTIONS.map(option => ({
                          value: option.id,
                          label: option.name
                        }))}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-stardust-300 mb-2 font-medium">Popularity</label>
                      <Select
                        variant="glass"
                        value={popularity}
                        onChange={(e) => setPopularity(e.target.value)}
                        options={POPULARITY_OPTIONS.map(option => ({
                          value: option.id,
                          label: option.name
                        }))}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-stardust-300 mb-2 font-medium">Time Range</label>
                      <Select
                        variant="glass"
                        value={timeRange}
                        onChange={(e) => setTimeRange(e.target.value)}
                        options={TIME_RANGE_OPTIONS.map(option => ({
                          value: option.id,
                          label: option.name
                        }))}
                      />
                    </div>
                  </div>
                </div>
              )}
            </AnimationWrapper>
          </form>
        </Card>
      </AnimationWrapper>

      <AnimationWrapper animation="slideUp" duration={600} delay={400}>
        <div className="mb-8">
          {searchStartTime && !isLoading && !isTimeout && results.length > 0 && (
            <div 
              className="mb-4 text-center"
            >
              <p className="text-stardust-400 text-sm">
                Search completed in {((Date.now() - searchStartTime) / 1000).toFixed(2)}s
              </p>
            </div>
          )}

          {isTimeout && (
            <div 
              className="text-center py-16"
            >
              <div className="text-6xl mb-4">⏱️</div>
              <Heading level={3} size="xl" className="text-stardust-300 mb-2">
                Search timed out
              </Heading>
              <p className="text-stardust-400 mb-6 max-w-md mx-auto">
                The search took longer than expected. This might be due to high server load or network issues.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button variant="primary" onClick={retrySearch}>
                  Try Again
                </Button>
                <Button variant="outline" onClick={resetFilters}>
                  Reset Filters
                </Button>
              </div>
            </div>
          )}

          {isLoading && !isTimeout && (
            <div 
              className="text-center py-16"
            >
              <div className="relative mx-auto w-16 h-16 mb-4">
                <div className="absolute inset-0 border-4 border-cosmic-500/20 rounded-full"></div>
                <div className="absolute inset-0 border-4 border-t-cosmic-500 rounded-full animate-spin"></div>
              </div>
              <Heading level={3} size="lg" className="text-stardust-300 mb-2">
                Searching the creative universe...
              </Heading>
              <p className="text-stardust-400">
                Finding the perfect matches for your query
              </p>
            </div>
          )}

          {!isLoading && !isTimeout && results.length > 0 && (
            <>
              <div 
                className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4"
              >
                <div>
                  <p className="text-stardust-400">
                    Found <span className="text-stardust-200 font-medium">{total.toLocaleString()}</span> results
                    {query && <span> for "<span className="text-cosmic-400 font-medium">{query}</span>"</span>}
                  </p>
                  {selectedCategories.length > 0 || selectedMediaTypes.length > 0 || popularity !== "any" || timeRange !== "any" ? (
                    <p className="text-stardust-500 text-sm mt-1">
                      {selectedCategories.length + selectedMediaTypes.length} filters applied
                    </p>
                  ) : null}
                </div>
                
                <div className="flex gap-2">
                  {SORT_OPTIONS.slice(0, 3).map(option => (
                    <button
                      key={option.id}
                      onClick={() => setSortOption(option.id)}
                      className={`px-3 py-1 text-xs rounded-full transition-all duration-200 ${
                        sortOption === option.id 
                          ? 'bg-cosmic-500 text-white shadow-cosmic/50' 
                          : 'bg-space-700 text-stardust-400 hover:bg-space-600 hover:text-stardust-300'
                      }`}
                    >
                      {option.name}
                    </button>
                  ))}
                </div>
              </div>
              
              <div 
                className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
              >
                {results.map((result, index) => (
                  <div
                    key={result.id}
                  >
                    <Card variant="elevated" hover="glow" className="overflow-hidden h-full group cursor-pointer">
                      <div className="h-48 overflow-hidden relative bg-space-800">
                        {result.thumbnail && (
                          <div 
                            className="w-full h-full bg-cover bg-center transition-transform duration-300 group-hover:scale-105"
                            style={{ backgroundImage: `url(${result.thumbnail})` }}
                          />
                        )}
                        
                        <div className="absolute inset-0 bg-gradient-to-t from-space-900/90 via-space-900/20 to-transparent"></div>
                        
                        <div className="absolute top-3 left-3">
                          <span className="px-2 py-1 text-xs rounded-full bg-space-900/80 backdrop-blur-sm text-neural-400 capitalize font-medium border border-space-700">
                            {result.type}
                          </span>
                        </div>
                        
                        <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <button className="p-2 rounded-full bg-space-900/80 backdrop-blur-sm border border-space-700 hover:bg-space-800 transition-colors">
                            <FiBookmark className="w-3 h-3 text-stardust-400" />
                          </button>
                          <button className="p-2 rounded-full bg-space-900/80 backdrop-blur-sm border border-space-700 hover:bg-space-800 transition-colors">
                            <FiShare2 className="w-3 h-3 text-stardust-400" />
                          </button>
                        </div>
                        
                        <div className="absolute bottom-3 right-3 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          {result.stats && (
                            <>
                              <div className="flex items-center gap-1 text-xs text-stardust-400">
                                <FiEye className="w-3 h-3" />
                                <span>{result.stats.views || 0}</span>
                              </div>
                              <div className="flex items-center gap-1 text-xs text-stardust-400">
                                <FiHeart className="w-3 h-3" />
                                <span>{result.stats.likes || 0}</span>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                      
                      <div className="p-5">
                        <h3 className="text-stardust-200 font-semibold text-lg mb-2 line-clamp-2 group-hover:text-cosmic-300 transition-colors duration-200">
                          {result.title}
                        </h3>
                        
                        {result.description && (
                          <p className="text-stardust-400 text-sm mb-3 line-clamp-2 leading-relaxed">
                            {result.description}
                          </p>
                        )}
                        
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-space-600 flex-shrink-0 flex items-center justify-center text-xs overflow-hidden border border-space-500">
                              {result.creator.avatar ? (
                                <Image 
                                  src={result.creator.avatar} 
                                  alt={result.creator.name} 
                                  width={32}
                                  height={32}
                                  className="w-full h-full rounded-full object-cover"
                                  loading="lazy"
                                  placeholder="blur"
                                  blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQABAAAEAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWEREiMxUf/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                                />
                              ) : (
                                <span className="text-stardust-300 font-medium">{result.creator.name.charAt(0)}</span>
                              )}
                            </div>
                            <div className="ml-3">
                              <span className="text-sm text-stardust-300 font-medium">{result.creator.name}</span>
                              {result.creator.verified && (
                                <div className="flex items-center">
                                  <FiStar className="w-3 h-3 text-cosmic-400 mr-1" />
                                  <span className="text-xs text-stardust-500">Verified</span>
                                </div>
                              )}
                            </div>
                          </div>
                          
                          {result.popularity && (
                            <div className="flex items-center">
                              <div className={`w-2 h-2 rounded-full mr-2 ${
                                result.popularity === 'viral' ? 'bg-nova-500 animate-pulse' :
                                result.popularity === 'high' ? 'bg-cosmic-500' :
                                result.popularity === 'medium' ? 'bg-neural-500' :
                                'bg-stardust-500'
                              }`} />
                              <span className="text-xs text-stardust-500 capitalize">{result.popularity}</span>
                            </div>
                          )}
                        </div>
                        
                        {result.tags && result.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mb-3">
                            {result.tags.slice(0, 3).map(tag => (
                              <span key={tag} className="text-xs px-2 py-1 bg-space-700 text-stardust-400 rounded-full hover:bg-space-600 transition-colors cursor-pointer">
                                #{tag}
                              </span>
                            ))}
                            {result.tags.length > 3 && (
                              <span className="text-xs px-2 py-1 bg-space-700 text-stardust-500 rounded-full">
                                +{result.tags.length - 3}
                              </span>
                            )}
                          </div>
                        )}
                        
                        <div className="flex items-center justify-between text-xs text-stardust-500 pt-2 border-t border-space-700">
                          <span>
                            {result.createdAt ? new Date(result.createdAt).toLocaleDateString() : 'Recently'}
                          </span>
                          <div className="flex items-center gap-3">
                            <span className="flex items-center gap-1">
                              <FiEye className="w-3 h-3" />
                              {result.stats?.views || 0}
                            </span>
                            <span className="flex items-center gap-1">
                              <FiHeart className="w-3 h-3" />
                              {result.stats?.likes || 0}
                            </span>
                          </div>
                        </div>
                      </div>
                    </Card>
                  </div>
                ))}
              </div>
              
              {hasMore && (
                <div 
                  className="mt-8 text-center"
                >
                  <Button 
                    variant="outline" 
                    size="lg" 
                    onClick={loadMore}
                    disabled={isLoading}
                    className="px-8"
                  >
                    {isLoading ? (
                      <span className="flex items-center">
                        <span className="animate-spin h-4 w-4 mr-2 border-2 border-t-transparent border-cosmic-500 rounded-full"></span>
                        Loading...
                      </span>
                    ) : (
                      <>
                        Load More Results
                      </>
                    )}
                  </Button>
                  <p className="text-stardust-500 text-sm mt-2">
                    Showing {results.length} of {total.toLocaleString()} results
                  </p>
                </div>
              )}
            </>
          )}

          {!isLoading && !isTimeout && results.length === 0 && (
            <div 
              className="text-center py-16"
            >
              {query ? (
                <>
                  <div 
                    className="text-6xl mb-6"
                  >
                    🔍
                  </div>
                  <Heading level={3} size="xl" className="text-stardust-300 mb-3">
                    No results found for "{query}"
                  </Heading>
                  <p className="text-stardust-400 mb-6 max-w-md mx-auto">
                    We couldn't find any matches. Try different keywords, adjust your filters, or explore trending content.
                  </p>
                  
                  <div className="mb-6">
                    <p className="text-stardust-500 text-sm mb-3">Try these suggestions:</p>
                    <div className="flex flex-wrap justify-center gap-2">
                      {["digital art", "illustration", "music", "photography"].map(suggestion => (
                        <button
                          key={suggestion}
                          onClick={() => {
                            setQuery(suggestion);
                            performSearch();
                          }}
                          className="px-3 py-1 text-xs bg-space-700 text-stardust-400 rounded-full hover:bg-space-600 hover:text-stardust-300 transition-colors"
                        >
                          {suggestion}
                        </button>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button variant="primary" onClick={resetFilters}>
                      Clear All Filters
                    </Button>
                    <Button variant="outline" onClick={() => router.push('/explore')}>
                      Browse Trending
                    </Button>
                  </div>
                </>
              ) : (
                <>
                  <div 
                    className="text-6xl mb-6"
                  >
                    ✨
                  </div>
                  <Heading level={3} size="xl" className="text-stardust-300 mb-3">
                    Discover amazing creative work
                  </Heading>
                  <p className="text-stardust-400 mb-6 max-w-md mx-auto">
                    Enter keywords above to search through thousands of projects, creators, and creative content.
                  </p>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 max-w-md mx-auto mb-6">
                    {[
                      { label: "Digital Art", icon: "🎨" },
                      { label: "Music", icon: "🎵" },
                      { label: "Writing", icon: "✍️" },
                      { label: "Photography", icon: "📸" }
                    ].map(category => (
                      <button
                        key={category.label}
                        onClick={() => {
                          toggleCategory(category.label.toLowerCase().replace(' ', '-'));
                          performSearch();
                        }}
                        className="p-3 bg-space-800 rounded-lg hover:bg-space-700 transition-colors group"
                      >
                        <div className="text-2xl mb-1 group-hover:scale-110 transition-transform">{category.icon}</div>
                        <div className="text-xs text-stardust-400 group-hover:text-stardust-300">{category.label}</div>
                      </button>
                    ))}
                  </div>
                  
                  <Button variant="outline" onClick={() => router.push('/explore')}>
                    Explore Trending Content
                  </Button>
                </>
              )}
            </div>
          )}
        </div>
      </AnimationWrapper>
    </Container>
  );
}

export default function SearchPage() {
  return (
    <Suspense fallback={
      <Container>
        <div className="flex min-h-screen items-center justify-center">
          <div className="animate-pulse text-cosmic-600">Loading Search...</div>
        </div>
      </Container>
    }>
      <SearchPageContent />
    </Suspense>
  );
} 