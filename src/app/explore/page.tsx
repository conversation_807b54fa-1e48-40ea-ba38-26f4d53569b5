"use client";

import { useState, useEffect } from "react";
import Container from "@/shared/components/Container";
import Heading from "@/shared/components/Heading";
import Button from "@/shared/components/Button/Button";
import Link from "next/link";
import { FiSearch, FiGrid, FiBarChart2, FiCompass, FiArrowLeft } from "react-icons/fi";
import CategoryGrid from "@/features/discovery/components/CategoryGrid";
import TrendingGrid from "@/features/discovery/components/TrendingGrid";
import RecommendationGrid from "@/features/discovery/components/RecommendationGrid";
import { trendingService } from "@/features/discovery/services/trendingService";

export default function ExplorePage() {
  const [trendingTags, setTrendingTags] = useState<{ tag: string; count: number }[]>([]);
  const [isTagsLoading, setIsTagsLoading] = useState(true);
  const [skeletonWidths, setSkeletonWidths] = useState<number[]>([]);

  // Generate skeleton widths on client side only
  useEffect(() => {
    const widths = Array.from({ length: 10 }, () => Math.floor(Math.random() * 80) + 60);
    setSkeletonWidths(widths);
  }, []);

  // Fetch trending tags
  useEffect(() => {
    const fetchTrendingTags = async () => {
      try {
        const tags = await trendingService.getTrendingTags(10);
        setTrendingTags(tags);
      } catch (error) {
        console.error("Error fetching trending tags:", error);
      } finally {
        setIsTagsLoading(false);
      }
    };

    fetchTrendingTags();
  }, []);

  // Mock user ID for recommendations (would come from auth in real app)
  const mockUserId = "user123";
  const userInterests = ["digital art", "illustration", "animation"];

  return (
    <div className="min-h-screen theme-bg-primary">
      <Container className="py-16">
        {/* Breadcrumb Navigation */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <FiArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Page Header */}
        <div className="text-center mb-16">
          <Heading level={1} gradient size="3xl" align="center" className="mb-4">
            Explore the Creative Universe
          </Heading>
          <p className="theme-text-secondary max-w-2xl mx-auto mb-8">
            Discover inspiring projects, talented creators, and innovative ideas from across the platform
          </p>

          {/* Search Bar */}
          <div className="flex justify-center max-w-2xl mx-auto">
            <Link href="/search" className="w-full">
              <div className="relative flex items-center w-full">
                <div className="absolute left-4 theme-text-tertiary">
                  <FiSearch size={20} />
                </div>
                <div className="w-full py-3 pl-12 pr-4 theme-bg-elevated border theme-border-primary rounded-lg text-left theme-text-secondary">
                  Search for projects, creators, or ideas...
                </div>
              </div>
            </Link>
          </div>
        </div>

        {/* Quick Navigation */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-16">
          <Link href="/search">
            <Button variant="ghost" size="lg" fullWidth className="h-20 flex flex-col neo-card hover:shadow-cosmic transition-all duration-300">
              <FiSearch size={24} className="mb-2" />
              <span>Search</span>
            </Button>
          </Link>
          <Link href="/categories">
            <Button variant="ghost" size="lg" fullWidth className="h-20 flex flex-col neo-card hover:shadow-nova transition-all duration-300">
              <FiGrid size={24} className="mb-2" />
              <span>Categories</span>
            </Button>
          </Link>
          <Link href="/trending">
            <Button variant="ghost" size="lg" fullWidth className="h-20 flex flex-col neo-card hover:shadow-neural transition-all duration-300">
              <FiBarChart2 size={24} className="mb-2" />
              <span>Trending</span>
            </Button>
          </Link>
          <Link href="/discover">
            <Button variant="ghost" size="lg" fullWidth className="h-20 flex flex-col neo-card hover:shadow-quantum transition-all duration-300">
              <FiCompass size={24} className="mb-2" />
              <span>Discover</span>
            </Button>
          </Link>
        </div>

        {/* Featured Categories */}
        <div className="mb-20">
          <CategoryGrid 
            title="Explore Categories" 
            subtitle="Browse creative work by discipline"
            featuredOnly={true}
            limit={6}
          />
        </div>

        {/* Trending Section */}
        <div className="mb-20">
          <TrendingGrid 
            title="Trending Now" 
            subtitle="See what's popular across the platform"
            limit={8}
          />
        </div>

        {/* Personalized Recommendations */}
        <div className="mb-20">
          <RecommendationGrid 
            userId={mockUserId}
            title="Recommended For You"
            subtitle="Content curated based on your interests and activity"
            limit={8}
            interests={userInterests}
          />
        </div>

        {/* Trending Tags */}
        <div className="mb-16">
          <Heading level={2} size="2xl" className="theme-text-primary mb-6">
            Trending Tags
          </Heading>

          {isTagsLoading ? (
            <div className="flex flex-wrap gap-3">
              {skeletonWidths.map((width, index) => (
                <div 
                  key={index} 
                  className="h-10 theme-bg-secondary/60 rounded-full animate-pulse"
                  style={{ width: `${width}px` }}
                ></div>
              ))}
            </div>
          ) : (
            <div className="flex flex-wrap gap-3">
              {trendingTags.map((tag) => (
                <Link href={`/search?q=${encodeURIComponent(tag.tag)}`} key={tag.tag}>
                  <span className="px-4 py-2 theme-bg-elevated border theme-border-primary hover:border-cosmic-400 rounded-full theme-text-primary text-sm flex items-center gap-2 transition-all duration-200 hover:shadow-cosmic">
                    {tag.tag}
                    <span className="inline-flex items-center justify-center bg-cosmic-600/20 text-cosmic-400 text-xs rounded-full h-5 px-2">
                      {tag.count}
                    </span>
                  </span>
                </Link>
              ))}
            </div>
          )}
        </div>

        {/* Featured Creators */}
        <div className="text-center">
          <Heading level={2} size="2xl" className="theme-text-primary mb-6">
            Ready to Explore?
          </Heading>
          <p className="theme-text-secondary max-w-xl mx-auto mb-8">
            Dive deeper into the creative universe and discover your next inspiration
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <Link href="/search">
              <Button variant="primary" size="lg">
                Start Searching
              </Button>
            </Link>
            <Link href="/categories">
              <Button variant="outline" size="lg">
                Browse Categories
              </Button>
            </Link>
          </div>
        </div>
      </Container>
    </div>
  );
} 