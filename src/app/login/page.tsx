"use client";

import { useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { signIn } from 'next-auth/react';
import Link from 'next/link';
import { Eye, EyeOff, ArrowLeft } from 'lucide-react';
import Container from '@/shared/components/Container';
import TestLoginPanel from '@/components/auth/TestLoginPanel';

function LoginPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const redirectUrl = searchParams?.get('redirect') || '/';
  
  // Get error message from URL if it exists
  const errorParam = searchParams?.get('error');
  const errorMessages: Record<string, string> = {
    CredentialsSignin: "Invalid email or password",
    default: "An error occurred during sign in"
  };
  
  const [email, setEmail] = useState('<EMAIL>'); // Pre-filled for demo
  const [password, setPassword] = useState('password123'); // Pre-filled for demo
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState(errorParam ? (errorMessages[errorParam] || errorMessages.default) : '');
  const [isLoading, setIsLoading] = useState(false);
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      setError('Please fill in all fields');
      return;
    }
    
    try {
      setIsLoading(true);
      setError('');
      
      const result = await signIn('credentials', {
        redirect: false,
        email,
        password,
      });
      
      if (result?.error) {
        setError(result.error);
        return;
      }
      
      if (result?.ok) {
        // Redirect to the intended destination
        router.push(redirectUrl);
        router.refresh();
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Container>
      <div className="flex min-h-screen items-center justify-center py-6 px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-4xl grid lg:grid-cols-2 gap-8 items-start">
          {/* Test Login Panel */}
          <div className="order-2 lg:order-1">
            <TestLoginPanel />
          </div>

          {/* Main Login Form */}
          <div className="order-1 lg:order-2 space-y-6 sm:space-y-8">
          
          {/* Mobile Back Button */}
          <div className="block sm:hidden">
            <button
              onClick={() => router.back()}
              className="
                flex items-center gap-2 p-3 -ml-3 rounded-xl
                text-white/70 hover:text-white
                hover:bg-cosmic-500/10 transition-all duration-200
                active:scale-95 min-h-[44px]
              "
            >
              <ArrowLeft className="w-5 h-5" />
              <span className="text-sm">Back</span>
            </button>
          </div>

          {/* Enhanced Header */}
          <div className="text-center">
            <div className="mb-4 sm:mb-6">
              <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-2xl flex items-center justify-center mb-4">
                <span className="text-2xl sm:text-3xl font-bold text-white">C</span>
              </div>
            </div>
            <h1 className="text-2xl sm:text-3xl font-orbitron font-bold text-gradient-cosmic">
              Welcome Back
            </h1>
            <p className="mt-2 text-sm sm:text-base text-stardust-400">
              Sign in to continue creating
            </p>
          </div>
          
          <form className="space-y-5 sm:space-y-6" onSubmit={handleSubmit}>
            {/* Enhanced Error Display */}
            {error && (
              <div className="
                bg-red-500/10 border border-red-500/30 text-red-400 
                px-4 py-4 rounded-xl text-sm
                animate-in slide-in-from-top duration-300
              ">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-red-400 rounded-full flex-shrink-0"></div>
                  <span>{error}</span>
                </div>
              </div>
            )}
            
            <div className="space-y-4 sm:space-y-5">
              {/* Enhanced Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-stardust-300 mb-2">
                  Email Address
                </label>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="
                    w-full bg-space-800/50 border border-cosmic-600/30 rounded-xl 
                    px-4 py-4 text-stardust-200 text-base
                    shadow-sm focus:border-cosmic-400 focus:ring-2 focus:ring-cosmic-400/30
                    transition-all duration-200
                    min-h-[52px] touch-pan-y
                  "
                  placeholder="<EMAIL>"
                />
              </div>
              
              {/* Enhanced Password Field with Show/Hide */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-stardust-300 mb-2">
                  Password
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="
                      w-full bg-space-800/50 border border-cosmic-600/30 rounded-xl 
                      px-4 py-4 pr-12 text-stardust-200 text-base
                      shadow-sm focus:border-cosmic-400 focus:ring-2 focus:ring-cosmic-400/30
                      transition-all duration-200
                      min-h-[52px] touch-pan-y
                    "
                    placeholder="Enter your password"
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="
                      absolute right-3 top-1/2 transform -translate-y-1/2
                      p-2 rounded-lg text-stardust-400 hover:text-stardust-200
                      hover:bg-cosmic-500/10 transition-all duration-200
                      min-h-[44px] min-w-[44px] flex items-center justify-center
                    "
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>
            </div>
            
            {/* Mobile-Optimized Options */}
            <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
              <div className="flex items-center">
                <input
                  id="remember_me"
                  name="remember_me"
                  type="checkbox"
                  className="
                    h-5 w-5 text-cosmic-600 focus:ring-cosmic-500 
                    border-cosmic-600/40 rounded bg-space-800
                    cursor-pointer
                  "
                />
                <label htmlFor="remember_me" className="ml-3 block text-sm text-stardust-400 cursor-pointer">
                  Remember me
                </label>
              </div>
              
              <div className="text-sm">
                <Link 
                  href="/forgot-password" 
                  className="
                    text-cosmic-400 hover:text-cosmic-300 transition-colors
                    underline underline-offset-2
                  "
                >
                  Forgot password?
                </Link>
              </div>
            </div>
            
            {/* Enhanced Submit Button */}
            <div className="pt-2">
              <button
                type="submit"
                disabled={isLoading}
                className="
                  w-full flex justify-center items-center gap-2
                  py-4 px-6 rounded-xl shadow-lg text-base font-semibold text-white 
                  bg-gradient-to-r from-cosmic-600 to-cosmic-500 
                  hover:from-cosmic-500 hover:to-cosmic-400 
                  focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-cosmic-500
                  disabled:opacity-50 disabled:cursor-not-allowed
                  transition-all duration-200 min-h-[52px]
                  active:scale-95 transform
                "
              >
                {isLoading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                    <span>Signing in...</span>
                  </>
                ) : (
                  <span>Sign In</span>
                )}
              </button>
            </div>
            
            {/* Enhanced Register Link */}
            <div className="text-center pt-2">
              <p className="text-sm text-stardust-400">
                Don't have an account?{' '}
                <Link 
                  href="/register" 
                  className="
                    text-cosmic-400 hover:text-cosmic-300 font-medium
                    transition-colors underline underline-offset-2
                  "
                >
                  Create Account
                </Link>
              </p>
            </div>
          </form>
          
          {/* Enhanced Demo Credentials */}
          <div className="
            bg-nova-500/10 border border-nova-500/30
            p-4 sm:p-5 rounded-xl 
          ">
            <div className="flex items-center gap-2 mb-2">
              <div className="w-2 h-2 bg-nova-400 rounded-full animate-pulse"></div>
              <p className="text-sm font-semibold text-nova-300">Demo Ready</p>
            </div>
            <p className="text-xs text-nova-200/80 mb-2">Pre-filled credentials for quick access:</p>
            <div className="text-xs space-y-1">
              <p className="text-nova-200/60">Email: <span className="text-nova-300 font-mono"><EMAIL></span></p>
              <p className="text-nova-200/60">Password: <span className="text-nova-300 font-mono">password123</span></p>
            </div>
          </div>
        </div>
      </div>
      </div>
    </Container>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={
      <Container>
        <div className="flex min-h-screen items-center justify-center">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 border-2 border-cosmic-500 border-t-transparent rounded-full animate-spin"></div>
            <span className="text-cosmic-400">Loading...</span>
          </div>
        </div>
      </Container>
    }>
      <LoginPageContent />
    </Suspense>
  );
} 