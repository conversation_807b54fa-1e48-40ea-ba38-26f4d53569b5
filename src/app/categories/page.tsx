"use client";

import { useState, useEffect } from "react";
import Container from "@/shared/components/Container";
import Heading from "@/shared/components/Heading";
import Button from "@/shared/components/Button/Button";
import Link from "next/link";
import { Fi<PERSON>rid, <PERSON><PERSON>ist, <PERSON>Search, FiArrowLeft } from "react-icons/fi";
import CategoryGrid from "@/features/discovery/components/CategoryGrid";
import { categoryService } from "@/features/discovery/services/categoryService";
import { Category } from "@/features/discovery/types";

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [filteredCategories, setFilteredCategories] = useState<Category[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const allCategories = await categoryService.getAllCategories();
        setCategories(allCategories);
        setFilteredCategories(allCategories);
      } catch (error) {
        console.error("Error fetching categories:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Filter categories based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredCategories(categories);
    } else {
      const filtered = categories.filter(category =>
        category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (category.description && category.description.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredCategories(filtered);
    }
  }, [searchTerm, categories]);

  return (
    <Container className="py-16">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <FiArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </Link>
      </div>

      {/* Page Header */}
      <div className="text-center mb-12">
        <Heading level={1} gradient size="3xl" align="center" className="mb-4">
          Explore Categories
        </Heading>
        <p className="text-stardust-400 max-w-2xl mx-auto mb-8">
          Discover creative work across different disciplines and find your inspiration
        </p>

        {/* Search and View Controls */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
          <div className="relative w-full sm:w-96">
            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-stardust-400" size={20} />
            <input
              type="text"
              placeholder="Search categories..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-space-800 border border-space-700 rounded-lg text-stardust-200 placeholder-stardust-400 focus:border-cosmic-500 focus:outline-none transition-colors"
            />
          </div>

          <div className="flex rounded-lg overflow-hidden border border-space-700">
            <button 
              className={`p-3 ${viewMode === 'grid' ? 'bg-space-700 text-stardust-200' : 'bg-space-800 text-stardust-400'} hover:bg-space-700 transition-colors`}
              onClick={() => setViewMode('grid')}
            >
              <FiGrid size={18} />
            </button>
            <button 
              className={`p-3 ${viewMode === 'list' ? 'bg-space-700 text-stardust-200' : 'bg-space-800 text-stardust-400'} hover:bg-space-700 transition-colors`}
              onClick={() => setViewMode('list')}
            >
              <FiList size={18} />
            </button>
          </div>
        </div>
      </div>

      {/* Featured Categories */}
      <div className="mb-16">
        <CategoryGrid 
          title="Featured Categories" 
          subtitle="Popular creative disciplines on the platform"
          featuredOnly={true}
          limit={6}
        />
      </div>

      {/* All Categories */}
      <div className="mb-16">
        <div className="flex items-center justify-between mb-6">
          <div>
            <Heading level={2} size="2xl" className="text-stardust-200 mb-1">
              All Categories
            </Heading>
            <p className="text-stardust-400">
              {filteredCategories.length} {filteredCategories.length === 1 ? 'category' : 'categories'} 
              {searchTerm && ` matching "${searchTerm}"`}
            </p>
          </div>
        </div>

        {isLoading ? (
          <CategoryGrid limit={12} />
        ) : filteredCategories.length === 0 ? (
          <div className="text-center py-16 bg-space-800/50 rounded-lg">
            <FiSearch size={48} className="mx-auto text-stardust-600 mb-4" />
            <Heading level={3} size="xl" className="text-stardust-300 mb-2">
              No categories found
            </Heading>
            <p className="text-stardust-400 mb-6">
              {searchTerm ? `No categories match "${searchTerm}"` : "No categories available"}
            </p>
            {searchTerm && (
              <Button variant="outline" onClick={() => setSearchTerm("")}>
                Clear Search
              </Button>
            )}
          </div>
        ) : viewMode === 'grid' ? (
          <CategoryGrid limit={filteredCategories.length} />
        ) : (
          // List view
          <div className="space-y-4">
            {filteredCategories.map(category => (
              <div key={category.id} className="neo-card p-6 flex items-center justify-between hover:shadow-cosmic transition-all duration-300">
                <div className="flex items-center">
                  <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${categoryService.getCategoryGradient(category.id)} mr-4`}></div>
                  <div>
                    <h3 className="text-stardust-200 font-semibold text-lg">{category.name}</h3>
                    {category.description && (
                      <p className="text-stardust-400 text-sm mt-1">{category.description}</p>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-stardust-300 font-medium">{category.projectCount}</div>
                  <div className="text-stardust-500 text-sm">projects</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* CTA Section */}
      <div className="text-center py-16 bg-gradient-to-br from-cosmic-900/20 via-nova-900/20 to-neural-900/20 rounded-2xl">
        <Heading level={2} size="2xl" className="text-stardust-200 mb-4">
          Ready to Create?
        </Heading>
        <p className="text-stardust-400 max-w-xl mx-auto mb-8">
          Join thousands of creators and share your work with the community
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link href="/upload">
            <Button variant="primary" size="lg">
              Start Creating
            </Button>
          </Link>
          <Link href="/explore">
            <Button variant="outline" size="lg">
              Continue Exploring
            </Button>
          </Link>
        </div>
      </div>
    </Container>
  );
} 