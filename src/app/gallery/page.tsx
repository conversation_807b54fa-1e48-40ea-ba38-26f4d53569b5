"use client";

import { useState, useEffect, useRef, useCallback } from 'react';
import Link from "next/link";
import Image from "next/image";
import { AnimationWrapper } from "@/shared/components";
import Button from "@/shared/components/Button/Button";
import Card from "@/shared/components/Card/Card";
import Container from "@/shared/components/Container";
import Heading from "@/shared/components/Heading";
import Input from "@/shared/components/Input";
import Select from "@/shared/components/Select";
import { PerformanceMonitor, LazyWrapper } from "@/shared/components/Performance";
import {
  FiSearch, FiFilter, FiHeart, FiEye, FiShare2, FiDownload,
  FiMaximize2, FiUser, FiCalendar, FiTag, FiGrid, FiList,
  FiChevronLeft, FiChevronRight, FiX, FiZoomIn, FiZoomOut, FiArrowLeft
} from 'react-icons/fi';
// NEW: Import autonomous AI components for TIER 2 content curation assistance
import AutonomousHealthIndicator from "@/components/AutonomousCore/AutonomousHealthIndicator";
import AIDecisionStream from "@/components/AutonomousCore/AIDecisionStream";
import SystemStatusDashboard from "@/components/AutonomousCore/SystemStatusDashboard";

// Mock gallery data (in real app, this would come from API)
const GALLERY_ITEMS = Array.from({ length: 24 }, (_, i) => ({
  id: `item-${i + 1}`,
  title: `Creative Work ${i + 1}`,
  description: `A stunning piece of digital art showcasing innovative creativity and artistic vision. This work explores themes of ${['technology', 'nature', 'emotion', 'abstraction', 'minimalism', 'surrealism'][i % 6]}.`,
  image: `/api/placeholder/400x${300 + Math.floor(Math.random() * 400)}?id=${i + 1}`,
  creator: {
    name: `Artist ${i + 1}`,
    avatar: `/api/placeholder/40x40?id=user-${i + 1}`,
    verified: Math.random() > 0.7
  },
  stats: {
    views: Math.floor(Math.random() * 5000) + 100,
    likes: Math.floor(Math.random() * 500) + 10,
    downloads: Math.floor(Math.random() * 100) + 5
  },
  tags: ['digital-art', 'creative', 'design', 'artwork'][Math.floor(Math.random() * 4)],
  category: ['Digital Art', 'Photography', 'Illustration', '3D Art', 'AI Art'][Math.floor(Math.random() * 5)],
  createdAt: new Date(Date.now() - Math.random() * 10000000000),
  featured: Math.random() > 0.8,
  aspectRatio: Math.random() > 0.5 ? 'portrait' : 'landscape'
}));

// Filter options
const CATEGORIES = ['All', 'Digital Art', 'Photography', 'Illustration', '3D Art', 'AI Art'];
const SORT_OPTIONS = [
  { value: 'recent', label: 'Most Recent' },
  { value: 'popular', label: 'Most Popular' },
  { value: 'likes', label: 'Most Liked' },
  { value: 'views', label: 'Most Viewed' }
];

// Lightbox component
interface LightboxProps {
  items: typeof GALLERY_ITEMS;
  currentIndex: number;
  onClose: () => void;
  onNext: () => void;
  onPrev: () => void;
}

function Lightbox({ items, currentIndex, onClose, onNext, onPrev }: LightboxProps) {
  const [isZoomed, setIsZoomed] = useState(false);
  const currentItem = items[currentIndex];

  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'Escape') onClose();
      if (e.key === 'ArrowLeft') onPrev();
      if (e.key === 'ArrowRight') onNext();
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [onClose, onNext, onPrev]);

  if (!currentItem) return null;

  return (
    <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center" onClick={onClose}>
      {/* Close button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 z-10 p-3 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
      >
        <FiX className="w-6 h-6" />
      </button>

      {/* Navigation */}
      <button
        onClick={(e) => { e.stopPropagation(); onPrev(); }}
        className="absolute left-4 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
      >
        <FiChevronLeft className="w-6 h-6" />
      </button>

      <button
        onClick={(e) => { e.stopPropagation(); onNext(); }}
        className="absolute right-4 top-1/2 -translate-y-1/2 z-10 p-3 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
      >
        <FiChevronRight className="w-6 h-6" />
      </button>

      {/* Zoom controls */}
      <div className="absolute top-4 left-4 z-10 flex gap-2">
        <button
          onClick={(e) => { e.stopPropagation(); setIsZoomed(!isZoomed); }}
          className="p-3 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
        >
          {isZoomed ? <FiZoomOut className="w-5 h-5" /> : <FiZoomIn className="w-5 h-5" />}
        </button>
      </div>

      {/* Image */}
      <div
        className={`relative max-w-[90vw] max-h-[90vh] transition-transform duration-300 ${isZoomed ? 'cursor-zoom-out scale-150' : 'cursor-zoom-in scale-100'}`}
        onClick={(e) => { e.stopPropagation(); setIsZoomed(!isZoomed); }}
      >
        <Image
          src={currentItem.image}
          alt={currentItem.title}
          width={800}
          height={600}
          className="object-contain w-full h-full rounded-lg"
          priority
        />
      </div>

      {/* Image info */}
      <div className="absolute bottom-4 left-4 right-4 bg-black/70 backdrop-blur-sm rounded-lg p-4 text-white">
        <div className="flex items-start justify-between gap-4">
          <div>
            <h3 className="text-xl font-semibold mb-2">{currentItem.title}</h3>
            <p className="text-white/80 text-sm mb-2">{currentItem.description}</p>
            <div className="flex items-center gap-4 text-sm text-white/60">
              <span className="flex items-center gap-1">
                <FiUser className="w-4 h-4" />
                {currentItem.creator.name}
              </span>
              <span className="flex items-center gap-1">
                <FiEye className="w-4 h-4" />
                {currentItem.stats.views.toLocaleString()}
              </span>
              <span className="flex items-center gap-1">
                <FiHeart className="w-4 h-4" />
                {currentItem.stats.likes.toLocaleString()}
              </span>
            </div>
          </div>
          <div className="flex gap-2">
            <button className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
              <FiHeart className="w-5 h-5" />
            </button>
            <button className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
              <FiShare2 className="w-5 h-5" />
            </button>
            <button className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors">
              <FiDownload className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Image counter */}
      <div className="absolute top-4 left-1/2 -translate-x-1/2 bg-black/50 text-white px-4 py-2 rounded-full text-sm">
        {currentIndex + 1} of {items.length}
      </div>
    </div>
  );
}

// Lazy image component with progressive loading
interface LazyImageProps {
  item: typeof GALLERY_ITEMS[0];
  onClick: () => void;
  index: number;
}

function LazyImage({ item, onClick, index }: LazyImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isInView, setIsInView] = useState(false);
  const imgRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div ref={imgRef} className="opacity-0 animate-fade-in" style={{ animationDelay: `${index * 50}ms` }}>
      <Card 
        variant="elevated" 
        hover="glow" 
        className="overflow-hidden aspect-square group cursor-pointer"
        onClick={onClick}
      >
        {/* Image container */}
        <div className="relative w-full h-full">
          {isInView && (
            <>
              {/* Placeholder while loading */}
              {!isLoaded && (
                <div className="absolute inset-0 bg-gradient-to-br from-space-800 to-space-900 animate-pulse" />
              )}
              
              {/* Actual image */}
              <Image
                src={item.image}
                alt={item.title}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className={`object-cover transition-all duration-500 group-hover:scale-105 ${
                  isLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoadingComplete={() => setIsLoaded(true)}
              />
            </>
          )}
          
          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          
          {/* Content overlay */}
          <div className="absolute inset-0 p-4 flex flex-col justify-end opacity-0 group-hover:opacity-100 transition-all duration-300 translate-y-2 group-hover:translate-y-0">
            <h3 className="text-white font-semibold text-lg mb-1 line-clamp-1">{item.title}</h3>
            <p className="text-white/80 text-sm mb-2 line-clamp-2">{item.description}</p>
            
            {/* Stats */}
            <div className="flex items-center justify-between text-white/70 text-sm">
              <span className="flex items-center gap-1">
                <FiUser className="w-3 h-3" />
                {item.creator.name}
              </span>
              <div className="flex items-center gap-3">
                <span className="flex items-center gap-1">
                  <FiEye className="w-3 h-3" />
                  {item.stats.views}
                </span>
                <span className="flex items-center gap-1">
                  <FiHeart className="w-3 h-3" />
                  {item.stats.likes}
                </span>
              </div>
            </div>
          </div>
          
          {/* Action buttons */}
          <div className="absolute top-2 right-2 flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button 
              onClick={(e) => { e.stopPropagation(); /* handle like */ }}
              className="p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            >
              <FiHeart className="w-4 h-4" />
            </button>
            <button 
              onClick={(e) => { e.stopPropagation(); /* handle share */ }}
              className="p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
            >
              <FiShare2 className="w-4 h-4" />
            </button>
          </div>
          
          {/* Featured badge */}
          {item.featured && (
            <div className="absolute top-2 left-2">
              <span className="bg-cosmic-500 text-white text-xs px-2 py-1 rounded-full font-medium">
                Featured
              </span>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

export default function GalleryPage() {
  const [items, setItems] = useState(GALLERY_ITEMS);
  const [filteredItems, setFilteredItems] = useState(GALLERY_ITEMS);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [sortBy, setSortBy] = useState('recent');
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'masonry'>('masonry');
  const [lightboxIndex, setLightboxIndex] = useState<number | null>(null);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // NEW: AI-Assisted Content Curation state
  const [aiContentCurationMode, setAIContentCurationMode] = useState(false);
  const [curationTransparency, setCurationTransparency] = useState(false);
  const [autonomousContentFiltering, setAutonomousContentFiltering] = useState(false);
  const [intelligentContentRanking, setIntelligentContentRanking] = useState(false);
  const [aiCurationAnalysis, setAICurationAnalysis] = useState<string>("");
  const [contentRelevanceScore, setContentRelevanceScore] = useState(92);
  const [curatedRecommendations, setCuratedRecommendations] = useState<string[]>([]);
  const [activeTab, setActiveTab] = useState<'gallery' | 'ai-curation'>('gallery');

  // NEW: AI-assisted content curation functions
  const analyzeContentPatterns = async () => {
    try {
      const curationData = {
        totalItems: items.length,
        categories: CATEGORIES.slice(1), // exclude 'All'
        userPreferences: {
          viewingHistory: "digital_art_focused",
          interactionPatterns: "high_engagement",
          qualityThreshold: "professional"
        },
        contentMetrics: {
          averageViews: Math.floor(items.reduce((sum, item) => sum + item.stats.views, 0) / items.length),
          popularCategories: ["Digital Art", "AI Art", "Photography"]
        }
      };

      // AI analysis of content curation patterns
      const analysis = "Based on viewing patterns, AI recommends prioritizing high-engagement digital art and AI-generated content. Content relevance analysis shows 92% match with user preferences. Autonomous filtering suggests featuring trending artwork with 500+ views and promoting emerging artists with verified badges.";
      setAICurationAnalysis(analysis);
      
      const recommendations = [
        "Prioritize digital art content with high engagement metrics",
        "Feature AI-generated artwork trending in community",
        "Promote verified artists with consistent quality uploads",
        "Curate themed collections based on color analysis",
        "Highlight emerging talent with growth potential"
      ];
      setCuratedRecommendations(recommendations);
      
      setContentRelevanceScore(Math.floor(Math.random() * 10) + 90); // 90-100%
    } catch (error) {
      console.error('Content curation analysis error:', error);
    }
  };

  const enableAutonomousContentFiltering = async () => {
    try {
      setAutonomousContentFiltering(true);
      await analyzeContentPatterns();
      
      // Apply AI-driven content filtering
      const filteredContent = items.filter(item => 
        item.stats.views > 200 || item.creator.verified || item.featured
      );
      console.log("AI-filtered content:", filteredContent.length, "items");
    } catch (error) {
      console.error('Autonomous filtering error:', error);
    }
  };

  const enableIntelligentContentRanking = async () => {
    try {
      setIntelligentContentRanking(true);
      await analyzeContentPatterns();
      
      // AI-powered content ranking based on multiple factors
      const rankedContent = [...items].sort((a, b) => {
        const scoreA = (a.stats.views * 0.3) + (a.stats.likes * 0.4) + (a.creator.verified ? 100 : 0) + (a.featured ? 50 : 0);
        const scoreB = (b.stats.views * 0.3) + (b.stats.likes * 0.4) + (b.creator.verified ? 100 : 0) + (b.featured ? 50 : 0);
        return scoreB - scoreA;
      });
      console.log("AI-ranked content:", rankedContent.slice(0, 5).map(item => item.title));
    } catch (error) {
      console.error('Intelligent ranking error:', error);
    }
  };

  const toggleAIContentCurationMode = () => {
    setAIContentCurationMode(!aiContentCurationMode);
    if (!aiContentCurationMode) {
      analyzeContentPatterns();
    }
  };

  // Initialize AI analysis when curation mode is enabled
  useEffect(() => {
    if (aiContentCurationMode) {
      analyzeContentPatterns();
    }
  }, [aiContentCurationMode]);

  // Filter and sort items
  useEffect(() => {
    let filtered = [...items];

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(item => item.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(item => 
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.creator.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Sort items
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'popular':
          return b.stats.views - a.stats.views;
        case 'likes':
          return b.stats.likes - a.stats.likes;
        case 'views':
          return b.stats.views - a.stats.views;
        case 'recent':
        default:
          return b.createdAt.getTime() - a.createdAt.getTime();
      }
    });

    setFilteredItems(filtered);
  }, [items, selectedCategory, sortBy, searchQuery]);

  const openLightbox = useCallback((index: number) => {
    setLightboxIndex(index);
  }, []);

  const closeLightbox = useCallback(() => {
    setLightboxIndex(null);
  }, []);

  const nextImage = useCallback(() => {
    if (lightboxIndex !== null) {
      setLightboxIndex((lightboxIndex + 1) % filteredItems.length);
    }
  }, [lightboxIndex, filteredItems.length]);

  const prevImage = useCallback(() => {
    if (lightboxIndex !== null) {
      setLightboxIndex(lightboxIndex === 0 ? filteredItems.length - 1 : lightboxIndex - 1);
    }
  }, [lightboxIndex, filteredItems.length]);

  return (
    <>
      {/* TEMPORARILY DISABLED: PerformanceMonitor causing infinite re-renders */}
      {/* <PerformanceMonitor 
        componentName="GalleryPage"
        threshold={200}
        enableRenderTracking={true}
      > */}

      {/* Enhanced Background */}
      <div className="absolute inset-0 z-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-cosmic-50/50 via-transparent to-nova-50/50" />
        <div className="absolute inset-0 bg-gradient-to-tr from-neural-50/30 via-transparent to-aura-50/30" />
        {/* Floating orbs for visual depth */}
        <div className="absolute top-20 left-20 w-32 h-32 bg-cosmic-400/5 rounded-full blur-2xl animate-float" />
        <div className="absolute bottom-20 right-20 w-48 h-48 bg-nova-400/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
      </div>

      <Container className="relative z-10 py-16">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <Link
            href="/creative"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <FiArrowLeft className="w-4 h-4" />
            <span>Back to Creative Hub</span>
          </Link>
        </div>

        {/* Enhanced Hero Section with AI Content Curation Controls */}
        <AnimationWrapper animation="fadeIn" duration={600}>
          <div className="text-center mb-12">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-6">
              <div>
                <Heading level={1} size="2xl" className="text-gradient-cosmic mb-4">
                  Creative Gallery
                </Heading>
                <p className="text-stardust-400 text-lg max-w-2xl mx-auto">
                  Discover amazing creative work from talented artists around the world. 
                  Each piece tells a unique story of creativity and innovation.
                </p>
              </div>
              
              {/* NEW: AI Content Curation Controls */}
              <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                <button
                  onClick={toggleAIContentCurationMode}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    aiContentCurationMode
                      ? 'bg-cosmic-500 text-white hover:bg-cosmic-400'
                      : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                  }`}
                >
                  {aiContentCurationMode ? '🤖 AI Curation Active' : '🤖 Enable AI Curation'}
                </button>
                
                {aiContentCurationMode && (
                  <button
                    onClick={() => setCurationTransparency(!curationTransparency)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                      curationTransparency
                        ? 'bg-neural-500 text-white hover:bg-neural-400'
                        : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                    }`}
                  >
                    {curationTransparency ? '🔍 Transparency On' : '🔍 Show Transparency'}
                  </button>
                )}
              </div>
            </div>
          </div>
        </AnimationWrapper>

        {/* NEW: AI Content Curation Indicator */}
        {aiContentCurationMode && (
          <div className="theme-bg-neural/10 border-b theme-border-neural px-4 py-2 mb-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <AutonomousHealthIndicator 
                  variant="compact"
                  realTimeEnabled={aiContentCurationMode}
                  className="text-neural-400"
                />
                <div className="flex items-center space-x-2 text-sm theme-text-secondary">
                  <span>Content Relevance Score:</span>
                  <span className="font-mono text-neural-400">{contentRelevanceScore}%</span>
                </div>
              </div>
              
              {curationTransparency && (
                <div className="flex items-center space-x-2 text-xs theme-text-secondary">
                  <div className="w-2 h-2 bg-neural-400 rounded-full animate-pulse"></div>
                  <span>AI Curation Transparency Active</span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* NEW: Tab Navigation for Gallery and AI Curation */}
        {aiContentCurationMode && (
          <div className="mb-6">
            <div className="flex space-x-1 p-1 bg-space-800/50 rounded-lg max-w-md">
              <button
                onClick={() => setActiveTab('gallery')}
                className={`flex-1 p-3 rounded-md text-sm font-medium transition-all ${
                  activeTab === 'gallery'
                    ? 'bg-cosmic-500 text-white'
                    : 'text-stardust-300 hover:text-white hover:bg-space-700'
                }`}
              >
                🎨 Gallery View
              </button>
              <button
                onClick={() => setActiveTab('ai-curation')}
                className={`flex-1 p-3 rounded-md text-sm font-medium transition-all ${
                  activeTab === 'ai-curation'
                    ? 'bg-cosmic-500 text-white'
                    : 'text-stardust-300 hover:text-white hover:bg-space-700'
                }`}
              >
                🤖 AI Curation
              </button>
            </div>
          </div>
        )}

        {/* Gallery Tab Content */}
        {activeTab === 'gallery' && (
          <>
            {/* Enhanced Search and Filter Bar */}
            <AnimationWrapper animation="slideUp" duration={600} delay={200}>
              <Card variant="glass" className="p-6 mb-8">
                <div className="flex flex-col lg:flex-row gap-4">
                  {/* Search */}
                  <div className="flex-1">
                    <Input
                      variant="glass"
                      placeholder="Search gallery..."
                      value={searchQuery}
                      onChange={(value) => setSearchQuery(value)}
                      leftIcon={<FiSearch className="text-stardust-400" />}
                      fullWidth
                    />
                  </div>
                  
                  {/* Filters */}
                  <div className="flex gap-3">
                    <Select
                      variant="glass"
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      options={CATEGORIES.map(cat => ({ value: cat, label: cat }))}
                    />
                    
                    <Select
                      variant="glass"
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      options={SORT_OPTIONS}
                    />
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setViewMode(viewMode === 'masonry' ? 'grid' : 'masonry')}
                    >
                      {viewMode === 'masonry' ? <FiList className="w-4 h-4 mr-2" /> : <FiGrid className="w-4 h-4 mr-2" />}
                      {viewMode === 'masonry' ? 'List' : 'Grid'}
                    </Button>
                  </div>
                </div>
              </Card>
            </AnimationWrapper>

        {/* Enhanced Gallery Grid */}
        <AnimationWrapper animation="slideUp" duration={600} delay={400}>
          <div className="mb-8">
            {filteredItems.length > 0 ? (
              <div className={`grid gap-6 ${
                viewMode === 'masonry' 
                  ? "columns-1 md:columns-2 lg:columns-3 xl:columns-4"
                  : "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
              }`}>
                {filteredItems.map((item, index) => (
                  <div key={item.id} className={viewMode === 'masonry' ? "break-inside-avoid mb-6" : ""}>
                    <LazyWrapper>
                      <LazyImage
                        item={item}
                        index={index}
                        onClick={() => openLightbox(index)}
                      />
                    </LazyWrapper>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="text-6xl mb-4">🎨</div>
                <Heading level={3} size="xl" className="text-stardust-300 mb-3">
                  No artwork found
                </Heading>
                <p className="text-stardust-400 mb-6">
                  Try adjusting your search terms or filters to find more creative work.
                </p>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedCategory('All');
                    setSortBy('recent');
                  }}
                >
                  Clear Filters
                </Button>
              </div>
            )}
          </div>
        </AnimationWrapper>
        </>
        )}

        {/* NEW: AI Content Curation Tab */}
        {activeTab === 'ai-curation' && aiContentCurationMode && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* AI Curation Controls Panel */}
              <div className="space-y-6">
                <Card variant="glass" className="p-6">
                  <h3 className="text-lg font-display text-neural-400 mb-4">🤖 AI Content Curation Assistant</h3>
                  
                  {/* AI Curation Mode Controls */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">Autonomous Content Filtering</span>
                      <button
                        onClick={enableAutonomousContentFiltering}
                        className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                          autonomousContentFiltering
                            ? 'bg-cosmic-500 text-white'
                            : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                        }`}
                      >
                        {autonomousContentFiltering ? 'Active' : 'Enable'}
                      </button>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">Intelligent Content Ranking</span>
                      <button
                        onClick={enableIntelligentContentRanking}
                        className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                          intelligentContentRanking
                            ? 'bg-cosmic-500 text-white'
                            : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                        }`}
                      >
                        {intelligentContentRanking ? 'Active' : 'Enable'}
                      </button>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">AI Curation Transparency</span>
                      <button
                        onClick={() => setCurationTransparency(!curationTransparency)}
                        className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                          curationTransparency
                            ? 'bg-neural-500 text-white'
                            : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                        }`}
                      >
                        {curationTransparency ? 'On' : 'Off'}
                      </button>
                    </div>
                  </div>

                  {/* Content Relevance Score */}
                  <div className="mt-6 p-4 bg-space-800/30 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-stardust-300">Content Relevance Score</span>
                      <span className="text-lg font-bold text-neural-400">{contentRelevanceScore}%</span>
                    </div>
                    <div className="w-full bg-space-700 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-neural-500 to-cosmic-500 h-2 rounded-full transition-all duration-1000"
                        style={{ width: `${contentRelevanceScore}%` }}
                      ></div>
                    </div>
                  </div>
                </Card>

                {/* AI Curation Analysis */}
                <Card variant="glass" className="p-6">
                  <h4 className="text-md font-display text-cosmic-400 mb-3">📊 AI Curation Analysis</h4>
                  <div className="space-y-3">
                    <div className="p-3 bg-space-800/30 rounded-lg">
                      <p className="text-sm text-stardust-300 leading-relaxed">
                        {aiCurationAnalysis || "Enable AI curation to see intelligent content analysis..."}
                      </p>
                    </div>
                    
                    {curatedRecommendations.length > 0 && (
                      <div className="space-y-2">
                        <h5 className="text-sm font-medium text-neural-400">AI Recommendations:</h5>
                        {curatedRecommendations.map((recommendation, index) => (
                          <div key={index} className="flex items-start space-x-2 text-xs text-stardust-400">
                            <span className="text-cosmic-400 mt-1">•</span>
                            <span>{recommendation}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </Card>
              </div>

              {/* AI Decision Stream and System Status */}
              <div className="space-y-6">
                <Card variant="glass" className="p-6">
                  <h4 className="text-md font-display text-cosmic-400 mb-3">🔍 AI Decision Stream</h4>
                  {curationTransparency && (
                    <AIDecisionStream 
                      maxDecisions={15}
                      showFilters={true}
                      autoScroll={true}
                      realTimeEnabled={curationTransparency}
                    />
                  )}
                  {!curationTransparency && (
                    <div className="p-4 text-center text-stardust-400 text-sm">
                      Enable transparency to see AI decision stream
                    </div>
                  )}
                </Card>

                <Card variant="glass" className="p-6">
                  <h4 className="text-md font-display text-cosmic-400 mb-3">📈 System Status</h4>
                  <div className="max-h-64 overflow-y-auto">
                    <SystemStatusDashboard />
                  </div>
                </Card>
              </div>
            </div>
          </div>
        )}
      </Container>

      {/* Lightbox */}
      {lightboxIndex !== null && (
        <Lightbox
          items={filteredItems}
          currentIndex={lightboxIndex}
          onClose={closeLightbox}
          onNext={nextImage}
          onPrev={prevImage}
        />
      )}
      {/* </PerformanceMonitor> */}
    </>
  );
} 