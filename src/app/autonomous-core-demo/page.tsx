/**
 * Autonomous Core Components Demo Page
 * Day 3: Advanced frontend components showcase
 * 
 * Demonstrates:
 * - AIDecisionStream: Real-time AI decision transparency
 * - AgentObservationPanel: Individual agent monitoring
 * - AutonomousHealthIndicator: System health overview
 * - Mobile-first responsive design
 * - Component integration patterns
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import {
  AIDecisionStream,
  AgentObservationPanel,
  AutonomousHealthIndicator
} from '@/components/AutonomousCore';

export default function AutonomousCoreDemo() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-space-900">
      {/* Header */}
      <div className="p-6 border-b border-gray-700">
        <div className="max-w-7xl mx-auto">
          {/* Breadcrumb Navigation */}
          <div className="mb-6">
            <Link
              href="/intelligence"
              className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Intelligence Hub</span>
            </Link>
          </div>

          <div className="mb-6">
            <h1 className="text-3xl font-bold text-gradient-cosmic mb-2">
              Autonomous Core Components
            </h1>
            <p className="text-lg text-gray-300 mb-4">
              Live demonstration of autonomous AI systems and decision transparency
            </p>
            
            {/* Phase Information */}
            <div className="flex flex-wrap gap-4">
              <div className="bg-cosmic-900/30 border border-cosmic-500/30 rounded-lg px-4 py-2">
                <span className="text-cosmic-400 font-medium">Live Autonomous Systems</span>
                <p className="text-sm text-gray-400 mt-1">
                  Real-time monitoring of 28 AI agents and autonomous decision making
                </p>
              </div>
              <div className="bg-nova-900/30 border border-nova-500/30 rounded-lg px-4 py-2">
                <span className="text-nova-400 font-medium">Intelligence Hub</span>
                <p className="text-sm text-gray-400 mt-1">
                  Advanced AI observation and transparency interfaces
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="max-w-7xl mx-auto space-y-8">
          
          {/* System Health Overview */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">System Health Overview</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <AutonomousHealthIndicator 
                variant="compact" 
                showExpanded={true}
                className="lg:col-span-2"
              />
              <AutonomousHealthIndicator 
                variant="minimal" 
                className="flex items-center justify-center p-6"
              />
            </div>
          </section>

          {/* AI Decision Transparency */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">AI Decision Transparency</h2>
            <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
              <div className="xl:col-span-2">
                <AIDecisionStream 
                  showFilters={true}
                  maxDecisions={20}
                  autoScroll={true}
                  realTimeEnabled={true}
                />
              </div>
              <div className="space-y-4">
                <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-3">Decision Types</h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-blue-400">Analysis</span>
                      <span className="text-gray-400">System evaluation</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-green-400">Action</span>
                      <span className="text-gray-400">Direct execution</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-purple-400">Optimization</span>
                      <span className="text-gray-400">Performance tuning</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-yellow-400">Response</span>
                      <span className="text-gray-400">User interaction</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-white mb-3">Real-Time Features</h3>
                  <div className="space-y-2 text-sm text-gray-300">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      Live decision streaming
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                      Agent filtering & search
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                      Confidence scoring
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                      Impact assessment
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Agent Observation Panels */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">Individual Agent Monitoring</h2>
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
              <AgentObservationPanel
                agentName="DevAgent"
                agentType="IntelligenceEnhanced"
                status="active"
                realTimeEnabled={true}
                onStatusChange={(status) => console.log('DevAgent status:', status)}
                onConfigureAgent={() => console.log('Configure DevAgent')}
              />
              <AgentObservationPanel
                agentName="UIAgent"
                agentType="IntelligenceEnhanced"
                status="idle"
                realTimeEnabled={true}
                onStatusChange={(status) => console.log('UIAgent status:', status)}
                onConfigureAgent={() => console.log('Configure UIAgent')}
              />
            </div>
          </section>

          {/* Mobile Demo Section */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">Mobile-First Design Demo</h2>
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-cosmic-400">Compact Health</h3>
                  <AutonomousHealthIndicator variant="compact" />
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-nova-400">Minimal Health</h3>
                  <AutonomousHealthIndicator variant="minimal" />
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-neural-400">Touch Targets</h3>
                  <div className="space-y-2">
                    <button className="w-full h-11 bg-cosmic-600 hover:bg-cosmic-700 text-white rounded-lg transition-colors">
                      44px Touch Target
                    </button>
                    <button className="w-full h-11 bg-nova-600 hover:bg-nova-700 text-white rounded-lg transition-colors">
                      iOS Accessible
                    </button>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-quantum-400">Responsive Grid</h3>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="h-8 bg-gray-600 rounded"></div>
                    <div className="h-8 bg-gray-600 rounded"></div>
                    <div className="h-8 bg-gray-600 rounded"></div>
                    <div className="h-8 bg-gray-600 rounded"></div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Integration Information */}
          <section>
            <h2 className="text-2xl font-bold text-white mb-4">Phase 2 Integration Ready</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-green-400 mb-3">WebSocket Preparation</h3>
                <div className="space-y-2 text-sm text-gray-300">
                  <div>✅ Connection status indicators</div>
                  <div>✅ Real-time data streaming architecture</div>
                  <div>✅ Automatic reconnection handling</div>
                  <div>✅ Offline state management</div>
                </div>
              </div>
              
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-blue-400 mb-3">Agent Integration</h3>
                <div className="space-y-2 text-sm text-gray-300">
                  <div>✅ 28-agent ecosystem awareness</div>
                  <div>✅ Individual agent control interfaces</div>
                  <div>✅ Performance metrics collection</div>
                  <div>✅ Status change handling</div>
                </div>
              </div>
              
              <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6">
                <h3 className="text-lg font-semibold text-purple-400 mb-3">AI Transparency</h3>
                <div className="space-y-2 text-sm text-gray-300">
                  <div>✅ Decision reasoning display</div>
                  <div>✅ Confidence scoring</div>
                  <div>✅ Impact assessment</div>
                  <div>✅ Dependency tracking</div>
                </div>
              </div>
            </div>
          </section>

          {/* Development Information */}
          <section className="pb-8">
            <div className="bg-gradient-to-r from-cosmic-900/20 to-nova-900/20 border border-cosmic-500/30 rounded-lg p-6">
              <h3 className="text-xl font-bold text-white mb-4">🎯 Day 3 Development Status</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
                <div>
                  <h4 className="font-semibold text-cosmic-400 mb-2">✅ Components Created</h4>
                  <ul className="space-y-1 text-gray-300">
                    <li>• AIDecisionStream</li>
                    <li>• AgentObservationPanel</li>
                    <li>• AutonomousHealthIndicator</li>
                    <li>• Comprehensive demo page</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-nova-400 mb-2">🚀 Features Implemented</h4>
                  <ul className="space-y-1 text-gray-300">
                    <li>• Real-time data simulation</li>
                    <li>• Mobile-first responsive design</li>
                    <li>• Agent control interfaces</li>
                    <li>• System health monitoring</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-neural-400 mb-2">📋 Next Steps</h4>
                  <ul className="space-y-1 text-gray-300">
                    <li>• Day 4: AgentEcosystemHub</li>
                    <li>• Mobile AI interfaces</li>
                    <li>• 5-tab navigation system</li>
                    <li>• Desktop AI dashboard</li>
                  </ul>
                </div>
              </div>
            </div>
          </section>

        </div>
      </div>
    </div>
  );
} 