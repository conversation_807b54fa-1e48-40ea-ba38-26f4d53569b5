/**
 * Models Management Page
 * Main interface for managing AI models with touch-optimized controls
 * Following Sequential Development Mandate - Model Management Interface UI
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { ArrowLeft, Brain } from 'lucide-react';
import Container from '@/shared/components/Container';
import ModelManagementInterface from '@/components/models/ModelManagementInterface';

export default function ModelsPage() {
  const handleModelSelect = (modelId: string) => {
    console.log('Model selected:', modelId);
  };

  const handleModelAction = (modelId: string, action: string) => {
    console.log(`Model ${modelId} action: ${action}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-space-900">
      <Container>
        <div className="py-8">
          {/* Breadcrumb Navigation */}
          <div className="mb-6">
            <Link
              href="/intelligence"
              className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Intelligence Hub</span>
            </Link>
          </div>

          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="h-12 w-12 bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-lg flex items-center justify-center">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-cosmic-400 via-nova-400 to-neural-400 bg-clip-text text-transparent">
                  AI Models Management
                </h1>
                <p className="text-stardust-300 text-lg">
                  Configure and manage AI models for the 28-agent ecosystem
                </p>
              </div>
            </div>
          </div>

          {/* Model Management Interface */}
          <ModelManagementInterface
            onModelSelect={handleModelSelect}
            onModelAction={handleModelAction}
          />
        </div>
      </Container>
    </div>
  );
} 