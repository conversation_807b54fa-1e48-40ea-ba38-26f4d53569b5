@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== CRITICAL CSS FIXES FOR TEXT-ONLY DISPLAY ===== */
@layer base {
  html, body {
    background-color: #080318;
    color: #d4d4ff;
    font-family: 'Barlow', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  }
}

@layer components {
  /* ===== ESSENTIAL MISSING CLASSES ONLY ===== */
  
  /* Theme card - used everywhere */
  .theme-card {
    background-color: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(16px);
    border-radius: 1rem;
    padding: 1.5rem;
    transition: all 0.3s ease;
  }
  
  .theme-card:hover {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(110, 122, 255, 0.3);
  }
  
  /* Text gradients */
  .text-gradient-cosmic {
    background: linear-gradient(135deg, #6E7AFF, #A1F5FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-nova {
    background: linear-gradient(135deg, #FF78F7, #6E7AFF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-neural {
    background: linear-gradient(135deg, #A1F5FF, #F1E0FF);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-quantum {
    background: linear-gradient(135deg, #F1E0FF, #FFC366);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-aura {
    background: linear-gradient(135deg, #FFC366, #FF78F7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .text-gradient-multi {
    background: linear-gradient(135deg, #6E7AFF, #FF78F7, #A1F5FF, #FFC366);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Brand backgrounds */
  .brand-cosmic-bg {
    background-color: #6E7AFF;
  }
  
  /* Essential utility classes */
  .theme-spacing-responsive {
    padding: clamp(1rem, 3vw, 2rem);
  }
  
  .bg-adaptive {
    background-color: #080318;
  }
  
  .theme-text-primary {
    color: #d4d4ff;
  }
  
  /* Typography */
  .heading-1 {
    font-family: 'Rajdhani', sans-serif;
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 700;
    line-height: 1.1;
  }
  
  .body-large {
    font-family: 'Barlow', sans-serif;
    font-size: clamp(1.125rem, 3vw, 1.25rem);
    font-weight: 400;
    line-height: 1.6;
  }
  
  /* Enhanced Animations for Dashboard Polish */
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out;
  }
  
  .animate-pulse-glow {
    animation: pulseGlow 3s ease-in-out infinite;
  }
  
  /* Enhanced Shadows */
  .shadow-cosmic {
    box-shadow: 0 10px 40px rgba(110, 122, 255, 0.3);
  }
  
  .text-shadow-cosmic {
    text-shadow: 0 0 20px rgba(110, 122, 255, 0.5);
  }
  
  .text-shadow-enhanced {
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  /* Enhanced scrollbar styling */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgba(110, 122, 255, 0.5) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(110, 122, 255, 0.5);
    border-radius: 3px;
  }
  
  .scrollbar-thumb-cosmic-500\/50::-webkit-scrollbar-thumb {
    background-color: rgba(110, 122, 255, 0.5);
  }
  
  /* Neo-UI Components */
  .neo-panel {
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(12px);
    border-radius: 0.75rem;
    transition: all 0.3s ease;
  }
  
  .neo-panel:hover {
    background: rgba(255, 255, 255, 0.06);
    border-color: rgba(110, 122, 255, 0.2);
    transform: translateY(-2px);
  }
  
  .neo-card {
    background: rgba(8, 3, 24, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 1rem;
    transition: all 0.4s ease;
  }
  
  .neo-card:hover {
    background: rgba(8, 3, 24, 0.9);
    border-color: rgba(110, 122, 255, 0.3);
    transform: translateY(-4px);
  }
  
  /* Color helpers */
  .text-primary-enhanced { color: #E8E8FF !important; }
  .text-secondary-enhanced { color: #B8B8D4 !important; }
  .text-muted-enhanced { color: #9090B0 !important; }
  
  /* ===== TAILWIND FALLBACKS (SIMPLE) ===== */
  .bg-cosmic-500 { background-color: #6E7AFF !important; }
  .bg-cosmic-600 { background-color: #5361F5 !important; }
  .text-cosmic-400 { color: #8B95FF !important; }
  .text-cosmic-300 { color: #A8B2FF !important; }
  .text-nova-300 { color: #FF9DFA !important; }
  .text-nova-400 { color: #FF78F7 !important; }
  .text-neural-300 { color: #B8F7FF !important; }
  .text-neural-400 { color: #A1F5FF !important; }
  .text-aura-300 { color: #FFD699 !important; }
  .text-aura-400 { color: #FFC366 !important; }
  .text-stardust-400 { color: #E0E0FF !important; }
  .bg-space-900 { background-color: #0C0522 !important; }
  .bg-space-800 { background-color: #151040 !important; }
  .bg-space-700 { background-color: #202254 !important; }
  .bg-space-600 { background-color: #2C2D68 !important; }
  .rounded-lg { border-radius: 0.5rem !important; }
  
  /* Gradient backgrounds */
  .bg-gradient-cosmic { background: linear-gradient(135deg, #6E7AFF, #A1F5FF) !important; }
  .bg-gradient-nova { background: linear-gradient(135deg, #FF78F7, #6E7AFF) !important; }
  .bg-gradient-neural { background: linear-gradient(135deg, #A1F5FF, #F1E0FF) !important; }
  .bg-gradient-aura { background: linear-gradient(135deg, #FFC366, #FF78F7) !important; }
  .bg-gradient-multi { background: linear-gradient(135deg, #6E7AFF, #FF78F7, #A1F5FF, #FFC366) !important; }
}

/* Enhanced Keyframe Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes shimmer {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(110, 122, 255, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(110, 122, 255, 0.6);
  }
}

/* Enhanced UI Polish Animations */
@keyframes cosmicPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(110, 122, 255, 0.4), 0 0 40px rgba(147, 51, 234, 0.2);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 30px rgba(110, 122, 255, 0.6), 0 0 60px rgba(147, 51, 234, 0.4);
    transform: scale(1.02);
  }
}

@keyframes glowRotate {
  0% {
    transform: rotate(0deg);
    filter: hue-rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
    filter: hue-rotate(360deg);
  }
}

@keyframes slideInBlur {
  from {
    opacity: 0;
    transform: translateX(100px);
    filter: blur(10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
    filter: blur(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3) translateY(-50px);
  }
  50% {
    opacity: 1;
    transform: scale(1.05) translateY(0);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes typewriter {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

/* Enhanced Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-cosmic-pulse {
  animation: cosmicPulse 2s ease-in-out infinite;
}

.animate-glow-rotate {
  animation: glowRotate 4s linear infinite;
}

.animate-slide-in-blur {
  animation: slideInBlur 0.6s ease-out;
}

.animate-bounce-in {
  animation: bounceIn 0.8s ease-out;
}

.animate-typewriter {
  animation: typewriter 2s steps(40, end);
}

.animate-blink {
  animation: blink 1s infinite;
}

.animate-ripple {
  animation: ripple 0.6s ease-out;
}

/* Enhanced Hover Effects */
.hover-glow:hover {
  box-shadow: 0 0 30px rgba(110, 122, 255, 0.5);
  transition: box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  transition: transform 0.3s ease;
}

.hover-scale:hover {
  transform: scale(1.05);
  transition: transform 0.3s ease;
}

/* Slider Styling */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6E7AFF, #A1F5FF);
  cursor: pointer;
  box-shadow: 0 0 10px rgba(110, 122, 255, 0.5);
  transition: all 0.3s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(110, 122, 255, 0.8);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: linear-gradient(135deg, #6E7AFF, #A1F5FF);
  cursor: pointer;
  border: none;
  box-shadow: 0 0 10px rgba(110, 122, 255, 0.5);
  transition: all 0.3s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 15px rgba(110, 122, 255, 0.8);
}
