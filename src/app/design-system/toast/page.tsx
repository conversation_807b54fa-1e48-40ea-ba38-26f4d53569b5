"use client";

import { useState } from "react";
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import Button from "@/shared/components/Button";
import Heading from "@/shared/components/Heading";
import Container from "@/shared/components/Container";
import Card from "@/shared/components/Card";
import Select from "@/shared/components/Select";
import Input from "@/shared/components/Input";
import Toggle from "@/shared/components/Toggle";

export default function ToastDemoPage() {
  const [message, setMessage] = useState("This is a toast notification");
  const [duration, setDuration] = useState(5000);
  const [withAction, setWithAction] = useState(false);
  const [demoToasts, setDemoToasts] = useState<Array<{id: string; type: string; message: string; timestamp: number}>>([]);
  
  const showToast = (type: "info" | "success" | "warning" | "error") => {
    // For demo purposes, show toast information in console and update demo state
    console.log(`Toast ${type.toUpperCase()}: ${message} (Duration: ${duration}ms, WithAction: ${withAction})`);
    
    // Add to demo toasts for visual representation
    const newToast = {
      id: Date.now().toString(),
      type,
      message,
      timestamp: Date.now()
    };
    
    setDemoToasts(prev => [...prev, newToast]);
    
    // Remove after duration for demo
    setTimeout(() => {
      setDemoToasts(prev => prev.filter(t => t.id !== newToast.id));
    }, duration);
  };
  
  return (
    <Container className="py-16">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <Link
          href="/design-system"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Design System</span>
        </Link>
      </div>

      <Heading level={1} gradient size="4xl" className="mb-6">
        Toast Notifications
      </Heading>
      <p className="text-xl mb-12 text-stardust-300">
        Non-intrusive notifications that appear temporarily at the edge of the screen.
      </p>
      
      <div className="space-y-12">
        {/* Basic Toasts */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Toast Types
            </Heading>
            <p className="mb-6 text-stardust-400">
              Toast notifications come in four different styles to match different types of information.
            </p>
            
            <div className="flex flex-wrap gap-4">
              <Button onClick={() => showToast("info")} variant="glass">
                Info Toast
              </Button>
              <Button onClick={() => showToast("success")} variant="primary">
                Success Toast
              </Button>
              <Button onClick={() => showToast("warning")} variant="outline">
                Warning Toast
              </Button>
              <Button onClick={() => showToast("error")} variant="secondary">
                Error Toast
              </Button>
            </div>
          </Card>
        </section>
        
        {/* Custom Toast Options */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Custom Toast Options
            </Heading>
            <p className="mb-6 text-stardust-400">
              Customize the behavior and content of your toast notifications.
            </p>
            
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Notification Message"
                  value={message}
                  onChange={(value: string) => setMessage(value)}
                  placeholder="Enter message text"
                  fullWidth
                />
                
                <div>
                  <label className="block text-sm font-medium text-stardust-300 mb-1.5">
                    Duration (ms)
                  </label>
                  <div className="flex items-center space-x-4">
                    <input
                      type="range"
                      min="1000"
                      max="10000"
                      step="1000"
                      value={duration}
                      onChange={(e) => setDuration(Number(e.target.value))}
                      className="flex-grow"
                    />
                    <span className="text-sm text-stardust-400 w-16 text-right">
                      {duration}ms
                    </span>
                  </div>
                </div>
              </div>
              
              <Toggle
                label="Include Action Button"
                checked={withAction}
                onChange={() => setWithAction(!withAction)}
              />
              
              <div className="flex flex-wrap gap-4 pt-4">
                <Button onClick={() => showToast("info")}>
                  Show Custom Toast
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    showToast("info");
                    setTimeout(() => showToast("success"), 500);
                    setTimeout(() => showToast("warning"), 1000);
                  }}
                >
                  Show Multiple Toasts
                </Button>
                <Button 
                  variant="ghost" 
                  onClick={() => setDemoToasts([])}
                >
                  Clear All Toasts
                </Button>
              </div>
            </div>
          </Card>
        </section>

        {/* Demo Toast Area */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Toast Demo Area
            </Heading>
            <p className="mb-6 text-stardust-400">
              Visual representation of toast notifications (for demo purposes).
            </p>
            
            <div className="relative h-40 bg-space-900/50 rounded-lg border border-space-700 overflow-hidden">
              {demoToasts.length === 0 ? (
                <div className="flex items-center justify-center h-full text-stardust-500">
                  No active toasts - click buttons above to see demo
                </div>
              ) : (
                <div className="absolute top-4 right-4 space-y-2">
                  {demoToasts.map((toast, index) => (
                    <div
                      key={toast.id}
                      className={`
                        max-w-sm p-3 rounded-lg shadow-lg border transform transition-all duration-300
                        ${toast.type === 'success' ? 'bg-neural-900/90 border-neural-500 text-neural-200' : ''}
                        ${toast.type === 'info' ? 'bg-cosmic-900/90 border-cosmic-500 text-cosmic-200' : ''}
                        ${toast.type === 'warning' ? 'bg-aura-900/90 border-aura-500 text-aura-200' : ''}
                        ${toast.type === 'error' ? 'bg-nova-900/90 border-nova-500 text-nova-200' : ''}
                        animate-slide-in-right
                      `}
                      style={{
                        animationDelay: `${index * 100}ms`
                      }}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center">
                          <span className="capitalize font-medium mr-2">{toast.type}:</span>
                          <span className="text-sm">{toast.message}</span>
                        </div>
                        <button
                          onClick={() => setDemoToasts(prev => prev.filter(t => t.id !== toast.id))}
                          className="ml-2 text-stardust-400 hover:text-stardust-200"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </section>
      </div>
    </Container>
  );
} 