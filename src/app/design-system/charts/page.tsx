"use client";

import { useState } from "react";
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import Heading from "@/shared/components/Heading";
import Container from "@/shared/components/Container";
import Card from "@/shared/components/Card";
import Button from "@/shared/components/Button";
import Select from "@/shared/components/Select";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/shared/components/Chart";
import Toggle from "@/shared/components/Toggle";

// Sample data for charts
const lineChartData = {
  labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"],
  datasets: [
    {
      label: "User Growth",
      data: [23, 45, 67, 78, 92, 110, 125],
    },
    {
      label: "Content Created",
      data: [53, 75, 93, 108, 142, 160, 185],
    }
  ],
};

const barChartData = {
  labels: ["Visual Arts", "Music", "Writing", "3D Models", "Photography", "Game Dev"],
  datasets: [
    {
      label: "Active Creators",
      data: [340, 230, 420, 150, 310, 180],
    }
  ],
};

const pieChartData = {
  labels: ["Visual Arts", "Music", "Writing", "3D Models", "Photography", "Game Dev"],
  datasets: [
    {
      data: [30, 22, 18, 10, 14, 6],
    }
  ],
};

export default function ChartsDemoPage() {
  const [lineTheme, setLineTheme] = useState("cosmic");
  const [lineGradient, setLineGradient] = useState(true);
  const [lineAnimated, setLineAnimated] = useState(true);
  
  const [barTheme, setBarTheme] = useState("neural");
  const [barGradient, setBarGradient] = useState(true);
  const [barRounded, setBarRounded] = useState(true);
  
  const [pieTheme, setPieTheme] = useState("custom");
  const [pieType, setPieType] = useState("pie");
  
  const themes = [
    { value: "cosmic", label: "Cosmic" },
    { value: "nova", label: "Nova" },
    { value: "neural", label: "Neural" },
    { value: "quantum", label: "Quantum" },
    { value: "aura", label: "Aura" },
    { value: "mixed", label: "Mixed" },
    { value: "custom", label: "Custom" }
  ];
  
  return (
    <Container className="py-16">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <Link
          href="/design-system"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Design System</span>
        </Link>
      </div>

      <Heading level={1} gradient size="4xl" className="mb-6">
        Data Visualization
      </Heading>
      <p className="text-xl mb-12 text-stardust-300">
        Beautiful, responsive chart components for visualizing data with cosmic flair.
      </p>
      
      <div className="space-y-16">
        {/* Line Chart */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Line Chart
            </Heading>
            <p className="mb-6 text-stardust-400">
              Visualize trends and changes over time with smooth, animated line charts.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div>
                <Select
                  label="Theme"
                  options={themes.filter(t => t.value !== "custom")}
                  value={lineTheme}
                  onChange={(e) => setLineTheme(e.target.value)}
                />
              </div>
              <div>
                <Toggle
                  label="Gradient Fill"
                  checked={lineGradient}
                  onChange={() => setLineGradient(!lineGradient)}
                />
              </div>
              <div>
                <Toggle
                  label="Animation"
                  checked={lineAnimated}
                  onChange={() => setLineAnimated(!lineAnimated)}
                />
              </div>
            </div>
            
            <div className="h-80">
              <LineChart
                data={lineChartData}
                theme={lineTheme as any}
                gradient={lineGradient}
                animated={lineAnimated}
              />
            </div>
          </Card>
        </section>
        
        {/* Bar Chart */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Bar Chart
            </Heading>
            <p className="mb-6 text-stardust-400">
              Display categorical data with colorful, customizable bar charts.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div>
                <Select
                  label="Theme"
                  options={themes.filter(t => t.value !== "custom" && t.value !== "mixed")}
                  value={barTheme}
                  onChange={(e) => setBarTheme(e.target.value)}
                />
              </div>
              <div>
                <Toggle
                  label="Gradient Fill"
                  checked={barGradient}
                  onChange={() => setBarGradient(!barGradient)}
                />
              </div>
              <div>
                <Toggle
                  label="Rounded Bars"
                  checked={barRounded}
                  onChange={() => setBarRounded(!barRounded)}
                />
              </div>
            </div>
            
            <div className="h-80">
              <BarChart
                data={barChartData}
                theme={barTheme as any}
                gradient={barGradient}
                rounded={barRounded}
              />
            </div>
          </Card>
        </section>
        
        {/* Pie & Doughnut Chart */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Pie & Doughnut Charts
            </Heading>
            <p className="mb-6 text-stardust-400">
              Show composition and part-to-whole relationships with vibrant circular charts.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <Select
                  label="Theme"
                  options={themes}
                  value={pieTheme}
                  onChange={(e) => setPieTheme(e.target.value)}
                />
              </div>
              <div>
                <div className="flex space-x-4">
                  <Button
                    variant={pieType === "pie" ? "primary" : "outline"}
                    onClick={() => setPieType("pie")}
                  >
                    Pie Chart
                  </Button>
                  <Button
                    variant={pieType === "doughnut" ? "primary" : "outline"}
                    onClick={() => setPieType("doughnut")}
                  >
                    Doughnut Chart
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="h-80">
              <PieChart
                data={pieChartData}
                theme={pieTheme as any}
                type={pieType as "pie" | "doughnut"}
              />
            </div>
          </Card>
        </section>
      </div>
    </Container>
  );
} 