"use client";

import { useState } from "react";
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import Heading from "@/shared/components/Heading";
import Container from "@/shared/components/Container";
import Card from "@/shared/components/Card";
import Button from "@/shared/components/Button";
import Badge from "@/shared/components/Badge";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/shared/components/Chart";
import Input from "@/shared/components/Input";

// Sample data for charts
const growthData = {
  labels: ["Jan", "Feb", "Mar", "Apr", "May", "Jun"],
  datasets: [
    {
      label: "New Users",
      data: [128, 234, 356, 410, 567, 682],
    },
    {
      label: "Active Users",
      data: [210, 308, 429, 587, 692, 810],
    }
  ],
};

const creationStats = {
  labels: ["Visual Art", "Music", "Writing", "3D Models", "Video", "Other"],
  datasets: [
    {
      label: "Content Created (Last 30 Days)",
      data: [420, 310, 280, 150, 230, 90],
    }
  ],
};

const categoryDistribution = {
  labels: ["Visual Art", "Music", "Writing", "3D Models", "Video", "Other"],
  datasets: [
    {
      data: [38, 24, 18, 8, 9, 3],
    }
  ],
};

const recentActivity = [
  { id: 1, user: "Alex Rivera", action: "created a new artwork", time: "2 minutes ago", type: "creation" },
  { id: 2, user: "Jordan Chen", action: "commented on your post", time: "15 minutes ago", type: "comment" },
  { id: 3, user: "Taylor Kim", action: "collaborated on your project", time: "1 hour ago", type: "collaboration" },
  { id: 4, user: "Morgan Lee", action: "purchased your artwork", time: "3 hours ago", type: "purchase" },
  { id: 5, user: "Casey Wilson", action: "shared your music track", time: "5 hours ago", type: "share" },
];

const topCreators = [
  { id: 1, name: "Alex Rivera", medium: "Digital Art", followers: 23482, growth: "+12%" },
  { id: 2, name: "Jordan Chen", medium: "Music", followers: 18743, growth: "+8%" },
  { id: 3, name: "Taylor Kim", medium: "3D Models", followers: 15291, growth: "+15%" },
  { id: 4, name: "Morgan Lee", medium: "Photography", followers: 13842, growth: "+5%" },
  { id: 5, name: "Casey Wilson", medium: "Writing", followers: 10573, growth: "+10%" },
];

const notifications = [
  { id: 1, message: "New collaboration invitation from Jordan Chen", type: "info" },
  { id: 2, message: "Your artwork 'Cosmic Dreams' was featured on the homepage", type: "success" },
  { id: 3, message: "Payment processing for order #38291 is pending", type: "warning" },
];

export default function DashboardDemo() {
  const [searchQuery, setSearchQuery] = useState("");
  
  const handleNotification = (message: string, type: "info" | "success" | "warning" | "error") => {
    // Simple console log for demo purposes (avoiding toast SSR issues)
    console.log(`${type.toUpperCase()}: ${message}`);
  };
  
  const activityTypeColors = {
    creation: "cosmic",
    comment: "neural",
    collaboration: "aura",
    purchase: "nova",
    share: "quantum",
  };
  
  return (
    <Container className="py-12">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <Link
          href="/design-system"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Design System</span>
        </Link>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <div>
          <Heading level={1} size="3xl" className="mb-2">
            Creative Dashboard
          </Heading>
          <p className="text-stardust-400">Welcome back to your creative universe</p>
        </div>
        
        <div className="flex items-center gap-4">
          <Input
            placeholder="Search..."
            value={searchQuery}
            onChange={(value: string) => setSearchQuery(value)}
            variant="glass"
            size="sm"
            leftIcon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            }
          />
          
          <Button 
            variant="primary"
            size="sm"
            onClick={() => handleNotification("Refreshed dashboard data", "info")}
          >
            <svg className="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Refresh
          </Button>
        </div>
      </div>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <Card variant="glass" className="p-5">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-stardust-400 text-sm">Total Creators</p>
              <p className="text-3xl font-display font-semibold mt-1 text-stardust-200">24,821</p>
            </div>
            <div className="bg-cosmic-500/10 p-2 rounded-lg">
              <svg className="w-6 h-6 text-cosmic-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
              </svg>
            </div>
          </div>
          <div className="mt-3 flex items-center">
            <Badge variant="cosmic">+12% ↑</Badge>
            <span className="text-xs text-stardust-400 ml-2">vs last month</span>
          </div>
        </Card>
        
        <Card variant="glass" className="p-5">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-stardust-400 text-sm">Content Created</p>
              <p className="text-3xl font-display font-semibold mt-1 text-stardust-200">38,495</p>
            </div>
            <div className="bg-nova-500/10 p-2 rounded-lg">
              <svg className="w-6 h-6 text-nova-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
          </div>
          <div className="mt-3 flex items-center">
            <Badge variant="nova">+18% ↑</Badge>
            <span className="text-xs text-stardust-400 ml-2">vs last month</span>
          </div>
        </Card>
        
        <Card variant="glass" className="p-5">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-stardust-400 text-sm">Collaborations</p>
              <p className="text-3xl font-display font-semibold mt-1 text-stardust-200">4,287</p>
            </div>
            <div className="bg-neural-500/10 p-2 rounded-lg">
              <svg className="w-6 h-6 text-neural-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
              </svg>
            </div>
          </div>
          <div className="mt-3 flex items-center">
            <Badge variant="neural">+8% ↑</Badge>
            <span className="text-xs text-stardust-400 ml-2">vs last month</span>
          </div>
        </Card>
        
        <Card variant="glass" className="p-5">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-stardust-400 text-sm">Revenue Generated</p>
              <p className="text-3xl font-display font-semibold mt-1 text-stardust-200">$142,538</p>
            </div>
            <div className="bg-aura-500/10 p-2 rounded-lg">
              <svg className="w-6 h-6 text-aura-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div className="mt-3 flex items-center">
            <Badge variant="aura">+15% ↑</Badge>
            <span className="text-xs text-stardust-400 ml-2">vs last month</span>
          </div>
        </Card>
      </div>
      
      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <Card variant="default" className="lg:col-span-2">
          <div className="p-5 border-b border-space-700/50">
            <Heading level={3} size="lg">User Growth</Heading>
          </div>
          <div className="p-4 h-80">
            <LineChart
              data={growthData}
              theme="cosmic"
              animated={true}
            />
          </div>
        </Card>
        
        <Card variant="default">
          <div className="p-5 border-b border-space-700/50">
            <Heading level={3} size="lg">Category Distribution</Heading>
          </div>
          <div className="p-4 h-80">
            <PieChart
              data={categoryDistribution}
              theme="custom"
              type="doughnut"
            />
          </div>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card variant="default">
          <div className="p-5 border-b border-space-700/50 flex justify-between items-center">
            <Heading level={3} size="lg">Content Creation Stats</Heading>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => handleNotification("Downloaded creation stats", "success")}
            >
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
              </svg>
              Export
            </Button>
          </div>
          <div className="p-4 h-80">
            <BarChart
              data={creationStats}
              theme="neural"
              animated={true}
              gradient={true}
              rounded={true}
            />
          </div>
        </Card>
        
        <Card variant="default">
          <div className="p-5 border-b border-space-700/50 flex justify-between items-center">
            <Heading level={3} size="lg">Recent Activity</Heading>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => handleNotification("Viewed all activity", "info")}
            >
              View All
            </Button>
          </div>
          <div className="divide-y divide-space-700/30">
            {recentActivity.map((activity) => (
              <div key={activity.id} className="p-4 flex items-center">
                <Badge 
                  variant={(activityTypeColors as any)[activity.type] || 'default'} 
                  className="mr-3"
                >
                  {activity.type}
                </Badge>
                <div>
                  <p className="text-stardust-200">
                    <span className="font-medium">{activity.user}</span> {activity.action}
                  </p>
                  <p className="text-xs text-stardust-500">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card variant="default" className="lg:col-span-2">
          <div className="p-5 border-b border-space-700/50">
            <Heading level={3} size="lg">Top Creators</Heading>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="text-left border-b border-space-700/30">
                  <th className="p-4 text-stardust-400 font-medium">Creator</th>
                  <th className="p-4 text-stardust-400 font-medium">Medium</th>
                  <th className="p-4 text-stardust-400 font-medium">Followers</th>
                  <th className="p-4 text-stardust-400 font-medium">Growth</th>
                  <th className="p-4 text-stardust-400 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-space-700/30">
                {topCreators.map((creator) => (
                  <tr key={creator.id} className="hover:bg-space-700/10">
                    <td className="p-4">
                      <div className="font-medium text-stardust-200">{creator.name}</div>
                    </td>
                    <td className="p-4 text-stardust-400">{creator.medium}</td>
                    <td className="p-4 text-stardust-300">{creator.followers.toLocaleString()}</td>
                    <td className="p-4">
                      <span className="text-neural-500">{creator.growth}</span>
                    </td>
                    <td className="p-4">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleNotification(`Viewing ${creator.name}'s profile`, "info")}
                      >
                        View Profile
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
        
        <Card variant="default">
          <div className="p-5 border-b border-space-700/50">
            <Heading level={3} size="lg">Notifications</Heading>
          </div>
          <div className="divide-y divide-space-700/30">
            {notifications.map((notification) => (
              <div key={notification.id} className="p-4">
                <div className="flex items-start">
                  <Badge variant={notification.type as any} dot className="mt-1 mr-3">
                    •
                  </Badge>
                  <div>
                    <p className="text-stardust-200">{notification.message}</p>
                    <div className="mt-2 flex gap-2">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleNotification(`Acknowledged notification`, "success")}
                      >
                        Dismiss
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleNotification(`Viewing notification details`, "info")}
                      >
                        View
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            <div className="p-4 text-center">
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => handleNotification(`Viewing all notifications`, "info")}
              >
                View All Notifications
              </Button>
            </div>
          </div>
        </Card>
      </div>
    </Container>
  );
} 