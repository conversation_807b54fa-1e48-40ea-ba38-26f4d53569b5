"use client";

import { useState } from "react";
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import Container from "@/shared/components/Container/Container";
import { Card } from "@/components/ui/card";

export default function CheckboxDemo() {
  const [checkedItems, setCheckedItems] = useState<string[]>([]);

  const toggleItem = (item: string) => {
    setCheckedItems(prev => 
      prev.includes(item) 
        ? prev.filter(i => i !== item)
        : [...prev, item]
    );
  };

  const demoItems = [
    "AI-Powered Creation",
    "Autonomous Agents", 
    "Global Marketplace",
    "Real-time Collaboration",
    "Smart Analytics"
  ];

  return (
    <div className="min-h-screen bg-adaptive">
      <Container className="py-12">
        <div className="max-w-4xl mx-auto">
          {/* Breadcrumb Navigation */}
          <div className="mb-8">
            <Link
              href="/design-system"
              className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Design System</span>
            </Link>
          </div>
          <h1 className="text-4xl font-display font-bold text-gradient-cosmic mb-8">
            Checkbox Component Demo
          </h1>
          
          <div className="grid md:grid-cols-2 gap-8">
            {/* Styled Checkboxes Card */}
            <Card variant="glass" className="p-6">
              <h2 className="text-xl font-semibold text-stardust-200 mb-4">
                ✅ Neo-Styled Checkboxes
              </h2>
              
              <div className="space-y-3">
                {demoItems.map(item => (
                  <div key={item} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`neo-${item}`}
                      checked={checkedItems.includes(item)}
                      onChange={() => toggleItem(item)}
                      className="neo-checkbox mr-3"
                    />
                    <label 
                      htmlFor={`neo-${item}`} 
                      className="text-stardust-300 cursor-pointer flex-1 hover:text-stardust-100 transition-colors"
                    >
                      {item}
                    </label>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-space-800/50 rounded-lg">
                <h3 className="text-sm font-medium text-stardust-400 mb-2">Selected Features:</h3>
                <div className="text-sm text-cosmic-300">
                  {checkedItems.length > 0 ? checkedItems.join(", ") : "None selected"}
                </div>
              </div>
            </Card>

            {/* Regular HTML Checkboxes for Comparison */}
            <Card variant="elevated" className="p-6">
              <h2 className="text-xl font-semibold text-stardust-200 mb-4">
                🔧 Standard HTML Checkboxes
              </h2>
              
              <div className="space-y-3">
                {demoItems.map(item => (
                  <div key={item} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`html-${item}`}
                      checked={checkedItems.includes(item)}
                      onChange={() => toggleItem(item)}
                      className="mr-3"
                    />
                    <label 
                      htmlFor={`html-${item}`} 
                      className="text-stardust-300 cursor-pointer flex-1 hover:text-stardust-100 transition-colors"
                    >
                      {item}
                    </label>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-space-700/50 rounded-lg">
                <p className="text-sm text-stardust-400">
                  These should now inherit the neo-checkbox styling from globals.css
                </p>
              </div>
            </Card>
          </div>

          {/* Usage Examples */}
          <Card variant="border" className="mt-8 p-6">
            <h2 className="text-xl font-semibold text-stardust-200 mb-4">
              💡 Fixed Issues
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-nova-300 mb-3">Before (White Cards):</h3>
                <ul className="space-y-2 text-sm text-stardust-400">
                  <li>• Unstyled form-checkbox classes</li>
                  <li>• Default browser checkbox appearance</li>
                  <li>• White background cards with basic ticks</li>
                  <li>• Inconsistent with design system</li>
                </ul>
              </div>
              
              <div>
                <h3 className="text-lg font-medium text-cosmic-300 mb-3">After (Neo-Themed):</h3>
                <ul className="space-y-2 text-sm text-stardust-400">
                  <li>• Consistent neo-checkbox styling</li>
                  <li>• Space-themed colors and effects</li>
                  <li>• Hover animations and interactions</li>
                  <li>• Perfect design system integration</li>
                </ul>
              </div>
            </div>
          </Card>
        </div>
      </Container>
    </div>
  );
} 