import Button from "@/shared/components/Button";
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';

export default function DesignSystemPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 py-16">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </Link>
      </div>
      <h1 className="text-4xl md:text-5xl font-display font-bold text-gradient-cosmic mb-6">Design System</h1>
      <p className="text-xl mb-12">A showcase of our futuristic UI components and visual design elements.</p>
      
      <div className="space-y-16">
        {/* Color Palette Section */}
        <section>
          <h2 className="text-3xl font-display font-semibold mb-6">Color Palette</h2>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-xl mb-4">Primary: Cosmic Blue</h3>
              <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
                {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900].map((shade) => (
                  <div key={shade} className="flex flex-col">
                    <div 
                      className={`h-16 w-full rounded-lg bg-cosmic-${shade}`} 
                      title={`cosmic-${shade}`}
                    ></div>
                    <span className="text-xs mt-1 text-center">{shade}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="text-xl mb-4">Secondary: Nova Pink</h3>
              <div className="grid grid-cols-5 md:grid-cols-10 gap-2">
                {[50, 100, 200, 300, 400, 500, 600, 700, 800, 900].map((shade) => (
                  <div key={shade} className="flex flex-col">
                    <div 
                      className={`h-16 w-full rounded-lg bg-nova-${shade}`} 
                      title={`nova-${shade}`}
                    ></div>
                    <span className="text-xs mt-1 text-center">{shade}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div>
              <h3 className="text-xl mb-4">Accent Colors</h3>
              <div className="grid grid-cols-3 gap-6">
                <div>
                  <h4 className="text-lg mb-2">Neural Cyan</h4>
                  <div className="grid grid-cols-5 gap-1">
                    {[300, 400, 500, 600, 700].map((shade) => (
                      <div key={shade} className="flex flex-col">
                        <div 
                          className={`h-12 w-full rounded-lg bg-neural-${shade}`} 
                          title={`neural-${shade}`}
                        ></div>
                        <span className="text-xs mt-1 text-center">{shade}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg mb-2">Quantum Lavender</h4>
                  <div className="grid grid-cols-5 gap-1">
                    {[300, 400, 500, 600, 700].map((shade) => (
                      <div key={shade} className="flex flex-col">
                        <div 
                          className={`h-12 w-full rounded-lg bg-quantum-${shade}`} 
                          title={`quantum-${shade}`}
                        ></div>
                        <span className="text-xs mt-1 text-center">{shade}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg mb-2">Aura Orange</h4>
                  <div className="grid grid-cols-5 gap-1">
                    {[300, 400, 500, 600, 700].map((shade) => (
                      <div key={shade} className="flex flex-col">
                        <div 
                          className={`h-12 w-full rounded-lg bg-aura-${shade}`} 
                          title={`aura-${shade}`}
                        ></div>
                        <span className="text-xs mt-1 text-center">{shade}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl mb-4">UI Colors</h3>
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <h4 className="text-lg mb-2">Space (Background)</h4>
                  <div className="grid grid-cols-6 gap-1">
                    {[950, 900, 800, 700, 600, 500].map((shade) => (
                      <div key={shade} className="flex flex-col">
                        <div 
                          className={`h-12 w-full rounded-lg bg-space-${shade}`} 
                          title={`space-${shade}`}
                        ></div>
                        <span className="text-xs mt-1 text-center">{shade}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                <div>
                  <h4 className="text-lg mb-2">Stardust (Text)</h4>
                  <div className="grid grid-cols-5 gap-1">
                    {[100, 200, 300, 400, 500].map((shade) => (
                      <div key={shade} className="flex flex-col">
                        <div 
                          className={`h-12 w-full rounded-lg bg-stardust-${shade}`} 
                          title={`stardust-${shade}`}
                        ></div>
                        <span className="text-xs mt-1 text-center">{shade}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Typography Section */}
        <section>
          <h2 className="text-3xl font-display font-semibold mb-6">Typography</h2>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-xl mb-4">Orbitron (Display)</h3>
              <div className="space-y-4 font-display">
                <div>
                  <span className="text-xs text-stardust-600 block mb-1">Heading 1</span>
                  <h1 className="text-5xl font-bold">The Future of Creativity</h1>
                </div>
                <div>
                  <span className="text-xs text-stardust-600 block mb-1">Heading 2</span>
                  <h2 className="text-4xl font-bold">The Future of Creativity</h2>
                </div>
                <div>
                  <span className="text-xs text-stardust-600 block mb-1">Heading 3</span>
                  <h3 className="text-3xl font-bold">The Future of Creativity</h3>
                </div>
                <div>
                  <span className="text-xs text-stardust-600 block mb-1">Heading 4</span>
                  <h4 className="text-2xl font-semibold">The Future of Creativity</h4>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl mb-4">Exo 2 (Body)</h3>
              <div className="space-y-4 font-body">
                <div>
                  <span className="text-xs text-stardust-600 block mb-1">Large</span>
                  <p className="text-xl">A symbiotic relationship between AI and human creativity</p>
                </div>
                <div>
                  <span className="text-xs text-stardust-600 block mb-1">Regular</span>
                  <p className="text-base">A symbiotic relationship between AI and human creativity that evolves and adapts over time, creating a truly self-sovereign ecosystem.</p>
                </div>
                <div>
                  <span className="text-xs text-stardust-600 block mb-1">Small</span>
                  <p className="text-sm">A symbiotic relationship between AI and human creativity that evolves and adapts over time, creating a truly self-sovereign ecosystem.</p>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl mb-4">Space Mono (Code)</h3>
              <div className="font-mono bg-space-800 p-4 rounded-xl">
                <code className="text-neural-500">const creativeGenome = new CreativeGenome();</code>
                <br />
                <code className="text-neural-500">creativeGenome.analyze(artwork);</code>
                <br />
                <code className="text-neural-500">const recommendations = creativeGenome.getSimilarWorks();</code>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl mb-4">Text Effects</h3>
              <div className="space-y-6">
                <div>
                  <span className="text-xs text-stardust-600 block mb-1">Gradient Text</span>
                  <h3 className="text-3xl font-display font-bold text-gradient-cosmic">Cosmic Gradient</h3>
                  <h3 className="text-3xl font-display font-bold text-gradient-nova mt-2">Nova Gradient</h3>
                  <h3 className="text-3xl font-display font-bold text-gradient-multi mt-2">Multi Gradient</h3>
                </div>
                
                <div>
                  <span className="text-xs text-stardust-600 block mb-1">Text Shadow</span>
                  <h3 className="text-3xl font-display font-bold text-cosmic-500 text-shadow-cosmic">Cosmic Glow</h3>
                  <h3 className="text-3xl font-display font-bold text-nova-500 text-shadow-nova mt-2">Nova Glow</h3>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Components Section */}
        <section>
          <h2 className="text-3xl font-display font-semibold mb-6">Components</h2>
          
          <div className="space-y-12">
            <div>
              <h3 className="text-xl mb-6">Buttons</h3>
              
              <div className="space-y-6">
                <div className="flex flex-wrap gap-4">
                  <span className="text-xs text-stardust-600 block mb-1 w-full">Button Variants</span>
                  <Button variant="primary">Primary</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="glass">Glass</Button>
                  <Button variant="neon">Neon</Button>
                </div>
                
                <div className="flex flex-wrap gap-4">
                  <span className="text-xs text-stardust-600 block mb-1 w-full">Button Sizes (4 standardized sizes)</span>
                  <Button variant="primary" size="sm">Small</Button>
                  <Button variant="primary" size="md">Medium</Button>
                  <Button variant="primary" size="lg">Large</Button>
                  <Button variant="primary" size="xl">Extra Large</Button>
                </div>
                
                <div className="flex flex-wrap gap-4">
                  <span className="text-xs text-stardust-600 block mb-1 w-full">Button States</span>
                  <Button variant="primary" glowing>Glowing</Button>
                  <Button variant="primary" animated>Animated</Button>
                  <Button variant="primary" isLoading>Loading</Button>
                  <Button variant="primary" disabled>Disabled</Button>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl mb-6">Cards & Panels</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="neo-card p-6">
                  <h4 className="text-xl font-display mb-3">Neo Card</h4>
                  <p className="text-stardust-500">A sleek card component with subtle glassmorphism effects.</p>
                </div>
                
                <div className="neo-panel p-6">
                  <h4 className="text-xl font-display mb-3">Neo Panel</h4>
                  <p className="text-stardust-500">A more prominent panel with stronger glass effects.</p>
                </div>
                
                <div className="bg-glass border border-space-600/50 p-6 rounded-2xl shadow-glass">
                  <h4 className="text-xl font-display mb-3">Glass Card</h4>
                  <p className="text-stardust-500">Maximum glassmorphism for that futuristic look.</p>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-xl mb-6">Visual Effects</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="bg-space-800 border border-space-600 p-6 rounded-2xl shadow-cosmic">
                  <h4 className="text-lg font-display mb-3">Cosmic Shadow</h4>
                  <div className="h-20 bg-space-700 rounded-xl shadow-cosmic flex items-center justify-center">
                    <span className="text-sm">Cosmic glow effect</span>
                  </div>
                </div>
                
                <div className="bg-space-800 border border-space-600 p-6 rounded-2xl">
                  <h4 className="text-lg font-display mb-3">Animation</h4>
                  <div className="h-20 bg-space-700 rounded-xl flex items-center justify-center animate-float">
                    <span className="text-sm">Floating element</span>
                  </div>
                </div>
                
                <div className="bg-space-800 border border-space-600 p-6 rounded-2xl">
                  <h4 className="text-lg font-display mb-3">Shimmer</h4>
                  <div className="h-20 bg-space-700 rounded-xl flex items-center justify-center group">
                    <span className="relative text-sm">
                      Shimmer effect
                      <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full group-hover:animate-shimmer"></span>
                    </span>
                  </div>
                </div>
                
                <div className="bg-space-800 border border-space-600 p-6 rounded-2xl">
                  <h4 className="text-lg font-display mb-3">Glow Pulse</h4>
                  <div className="h-20 bg-space-700 rounded-xl flex items-center justify-center animate-pulse-slow shadow-neon">
                    <span className="text-sm">Pulsing glow</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Patterns & Backgrounds */}
        <section>
          <h2 className="text-3xl font-display font-semibold mb-6">Patterns & Backgrounds</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="h-48 rounded-2xl bg-tech-pattern flex items-center justify-center">
              <div className="bg-space-800/80 backdrop-blur-sm px-4 py-2 rounded-lg">
                <span>Tech Pattern</span>
              </div>
            </div>
            
            <div className="h-48 rounded-2xl bg-neural-noise flex items-center justify-center">
              <div className="bg-space-800/80 backdrop-blur-sm px-4 py-2 rounded-lg">
                <span>Neural Noise</span>
              </div>
            </div>
            
            <div className="h-48 rounded-2xl bg-data-grid flex items-center justify-center">
              <div className="bg-space-800/80 backdrop-blur-sm px-4 py-2 rounded-lg">
                <span>Data Grid</span>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
} 