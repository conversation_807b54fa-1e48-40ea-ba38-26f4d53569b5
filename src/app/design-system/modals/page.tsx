"use client";

import { useState } from "react";
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import Button from "@/shared/components/Button";
import Heading from "@/shared/components/Heading";
import Container from "@/shared/components/Container";
import Modal from "@/shared/components/Modal";
import Card from "@/shared/components/Card";
import Input from "@/shared/components/Input";
import Select from "@/shared/components/Select";
import Toggle from "@/shared/components/Toggle";

export default function ModalDemoPage() {
  const [basicModalOpen, setBasicModalOpen] = useState(false);
  const [variantModalOpen, setVariantModalOpen] = useState<string | null>(null);
  const [sizeModalOpen, setSizeModalOpen] = useState<string | null>(null);
  const [formModalOpen, setFormModalOpen] = useState(false);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  
  const variants = ["default", "glass", "dark", "minimal"];
  const sizes = ["sm", "md", "lg", "xl", "full"];
  
  return (
    <Container className="py-16">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <Link
          href="/design-system"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Design System</span>
        </Link>
      </div>

      <Heading level={1} gradient size="4xl" className="mb-6">
        Modal Component
      </Heading>
      <p className="text-xl mb-12 text-stardust-300">
        Interactive modal dialogs for displaying content or collecting user input.
      </p>
      
      <div className="space-y-12">
        {/* Basic Modal */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Basic Modal
            </Heading>
            <p className="mb-6 text-stardust-400">
              A simple modal with a title and close button.
            </p>
            
            <div className="flex space-x-4">
              <Button onClick={() => setBasicModalOpen(true)}>
                Open Basic Modal
              </Button>
            </div>
            
            <Modal
              isOpen={basicModalOpen}
              onClose={() => setBasicModalOpen(false)}
              title="Basic Modal"
            >
              <p className="text-stardust-300">
                This is a basic modal dialog with a title and close button.
              </p>
              <p className="mt-4 text-stardust-400">
                Modals are useful for focusing the user's attention on a specific task
                without navigating away from the current page.
              </p>
            </Modal>
          </Card>
        </section>
        
        {/* Variant Modals */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Modal Variants
            </Heading>
            <p className="mb-6 text-stardust-400">
              Modals with different visual styles.
            </p>
            
            <div className="flex flex-wrap gap-4">
              {variants.map(variant => (
                <Button 
                  key={variant}
                  onClick={() => setVariantModalOpen(variant)}
                  variant={variant === "default" ? "primary" : variant === "glass" ? "glass" : variant === "dark" ? "outline" : "ghost"}
                >
                  {variant.charAt(0).toUpperCase() + variant.slice(1)}
                </Button>
              ))}
            </div>
            
            {variants.map(variant => (
              <Modal
                key={variant}
                isOpen={variantModalOpen === variant}
                onClose={() => setVariantModalOpen(null)}
                title={`${variant.charAt(0).toUpperCase() + variant.slice(1)} Variant`}
                variant={variant as "default" | "glass" | "dark" | "minimal"}
              >
                <p className="text-stardust-300">
                  This modal uses the <strong>{variant}</strong> variant styling.
                </p>
                <p className="mt-4 text-stardust-400">
                  Different variants can be used to match the visual context of your application
                  or to create visual hierarchy between multiple modals.
                </p>
              </Modal>
            ))}
          </Card>
        </section>
        
        {/* Size Modals */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Modal Sizes
            </Heading>
            <p className="mb-6 text-stardust-400">
              Modals in different sizes to accommodate varying content needs.
            </p>
            
            <div className="flex flex-wrap gap-4">
              {sizes.map(size => (
                <Button 
                  key={size}
                  onClick={() => setSizeModalOpen(size)}
                >
                  {size.toUpperCase()} Size
                </Button>
              ))}
            </div>
            
            {sizes.map(size => (
              <Modal
                key={size}
                isOpen={sizeModalOpen === size}
                onClose={() => setSizeModalOpen(null)}
                title={`${size.toUpperCase()} Size Modal`}
                size={size as "sm" | "md" | "lg" | "xl" | "full"}
              >
                <div className="text-stardust-300">
                  <p>This modal uses the <strong>{size}</strong> size setting.</p>
                  <div className="h-40 bg-space-700/30 rounded-lg mt-4 flex items-center justify-center">
                    <span className="text-stardust-400">Content Area</span>
                  </div>
                </div>
              </Modal>
            ))}
          </Card>
        </section>
        
        {/* Form Modal */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Form Modal
            </Heading>
            <p className="mb-6 text-stardust-400">
              A modal containing a form for user input.
            </p>
            
            <Button onClick={() => setFormModalOpen(true)}>
              Open Form Modal
            </Button>
            
            <Modal
              isOpen={formModalOpen}
              onClose={() => setFormModalOpen(false)}
              title="User Profile"
              variant="glass"
              footerContent={
                <div className="flex justify-end space-x-3">
                  <Button variant="ghost" onClick={() => setFormModalOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="primary" onClick={() => setFormModalOpen(false)}>
                    Save Changes
                  </Button>
                </div>
              }
            >
              <div className="space-y-4">
                <Input
                  label="Display Name"
                  placeholder="Enter your display name"
                  fullWidth
                />
                
                <Input
                  label="Email Address"
                  placeholder="Enter your email"
                  type="email"
                  fullWidth
                />
                
                <Select
                  label="Preferred Medium"
                  options={[
                    { value: "digital-art", label: "Digital Art" },
                    { value: "music", label: "Music" },
                    { value: "writing", label: "Writing" },
                    { value: "3d", label: "3D Modeling" },
                    { value: "photography", label: "Photography" }
                  ]}
                  fullWidth
                />
                
                <Toggle
                  label="Public Profile"
                  description="Allow others to view your profile and works"
                />
              </div>
            </Modal>
          </Card>
        </section>
        
        {/* Confirmation Modal */}
        <section>
          <Card className="p-6">
            <Heading level={2} size="2xl" className="mb-4">
              Confirmation Modal
            </Heading>
            <p className="mb-6 text-stardust-400">
              A modal for confirming user actions.
            </p>
            
            <Button 
              variant="secondary"
              onClick={() => setConfirmModalOpen(true)}
            >
              Delete Item
            </Button>
            
            <Modal
              isOpen={confirmModalOpen}
              onClose={() => setConfirmModalOpen(false)}
              title="Confirm Deletion"
              variant="dark"
              size="sm"
              footerContent={
                <div className="flex justify-end space-x-3">
                  <Button variant="ghost" onClick={() => setConfirmModalOpen(false)}>
                    Cancel
                  </Button>
                  <Button variant="primary" onClick={() => setConfirmModalOpen(false)}>
                    Delete
                  </Button>
                </div>
              }
            >
              <p className="text-stardust-300">
                Are you sure you want to delete this item? This action cannot be undone.
              </p>
            </Modal>
          </Card>
        </section>
      </div>
    </Container>
  );
} 