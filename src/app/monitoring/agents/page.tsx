/**
 * Agent Monitoring Page - Enhanced with Autonomous AI Observation (Day 11)
 * Comprehensive real-time monitoring interface for the 28-agent system
 * Following Sequential Development Mandate - Agent Monitoring Dashboard UI
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import dynamic from 'next/dynamic';
import Container from '@/shared/components/Container';
import AgentMonitoringDashboard from '@/components/monitoring/AgentMonitoringDashboard';

// Dynamic imports for autonomous AI components (Day 11 Enhancement)
const AutonomousHealthIndicator = dynamic(
  () => import('@/components/AutonomousCore/AutonomousHealthIndicator'),
  { ssr: false, loading: () => <div className="h-16 bg-space-800/50 animate-pulse rounded-lg" /> }
);

const AgentCommunicationMonitor = dynamic(
  () => import('@/components/AutonomousCore/AgentCommunicationMonitor'),
  { ssr: false, loading: () => <div className="h-64 bg-space-800/50 animate-pulse rounded-lg" /> }
);

const AIDecisionStream = dynamic(
  () => import('@/components/AutonomousCore/AIDecisionStream'),
  { ssr: false, loading: () => <div className="h-96 bg-space-800/50 animate-pulse rounded-lg" /> }
);

export default function AgentMonitoringPage() {
  // Day 11: Autonomous AI state management
  const [isMounted, setIsMounted] = useState(false);
  const [autonomousMode, setAutonomousMode] = useState(false);
  const [aiTransparency, setAiTransparency] = useState(true);
  const [communicationMonitoring, setCommunicationMonitoring] = useState(false);
  const [realTimeDecisions, setRealTimeDecisions] = useState(true);

  // Day 11: Mount detection for SSR compatibility
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <Container>
      <div className="min-h-screen py-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <Link
            href="/monitoring"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Monitoring</span>
          </Link>
        </div>

        {/* Day 11: Enhanced Header with Autonomous AI Controls */}
        <div className="mb-6 p-6 bg-gradient-to-r from-space-900 via-space-800 to-space-900 rounded-lg border border-cosmic-400/20">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gradient-cosmic mb-2">28-Agent Monitoring Center</h1>
              <p className="text-stardust-300">
                Real-time agent monitoring with autonomous AI transparency
              </p>
            </div>
            
            {/* Day 11: Autonomous AI Controls */}
            {isMounted && (
              <div className="flex flex-wrap items-center gap-3">
                <button
                  onClick={() => setAutonomousMode(!autonomousMode)}
                  className={`px-4 py-2 rounded-lg font-medium transition-all ${
                    autonomousMode 
                      ? 'bg-neural-600 text-white shadow-neural animate-pulse' 
                      : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                  }`}
                >
                  {autonomousMode ? '🤖 Autonomous ON' : '🤖 Enable Autonomous'}
                </button>
                
                <button
                  onClick={() => setAiTransparency(!aiTransparency)}
                  className={`px-3 py-2 rounded text-sm transition-colors ${
                    aiTransparency 
                      ? 'bg-cosmic-600 text-white' 
                      : 'bg-space-700 text-stardust-400'
                  }`}
                >
                  {aiTransparency ? '👁️ AI Transparency' : '👁️ Enable Transparency'}
                </button>

                <button
                  onClick={() => setCommunicationMonitoring(!communicationMonitoring)}
                  className={`px-3 py-2 rounded text-sm transition-colors ${
                    communicationMonitoring 
                      ? 'bg-nova-600 text-white' 
                      : 'bg-space-700 text-stardust-400'
                  }`}
                >
                  {communicationMonitoring ? '💬 Comm Monitor' : '💬 Enable Comm'}
                </button>

                <button
                  onClick={() => setRealTimeDecisions(!realTimeDecisions)}
                  className={`px-3 py-2 rounded text-sm transition-colors ${
                    realTimeDecisions 
                      ? 'bg-quantum-600 text-white' 
                      : 'bg-space-700 text-stardust-400'
                  }`}
                >
                  {realTimeDecisions ? '📊 Live Decisions' : '📊 Enable Decisions'}
                </button>
              </div>
            )}
          </div>

          {/* Day 11: Autonomous AI Health Indicator */}
          {isMounted && autonomousMode && (
            <div className="mt-4 p-4 bg-space-900/30 rounded-lg border border-cosmic-400/10">
              <AutonomousHealthIndicator
                variant="compact"
                showExpanded={true}
                className="shadow-md"
              />
            </div>
          )}
        </div>

        {/* Day 11: Agent Communication Monitor */}
        {isMounted && communicationMonitoring && (
          <div className="mb-6">
            <div className="mb-4">
              <h2 className="text-xl font-bold text-gradient-nova mb-2">🤖 Agent Communication Monitor</h2>
              <p className="text-stardust-400 text-sm">
                Real-time inter-agent communication and consensus monitoring
              </p>
            </div>
            <AgentCommunicationMonitor />
          </div>
        )}

        {/* Existing Agent Monitoring Dashboard - Preserved */}
        <AgentMonitoringDashboard 
          autoRefresh={true}
          refreshInterval={15000}
        />

        {/* Day 11: Real-Time AI Decision Stream for Agents */}
        {isMounted && realTimeDecisions && (
          <div className="mt-6">
            <div className="mb-4">
              <h2 className="text-xl font-bold text-gradient-quantum mb-2">🤖 Agent AI Decision Stream</h2>
              <p className="text-stardust-400 text-sm">
                Real-time autonomous agent decision monitoring and transparency
              </p>
            </div>
            <AIDecisionStream
              realTimeEnabled={autonomousMode}
              maxDecisions={30}
              autoScroll={true}
              className="neo-panel border border-space-600 rounded-lg shadow-lg max-h-96 overflow-y-auto"
            />
          </div>
        )}

        {/* Day 11: Enhanced Footer */}
        {isMounted && (autonomousMode || aiTransparency) && (
          <div className="mt-8 p-4 bg-space-900/20 rounded-lg border border-cosmic-400/10 text-center">
            <div className="inline-flex items-center space-x-2 text-stardust-400 text-sm">
              <span>🔍</span>
              <span>Agent Monitoring Enhanced</span>
              <span>•</span>
              {autonomousMode && (
                <>
                  <span>🤖 Autonomous Agent Monitoring Active</span>
                  <span>•</span>
                </>
              )}
              {aiTransparency && (
                <>
                  <span>👁️ AI Transparency Enabled</span>
                  <span>•</span>
                </>
              )}
              <span>Day 11 Implementation</span>
            </div>
            <div className="mt-2 text-xs text-neural-400">
              Enhanced with real-time AI transparency and autonomous agent observation
            </div>
          </div>
        )}
      </div>
    </Container>
  );
} 