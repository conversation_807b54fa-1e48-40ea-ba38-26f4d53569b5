'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import dynamic from 'next/dynamic';
import PerformanceDashboard from '@/monitoring/ai-performance/PerformanceDashboard';

// Dynamic imports for autonomous AI components
const AutonomousHealthIndicator = dynamic(
  () => import('@/components/AutonomousCore/AutonomousHealthIndicator'),
  { ssr: false, loading: () => <div className="h-16 bg-space-800/50 animate-pulse rounded-lg" /> }
);

const SystemStatusDashboard = dynamic(
  () => import('@/components/AutonomousCore/SystemStatusDashboard'),
  { ssr: false, loading: () => <div className="h-64 bg-space-800/50 animate-pulse rounded-lg" /> }
);

const AIDecisionStream = dynamic(
  () => import('@/components/AutonomousCore/AIDecisionStream'),
  { ssr: false, loading: () => <div className="h-96 bg-space-800/50 animate-pulse rounded-lg" /> }
);

export default function MonitoringPage() {
  // Autonomous AI state management
  const [isMounted, setIsMounted] = useState(false);
  const [autonomousMode, setAutonomousMode] = useState(false);
  const [aiHealthExpanded, setAiHealthExpanded] = useState(false);
  const [realTimeDecisions, setRealTimeDecisions] = useState(true);

  // Mount detection for SSR compatibility
  useEffect(() => {
    setIsMounted(true);
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-space-900">
      {/* Breadcrumb Navigation */}
      <div className="container mx-auto px-6 py-4">
        <Link
          href="/dashboard"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Dashboard Hub</span>
        </Link>
      </div>

      {/* Enhanced Header with Autonomous AI Controls */}
      <div className="border-b border-cosmic-400/20 bg-space-800/50 backdrop-blur">
        <div className="container mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gradient-cosmic">AI Monitoring Center</h1>
              <p className="text-stardust-300 mt-1">
                Real-time performance monitoring and autonomous AI observation
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {/* Day 10: Autonomous AI Controls */}
              {isMounted && (
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setAutonomousMode(!autonomousMode)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all ${
                      autonomousMode 
                        ? 'bg-neural-600 text-white shadow-neural animate-pulse' 
                        : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                    }`}
                  >
                    {autonomousMode ? '🤖 Autonomous ON' : '🤖 Enable Autonomous'}
                  </button>
                  <button
                    onClick={() => setRealTimeDecisions(!realTimeDecisions)}
                    className={`px-3 py-2 rounded text-sm transition-colors ${
                      realTimeDecisions 
                        ? 'bg-cosmic-600 text-white' 
                        : 'bg-space-700 text-stardust-400'
                    }`}
                  >
                    {realTimeDecisions ? '📊 Live Decisions' : '📊 Enable Decisions'}
                  </button>
                </div>
              )}
              <div className="text-right">
                <div className="text-sm text-stardust-400">Status</div>
                <div className="text-green-400 font-semibold">✅ All Systems Operational</div>
              </div>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </div>

          {/* Day 10: Autonomous AI Health Indicator */}
          {isMounted && autonomousMode && (
            <div className="mt-4 p-4 bg-space-900/30 rounded-lg border border-cosmic-400/20">
              <AutonomousHealthIndicator
                variant="compact"
                showExpanded={aiHealthExpanded}
                className="shadow-md"
              />
            </div>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        {/* Day 10: Enhanced System Status Dashboard */}
        {isMounted && autonomousMode && (
          <div className="mb-8">
            <SystemStatusDashboard />
          </div>
        )}

        {/* Existing Monitoring Dashboard - Preserved */}
        <PerformanceDashboard />

        {/* Day 10: Real-Time AI Decision Stream */}
        {isMounted && realTimeDecisions && (
          <div className="mt-8">
            <div className="mb-4">
              <h2 className="text-xl font-bold text-gradient-nova mb-2">🤖 AI Decision Stream</h2>
              <p className="text-stardust-400 text-sm">
                Real-time autonomous AI decision monitoring and transparency
              </p>
            </div>
            <AIDecisionStream
              realTimeEnabled={autonomousMode}
              maxDecisions={25}
              autoScroll={true}
              className="neo-panel border border-space-600 rounded-lg shadow-lg max-h-96 overflow-y-auto"
            />
          </div>
        )}

        {/* Enhanced Footer Info - Day 10 Enhancement */}
        <div className="mt-12 text-center">
          <div className="inline-flex items-center space-x-2 text-stardust-400 text-sm">
            <span>🛡️</span>
            <span>Crisis Prevention System Active</span>
            <span>•</span>
            <span>Monitoring since AI Crisis Resolution</span>
            <span>•</span>
            {isMounted && autonomousMode && (
              <>
                <span>🤖 Autonomous AI Monitoring Active</span>
                <span>•</span>
              </>
            )}
            <span>Live System Active</span>
          </div>
          {isMounted && autonomousMode && (
            <div className="mt-2 text-xs text-neural-400">
              Enhanced with autonomous AI observation capabilities
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Metadata moved to layout.tsx due to 'use client' directive 