"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Users, Activity, Settings, Eye, Grid3X3, Zap, ArrowRight, Shield, Brain } from 'lucide-react';

export default function AgentsPage() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [agentStats, setAgentStats] = useState({
    totalAgents: 28,
    activeAgents: 24,
    tasksCompleted: 1847,
    systemHealth: 98
  });

  // Simulate live data updates
  useEffect(() => {
    const interval = setInterval(() => {
      setAgentStats(prev => ({
        totalAgents: 28,
        activeAgents: 22 + Math.floor(Math.random() * 6),
        tasksCompleted: prev.tasksCompleted + Math.floor(Math.random() * 3),
        systemHealth: 95 + Math.floor(Math.random() * 5)
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  const agentSystems = [
    {
      title: "28-Agent Ecosystem",
      href: "/agent-ecosystem",
      icon: Grid3X3,
      description: "Complete visualization and control of all 28 AI agents",
      status: "Live",
      color: "nova",
      isPrimary: true
    },
    {
      title: "Agent Orchestration",
      href: "/orchestration",
      icon: Settings,
      description: "Coordinate multi-agent workflows and task distribution",
      status: "Active",
      color: "cosmic"
    },
    {
      title: "Swarm Intelligence",
      href: "/swarm",
      icon: Users,
      description: "Collaborative AI agent coordination and swarm behavior",
      status: "Coordinating",
      color: "neural"
    },
    {
      title: "Agent Communication",
      href: "/agent-ecosystem",
      icon: Activity,
      description: "Monitor real-time agent communication and collaboration",
      status: "Streaming",
      color: "aura"
    },
    {
      title: "Autonomous Operations",
      href: "/autonomous-core-demo",
      icon: Brain,
      description: "Watch autonomous agents in action with live demonstrations",
      status: "Autonomous",
      color: "quantum"
    },
    {
      title: "Security Monitoring",
      href: "/monitoring",
      icon: Shield,
      description: "Agent security, performance monitoring, and system health",
      status: "Protected",
      color: "cosmic"
    }
  ];

  return (
    <div className="min-h-screen bg-space-900 pt-20">
      {/* Hero Section */}
      <section className="py-16 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-nova-500/20 via-space-900 to-cosmic-500/20" />
        <div className="absolute inset-0 bg-gradient-to-tr from-neural-500/10 via-transparent to-aura-500/10" />

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-3 mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-nova-500 to-cosmic-500 rounded-2xl flex items-center justify-center">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-nova-400 via-cosmic-400 to-neural-400 bg-clip-text text-transparent">
                Agent Hub
              </h1>
            </div>

            <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
              {isLoggedIn
                ? "Command and control your 28-agent autonomous AI ecosystem with real-time monitoring and orchestration."
                : "Witness the power of 28 autonomous AI agents working together. See live agent coordination and task execution."
              }
            </p>

            {!isLoggedIn && (
              <div className="bg-nova-500/10 border border-nova-500/30 rounded-lg p-4 max-w-2xl mx-auto mb-8">
                <p className="text-nova-300 text-sm">
                  🤖 <strong>Observer Mode:</strong> You're viewing live agent activity.
                  <Link href="/login" className="text-nova-400 hover:text-nova-300 underline ml-1">
                    Login to control agents
                  </Link>
                </p>
              </div>
            )}
          </div>

          {/* Live Agent Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-space-800/50 backdrop-blur-sm border border-nova-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-nova-400 mb-2">{agentStats.totalAgents}</div>
              <div className="text-white/60 text-sm">Total Agents</div>
              <div className="w-2 h-2 bg-green-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>

            <div className="bg-space-800/50 backdrop-blur-sm border border-cosmic-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-cosmic-400 mb-2">{agentStats.activeAgents}</div>
              <div className="text-white/60 text-sm">Active Now</div>
              <div className="w-2 h-2 bg-green-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>

            <div className="bg-space-800/50 backdrop-blur-sm border border-neural-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-neural-400 mb-2">{agentStats.tasksCompleted.toLocaleString()}</div>
              <div className="text-white/60 text-sm">Tasks Completed</div>
              <div className="w-2 h-2 bg-green-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>

            <div className="bg-space-800/50 backdrop-blur-sm border border-aura-500/20 rounded-xl p-6 text-center">
              <div className="text-3xl font-bold text-aura-400 mb-2">{agentStats.systemHealth}%</div>
              <div className="text-white/60 text-sm">System Health</div>
              <div className="w-2 h-2 bg-green-400 rounded-full mx-auto mt-2 animate-pulse"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Agent Systems Grid */}
      <section className="py-16">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">Agent Management Systems</h2>
            <p className="text-white/70 max-w-2xl mx-auto">
              Comprehensive tools for monitoring, controlling, and orchestrating your AI agent ecosystem
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {agentSystems.map((system, index) => {
              const Icon = system.icon;
              return (
                <Link
                  key={system.href}
                  href={system.href}
                  className={`group bg-space-800/30 backdrop-blur-sm border rounded-xl p-6 transition-all duration-300 hover:scale-105 ${
                    system.isPrimary
                      ? 'border-nova-500/50 bg-nova-500/5'
                      : 'border-white/10 hover:border-nova-500/50'
                  }`}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`w-12 h-12 bg-gradient-to-br from-${system.color}-500 to-${system.color}-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white group-hover:text-nova-300 transition-colors">
                        {system.title}
                        {system.isPrimary && <span className="text-xs bg-nova-500/20 text-nova-400 px-2 py-1 rounded-full ml-2">Primary</span>}
                      </h3>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                        <span className="text-xs text-green-400">{system.status}</span>
                      </div>
                    </div>
                  </div>

                  <p className="text-white/70 text-sm mb-4 group-hover:text-white/90 transition-colors">
                    {system.description}
                  </p>

                  <div className="flex items-center text-nova-400 text-sm group-hover:text-nova-300 transition-colors">
                    <span>{isLoggedIn ? 'Access System' : 'View Demo'}</span>
                    <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16">
        <div className="container mx-auto px-6 text-center">
          <div className="bg-gradient-to-r from-nova-500/10 to-cosmic-500/10 border border-nova-500/30 rounded-2xl p-8 max-w-4xl mx-auto">
            <h2 className="text-2xl font-bold text-white mb-4">
              {isLoggedIn ? "Command Your Agent Army" : "Ready to Control 28 AI Agents?"}
            </h2>
            <p className="text-white/70 mb-6">
              {isLoggedIn
                ? "Access full agent control, orchestration tools, and real-time monitoring capabilities."
                : "Login to command your personal AI agent ecosystem and orchestrate autonomous workflows."
              }
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              {isLoggedIn ? (
                <>
                  <Link href="/intelligence" className="bg-nova-500 hover:bg-nova-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Grid3X3 className="w-5 h-5" />
                    Intelligence Hub
                  </Link>
                  <Link href="/dashboard" className="bg-space-700 hover:bg-space-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Dashboard Hub
                  </Link>
                </>
              ) : (
                <>
                  <Link href="/login" className="bg-nova-500 hover:bg-nova-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Login for Agent Control
                  </Link>
                  <Link href="/intelligence" className="bg-space-700 hover:bg-space-600 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center gap-2">
                    <Eye className="w-5 h-5" />
                    View Intelligence Hub
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}