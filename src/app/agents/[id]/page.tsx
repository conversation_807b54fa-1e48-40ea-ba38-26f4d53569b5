/**
 * Individual Agent Page - Detailed view for specific agents
 * Part of Synchronous Backend-Frontend Development Protocol
 * Connects to agent-core backend for real agent data
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import dynamic from 'next/dynamic';
import { Card } from '@/shared/components/Card';
import { Button } from '@/shared/components/Button';
import Container from '@/shared/components/Container';
import AgentCard from '@/components/AgentVisual/AgentCard';
import { 
  getAgentVisuals, 
  getAgentStatusColor,
  getAgentStatusClass,
  getHealthColor
} from '@/utils/agentVisuals';

// Dynamic imports for autonomous AI components (Day 9 Enhancement)
const AutonomousHealthIndicator = dynamic(
  () => import('@/components/AutonomousCore/AutonomousHealthIndicator'),
  { ssr: false }
);

const AIDecisionStream = dynamic(
  () => import('@/components/AutonomousCore/AIDecisionStream'),
  { ssr: false }
);

const AgentObservationPanel = dynamic(
  () => import('@/components/AutonomousCore/AgentObservationPanel'),
  { ssr: false }
);

interface AgentDetails {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'error' | 'busy';
  capabilities: string[];
  currentTasks: number;
  completedTasks: number;
  successRate: number;
  averageResponseTime: number;
  lastActivity: string;
  health: {
    cpu: number;
    memory: number;
    performance: number;
  };
  metadata: {
    version: string;
    created: string;
    lastUpdate: string;
    description: string;
  };
}

interface TaskSummary {
  id: string;
  title: string;
  status: string;
  priority: string;
  createdAt: string;
  estimatedDuration: number;
}

export default function AgentDetailPage() {
  const params = useParams();
  const router = useRouter();
  const agentId = params?.id as string;
  
  const [agent, setAgent] = useState<AgentDetails | null>(null);
  const [tasks, setTasks] = useState<TaskSummary[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'tasks' | 'performance' | 'logs' | 'ai-decisions'>('overview');
  
  // Day 9 Enhancement: Autonomous AI observation state
  const [autonomousMode, setAutonomousMode] = useState(false);
  const [aiObservationEnabled, setAIObservationEnabled] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  // Mount state for SSR compatibility
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Fetch agent details
  const fetchAgentDetails = async () => {
    if (!agentId) {
      setError('No agent ID provided');
      setLoading(false);
      return;
    }
    
    try {
      setLoading(true);
      
      // Fetch agent info
      const agentResponse = await fetch(`/api/agents/${agentId}`);
      const agentData = await agentResponse.json();
      
      // Fetch agent tasks
      const tasksResponse = await fetch(`/api/agents/${agentId}/tasks`);
      const tasksData = await tasksResponse.json();
      
      if (agentData.success) {
        setAgent(agentData.data);
        setTasks(tasksData.success ? tasksData.data : []);
        setError(null);
      } else {
        setError(agentData.error || 'Agent not found');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Using centralized getAgentStatusClass and getHealthColor from @/utils/agentVisuals

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  useEffect(() => {
    if (agentId) {
      fetchAgentDetails();
      
      // Poll for updates every 30 seconds
      const interval = setInterval(fetchAgentDetails, 30000);
      return () => clearInterval(interval);
    }
  }, [agentId]);

  if (loading) {
    return (
      <div className="min-h-screen theme-bg-primary flex items-center justify-center">
        <Card className="p-12 text-center max-w-md">
          <div className="animate-spin w-8 h-8 border-2 border-cosmic-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="theme-text-secondary">Loading agent details...</p>
        </Card>
      </div>
    );
  }

  if (error || !agent) {
    return (
      <div className="min-h-screen theme-bg-primary flex items-center justify-center">
        <Card className="p-12 text-center max-w-md">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-red-400 mb-2">Agent Not Found</h2>
          <p className="theme-text-secondary mb-4">{error || 'The requested agent could not be found'}</p>
          <Button
            variant="primary"
            onClick={() => router.push('/agents')}
          >
            Back to Agents
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Background patterns */}
      <div className="absolute inset-0 z-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-cosmic-50/50 via-transparent to-nova-50/50" />
        <div className="absolute inset-0 bg-gradient-to-tr from-neural-50/30 via-transparent to-aura-50/30" />
      </div>
      
      <main className="relative z-10">
        <Container className="py-8">
          {/* Breadcrumb Navigation */}
          <div className="mb-8">
            <Link
              href="/agents"
              className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Agents</span>
            </Link>
          </div>

          {/* Header */}
          <div className="mb-8">

            <div className="flex items-center gap-4 mb-6">
              {/* Use centralized agent visual system */}
              <AgentCard 
                agent={agent}
                variant="circle"
                size="large"
                showTooltip={false}
                className="flex-shrink-0"
              />
              <div>
                <h1 className="text-3xl font-display font-bold text-gradient-multi">
                  {agent.name}
                </h1>
                <div className="flex items-center gap-3 mt-2">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium ${getAgentStatusClass(agent.status)}`}>
                    {agent.status}
                  </span>
                  <span className="px-3 py-1 rounded-full text-sm font-medium bg-cosmic-500/20 text-cosmic-300">
                    {agent.type}
                  </span>
                  <span className="px-3 py-1 rounded-full text-sm font-medium" style={{
                    backgroundColor: `${getAgentVisuals(agent.name).baseColor}20`,
                    color: getAgentVisuals(agent.name).baseColor
                  }}>
                    {getAgentVisuals(agent.name).category}
                  </span>
                </div>
              </div>
            </div>

            {/* Enhanced Action Buttons - Day 9 Enhancement */}
            <div className="flex flex-wrap gap-4">
              <Button
                variant="primary"
                onClick={() => router.push(`/tasks/create?agent=${agentId}`)}
                className="animate-glow"
              >
                + Assign Task
              </Button>
              
              {/* Day 9: Autonomous AI Observation Toggle */}
              {isMounted && (
                <Button
                  variant={autonomousMode ? "neon" : "outline"}
                  onClick={() => setAutonomousMode(!autonomousMode)}
                  className={autonomousMode ? "animate-pulse" : ""}
                >
                  {autonomousMode ? "🤖 Autonomous ON" : "🤖 Enable Autonomous"}
                </Button>
              )}
              
              <Button
                variant="outline"
                onClick={fetchAgentDetails}
                disabled={loading}
              >
                {loading ? 'Refreshing...' : 'Refresh'}
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push('/tasks')}
              >
                View All Tasks
              </Button>
            </div>
          </div>

          {/* Enhanced Stats Cards with Autonomous AI Health - Day 9 Enhancement */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="neo-panel p-6 backdrop-blur-md">
              <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Current Tasks</h3>
              <div className="text-3xl font-bold text-gradient-cosmic mb-2">
                {agent.currentTasks}
              </div>
              <div className="text-xs text-muted-enhanced">
                Active assignments
              </div>
              {/* Day 9: Autonomous AI indicator */}
              {isMounted && autonomousMode && (
                <div className="mt-2 flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-green-400">AI Monitoring</span>
                </div>
              )}
            </div>
            
            <div className="neo-panel p-6 backdrop-blur-md">
              <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Success Rate</h3>
              <div className="text-3xl font-bold text-gradient-nova mb-2">
                {agent.successRate}%
              </div>
              <div className="text-xs text-muted-enhanced">
                {agent.completedTasks} completed
              </div>
            </div>
            
            <div className="neo-panel p-6 backdrop-blur-md">
              <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Response Time</h3>
              <div className="text-3xl font-bold text-gradient-neural mb-2">
                {agent.averageResponseTime}ms
              </div>
              <div className="text-xs text-muted-enhanced">
                Average processing
              </div>
            </div>
            
            <div className="neo-panel p-6 backdrop-blur-md">
              <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Health Score</h3>
              <div className={`text-3xl font-bold mb-2 ${getHealthColor(agent.health.performance)}`}>
                {agent.health.performance}%
              </div>
              <div className="text-xs text-muted-enhanced">
                Overall performance
              </div>
            </div>
          </div>

          {/* Enhanced Tab Navigation with AI Decisions - Day 9 Enhancement */}
          <div className="flex space-x-1 p-1 bg-space-800/50 rounded-lg mb-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'tasks', label: 'Tasks' },
              { id: 'performance', label: 'Performance' },
              { id: 'logs', label: 'Logs' },
              { id: 'ai-decisions', label: '🤖 AI Decisions' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
                  activeTab === tab.id
                    ? 'bg-cosmic-500 text-white'
                    : 'text-stardust-300 hover:text-white hover:bg-space-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Enhanced Tab Content with Autonomous AI - Day 9 Enhancement */}
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Day 9: Autonomous Health Indicator */}
              {isMounted && autonomousMode && (
                <div className="lg:col-span-2 mb-6">
                  <AutonomousHealthIndicator
                    variant="detailed"
                    showExpanded={true}
                    className="shadow-lg"
                  />
                </div>
              )}
              
              {/* Agent Info */}
              <Card className="p-6">
                <h3 className="text-lg font-bold theme-text-primary mb-4">Agent Information</h3>
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Description</label>
                    <p className="theme-text-primary mt-1">{agent.metadata.description}</p>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium theme-text-secondary">Version</label>
                      <p className="theme-text-primary mt-1">{agent.metadata.version}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium theme-text-secondary">Last Activity</label>
                      <p className="theme-text-primary mt-1">{formatDate(agent.lastActivity)}</p>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium theme-text-secondary">Capabilities</label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {agent.capabilities.map((capability, index) => (
                        <span key={index} className="px-2 py-1 bg-cosmic-500/20 text-cosmic-300 rounded text-sm">
                          {capability}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </Card>

              {/* Health Metrics */}
              <Card className="p-6">
                <h3 className="text-lg font-bold theme-text-primary mb-4">Health Metrics</h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium theme-text-secondary">CPU Usage</span>
                      <span className={`text-sm font-medium ${getHealthColor(100 - agent.health.cpu)}`}>
                        {agent.health.cpu}%
                      </span>
                    </div>
                    <div className="w-full bg-space-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          agent.health.cpu > 80 ? 'bg-red-500' : 
                          agent.health.cpu > 60 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${agent.health.cpu}%` }}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium theme-text-secondary">Memory Usage</span>
                      <span className={`text-sm font-medium ${getHealthColor(100 - agent.health.memory)}`}>
                        {agent.health.memory}%
                      </span>
                    </div>
                    <div className="w-full bg-space-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          agent.health.memory > 80 ? 'bg-red-500' : 
                          agent.health.memory > 60 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${agent.health.memory}%` }}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium theme-text-secondary">Performance</span>
                      <span className={`text-sm font-medium ${getHealthColor(agent.health.performance)}`}>
                        {agent.health.performance}%
                      </span>
                    </div>
                    <div className="w-full bg-space-700 rounded-full h-2">
                      <div
                        className="h-2 rounded-full bg-cosmic-500"
                        style={{ width: `${agent.health.performance}%` }}
                      />
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          )}

          {activeTab === 'tasks' && (
            <Card className="p-6">
              <h3 className="text-lg font-bold theme-text-primary mb-4">Assigned Tasks</h3>
              {tasks.length > 0 ? (
                <div className="space-y-4">
                  {tasks.map((task) => (
                    <div key={task.id} className="p-4 bg-space-800/50 rounded-lg border border-space-600">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium theme-text-primary">{task.title}</h4>
                        <div className="flex items-center gap-2">
                          <span className={`px-2 py-1 rounded text-xs ${getAgentStatusClass(task.status)}`}>
                            {task.status}
                          </span>
                          <span className="px-2 py-1 bg-nova-500/20 text-nova-300 rounded text-xs">
                            {task.priority}
                          </span>
                        </div>
                      </div>
                      <div className="flex justify-between text-sm theme-text-secondary">
                        <span>Created: {formatDate(task.createdAt)}</span>
                        <span>Est. {task.estimatedDuration}min</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="theme-text-secondary">No tasks currently assigned to this agent</p>
                  <Button
                    variant="outline"
                    onClick={() => router.push(`/tasks/create?agent=${agentId}`)}
                    className="mt-4"
                  >
                    Assign First Task
                  </Button>
                </div>
              )}
            </Card>
          )}

          {activeTab === 'performance' && (
            <Card className="p-6">
              <h3 className="text-lg font-bold theme-text-primary mb-4">Performance Analytics</h3>
              <div className="text-center py-8">
                <p className="theme-text-secondary">Performance charts coming soon...</p>
                <p className="text-sm theme-text-muted mt-2">This will show detailed performance metrics over time</p>
              </div>
            </Card>
          )}

          {activeTab === 'logs' && (
            <Card className="p-6">
              <h3 className="text-lg font-bold theme-text-primary mb-4">Agent Logs</h3>
              <div className="text-center py-8">
                <p className="theme-text-secondary">Log viewer coming soon...</p>
                <p className="text-sm theme-text-muted mt-2">This will show real-time agent activity logs</p>
              </div>
            </Card>
          )}

          {/* Day 9 Enhancement: AI Decisions Tab */}
          {activeTab === 'ai-decisions' && isMounted && (
            <div className="space-y-6">
              {/* AI Decisions Information Banner */}
              <Card className="p-6 bg-gradient-to-r from-cosmic-500/10 to-neural-500/10 border border-cosmic-500/20">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-cosmic-500/20 rounded-full flex items-center justify-center">
                    <span className="text-cosmic-300 text-sm">🤖</span>
                  </div>
                  <h3 className="text-lg font-bold text-cosmic-300">AI Decision Transparency</h3>
                </div>
                <p className="theme-text-secondary mb-3">
                  Real-time monitoring of AI decisions made by <strong>{agent?.name}</strong>. 
                  This transparency layer shows autonomous reasoning, confidence levels, and decision impact.
                </p>
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-sm text-green-400">Live AI Stream</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-cosmic-400 rounded-full"></div>
                    <span className="text-sm text-cosmic-400">Decision Logging Active</span>
                  </div>
                </div>
              </Card>

              {/* Agent Observation Panel */}
              <AgentObservationPanel
                agentName={agent?.name || agentId}
                agentType={agent?.type}
                status={agent?.status === 'inactive' ? 'idle' : 
                       agent?.status === 'busy' ? 'active' : 
                       agent?.status || 'offline'}
                className="shadow-lg"
                onStatusChange={(newStatus) => {
                  if (agent) {
                    const mappedStatus = newStatus === 'idle' ? 'inactive' : 
                                        newStatus === 'offline' ? 'inactive' : 
                                        'active';
                    setAgent({...agent, status: mappedStatus as any});
                  }
                }}
              />

              {/* AI Decision Stream */}
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold theme-text-primary">Recent AI Decisions</h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setAIObservationEnabled(!aiObservationEnabled)}
                  >
                    {aiObservationEnabled ? "Pause Stream" : "Resume Stream"}
                  </Button>
                </div>
                <AIDecisionStream
                  realTimeEnabled={aiObservationEnabled}
                  maxDecisions={30}
                  autoScroll={true}
                  className="border border-space-600 rounded-lg max-h-96 overflow-y-auto"
                />
              </Card>

              {/* Autonomous Capabilities Info */}
              <Card className="p-6 bg-gradient-to-r from-neural-500/10 to-aura-500/10 border border-neural-500/20">
                <h3 className="text-lg font-bold text-neural-300 mb-4">Autonomous Capabilities</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold theme-text-primary mb-2">Decision Autonomy</h4>
                    <p className="text-sm theme-text-secondary">
                      This agent can make autonomous decisions within its defined scope and safety parameters.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold theme-text-primary mb-2">Learning Mode</h4>
                    <p className="text-sm theme-text-secondary">
                      Real-time adaptation based on performance feedback and success patterns.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold theme-text-primary mb-2">Safety Constraints</h4>
                    <p className="text-sm theme-text-secondary">
                      Multi-layered safety protocols ensure all autonomous actions remain within bounds.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold theme-text-primary mb-2">Transparency Level</h4>
                    <p className="text-sm theme-text-secondary">
                      Full decision transparency with reasoning, confidence scores, and impact assessment.
                    </p>
                  </div>
                </div>
              </Card>
            </div>
          )}
        </Container>
      </main>
    </div>
  );
} 