"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useAuthContext } from "@/contexts/AuthContext";
import Button from "@/shared/components/Button";
import Container from "@/shared/components/Container";
import { PerformanceMonitor, LazyWrapper } from "@/shared/components/Performance";
import { AnimationWrapper } from "@/shared/components";
import Link from "next/link";
import { FiZap, FiShoppingBag, FiTrendingUp, FiUsers, FiTool, FiStar, FiArrowRight, FiBell, FiSettings, FiActivity } from "react-icons/fi";
import MobileDashboard from "@/components/dashboard/MobileDashboard";
import { useNotifications, useSuccessNotification, useAINotification } from "@/components/ui/enhanced-notifications";
import { useSystemMetrics } from "@/components/monitoring/SystemMonitoringInitializer";

/**
 * 📱 TRACK 10 ENHANCEMENT: Mobile-First Dashboard Integration
 * 
 * ADDRESSES GAP #2: RESPONSIVE DASHBOARD UI
 * 
 * STRATEGY: Progressive enhancement approach
 * - Mobile-first experience using MobileDashboard component
 * - Desktop experience maintains existing functionality
 * - Responsive breakpoints for optimal UX across devices
 */

export default function Dashboard() {
  const router = useRouter();
  const { user, isAuthenticated, isLoading, logout } = useAuthContext();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Enhanced UI hooks
  const addSuccessNotification = useSuccessNotification();
  const addAINotification = useAINotification();
  const { getLatestMetrics } = useSystemMetrics();

  // Check if user is new (for onboarding)
  useEffect(() => {
    if (user && !localStorage.getItem('dashboard_toured')) {
      setShowOnboarding(true);
      // Welcome notification
      addAINotification(
        'Welcome to CreAItive!',
        'Your AI-powered creative platform is ready. Let\'s start building something amazing!',
        {
          duration: 8000,
          action: {
            label: 'Take Tour',
            onClick: () => setShowOnboarding(true)
          }
        }
      );
    }
  }, [user, addAINotification]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push("/login");
    }
  }, [isAuthenticated, isLoading, router]);

  const handleLogout = () => {
    logout();
    router.push("/login");
  };

  const dismissOnboarding = () => {
    setShowOnboarding(false);
    localStorage.setItem('dashboard_toured', 'true');
    addSuccessNotification(
      'Welcome aboard!',
      'You\'re all set to start creating with AI-powered tools.'
    );
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-adaptive">
        <div className="relative">
          <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-cosmic-500"></div>
          <div className="absolute inset-0 rounded-full border-t-2 border-b-2 border-nova-500 animate-spin animation-delay-150"></div>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null; // Will redirect in useEffect
  }

  return (
    // TEMPORARILY DISABLED: PerformanceMonitor causing infinite re-renders
    // <PerformanceMonitor 
    //   componentName="Dashboard"
    //   threshold={100}
    //   enableRenderTracking={true}
    //   showDebugInfo={false}
    // >
      <div className="min-h-screen bg-adaptive">
        {/* Enhanced Background patterns */}
        <div className="absolute inset-0 z-0 opacity-30">
          <div className="absolute inset-0 bg-gradient-to-br from-cosmic-50/50 via-transparent to-nova-50/50" />
          <div className="absolute inset-0 bg-gradient-to-tr from-neural-50/30 via-transparent to-aura-50/30" />
          {/* Floating orbs for visual depth */}
          <div className="absolute top-20 left-20 w-32 h-32 bg-cosmic-400/5 rounded-full blur-2xl animate-float" />
          <div className="absolute bottom-20 right-20 w-48 h-48 bg-nova-400/5 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
        </div>
        
        <main className="relative z-10">
          <Container className="theme-spacing-responsive">
            {/* Enhanced Welcome Header with Better Information Architecture */}
            <AnimationWrapper animation="fadeIn" duration={600}>
              <div className="theme-card theme-spacing-responsive mb-responsive-md relative overflow-hidden">
                {/* Header Background Gradient */}
                <div className="absolute inset-0 bg-gradient-to-r from-cosmic-500/5 via-transparent to-nova-500/5" />
                
                <div className="relative z-10">
                  <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-responsive-sm">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-responsive-xs">
                        <AnimationWrapper animation="slideLeft" duration={800} delay={200}>
                          <div className="text-responsive-4xl font-display font-bold text-gradient-cosmic">
                            Welcome back, {user?.name || user?.email?.split('@')[0]}!
                          </div>
                        </AnimationWrapper>
                        {/* Quick Actions Toolbar */}
                        <div className="hidden lg:flex items-center gap-2">
                          <button className="p-2 rounded-lg bg-space-700/50 hover:bg-space-600/50 transition-colors duration-200 group">
                            <FiBell className="w-5 h-5 text-white/70 group-hover:text-white" />
                          </button>
                          <button className="p-2 rounded-lg bg-space-700/50 hover:bg-space-600/50 transition-colors duration-200 group">
                            <FiSettings className="w-5 h-5 text-white/70 group-hover:text-white" />
                          </button>
                        </div>
                      </div>
                      <AnimationWrapper animation="slideLeft" duration={800} delay={400}>
                        <div className="theme-text-secondary text-responsive-lg mb-responsive-sm">
                          Your unlimited creative universe awaits. Ready to create something revolutionary?
                        </div>
                      </AnimationWrapper>
                      
                      {/* Enhanced Activity Summary */}
                      <AnimationWrapper animation="slideUp" duration={600} delay={600}>
                        <div className="flex flex-wrap items-center gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                            <span className="text-white/60">System Active</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FiActivity className="w-4 h-4 text-cosmic-400" />
                            <span className="text-white/60">Last active: Today</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <FiZap className="w-4 h-4 text-nova-400" />
                            <span className="text-white/60">AI Ready</span>
                          </div>
                        </div>
                      </AnimationWrapper>
                    </div>
                    
                    {/* Enhanced User Avatar */}
                    <AnimationWrapper animation="scaleIn" duration={600} delay={300}>
                      <div className="flex-shrink-0 mt-4 lg:mt-0">
                        <div className="w-20 h-20 rounded-full bg-gradient-to-br from-cosmic-400 to-nova-400 flex items-center justify-center shadow-cosmic relative group">
                          <span className="text-white text-responsive-2xl font-bold">
                            {(user?.name || user?.email)?.[0]?.toUpperCase()}
                          </span>
                          {/* Status indicator */}
                          <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-400 rounded-full border-2 border-space-900 flex items-center justify-center">
                            <div className="w-2 h-2 bg-white rounded-full" />
                          </div>
                        </div>
                      </div>
                    </AnimationWrapper>
                  </div>
                  
                  {/* Enhanced Revolutionary Vision Badge */}
                  <AnimationWrapper animation="slideUp" duration={600} delay={800}>
                    <div className="bg-gradient-to-r from-cosmic-500/10 to-nova-500/10 theme-border-elevated border rounded-lg theme-spacing-responsive-sm relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-r from-cosmic-400/5 to-nova-400/5" />
                      <div className="relative z-10">
                        <div className="flex items-center justify-between mb-responsive-xs">
                          <div className="flex items-center space-x-2">
                            <FiStar className="brand-cosmic animate-pulse" />
                            <span className="font-semibold theme-text-primary text-responsive">Revolutionary Platform Access</span>
                            <span className="bg-gradient-to-r from-cosmic-500 to-nova-500 text-white text-responsive-xs px-3 py-1 rounded-full font-bold shadow-cosmic">
                              UNLIMITED
                            </span>
                          </div>
                          {showOnboarding && (
                            <button 
                              onClick={dismissOnboarding}
                              className="text-white/60 hover:text-white transition-colors duration-200"
                            >
                              ✕
                            </button>
                          )}
                        </div>
                        <p className="text-responsive-sm theme-text-secondary">
                          You're part of the revolution! Access to unlimited creative tools, AI agents, and the future marketplace.
                        </p>
                      </div>
                    </div>
                  </AnimationWrapper>
                </div>
              </div>
            </AnimationWrapper>

            {/* New User Onboarding Guide */}
            {showOnboarding && (
              <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                <AnimationWrapper animation="scaleIn" duration={600}>
                  <div className="theme-card theme-spacing-responsive max-w-2xl w-full relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-cosmic-500/10 to-nova-500/10 rounded-lg" />
                    
                    <div className="relative z-10">
                      <div className="flex items-center justify-between mb-responsive-md">
                        <h3 className="heading-3 text-gradient-cosmic">Welcome to the Revolution!</h3>
                        <button 
                          onClick={dismissOnboarding}
                          className="text-white/60 hover:text-white transition-colors duration-200 text-2xl leading-none"
                        >
                          ✕
                        </button>
                      </div>
                      
                      <div className="space-y-4 mb-responsive-lg">
                        <div className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                          <div>
                            <p className="font-semibold text-white mb-1">Create Unlimited Projects</p>
                            <p className="text-white/60 text-sm">Access powerful AI-enhanced creative tools and start building your vision</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-nova-400 rounded-full mt-2 flex-shrink-0" />
                          <div>
                            <p className="font-semibold text-white mb-1">Deploy AI Agents</p>
                            <p className="text-white/60 text-sm">Autonomous assistants working 24/7 to enhance your creative workflow</p>
                          </div>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="w-2 h-2 bg-neural-400 rounded-full mt-2 flex-shrink-0" />
                          <div>
                            <p className="font-semibold text-white mb-1">Join the Marketplace</p>
                            <p className="text-white/60 text-sm">Trade creative assets globally and monetize your revolutionary work</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex gap-3">
                        <Button variant="primary" size="lg" onClick={dismissOnboarding} className="flex-1">
                          Start Creating
                        </Button>
                        <Link href="/agents" className="flex-1">
                          <Button variant="outline" size="lg" className="w-full">
                            Meet AI Agents
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </AnimationWrapper>
              </div>
            )}

            {/* Enhanced Revolutionary Features Grid with Improved Card Interactions */}
            <AnimationWrapper animation="fadeIn" duration={800} delay={300}>
              <div className="grid-responsive mb-responsive-md">
                {/* My Creative Projects - Enhanced with Progress Visualization */}
                <AnimationWrapper animation="slideUp" duration={600} delay={100}>
                  <div className="theme-card theme-spacing-responsive group hover:shadow-cosmic animate-theme-normal relative overflow-hidden cursor-pointer">
                    {/* Card Background Gradient on Hover */}
                    <div className="absolute inset-0 bg-gradient-to-br from-cosmic-500/10 via-transparent to-nova-500/10 opacity-0 group-hover:opacity-100 transition-all duration-500" />
                    
                    <div className="relative z-10">
                      <div className="flex items-center justify-between mb-responsive-sm">
                        <div className="flex items-center">
                          <div className="w-12 h-12 rounded-lg brand-cosmic-bg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                            <FiTool className="w-6 h-6 text-white" />
                          </div>
                          <h2 className="text-responsive-xl font-display font-semibold theme-text-primary group-hover:text-gradient-cosmic transition-all duration-300">My Projects</h2>
                        </div>
                        {/* Project count badge */}
                        <div className="bg-cosmic-500/20 text-cosmic-300 text-xs px-2 py-1 rounded-full font-bold">
                          0
                        </div>
                      </div>
                      
                      <p className="theme-text-secondary mb-responsive-md text-responsive group-hover:text-white/90 transition-colors duration-300">
                        Unlimited creative projects with AI enhancement and collaboration.
                      </p>
                      
                      {/* Enhanced Progress Indicators */}
                      <div className="space-y-3 mb-responsive-md">
                        <div className="flex items-center justify-between">
                          <span className="text-responsive-sm theme-text-tertiary">✨ Projects created</span>
                          <span className="text-responsive-sm font-semibold text-cosmic-300">0</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-responsive-sm theme-text-tertiary">🤖 AI assistance</span>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                            <span className="text-responsive-sm font-semibold text-green-400">Ready</span>
                          </div>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-responsive-sm theme-text-tertiary">🔄 Auto-sync</span>
                          <span className="text-responsive-sm font-semibold text-nova-300">Enabled</span>
                        </div>
                      </div>
                      
                      <Link href="/creative">
                        <Button variant="primary" size="sm" className="w-full group-hover:shadow-cosmic animate-theme-normal text-responsive-sm relative overflow-hidden">
                          <span className="relative z-10">Open Creative Hub</span>
                          <FiArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                          <div className="absolute inset-0 bg-gradient-to-r from-cosmic-400/20 to-nova-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </AnimationWrapper>

                {/* AI Agent Assistants - Enhanced with Status Indicators */}
                <AnimationWrapper animation="slideUp" duration={600} delay={200}>
                  <div className="theme-card theme-spacing-responsive group hover:shadow-nova animate-theme-normal relative overflow-hidden cursor-pointer">
                    <div className="absolute inset-0 bg-gradient-to-br from-nova-500/10 via-transparent to-cosmic-500/10 opacity-0 group-hover:opacity-100 transition-all duration-500" />
                    
                    <div className="relative z-10">
                      <div className="flex items-center justify-between mb-responsive-sm">
                        <div className="flex items-center">
                          <div className="w-12 h-12 rounded-lg brand-nova-bg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                            <FiZap className="w-6 h-6 text-white" />
                          </div>
                          <h2 className="text-responsive-xl font-display font-semibold theme-text-primary group-hover:text-gradient-nova transition-all duration-300">AI Agents</h2>
                        </div>
                        <div className="bg-nova-500/20 text-nova-300 text-xs px-2 py-1 rounded-full font-bold animate-pulse">
                          3
                        </div>
                      </div>
                      
                      <p className="theme-text-secondary mb-responsive-md text-responsive group-hover:text-white/90 transition-colors duration-300">
                        Your personal creative assistants ready to help with any task.
                      </p>
                      
                      <div className="space-y-3 mb-responsive-md">
                        {[
                          { name: "🎨 Design Agent", status: "Available", color: "text-green-400" },
                          { name: "✍️ Writing Agent", status: "Available", color: "text-green-400" },
                          { name: "🎵 Music Agent", status: "Available", color: "text-green-400" }
                        ].map((agent, index) => (
                          <AnimationWrapper key={index} animation="slideLeft" duration={400} delay={300 + (index * 100)}>
                            <div className="flex items-center justify-between">
                              <span className="text-responsive-sm theme-text-tertiary">{agent.name}</span>
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                                <span className={`text-responsive-sm font-semibold ${agent.color}`}>{agent.status}</span>
                              </div>
                            </div>
                          </AnimationWrapper>
                        ))}
                      </div>
                      
                      <Link href="/agents">
                        <Button variant="primary" size="sm" className="w-full group-hover:shadow-nova animate-theme-normal text-responsive-sm relative overflow-hidden">
                          <span className="relative z-10">Manage AI Agents</span>
                          <FiArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                          <div className="absolute inset-0 bg-gradient-to-r from-nova-400/20 to-cosmic-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </AnimationWrapper>

                {/* Marketplace Preview - Enhanced with Timeline Visualization */}
                <AnimationWrapper animation="slideUp" duration={600} delay={300}>
                  <div className="theme-card theme-spacing-responsive group hover:shadow-neural animate-theme-normal relative overflow-hidden cursor-pointer">
                    <div className="absolute inset-0 bg-gradient-to-br from-neural-500/10 via-transparent to-quantum-500/10 opacity-0 group-hover:opacity-100 transition-all duration-500" />
                    
                    <div className="relative z-10">
                      <div className="flex items-center mb-responsive-sm">
                        <div className="w-12 h-12 rounded-lg brand-neural-bg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                          <FiShoppingBag className="w-6 h-6 text-white" />
                        </div>
                        <h2 className="text-responsive-xl font-display font-semibold theme-text-primary group-hover:text-gradient-neural transition-all duration-300">Creative Hub</h2>
                        <div className="ml-2 bg-gradient-to-r from-cosmic-500 to-nova-500 text-white text-responsive-xs px-3 py-1 rounded-full font-bold animate-pulse">
                          ACTIVE
                        </div>
                      </div>
                      
                      <p className="theme-text-secondary mb-responsive-md text-responsive group-hover:text-white/90 transition-colors duration-300">
                        Access AI Canvas, Gallery, Marketplace, and all creative tools in one hub.
                      </p>
                      
                      <div className="space-y-3 mb-responsive-md">
                        {[
                          { feature: "🎨 AI Canvas", timeline: "Active", progress: 100 },
                          { feature: "🖼️ Gallery", timeline: "Active", progress: 100 },
                          { feature: "🏪 Marketplace", timeline: "Active", progress: 85 }
                        ].map((item, index) => (
                          <AnimationWrapper key={index} animation="slideLeft" duration={600} delay={400 + (index * 100)}>
                            <div className="space-y-1">
                              <div className="flex items-center justify-between">
                                <span className="text-responsive-sm theme-text-tertiary">{item.feature}</span>
                                <span className="text-responsive-sm font-semibold text-neural-300">{item.timeline}</span>
                              </div>
                              <div className="w-full bg-space-700/50 rounded-full h-1">
                                <div 
                                  className="bg-gradient-to-r from-neural-400 to-quantum-400 h-1 rounded-full transition-all duration-1000"
                                  style={{ width: `${item.progress}%` }}
                                />
                              </div>
                            </div>
                          </AnimationWrapper>
                        ))}
                      </div>
                      
                      <Link href="/creative">
                        <Button variant="outline" size="sm" className="w-full group-hover:shadow-neural animate-theme-normal text-responsive-sm">
                          <span>Open Creative Hub</span>
                          <FiArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </AnimationWrapper>

                {/* Creative Discovery - Enhanced with Activity Metrics */}
                <AnimationWrapper animation="slideUp" duration={600} delay={400}>
                  <div className="theme-card theme-spacing-responsive group hover:shadow-quantum animate-theme-normal relative overflow-hidden cursor-pointer">
                    <div className="absolute inset-0 bg-gradient-to-br from-quantum-500/10 via-transparent to-aura-500/10 opacity-0 group-hover:opacity-100 transition-all duration-500" />
                    
                    <div className="relative z-10">
                      <div className="flex items-center justify-between mb-responsive-sm">
                        <div className="flex items-center">
                          <div className="w-12 h-12 rounded-lg brand-quantum-bg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                            <FiTrendingUp className="w-6 h-6 text-white" />
                          </div>
                          <h2 className="text-responsive-xl font-display font-semibold theme-text-primary group-hover:text-gradient-quantum transition-all duration-300">Intelligence Hub</h2>
                        </div>
                        <div className="bg-quantum-500/20 text-quantum-300 text-xs px-2 py-1 rounded-full font-bold">
                          NEW
                        </div>
                      </div>
                      
                      <p className="theme-text-secondary mb-responsive-md text-responsive group-hover:text-white/90 transition-colors duration-300">
                        Access AI analytics, autonomous systems, and intelligence insights.
                      </p>
                      
                      <div className="space-y-3 mb-responsive-md">
                        {[
                          { metric: "🧠 AI Analytics", value: "Real-time", icon: FiTrendingUp },
                          { metric: "🤖 28 Agents", value: "Active", icon: FiZap },
                          { metric: "⚡ Intelligence", value: "Autonomous", icon: FiUsers }
                        ].map((item, index) => (
                          <AnimationWrapper key={index} animation="slideLeft" duration={600} delay={500 + (index * 100)}>
                            <div className="flex items-center justify-between">
                              <span className="text-responsive-sm theme-text-tertiary">{item.metric}</span>
                              <div className="flex items-center gap-2">
                                <item.icon className="w-3 h-3 text-quantum-400" />
                                <span className="text-responsive-sm font-semibold text-quantum-300">{item.value}</span>
                              </div>
                            </div>
                          </AnimationWrapper>
                        ))}
                      </div>
                      
                      <Link href="/intelligence">
                        <Button variant="secondary" size="sm" className="w-full group-hover:shadow-quantum animate-theme-normal text-responsive-sm">
                          <span>Intelligence Hub</span>
                          <FiArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </AnimationWrapper>

                {/* Community & Collaboration - Enhanced with Social Metrics */}
                <AnimationWrapper animation="slideUp" duration={600} delay={500}>
                  <div className="theme-card theme-spacing-responsive group hover:shadow-aura animate-theme-normal relative overflow-hidden cursor-pointer">
                    <div className="absolute inset-0 bg-gradient-to-br from-aura-500/10 via-transparent to-cosmic-500/10 opacity-0 group-hover:opacity-100 transition-all duration-500" />
                    
                    <div className="relative z-10">
                      <div className="flex items-center justify-between mb-responsive-sm">
                        <div className="flex items-center">
                          <div className="w-12 h-12 rounded-lg brand-aura-bg flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                            <FiUsers className="w-6 h-6 text-white" />
                          </div>
                          <h2 className="text-responsive-xl font-display font-semibold theme-text-primary group-hover:text-gradient-aura transition-all duration-300">Community</h2>
                        </div>
                        <div className="bg-aura-500/20 text-aura-300 text-xs px-2 py-1 rounded-full font-bold">
                          ACTIVE
                        </div>
                      </div>
                      
                      <p className="theme-text-secondary mb-responsive-md text-responsive group-hover:text-white/90 transition-colors duration-300">
                        Connect and collaborate with creators across all mediums.
                      </p>
                      
                      <div className="space-y-3 mb-responsive-md">
                        {[
                          { metric: "👥 Connections", value: "0", status: "growing" },
                          { metric: "🤝 Collaborations", value: "Ready", status: "available" },
                          { metric: "💬 Messages", value: "Available", status: "active" }
                        ].map((item, index) => (
                          <AnimationWrapper key={index} animation="slideLeft" duration={600} delay={600 + (index * 100)}>
                            <div className="flex items-center justify-between">
                              <span className="text-responsive-sm theme-text-tertiary">{item.metric}</span>
                              <div className="flex items-center gap-2">
                                <div className={`w-2 h-2 rounded-full ${
                                  item.status === 'growing' ? 'bg-aura-400 animate-pulse' :
                                  item.status === 'available' ? 'bg-green-400 animate-pulse' :
                                  'bg-blue-400 animate-pulse'
                                }`} />
                                <span className="text-responsive-sm font-semibold text-aura-300">{item.value}</span>
                              </div>
                            </div>
                          </AnimationWrapper>
                        ))}
                      </div>
                      
                      <Link href="/community">
                        <Button variant="ghost" size="sm" className="w-full group-hover:shadow-aura animate-theme-normal text-responsive-sm">
                          <span>Join Community</span>
                          <FiArrowRight className="ml-2 w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </AnimationWrapper>

                {/* Account & Settings - Enhanced with Profile Completeness */}
                <AnimationWrapper animation="slideUp" duration={600} delay={600}>
                  <div className="theme-card theme-spacing-responsive group hover:shadow-cosmic animate-theme-normal relative overflow-hidden cursor-pointer">
                    <div className="absolute inset-0 bg-gradient-to-br from-cosmic-500/10 via-transparent to-nova-500/10 opacity-0 group-hover:opacity-100 transition-all duration-500" />
                    
                    <div className="relative z-10">
                      <div className="flex items-center justify-between mb-responsive-sm">
                        <div className="flex items-center">
                          <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-cosmic-600 to-nova-600 flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-300">
                            <FiStar className="w-6 h-6 text-white" />
                          </div>
                          <h2 className="text-responsive-xl font-display font-semibold theme-text-primary group-hover:text-gradient-cosmic transition-all duration-300">Profile</h2>
                        </div>
                        {/* Profile completeness indicator */}
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 relative">
                            <svg className="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
                              <circle cx="16" cy="16" r="14" stroke="currentColor" strokeWidth="2" fill="none" className="text-space-600" />
                              <circle 
                                cx="16" 
                                cy="16" 
                                r="14" 
                                stroke="currentColor" 
                                strokeWidth="2" 
                                fill="none" 
                                className="text-cosmic-400"
                                strokeDasharray={88}
                                strokeDashoffset={88 - (88 * 0.65)}
                                style={{
                                  transition: 'stroke-dashoffset 1s ease-in-out 0.8s'
                                }}
                              />
                            </svg>
                            <div className="absolute inset-0 flex items-center justify-center">
                              <span className="text-xs font-bold text-cosmic-300">65%</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-4 mb-responsive-md">
                        <AnimationWrapper animation="slideLeft" duration={400} delay={700}>
                          <p className="text-responsive-sm theme-text-tertiary mb-1">Email</p>
                          <p className="font-medium theme-text-primary text-sm">{user?.email}</p>
                        </AnimationWrapper>
                        
                        <AnimationWrapper animation="slideLeft" duration={400} delay={800}>
                          <p className="text-responsive-sm theme-text-tertiary mb-1">Member Since</p>
                          <p className="font-medium theme-text-primary text-sm">
                            {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : "Revolutionary Era"}
                          </p>
                        </AnimationWrapper>
                        
                        <AnimationWrapper animation="slideLeft" duration={400} delay={900}>
                          <p className="text-responsive-sm theme-text-tertiary mb-1">Platform Access</p>
                          <div className="flex items-center gap-2">
                            <span className="font-medium brand-cosmic text-sm">Unlimited Creator</span>
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                          </div>
                        </AnimationWrapper>
                      </div>
                      
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={handleLogout}
                        className="w-full group-hover:shadow-cosmic animate-theme-normal text-responsive-sm"
                      >
                        <span>Sign Out</span>
                      </Button>
                    </div>
                  </div>
                </AnimationWrapper>
              </div>
            </AnimationWrapper>

            {/* Revolutionary Roadmap */}
            <div className="theme-card theme-spacing-responsive-lg">
              <h2 className="text-responsive-2xl font-display font-bold mb-responsive-md text-gradient-multi">
                Your Creative Journey on the Revolutionary Platform
              </h2>
              
              <div className="grid md:grid-cols-4 gap-responsive-md">
                <div className="text-center">
                  <div className="w-12 h-12 rounded-full brand-cosmic-bg flex items-center justify-center mx-auto mb-3 shadow-cosmic">
                    <span className="text-white font-bold">1</span>
                  </div>
                  <h3 className="font-semibold theme-text-primary mb-2">Create</h3>
                  <p className="text-responsive-sm theme-text-secondary">Start with unlimited creative tools and AI assistance</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 rounded-full brand-nova-bg flex items-center justify-center mx-auto mb-3 shadow-nova">
                    <span className="text-white font-bold">2</span>
                  </div>
                  <h3 className="font-semibold theme-text-primary mb-2">Collaborate</h3>
                  <p className="text-responsive-sm theme-text-secondary">Connect with creators and AI agents globally</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 rounded-full brand-neural-bg flex items-center justify-center mx-auto mb-3 shadow-neural">
                    <span className="text-white font-bold">3</span>
                  </div>
                  <h3 className="font-semibold theme-text-primary mb-2">Trade</h3>
                  <p className="text-responsive-sm theme-text-secondary">Monetize your work in the revolutionary marketplace</p>
                </div>
                
                <div className="text-center">
                  <div className="w-12 h-12 rounded-full brand-quantum-bg flex items-center justify-center mx-auto mb-3 shadow-quantum">
                    <span className="text-white font-bold">4</span>
                  </div>
                  <h3 className="font-semibold theme-text-primary mb-2">Evolve</h3>
                  <p className="text-responsive-sm theme-text-secondary">Grow with the platform's unlimited capabilities</p>
                </div>
              </div>
            </div>
          </Container>
        </main>
      </div>
    // </PerformanceMonitor>
  );
} 