"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Container from "@/shared/components/Container";
import Heading from "@/shared/components/Heading";
import Button from "@/shared/components/Button/Button";
import { FiT<PERSON>dingUp, <PERSON><PERSON><PERSON>, <PERSON>Filter, <PERSON>Grid, <PERSON>List, FiArrowLeft } from "react-icons/fi";
import TrendingGrid from "@/features/discovery/components/TrendingGrid";
import { trendingService } from "@/features/discovery/services/trendingService";
import { TrendingItem } from "@/features/discovery/types";

export default function TrendingPage() {
  const [trendingItems, setTrendingItems] = useState<TrendingItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<TrendingItem[]>([]);
  const [trendingTags, setTrendingTags] = useState<{ tag: string; count: number }[]>([]);
  const [timeFrame, setTimeFrame] = useState<'today' | 'week' | 'month' | 'all'>('week');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchTrendingData = async () => {
      setIsLoading(true);
      try {
        const [items, tags] = await Promise.all([
          trendingService.getTrendingItems(20),
          trendingService.getTrendingTags(15)
        ]);
        setTrendingItems(items);
        setFilteredItems(items);
        setTrendingTags(tags);
      } catch (error) {
        console.error("Error fetching trending data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchTrendingData();
  }, [timeFrame]);

  // Filter items based on category
  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredItems(trendingItems);
    } else {
      const filtered = trendingItems.filter(item => item.category === selectedCategory);
      setFilteredItems(filtered);
    }
  }, [selectedCategory, trendingItems]);

  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'digital art', name: 'Digital Art' },
    { id: 'illustration', name: 'Illustration' },
    { id: 'audio', name: 'Audio' },
    { id: 'writing', name: 'Writing' },
    { id: '3d', name: '3D Art' },
    { id: 'mixed reality', name: 'Mixed Reality' },
    { id: 'architecture', name: 'Architecture' },
    { id: 'generative art', name: 'Generative Art' },
  ];

  const timeFrameOptions = [
    { id: 'today', name: 'Today', icon: FiClock },
    { id: 'week', name: 'This Week', icon: FiTrendingUp },
    { id: 'month', name: 'This Month', icon: FiTrendingUp },
    { id: 'all', name: 'All Time', icon: FiTrendingUp },
  ];

  return (
    <Container className="py-16">
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <FiArrowLeft className="w-4 h-4" />
          <span>Back to Home</span>
        </Link>
      </div>

      {/* Page Header */}
      <div className="text-center mb-12">
        <Heading level={1} gradient size="3xl" align="center" className="mb-4">
          <FiTrendingUp className="inline mr-2" size={48} />
          Trending Now
        </Heading>
        <p className="text-stardust-400 max-w-2xl mx-auto mb-8">
          Discover what's hot across the creative community. See the most popular and engaging content.
        </p>

        {/* Time Frame and Filter Controls */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
          {/* Time Frame Filter */}
          <div className="flex rounded-lg overflow-hidden border border-space-700">
            {timeFrameOptions.map(option => (
              <button
                key={option.id}
                className={`px-4 py-2 text-sm flex items-center gap-2 ${
                  timeFrame === option.id 
                    ? 'bg-space-700 text-stardust-200' 
                    : 'bg-space-800 text-stardust-400 hover:bg-space-700'
                } transition-colors`}
                onClick={() => setTimeFrame(option.id as any)}
              >
                <option.icon size={16} />
                {option.name}
              </button>
            ))}
          </div>

          {/* View Mode Toggle */}
          <div className="flex rounded-lg overflow-hidden border border-space-700">
            <button 
              className={`p-3 ${viewMode === 'grid' ? 'bg-space-700 text-stardust-200' : 'bg-space-800 text-stardust-400'} hover:bg-space-700 transition-colors`}
              onClick={() => setViewMode('grid')}
            >
              <FiGrid size={18} />
            </button>
            <button 
              className={`p-3 ${viewMode === 'list' ? 'bg-space-700 text-stardust-200' : 'bg-space-800 text-stardust-400'} hover:bg-space-700 transition-colors`}
              onClick={() => setViewMode('list')}
            >
              <FiList size={18} />
            </button>
          </div>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {categories.map(category => (
            <button
              key={category.id}
              className={`px-4 py-2 rounded-full text-sm transition-all duration-300 ${
                selectedCategory === category.id
                  ? 'bg-cosmic-600 text-white shadow-cosmic'
                  : 'bg-space-800 text-stardust-300 hover:bg-space-700'
              }`}
              onClick={() => setSelectedCategory(category.id)}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Trending Tags */}
      <div className="mb-16">
        <Heading level={2} size="xl" className="text-stardust-200 mb-4">
          Trending Tags
        </Heading>
        <div className="flex flex-wrap gap-2">
          {isLoading ? (
            Array.from({ length: 10 }).map((_, index) => (
              <div key={index} className="h-8 bg-space-800 rounded-full animate-pulse w-20"></div>
            ))
          ) : (
            trendingTags.map(({ tag, count }) => (
              <span
                key={tag}
                className="px-3 py-1 bg-gradient-to-r from-space-800 to-space-700 rounded-full text-stardust-300 text-sm border border-space-600 hover:border-cosmic-500 cursor-pointer transition-all duration-300"
              >
                #{tag} <span className="text-stardust-500">({count})</span>
              </span>
            ))
          )}
        </div>
      </div>

      {/* Trending Content */}
      <div className="mb-16">
        <div className="flex items-center justify-between mb-6">
          <div>
            <Heading level={2} size="2xl" className="text-stardust-200 mb-1">
              {selectedCategory === 'all' ? 'All Trending Content' : `Trending in ${categories.find(c => c.id === selectedCategory)?.name}`}
            </Heading>
            <p className="text-stardust-400">
              {filteredItems.length} trending {filteredItems.length === 1 ? 'item' : 'items'} for {timeFrameOptions.find(t => t.id === timeFrame)?.name.toLowerCase()}
            </p>
          </div>
        </div>

        {isLoading ? (
          <TrendingGrid limit={12} />
        ) : filteredItems.length === 0 ? (
          <div className="text-center py-16 bg-space-800/50 rounded-lg">
            <FiTrendingUp size={48} className="mx-auto text-stardust-600 mb-4" />
            <Heading level={3} size="xl" className="text-stardust-300 mb-2">
              No trending content
            </Heading>
            <p className="text-stardust-400 mb-6">
              No trending content found for the selected filters
            </p>
            <Button variant="outline" onClick={() => {
              setSelectedCategory('all');
              setTimeFrame('week');
            }}>
              Reset Filters
            </Button>
          </div>
        ) : viewMode === 'grid' ? (
          <TrendingGrid 
            title=""
            subtitle=""
            limit={filteredItems.length}
            showViewAll={false}
          />
        ) : (
          // List view
          <div className="space-y-4">
            {filteredItems.map((item, index) => (
              <div key={item.id} className="neo-card p-6 flex items-center gap-6 hover:shadow-cosmic transition-all duration-300">
                <div className="flex-shrink-0 text-2xl font-bold text-cosmic-400 w-12">
                  #{index + 1}
                </div>
                <div className="w-16 h-16 rounded-lg overflow-hidden bg-space-700 flex-shrink-0">
                  {item.thumbnail ? (
                    <div 
                      className="w-full h-full bg-cover bg-center"
                      style={{ backgroundImage: `url(${item.thumbnail})` }}
                    />
                  ) : (
                    <div className="w-full h-full bg-gradient-to-br from-cosmic-500 to-nova-500"></div>
                  )}
                </div>
                <div className="flex-grow">
                  <h3 className="text-stardust-200 font-semibold text-lg mb-1">{item.title}</h3>
                  <p className="text-stardust-400 text-sm mb-2">by {item.creator.name}</p>
                  <div className="flex items-center gap-4 text-sm text-stardust-500">
                    <span>{item.engagement.views.toLocaleString()} views</span>
                    <span>{item.engagement.likes.toLocaleString()} likes</span>
                    <span className="capitalize">{item.category}</span>
                  </div>
                </div>
                <div className="flex-shrink-0 text-right">
                  <div className="text-nova-400 font-bold text-lg">
                    {Math.floor(item.trendingScore)}
                  </div>
                  <div className="text-stardust-500 text-sm">score</div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* CTA Section */}
      <div className="text-center py-16 bg-gradient-to-br from-nova-900/20 via-cosmic-900/20 to-neural-900/20 rounded-2xl">
        <Heading level={2} size="2xl" className="text-stardust-200 mb-4">
          Ready to Trend?
        </Heading>
        <p className="text-stardust-400 max-w-xl mx-auto mb-8">
          Create amazing content and see it rise to the top of the trending charts
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <Link href="/upload">
            <Button variant="primary" size="lg">
              Share Your Work
            </Button>
          </Link>
          <Link href="/explore">
            <Button variant="outline" size="lg">
              Explore More
            </Button>
          </Link>
        </div>
      </div>
    </Container>
  );
} 