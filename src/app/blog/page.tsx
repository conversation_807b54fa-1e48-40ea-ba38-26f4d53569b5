'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Calendar, User, ArrowRight, Search, Tag, ArrowLeft } from 'lucide-react';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  author: string;
  date: string;
  category: string;
  readTime: string;
  featured: boolean;
}

const blogPosts: BlogPost[] = [
  {
    id: '1',
    title: 'The Future of AI Agent Collaboration',
    excerpt: 'Exploring how 41 specialized AI agents work together to create unprecedented creative possibilities.',
    author: 'CreAItive Team',
    date: '2024-01-15',
    category: 'AI Innovation',
    readTime: '5 min read',
    featured: true
  },
  {
    id: '2',
    title: 'Building Intelligent Workflows with Agent Swarms',
    excerpt: 'Learn how to orchestrate multiple AI agents to automate complex creative and technical workflows.',
    author: 'CreAItive Team',
    date: '2024-01-12',
    category: 'Tutorials',
    readTime: '8 min read',
    featured: false
  },
  {
    id: '3',
    title: 'Real-Time AI Adaptation: The CreAItive Advantage',
    excerpt: 'Discover how our platform learns and adapts to your unique workflow patterns in real-time.',
    author: 'CreAItive Team',
    date: '2024-01-10',
    category: 'Technology',
    readTime: '6 min read',
    featured: false
  },
  {
    id: '4',
    title: 'From Concept to Creation: AI-Powered Design Workflows',
    excerpt: 'A deep dive into how AI agents can transform your creative process from ideation to final output.',
    author: 'CreAItive Team',
    date: '2024-01-08',
    category: 'Design',
    readTime: '7 min read',
    featured: false
  }
];

const categories = ['All', 'AI Innovation', 'Tutorials', 'Technology', 'Design'];

export default function BlogPage() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchTerm, setSearchTerm] = useState('');

  const filteredPosts = blogPosts.filter(post => {
    const matchesCategory = selectedCategory === 'All' || post.category === selectedCategory;
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.excerpt.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const featuredPost = blogPosts.find(post => post.featured);
  const regularPosts = filteredPosts.filter(post => !post.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900">
      <div className="container mx-auto px-4 py-12">
        {/* Breadcrumb Navigation */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            CreAItive
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-purple-400 ml-2">
              Blog
            </span>
          </h1>
          <p className="text-xl text-white/70 max-w-2xl mx-auto">
            Insights, tutorials, and updates from the world of AI-powered creativity
          </p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-12">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-white/40" />
            <input
              type="text"
              placeholder="Search articles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-3 bg-space-800/50 border border-white/10 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-cosmic-500/50"
            />
          </div>
          
          <div className="flex gap-2 overflow-x-auto">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg whitespace-nowrap transition-all ${
                  selectedCategory === category
                    ? 'bg-cosmic-500/20 text-cosmic-300 border border-cosmic-500/30'
                    : 'bg-space-800/50 text-white/60 hover:text-white hover:bg-space-700/50'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>

        {/* Featured Post */}
        {featuredPost && selectedCategory === 'All' && !searchTerm && (
          <div className="mb-12">
            <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 border border-white/10">
              <div className="flex items-center gap-2 mb-4">
                <Tag className="w-4 h-4 text-cosmic-400" />
                <span className="text-cosmic-300 text-sm font-medium">Featured</span>
              </div>
              
              <h2 className="text-3xl font-bold text-white mb-4">{featuredPost.title}</h2>
              <p className="text-white/70 text-lg mb-6">{featuredPost.excerpt}</p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-white/60">
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4" />
                    <span>{featuredPost.author}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4" />
                    <span>{new Date(featuredPost.date).toLocaleDateString()}</span>
                  </div>
                  <span>{featuredPost.readTime}</span>
                </div>
                
                <Link
                  href={`/blog/${featuredPost.id}`}
                  className="flex items-center gap-2 text-cosmic-400 hover:text-cosmic-300 transition-colors"
                >
                  Read More
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Blog Posts Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {regularPosts.map((post) => (
            <article
              key={post.id}
              className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:border-cosmic-500/30 transition-all group"
            >
              <div className="flex items-center gap-2 mb-3">
                <span className="px-2 py-1 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full">
                  {post.category}
                </span>
              </div>
              
              <h3 className="text-xl font-semibold text-white mb-3 group-hover:text-cosmic-300 transition-colors">
                {post.title}
              </h3>
              
              <p className="text-white/60 mb-4 line-clamp-3">
                {post.excerpt}
              </p>
              
              <div className="flex items-center justify-between text-sm text-white/50">
                <div className="flex items-center gap-3">
                  <span>{post.author}</span>
                  <span>•</span>
                  <span>{new Date(post.date).toLocaleDateString()}</span>
                </div>
                <span>{post.readTime}</span>
              </div>
              
              <Link
                href={`/blog/${post.id}`}
                className="inline-flex items-center gap-2 text-cosmic-400 hover:text-cosmic-300 transition-colors mt-4"
              >
                Read More
                <ArrowRight className="w-4 h-4" />
              </Link>
            </article>
          ))}
        </div>

        {/* No Results */}
        {filteredPosts.length === 0 && (
          <div className="text-center py-12">
            <p className="text-white/60 text-lg">No articles found matching your criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
}
