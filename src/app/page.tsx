"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import Button from "@/shared/components/Button";
import Container from "@/shared/components/Container";
import { LazyWrapper, PerformanceMonitor } from "@/shared/components/Performance";
import { AnimationWrapper } from "@/shared/components";
import { FiArrowRight, FiCheck, FiGitBranch, FiUser, FiStar, FiZap } from "react-icons/fi";

export default function Home() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen bg-adaptive flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-cosmic-500"></div>
      </div>
    );
  }



  return (
    // TEMPORARILY DISABLED: PerformanceMonitor causing infinite re-renders
    // <PerformanceMonitor 
    //   componentName="Homepage"
    //   threshold={100}
    //   enableRenderTracking={true}
    //   showDebugInfo={false}
    // >
      <div className="min-h-screen bg-adaptive">
        {/* Background patterns */}
        <div className="absolute inset-0 z-0 opacity-40">
          <div className="absolute inset-0 bg-gradient-to-br from-cosmic-50/30 via-transparent to-nova-50/30" />
          <div className="absolute inset-0 bg-gradient-to-tr from-neural-50/20 via-transparent to-aura-50/20" />
        </div>

        <main className="relative z-10">
          {/* Enhanced Hero Section */}
          <section className="relative py-responsive-xl overflow-hidden">
            {/* Enhanced Background with Particles */}
            <div className="absolute inset-0 z-0">
              <div className="absolute inset-0 bg-gradient-to-br from-cosmic-500/20 via-space-900 to-nova-500/20" />
              <div className="absolute inset-0 bg-gradient-to-tr from-neural-500/10 via-transparent to-aura-500/10" />
              {/* Animated background orbs */}
              <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-cosmic-400/10 rounded-full blur-3xl animate-float" />
              <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-nova-400/10 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }} />
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 bg-neural-400/20 rounded-full blur-2xl animate-pulse" />
            </div>

            <Container className="relative z-10 text-center theme-spacing-responsive">
              <LazyWrapper componentName="HeroAnimation">
                <AnimationWrapper animation="fadeIn" duration={1000}>
                  {/* Enhanced Title with Better Typography */}
                  <AnimationWrapper animation="scaleIn" duration={1200} delay={200}>
                    <div className="heading-1 mb-responsive-md text-gradient-cosmic animate-fade-in relative">
                      <span className="block">The Revolution</span>
                      <span className="block text-gradient-nova">Starts Here</span>
                      {/* Glowing accent */}
                      <div className="absolute -inset-4 bg-cosmic-400/20 rounded-full blur-2xl -z-10 animate-pulse" />
                    </div>
                  </AnimationWrapper>
                  
                  {/* Enhanced Description with Better Spacing */}
                  <AnimationWrapper animation="slideUp" duration={800} delay={400}>
                    <div className="body-large mb-responsive-lg text-white/90 max-w-4xl mx-auto leading-relaxed">
                      <span className="text-gradient-multi font-medium">Where unlimited human creativity</span> meets the{' '}
                      <span className="text-gradient-cosmic font-medium">infinite potential of artificial intelligence</span>.{' '}
                      Create, discover, and revolutionize the future of creative expression.
                    </div>
                  </AnimationWrapper>
                  
                  {/* Enhanced CTA Buttons with Improved Design */}
                  <AnimationWrapper animation="slideUp" duration={800} delay={600}>
                    <div className="flex flex-col sm:flex-row gap-responsive-sm justify-center items-center mb-responsive-lg">
                      <Link href="/dashboard" className="group">
                        <div className="neo-button neo-button-primary text-responsive-lg px-responsive-lg py-responsive-md flex items-center gap-3 relative overflow-hidden transition-all duration-300 hover:scale-105 hover:shadow-cosmic">
                          <span className="relative z-10">Start Creating</span>
                          <FiArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                          {/* Hover glow effect */}
                          <div className="absolute inset-0 bg-gradient-to-r from-cosmic-400/20 to-nova-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                        </div>
                      </Link>
                      <Link href="/gallery" className="group">
                        <div className="neo-button neo-button-secondary text-responsive-lg px-responsive-lg py-responsive-md flex items-center gap-3 transition-all duration-300 hover:scale-105 hover:shadow-nova">
                          <span>Explore Gallery</span>
                          <FiStar className="w-5 h-5 transition-transform duration-300 group-hover:rotate-12" />
                        </div>
                      </Link>
                    </div>
                  </AnimationWrapper>

                  {/* New: Trust Indicators */}
                  <AnimationWrapper animation="fadeIn" duration={800} delay={800}>
                    <div className="flex flex-wrap justify-center items-center gap-6 text-white/60 text-sm">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                        <span>10K+ Active Creators</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-cosmic-400 rounded-full animate-pulse" style={{ animationDelay: '0.5s' }} />
                        <span>50+ AI Agents</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-nova-400 rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
                        <span>24/7 Autonomous</span>
                      </div>
                    </div>
                  </AnimationWrapper>
                </AnimationWrapper>
              </LazyWrapper>
            </Container>
          </section>

          {/* Main Platform Sections Overview */}
          <section className="py-responsive-xl relative">
            <Container className="relative z-10 theme-spacing-responsive">
              <LazyWrapper componentName="PlatformSections">
                <AnimationWrapper animation="slideUp" trigger="scroll" duration={800}>
                  <div className="text-center mb-responsive-xl">
                    <h2 className="heading-2 mb-responsive-sm text-gradient-multi relative">
                      🌟 Complete Autonomous AI Platform
                      <div className="absolute -bottom-4 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gradient-to-r from-cosmic-400 via-nova-400 to-neural-400 rounded-full" />
                    </h2>
                    <div className="body-large text-white/80 max-w-4xl mx-auto mb-responsive-lg">
                      Navigate through our comprehensive autonomous AI ecosystem with 52+ pages organized into logical sections
                    </div>
                  </div>
                </AnimationWrapper>
              </LazyWrapper>

              {/* Main Platform Sections Grid */}
              <div className="grid lg:grid-cols-3 gap-responsive-lg mb-responsive-xl">
                {/* Section 1: AI Intelligence Hub */}
                <LazyWrapper componentName="AIIntelligenceHub">
                  <AnimationWrapper animation="slideUp" trigger="scroll" duration={600} delay={0}>
                    <div className="theme-card theme-spacing-responsive group hover:scale-105 transition-all duration-500 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-br from-cosmic-500/10 to-nova-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      
                      <div className="relative z-10">
                        <div className="flex items-center mb-responsive-md">
                          <div className="w-12 h-12 bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-xl flex items-center justify-center mr-4">
                            <span className="text-2xl">🧠</span>
                          </div>
                          <div>
                            <h3 className="heading-3 text-gradient-cosmic">Intelligence Hub</h3>
                            <p className="text-white/60 text-sm">AI Analytics • Real-time Intelligence</p>
                          </div>
                        </div>

                        <div className="mb-responsive-sm">
                          <Link href="/intelligence" className="block w-full text-center px-4 py-3 bg-cosmic-500/20 hover:bg-cosmic-500/30 rounded-lg transition-colors text-cosmic-300 hover:text-cosmic-200 font-medium">
                            🧠 Explore Intelligence Hub
                          </Link>
                        </div>

                        <div className="text-white/60 text-xs space-y-1">
                          <div>• Real-Time Coordination</div>
                          <div>• Cross-Agent Learning</div>
                          <div>• Advanced ML Engine</div>
                          <div>• Autonomous Decision Making</div>
                        </div>
                      </div>
                    </div>
                  </AnimationWrapper>
                </LazyWrapper>

                {/* Section 2: Creative Tools */}
                <LazyWrapper componentName="CreativeTools">
                  <AnimationWrapper animation="slideUp" trigger="scroll" duration={600} delay={100}>
                    <div className="theme-card theme-spacing-responsive group hover:scale-105 transition-all duration-500 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-br from-nova-500/10 to-aura-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      
                      <div className="relative z-10">
                        <div className="flex items-center mb-responsive-md">
                          <div className="w-12 h-12 bg-gradient-to-br from-nova-500 to-aura-500 rounded-xl flex items-center justify-center mr-4">
                            <span className="text-2xl">🎨</span>
                          </div>
                          <div>
                            <h3 className="heading-3 text-gradient-nova">Creative Hub</h3>
                            <p className="text-white/60 text-sm">AI Canvas • Creative Tools • Gallery</p>
                          </div>
                        </div>

                        <div className="mb-responsive-sm">
                          <Link href="/creative" className="block w-full text-center px-4 py-3 bg-nova-500/20 hover:bg-nova-500/30 rounded-lg transition-colors text-nova-300 hover:text-nova-200 font-medium">
                            🎨 Explore Creative Hub
                          </Link>
                        </div>

                        <div className="text-white/60 text-xs space-y-1">
                          <div>• AI-Powered Creation</div>
                          <div>• Asset Management</div>
                          <div>• Voice Interface</div>
                          <div>• Global Marketplace</div>
                        </div>
                      </div>
                    </div>
                  </AnimationWrapper>
                </LazyWrapper>

                {/* Section 3: Collaboration & System */}
                <LazyWrapper componentName="CollaborationSystem">
                  <AnimationWrapper animation="slideUp" trigger="scroll" duration={600} delay={200}>
                    <div className="theme-card theme-spacing-responsive group hover:scale-105 transition-all duration-500 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-br from-neural-500/10 to-quantum-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                      
                      <div className="relative z-10">
                        <div className="flex items-center mb-responsive-md">
                          <div className="w-12 h-12 bg-gradient-to-br from-neural-500 to-quantum-500 rounded-xl flex items-center justify-center mr-4">
                            <span className="text-2xl">👥</span>
                          </div>
                          <div>
                            <h3 className="heading-3 text-gradient-neural">Agent & System Hub</h3>
                            <p className="text-white/60 text-sm">28 Agents • Dashboard • Monitoring</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2 mb-responsive-sm">
                          <Link href="/agents" className="text-sm px-3 py-2 bg-neural-500/10 hover:bg-neural-500/20 rounded-lg transition-colors text-neural-300 hover:text-neural-200">
                            🤖 Agent Hub
                          </Link>
                          <Link href="/dashboard" className="text-sm px-3 py-2 bg-neural-500/10 hover:bg-neural-500/20 rounded-lg transition-colors text-neural-300 hover:text-neural-200">
                            📊 Dashboard
                          </Link>
                        </div>

                        <div className="text-white/60 text-xs space-y-1">
                          <div>• Team Communication</div>
                          <div>• Project Management</div>
                          <div>• System Monitoring</div>
                          <div>• Community Features</div>
                        </div>
                      </div>
                    </div>
                  </AnimationWrapper>
                </LazyWrapper>
              </div>

              {/* Quick Access to Key Features */}
              <LazyWrapper componentName="QuickAccess">
                <AnimationWrapper animation="slideUp" trigger="scroll" duration={600} delay={300}>
                  <div className="text-center">
                    <h3 className="heading-4 mb-responsive-md text-white">🚀 Quick Access to Platform Hubs</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                      <Link href="/intelligence">
                        <Button variant="primary" size="lg" className="text-responsive-lg w-full">
                          🧠 Intelligence
                          <FiArrowRight className="ml-2 w-5 h-5" />
                        </Button>
                      </Link>
                      <Link href="/agents">
                        <Button variant="outline" size="lg" className="text-responsive-lg w-full">
                          🤖 Agents
                          <FiStar className="ml-2 w-5 h-5" />
                        </Button>
                      </Link>
                      <Link href="/creative">
                        <Button variant="secondary" size="lg" className="text-responsive-lg w-full">
                          🎨 Creative
                          <FiZap className="ml-2 w-5 h-5" />
                        </Button>
                      </Link>
                      <Link href="/dashboard">
                        <Button variant="outline" size="lg" className="text-responsive-lg w-full">
                          📊 Dashboard
                          <FiArrowRight className="ml-2 w-5 h-5" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </AnimationWrapper>
              </LazyWrapper>
            </Container>
          </section>

          {/* Revolutionary Vision Section */}
          <section className="py-responsive-xl">
            <Container className="theme-spacing-responsive">
              <div className="max-w-4xl mx-auto text-center">
                <h2 className="heading-2 mb-responsive-md text-gradient-cosmic">
                  Join the Creative Revolution
                </h2>
                
                <div className="body-large text-white/80 mb-responsive-lg">
                  We're not just building another creative platform. We're engineering the future where 
                  human creativity and artificial intelligence merge to create unlimited possibilities. 
                  Where autonomous agents work alongside creators to push the boundaries of what's possible.
                </div>
                
                <div className="grid md:grid-cols-2 gap-responsive-md mb-responsive-lg">
                  <div className="theme-card theme-spacing-responsive text-left">
                    <div className="flex items-center mb-responsive-sm">
                      <FiUser className="w-6 h-6 text-cosmic-400 mr-3" />
                      <h3 className="heading-6">For Creators</h3>
                    </div>
                    <div className="body-base text-white/70">
                      Unlimited creative tools, AI-powered assistance, and global marketplace access. 
                      Create without limits, enhanced by intelligence.
                    </div>
                  </div>
                  
                  <div className="theme-card theme-spacing-responsive text-left">
                    <div className="flex items-center mb-responsive-sm">
                      <FiGitBranch className="w-6 h-6 text-nova-400 mr-3" />
                      <h3 className="heading-6">For Innovators</h3>
                    </div>
                    <div className="body-base text-white/70">
                      Revolutionary AI agents, autonomous systems, and cutting-edge technology. 
                      Shape the future of creative expression.
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-responsive-sm justify-center">
                  <Link href="/dashboard">
                    <Button variant="primary" size="lg" className="text-responsive-lg">
                      Start Your Revolution
                      <FiArrowRight className="ml-2 w-5 h-5" />
                    </Button>
                  </Link>
                  <Link href="/agents">
                    <Button variant="outline" size="lg" className="text-responsive-lg">
                      Explore Agent Hub
                      <FiStar className="ml-2 w-5 h-5" />
                    </Button>
                  </Link>
                </div>
              </div>
            </Container>
          </section>
        </main>
      </div>
    // </PerformanceMonitor>
  );
}
