'use client';

import React from 'react';
import { FileText, Scale, Shield, AlertTriangle, Users, Zap } from 'lucide-react';

export default function TermsPage() {
  const sections = [
    {
      icon: Users,
      title: 'Account Terms',
      content: [
        'You must be 13 years or older to use CreAItive',
        'Provide accurate and complete registration information',
        'Maintain the security of your account credentials',
        'You are responsible for all activity under your account',
        'One account per person; no sharing of accounts'
      ]
    },
    {
      icon: Zap,
      title: 'Service Usage',
      content: [
        'Use CreAItive in compliance with all applicable laws',
        'Do not attempt to reverse engineer or hack the platform',
        'Respect usage limits and fair use policies',
        'Do not use the service for illegal or harmful activities',
        'AI-generated content is subject to platform guidelines'
      ]
    },
    {
      icon: Shield,
      title: 'Intellectual Property',
      content: [
        'You retain rights to content you create using CreAItive',
        'CreAItive retains rights to the platform and AI technology',
        'Respect third-party intellectual property rights',
        'Do not upload copyrighted material without permission',
        'Report any intellectual property violations'
      ]
    },
    {
      icon: AlertTriangle,
      title: 'Prohibited Activities',
      content: [
        'Creating harmful, offensive, or illegal content',
        'Attempting to circumvent security measures',
        'Spamming or sending unsolicited communications',
        'Impersonating others or providing false information',
        'Interfering with other users\' experience'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-cosmic-500/20 px-4 py-2 rounded-full mb-6">
            <Scale className="w-4 h-4 text-cosmic-400" />
            <span className="text-cosmic-300 text-sm font-medium">Terms of Service</span>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Terms of
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-purple-400 ml-2">
              Service
            </span>
          </h1>
          
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            These terms govern your use of CreAItive and our AI agent platform. 
            Please read them carefully.
          </p>
          
          <div className="mt-6 text-white/60">
            <p>Last updated: January 15, 2024</p>
            <p>Effective date: January 15, 2024</p>
          </div>
        </div>

        {/* Agreement Notice */}
        <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 mb-16 border border-white/10">
          <div className="text-center">
            <FileText className="w-12 h-12 text-cosmic-400 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">Agreement to Terms</h2>
            <p className="text-white/70 max-w-2xl mx-auto">
              By accessing or using CreAItive, you agree to be bound by these Terms of Service 
              and all applicable laws and regulations. If you do not agree with any of these terms, 
              you are prohibited from using the service.
            </p>
          </div>
        </div>

        {/* Terms Sections */}
        <div className="space-y-8 mb-16">
          {sections.map((section, index) => (
            <div
              key={index}
              className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="p-2 bg-cosmic-500/20 rounded-lg">
                  <section.icon className="w-5 h-5 text-cosmic-400" />
                </div>
                <h2 className="text-2xl font-bold text-white">{section.title}</h2>
              </div>
              
              <ul className="space-y-3">
                {section.content.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start gap-3">
                    <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-white/70">{item}</span>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Additional Terms */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6">Payment Terms</h2>
            <ul className="space-y-3 text-white/70">
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                Subscription fees are billed in advance
              </li>
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                No refunds for partial months
              </li>
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                Prices may change with 30 days notice
              </li>
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                Failed payments may result in service suspension
              </li>
            </ul>
          </div>
          
          <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6">Service Availability</h2>
            <ul className="space-y-3 text-white/70">
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                We strive for 99.9% uptime but cannot guarantee it
              </li>
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                Scheduled maintenance will be announced in advance
              </li>
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                Emergency maintenance may occur without notice
              </li>
              <li className="flex items-start gap-3">
                <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full mt-2 flex-shrink-0" />
                Service credits may be provided for extended outages
              </li>
            </ul>
          </div>
        </div>

        {/* Limitation of Liability */}
        <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 mb-16">
          <h2 className="text-2xl font-bold text-white mb-6">Limitation of Liability</h2>
          <div className="space-y-4 text-white/70">
            <p>
              CreAItive is provided "as is" without warranties of any kind. We do not guarantee 
              that the service will be uninterrupted, secure, or error-free.
            </p>
            <p>
              In no event shall CreAItive be liable for any indirect, incidental, special, 
              consequential, or punitive damages, including without limitation, loss of profits, 
              data, use, goodwill, or other intangible losses.
            </p>
            <p>
              Our total liability to you for any claim arising out of or relating to these terms 
              or the service shall not exceed the amount you paid us in the twelve months 
              preceding the claim.
            </p>
          </div>
        </div>

        {/* Termination */}
        <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 mb-16">
          <h2 className="text-2xl font-bold text-white mb-6">Termination</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">By You</h3>
              <p className="text-white/70">
                You may terminate your account at any time through your account settings. 
                Termination will be effective at the end of your current billing period.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-semibold text-white mb-3">By Us</h3>
              <p className="text-white/70">
                We may terminate or suspend your account immediately for violations of these terms, 
                illegal activity, or other reasons at our sole discretion.
              </p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-4">Questions About These Terms?</h3>
            <p className="text-white/70 mb-6 max-w-2xl mx-auto">
              If you have any questions about these Terms of Service, please contact our legal team.
            </p>
            <button className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-8 py-3 rounded-lg transition-all">
              Contact Legal Team
            </button>
          </div>
        </div>

        {/* Footer Note */}
        <div className="mt-12 text-center text-white/60 text-sm">
          <p>
            These terms may be updated from time to time. We'll notify you of material changes 
            via email or platform notification. Continued use after changes constitutes acceptance.
          </p>
        </div>
      </div>
    </div>
  );
}
