/**
 * Orchestration Dashboard - Enhanced with Autonomous AI Coordination (Day 12)
 * Multi-Agent Coordination Interface with Autonomous AI Observation
 * Part of Synchronous Backend-Frontend Development Protocol
 * Connects to MLCoordinationLayer backend for real-time coordination
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import dynamic from 'next/dynamic';
import { Card } from '@/shared/components/Card';
import { Button } from '@/shared/components/Button';
import Container from '@/shared/components/Container';

// Core dashboard components
import AgentCommunicationFlows from '@/components/orchestration/AgentCommunicationFlows';
import TaskDistributionMap from '@/components/orchestration/TaskDistributionMap';
import RealTimeOrchestrationStatus from '@/components/orchestration/RealTimeOrchestrationStatus';
import WorkflowManagementControls from '@/components/orchestration/WorkflowManagementControls';

// Dynamic imports for autonomous AI components (Day 12 Enhancement)
const AutonomousHealthIndicator = dynamic(
  () => import('@/components/AutonomousCore/AutonomousHealthIndicator'),
  { ssr: false, loading: () => <div className="h-16 bg-space-800/50 animate-pulse rounded-lg" /> }
);

const AgentCommunicationMonitor = dynamic(
  () => import('@/components/AutonomousCore/AgentCommunicationMonitor'),
  { ssr: false, loading: () => <div className="h-64 bg-space-800/50 animate-pulse rounded-lg" /> }
);

const AIDecisionStream = dynamic(
  () => import('@/components/AutonomousCore/AIDecisionStream'),
  { ssr: false, loading: () => <div className="h-96 bg-space-800/50 animate-pulse rounded-lg" /> }
);

const SystemStatusDashboard = dynamic(
  () => import('@/components/AutonomousCore/SystemStatusDashboard'),
  { ssr: false, loading: () => <div className="h-64 bg-space-800/50 animate-pulse rounded-lg" /> }
);

interface OrchestrationMetrics {
  totalAgents: number;
  activeAgents: number;
  activeTasks: number;
  completedTasks: number;
  averageResponseTime: number;
  systemLoad: number;
  orchestrationHealth: number;
  lastUpdate: string;
}

interface ActiveWorkflow {
  id: string;
  name: string;
  status: 'running' | 'paused' | 'completed' | 'error';
  agentsInvolved: string[];
  progress: number;
  startTime: string;
  estimatedCompletion: string;
}

export default function OrchestrationDashboard() {
  const router = useRouter();
  const [metrics, setMetrics] = useState<OrchestrationMetrics | null>(null);
  const [workflows, setWorkflows] = useState<ActiveWorkflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'communication' | 'distribution' | 'status' | 'workflows' | 'ai-coordination'>('overview');

  // Day 12: Autonomous AI state management
  const [isMounted, setIsMounted] = useState(false);
  const [autonomousMode, setAutonomousMode] = useState(false);
  const [coordinationTransparency, setCoordinationTransparency] = useState(true);
  const [aiWorkflowOptimization, setAiWorkflowOptimization] = useState(false);
  const [realTimeCoordination, setRealTimeCoordination] = useState(true);
  const [autonomousTaskAssignment, setAutonomousTaskAssignment] = useState(false);

  // Day 12: Mount detection for SSR compatibility
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Fetch orchestration data
  const fetchOrchestrationData = async () => {
    try {
      setLoading(true);
      
      // Fetch orchestration metrics
      const metricsResponse = await fetch('/api/orchestration/metrics');
      const metricsData = await metricsResponse.json();
      
      // Fetch active workflows
      const workflowsResponse = await fetch('/api/orchestration/workflows');
      const workflowsData = await workflowsResponse.json();
      
      if (metricsData.success) {
        setMetrics(metricsData.data);
        setError(null);
      } else {
        setError(metricsData.error || 'Failed to fetch orchestration data');
      }
      
      if (workflowsData.success) {
        setWorkflows(workflowsData.data || []);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-400 bg-green-500/20';
      case 'paused': return 'text-yellow-400 bg-yellow-500/20';
      case 'completed': return 'text-blue-400 bg-blue-500/20';
      case 'error': return 'text-red-400 bg-red-500/20';
      default: return 'text-gray-400 bg-gray-500/20';
    }
  };

  // Get health color
  const getHealthColor = (value: number) => {
    if (value >= 80) return 'text-green-400';
    if (value >= 60) return 'text-yellow-400';
    return 'text-red-400';
  };

  useEffect(() => {
    fetchOrchestrationData();
    
    // Poll for updates every 5 seconds for real-time coordination
    const interval = setInterval(fetchOrchestrationData, 5000);
    return () => clearInterval(interval);
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen theme-bg-primary flex items-center justify-center">
        <Card className="p-12 text-center max-w-md">
          <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-cosmic flex items-center justify-center animate-glow mb-6">
            <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
          <h2 className="text-xl font-bold text-gradient-cosmic mb-2">Initializing Orchestration</h2>
          <p className="theme-text-secondary">Loading multi-agent coordination interface...</p>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen theme-bg-primary flex items-center justify-center">
        <Card className="p-12 text-center max-w-md">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-red-400 mb-2">Orchestration Error</h2>
          <p className="theme-text-secondary mb-4">{error}</p>
          <Button
            variant="primary"
            onClick={fetchOrchestrationData}
          >
            Retry Connection
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Background patterns */}
      <div className="absolute inset-0 z-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-cosmic-50/50 via-transparent to-nova-50/50" />
        <div className="absolute inset-0 bg-gradient-to-tr from-neural-50/30 via-transparent to-aura-50/30" />
      </div>
      
      <main className="relative z-10">
        <Container className="py-8">
          {/* Enhanced Header with Autonomous AI Coordination */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-6">
              <Link
                href="/agents"
                className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
              >
                <ArrowLeft className="w-4 h-4" />
                <span>Back to Agent Hub</span>
              </Link>
            </div>

            <div className="flex items-center gap-4 mb-6">
              <div className="w-16 h-16 rounded-xl bg-gradient-cosmic flex items-center justify-center animate-glow">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl font-display font-bold text-gradient-multi">
                  Orchestration Dashboard
                </h1>
                <p className="theme-text-secondary mt-2">
                  Multi-agent coordination with autonomous AI intelligence
                </p>
              </div>
            </div>

            {/* Enhanced Action Buttons with Autonomous Controls */}
            <div className="flex flex-wrap gap-4 mb-6">
              <Link href="/tasks">
                <Button
                  variant="primary"
                  className="animate-glow"
                >
                  + Create Workflow
                </Button>
              </Link>
              <Button
                variant="outline"
                onClick={fetchOrchestrationData}
                disabled={loading}
              >
                {loading ? 'Refreshing...' : 'Refresh Status'}
              </Button>
              <Link href="/agents">
                <Button
                  variant="outline"
                >
                  View Agent Hub
                </Button>
              </Link>
              
              {/* Autonomous AI Controls */}
              {isMounted && (
                <>
                  <button
                    onClick={() => setAutonomousMode(!autonomousMode)}
                    className={`px-4 py-2 rounded-lg font-medium transition-all ${
                      autonomousMode 
                        ? 'bg-cosmic-600 text-white shadow-cosmic animate-pulse' 
                        : 'bg-space-700 text-stardust-300 hover:bg-space-600'
                    }`}
                  >
                    {autonomousMode ? '🤖 Autonomous ON' : '🤖 Enable Autonomous'}
                  </button>
                  
                  <button
                    onClick={() => setCoordinationTransparency(!coordinationTransparency)}
                    className={`px-3 py-2 rounded text-sm transition-colors ${
                      coordinationTransparency 
                        ? 'bg-nova-600 text-white' 
                        : 'bg-space-700 text-stardust-400'
                    }`}
                  >
                    {coordinationTransparency ? '🔍 Coordination Transparency' : '🔍 Enable Transparency'}
                  </button>

                  <button
                    onClick={() => setAiWorkflowOptimization(!aiWorkflowOptimization)}
                    className={`px-3 py-2 rounded text-sm transition-colors ${
                      aiWorkflowOptimization 
                        ? 'bg-neural-600 text-white' 
                        : 'bg-space-700 text-stardust-400'
                    }`}
                  >
                    {aiWorkflowOptimization ? '⚡ AI Optimization' : '⚡ Enable AI Optimization'}
                  </button>

                  <button
                    onClick={() => setAutonomousTaskAssignment(!autonomousTaskAssignment)}
                    className={`px-3 py-2 rounded text-sm transition-colors ${
                      autonomousTaskAssignment 
                        ? 'bg-quantum-600 text-white' 
                        : 'bg-space-700 text-stardust-400'
                    }`}
                  >
                    {autonomousTaskAssignment ? '🎯 Auto-Assignment' : '🎯 Enable Auto-Assignment'}
                  </button>
                </>
              )}
            </div>

            {/* Day 12: Autonomous AI Health Indicator for Orchestration */}
            {isMounted && autonomousMode && (
              <div className="mb-6 p-4 bg-space-900/30 rounded-lg border border-cosmic-400/10">
                <div className="mb-2">
                  <h3 className="text-lg font-bold text-gradient-cosmic mb-1">🤖 Autonomous Orchestration Status</h3>
                  <p className="text-stardust-400 text-sm">Real-time autonomous coordination and workflow management</p>
                </div>
                <AutonomousHealthIndicator
                  variant="detailed"
                  showExpanded={true}
                  className="shadow-md"
                />
              </div>
            )}
          </div>

          {/* Existing Orchestration Metrics - Preserved */}
          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="neo-panel p-6 backdrop-blur-md">
                <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Active Agents</h3>
                <div className="text-3xl font-bold text-gradient-cosmic mb-2">
                  {metrics.activeAgents}/{metrics.totalAgents}
                </div>
                <div className="text-xs text-muted-enhanced">
                  Agent coordination active
                  {autonomousMode && <span className="text-cosmic-400 ml-1">• AI Enhanced</span>}
                </div>
              </div>
              
              <div className="neo-panel p-6 backdrop-blur-md">
                <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Task Throughput</h3>
                <div className="text-3xl font-bold text-gradient-nova mb-2">
                  {metrics.activeTasks}
                </div>
                <div className="text-xs text-muted-enhanced">
                  {metrics.completedTasks} completed
                  {autonomousTaskAssignment && <span className="text-nova-400 ml-1">• Auto-Assigned</span>}
                </div>
              </div>
              
              <div className="neo-panel p-6 backdrop-blur-md">
                <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Response Time</h3>
                <div className="text-3xl font-bold text-gradient-neural mb-2">
                  {metrics.averageResponseTime}ms
                </div>
                <div className="text-xs text-muted-enhanced">
                  Orchestration latency
                  {aiWorkflowOptimization && <span className="text-neural-400 ml-1">• AI Optimized</span>}
                </div>
              </div>
              
              <div className="neo-panel p-6 backdrop-blur-md">
                <h3 className="text-sm font-semibold text-muted-enhanced mb-3">System Health</h3>
                <div className={`text-3xl font-bold mb-2 ${getHealthColor(metrics.orchestrationHealth)}`}>
                  {metrics.orchestrationHealth}%
                </div>
                <div className="text-xs text-muted-enhanced">
                  {metrics.systemLoad}% load
                  {coordinationTransparency && <span className="text-quantum-400 ml-1">• Transparent</span>}
                </div>
              </div>
            </div>
          )}

          {/* Day 12: Enhanced Tab Navigation with AI Coordination */}
          <div className="flex space-x-1 p-1 bg-space-800/50 rounded-lg mb-6">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'communication', label: 'Communication' },
              { id: 'distribution', label: 'Distribution' },
              { id: 'status', label: 'Status' },
              { id: 'workflows', label: 'Workflows' },
              { id: 'ai-coordination', label: '🤖 AI Coordination' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id as any)}
                className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all ${
                  activeView === tab.id
                    ? 'bg-cosmic-500 text-white'
                    : 'text-stardust-300 hover:text-white hover:bg-space-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content - Enhanced with Autonomous AI Features */}
          {activeView === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Day 12: Enhanced Active Workflows */}
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold theme-text-primary">Active Workflows</h3>
                  {autonomousMode && (
                    <span className="px-2 py-1 bg-cosmic-600/20 text-cosmic-400 text-xs rounded-full">
                      🤖 AI Enhanced
                    </span>
                  )}
                </div>
                {workflows.length > 0 ? (
                  <div className="space-y-4">
                    {workflows.slice(0, 3).map((workflow) => (
                      <div key={workflow.id} className="p-4 bg-space-800/50 rounded-lg border border-space-600">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium theme-text-primary">{workflow.name}</h4>
                          <div className="flex items-center gap-2">
                            <span className={`px-2 py-1 rounded text-xs ${getStatusColor(workflow.status)}`}>
                              {workflow.status}
                            </span>
                            {aiWorkflowOptimization && (
                              <span className="px-2 py-1 bg-neural-600/20 text-neural-400 text-xs rounded">
                                AI Optimized
                              </span>
                            )}
                          </div>
                        </div>
                        <div className="w-full bg-space-700 rounded-full h-2 mb-2">
                          <div
                            className="bg-cosmic-500 h-2 rounded-full transition-all duration-700"
                            style={{ width: `${workflow.progress}%` }}
                          />
                        </div>
                        <div className="flex justify-between text-sm theme-text-secondary">
                          <span>{workflow.agentsInvolved.length} agents</span>
                          <span>{workflow.progress}% complete</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="theme-text-secondary">No active workflows</p>
                    <Button
                      variant="outline"
                      onClick={() => router.push('/tasks/create')}
                      className="mt-4"
                    >
                      Create Workflow
                    </Button>
                  </div>
                )}
              </Card>

              {/* Day 12: Enhanced System Overview */}
              <Card className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold theme-text-primary">System Overview</h3>
                  {coordinationTransparency && (
                    <span className="px-2 py-1 bg-nova-600/20 text-nova-400 text-xs rounded-full">
                      🔍 Transparent
                    </span>
                  )}
                </div>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium theme-text-secondary">Agent Utilization</span>
                      <span className="text-sm font-medium theme-text-primary">
                        {metrics ? Math.round((metrics.activeAgents / metrics.totalAgents) * 100) : 0}%
                      </span>
                    </div>
                    <div className="w-full bg-space-700 rounded-full h-2">
                      <div
                        className="bg-cosmic-500 h-2 rounded-full transition-all duration-700"
                        style={{ width: `${metrics ? (metrics.activeAgents / metrics.totalAgents) * 100 : 0}%` }}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium theme-text-secondary">System Load</span>
                      <span className={`text-sm font-medium ${getHealthColor(100 - (metrics?.systemLoad || 0))}`}>
                        {metrics?.systemLoad || 0}%
                      </span>
                    </div>
                    <div className="w-full bg-space-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-700 ${
                          (metrics?.systemLoad || 0) > 80 ? 'bg-red-500' : 
                          (metrics?.systemLoad || 0) > 60 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${metrics?.systemLoad || 0}%` }}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-sm font-medium theme-text-secondary">Orchestration Health</span>
                      <span className={`text-sm font-medium ${getHealthColor(metrics?.orchestrationHealth || 0)}`}>
                        {metrics?.orchestrationHealth || 0}%
                      </span>
                    </div>
                    <div className="w-full bg-space-700 rounded-full h-2">
                      <div
                        className="bg-gradient-cosmic h-2 rounded-full transition-all duration-700"
                        style={{ width: `${metrics?.orchestrationHealth || 0}%` }}
                      />
                    </div>
                  </div>

                  {/* Day 12: Autonomous AI Status Indicators */}
                  {autonomousMode && (
                    <div className="pt-4 mt-4 border-t border-space-600">
                      <div className="text-sm font-medium theme-text-secondary mb-2">Autonomous AI Status</div>
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-stardust-400">Coordination Intelligence</span>
                          <span className="text-cosmic-400">Active</span>
                        </div>
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-stardust-400">Workflow Optimization</span>
                          <span className={aiWorkflowOptimization ? 'text-neural-400' : 'text-gray-500'}>
                            {aiWorkflowOptimization ? 'Enabled' : 'Disabled'}
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-stardust-400">Auto Task Assignment</span>
                          <span className={autonomousTaskAssignment ? 'text-quantum-400' : 'text-gray-500'}>
                            {autonomousTaskAssignment ? 'Enabled' : 'Disabled'}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            </div>
          )}

          {/* Day 12: New AI Coordination Tab */}
          {activeView === 'ai-coordination' && isMounted && (
            <div className="space-y-6">
              <div className="mb-6">
                <h2 className="text-xl font-bold text-gradient-cosmic mb-2">🤖 Autonomous AI Coordination Center</h2>
                <p className="text-stardust-400">
                  Advanced autonomous coordination with real-time AI decision transparency and workflow optimization
                </p>
              </div>

              {/* System Status Dashboard */}
              <div className="mb-6">
                <h3 className="text-lg font-bold text-gradient-nova mb-3">🔍 System Status & Agent Communication</h3>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <SystemStatusDashboard />
                  </div>
                  <div>
                    <AgentCommunicationMonitor />
                  </div>
                </div>
              </div>

              {/* AI Decision Stream */}
              <div>
                <h3 className="text-lg font-bold text-gradient-quantum mb-3">📊 Real-Time AI Coordination Decisions</h3>
                <AIDecisionStream
                  realTimeEnabled={realTimeCoordination}
                  maxDecisions={40}
                  autoScroll={true}
                  className="neo-panel border border-space-600 rounded-lg shadow-lg max-h-96 overflow-y-auto"
                />
              </div>
            </div>
          )}

          {/* Existing Tab Content - Preserved */}
          {activeView === 'communication' && (
            <AgentCommunicationFlows />
          )}

          {activeView === 'distribution' && (
            <TaskDistributionMap />
          )}

          {activeView === 'status' && (
            <RealTimeOrchestrationStatus />
          )}

          {activeView === 'workflows' && (
            <WorkflowManagementControls />
          )}

          {/* Day 12: Enhanced Footer */}
          {isMounted && (autonomousMode || coordinationTransparency) && (
            <div className="mt-8 p-4 bg-space-900/20 rounded-lg border border-cosmic-400/10 text-center">
              <div className="inline-flex items-center space-x-2 text-stardust-400 text-sm">
                <span>🤖</span>
                <span>Orchestration Enhanced</span>
                <span>•</span>
                {autonomousMode && (
                  <>
                    <span>🤖 Autonomous Coordination Active</span>
                    <span>•</span>
                  </>
                )}
                {coordinationTransparency && (
                  <>
                    <span>🔍 Coordination Transparency Enabled</span>
                    <span>•</span>
                  </>
                )}
                <span>Day 12 Implementation</span>
              </div>
              <div className="mt-2 text-xs text-cosmic-400">
                TIER 1 COMPLETE: Enhanced with autonomous AI coordination and workflow optimization
              </div>
            </div>
          )}
        </Container>
      </main>
    </div>
  );
} 