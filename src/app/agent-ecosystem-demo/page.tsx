/**
 * Agent Ecosystem Demo - Components Showcase
 * Frontend preparation for autonomous AI ecosystem monitoring
 *
 * Features:
 * - AgentEcosystemHub component demonstration
 * - AgentStatusGrid component showcase
 * - Mobile-first responsive design examples
 */

'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import {
  Bot,
  Grid3X3,
  BarChart3,
  Activity,
  Settings,
  Eye,
  Coins,
  Network,
  Users,
  Cpu,
  MemoryStick,
  RefreshCw,
  Info,
  Zap,
  ArrowLeft
} from 'lucide-react';
import { AgentEcosystemHub, AgentStatusGrid } from '@/components/AutonomousCore';

const AgentEcosystemDemoPage = () => {
  const [currentDemo, setCurrentDemo] = useState<'ecosystem' | 'grid' | 'both'>('both');
  const [showBlockchainPrep, setShowBlockchainPrep] = useState(false);
  const [gridSize, setGridSize] = useState<'compact' | 'normal' | 'large'>('normal');
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'analytics'>('grid');

  const demoSections = [
    {
      id: 'ecosystem',
      title: 'Agent Ecosystem Hub',
      description: 'Primary hub for 28-agent ecosystem monitoring with performance analytics',
      icon: <Users className="w-5 h-5" />,
      component: 'AgentEcosystemHub'
    },
    {
      id: 'grid',
      title: 'Agent Status Grid',
      description: 'Real-time agent monitoring grid with enhanced visual indicators',
      icon: <Grid3X3 className="w-5 h-5" />,
      component: 'AgentStatusGrid'
    },
    {
      id: 'both',
      title: 'Complete Integration',
      description: 'Full ecosystem monitoring with both components integrated',
      icon: <Activity className="w-5 h-5" />,
      component: 'Integrated'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-space-900">
      {/* Breadcrumb Navigation */}
      <div className="container mx-auto px-4 py-4">
        <Link
          href="/agents"
          className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Agent Hub</span>
        </Link>
      </div>

      {/* Header */}
      <div className="border-b border-space-600 bg-space-900/80 backdrop-blur-lg sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <div className="flex items-center gap-3">
              <Bot className="w-8 h-8 text-cosmic-400" />
              <div>
                <h1 className="text-2xl font-bold text-stardust-100">
                  Agent Ecosystem Demo
                </h1>
                <p className="text-sm text-stardust-400">
                  Live Agent Ecosystem Components
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {/* Demo Section Toggle */}
              <div className="flex border border-space-600 rounded-lg overflow-hidden">
                {demoSections.map(section => (
                  <button
                    key={section.id}
                    onClick={() => setCurrentDemo(section.id as any)}
                    className={`px-3 py-2 text-sm flex items-center gap-2 ${
                      currentDemo === section.id
                        ? 'bg-cosmic-500 text-white'
                        : 'bg-space-800 text-stardust-400 hover:bg-space-700'
                    }`}
                  >
                    {section.icon}
                    <span className="hidden sm:inline">{section.title}</span>
                  </button>
                ))}
              </div>

              {/* Blockchain Prep Toggle */}
              <label className="flex items-center gap-2 bg-space-800 px-3 py-2 rounded-lg border border-space-600">
                <input
                  type="checkbox"
                  checked={showBlockchainPrep}
                  onChange={(e) => setShowBlockchainPrep(e.target.checked)}
                  className="rounded"
                />
                <Coins className="w-4 h-4 text-nova-400" />
                <span className="text-sm text-stardust-300 hidden sm:inline">Blockchain Prep</span>
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Phase Information Banner */}
      <div className="container mx-auto px-4 py-6">
        <div className="neo-card p-6 border-cosmic-500/30">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-cosmic-500/20 rounded-lg">
                <Activity className="w-6 h-6 text-cosmic-400" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-stardust-100">
                  Enhanced Agent Ecosystem Components
                </h2>
                <p className="text-stardust-400">
                  Live monitoring with real-time agent status and performance metrics
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-4 text-sm">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full" />
                <span className="text-stardust-300">Real-time monitoring</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-cosmic-400 rounded-full" />
                <span className="text-stardust-300">28-agent ecosystem</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-nova-400 rounded-full" />
                <span className="text-stardust-300">Mobile-first design</span>
              </div>
              {showBlockchainPrep && (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full" />
                  <span className="text-stardust-300">Validator ready</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Controls */}
      <div className="container mx-auto px-4 pb-6">
        <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
          <div className="flex flex-wrap gap-2">
            {currentDemo === 'ecosystem' && (
              <div className="flex border border-space-600 rounded-lg overflow-hidden">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`px-3 py-2 text-sm ${viewMode === 'grid' ? 'bg-cosmic-500 text-white' : 'bg-space-800 text-stardust-400'}`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`px-3 py-2 text-sm ${viewMode === 'list' ? 'bg-cosmic-500 text-white' : 'bg-space-800 text-stardust-400'}`}
                >
                  <BarChart3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('analytics')}
                  className={`px-3 py-2 text-sm ${viewMode === 'analytics' ? 'bg-cosmic-500 text-white' : 'bg-space-800 text-stardust-400'}`}
                >
                  <BarChart3 className="w-4 h-4" />
                </button>
              </div>
            )}

            {(currentDemo === 'grid' || currentDemo === 'both') && (
              <select
                value={gridSize}
                onChange={(e) => setGridSize(e.target.value as any)}
                className="px-3 py-2 bg-space-800 border border-space-600 rounded-lg text-sm text-stardust-100"
              >
                <option value="compact">Compact Grid</option>
                <option value="normal">Normal Grid</option>
                <option value="large">Large Grid</option>
              </select>
            )}
          </div>

          <div className="text-sm text-stardust-400">
            Live system integration with real-time backend
          </div>
        </div>
      </div>

      {/* Component Demonstrations */}
      <div className="container mx-auto px-4 pb-12">
        <div className="space-y-8">
          {/* AgentEcosystemHub Demo */}
          {(currentDemo === 'ecosystem' || currentDemo === 'both') && (
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Users className="w-6 h-6 text-cosmic-400" />
                <h3 className="text-xl font-semibold text-stardust-100">
                  Agent Ecosystem Hub
                </h3>
                <span className="px-2 py-1 text-xs bg-cosmic-500/20 text-cosmic-300 rounded">
                  Blockchain-Enhanced
                </span>
              </div>
              
              <AgentEcosystemHub
                viewMode={viewMode}
                showBlockchainPrep={showBlockchainPrep}
                className="max-w-full"
              />
            </div>
          )}

          {/* AgentStatusGrid Demo */}
          {(currentDemo === 'grid' || currentDemo === 'both') && (
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Grid3X3 className="w-6 h-6 text-nova-400" />
                <h3 className="text-xl font-semibold text-stardust-100">
                  Agent Status Grid
                </h3>
                <span className="px-2 py-1 text-xs bg-nova-500/20 text-nova-300 rounded">
                  Validator-Ready
                </span>
              </div>

              <AgentStatusGrid
                gridSize={gridSize}
                showValidatorInfo={showBlockchainPrep}
                className="max-w-full"
              />
            </div>
          )}

          {/* Integration Notes */}
          <div className="neo-card p-6 border-neural-500/30">
            <div className="flex items-start gap-3">
              <Info className="w-6 h-6 text-neural-400 mt-1" />
              <div className="space-y-3">
                <h3 className="text-lg font-semibold text-stardust-100">
                  Day 4 Development Notes
                </h3>
                
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-stardust-200 mb-2">✅ Completed Features</h4>
                    <ul className="space-y-1 text-sm text-stardust-400">
                      <li>• Real-time 28-agent performance display</li>
                      <li>• Agent categorization and filtering systems</li>
                      <li>• Performance analytics dashboard</li>
                      <li>• Mobile-first responsive grid layouts</li>
                      <li>• Enhanced visual indicators for agent status</li>
                      <li>• Cross-section integration preparation</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-stardust-200 mb-2">🔗 Blockchain Preparation (Phase 4)</h4>
                    <ul className="space-y-1 text-sm text-stardust-400">
                      <li>• Validator status indicators and rankings</li>
                      <li>• Staking power and rewards display</li>
                      <li>• Consensus participation metrics</li>
                      <li>• Network health monitoring hooks</li>
                      <li>• Validator transformation readiness</li>
                      <li>• Zero reorganization evolution path</li>
                    </ul>
                  </div>
                </div>

                <div className="pt-4 border-t border-space-600">
                  <h4 className="font-semibold text-stardust-200 mb-2">🎯 Phase 2 Integration Ready</h4>
                  <p className="text-sm text-stardust-400">
                    Both components include WebSocket preparation hooks for real-time backend integration. 
                    The frontend observation layer is ready to display autonomous AI operations with 
                    cross-section event bus coordination across all platform sections.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="border-t border-space-600 bg-space-900/50 backdrop-blur-lg">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center space-y-2">
            <p className="text-stardust-300 font-semibold">
              Day 4: Enhanced Agent Ecosystem Components Complete ✅
            </p>
            <p className="text-sm text-stardust-400">
              Next: Day 5 components (SystemStatusDashboard, AgentCommunicationMonitor) 
              for blockchain-ready system monitoring
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentEcosystemDemoPage; 