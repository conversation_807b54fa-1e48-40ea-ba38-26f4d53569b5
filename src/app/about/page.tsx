'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON>, Brain, Users, Zap, Target, Globe } from 'lucide-react';

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900">
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-cosmic-500/20 px-4 py-2 rounded-full mb-6">
            <Sparkles className="w-4 h-4 text-cosmic-400" />
            <span className="text-cosmic-300 text-sm font-medium">About CreAItive</span>
          </div>
          
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            The Future of
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-purple-400 ml-3">
              AI Collaboration
            </span>
          </h1>
          
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            CreAItive is a revolutionary platform that brings together 41 specialized AI agents 
            to create, collaborate, and innovate in ways never before possible.
          </p>
        </div>

        {/* Mission Section */}
        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-4">
              <Target className="w-6 h-6 text-cosmic-400" />
              <h2 className="text-2xl font-bold text-white">Our Mission</h2>
            </div>
            <p className="text-white/70 leading-relaxed">
              To democratize AI creativity by providing an intelligent ecosystem where 
              human imagination meets artificial intelligence capabilities. We believe 
              the future belongs to those who can seamlessly collaborate with AI.
            </p>
          </div>
          
          <div className="space-y-6">
            <div className="flex items-center gap-3 mb-4">
              <Globe className="w-6 h-6 text-cosmic-400" />
              <h2 className="text-2xl font-bold text-white">Our Vision</h2>
            </div>
            <p className="text-white/70 leading-relaxed">
              A world where AI agents work alongside humans as creative partners, 
              amplifying human potential and enabling breakthrough innovations across 
              every industry and creative discipline.
            </p>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <Brain className="w-8 h-8 text-cosmic-400 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-3">41 AI Agents</h3>
            <p className="text-white/60">
              Specialized agents for every creative and technical need, from content creation 
              to system optimization.
            </p>
          </div>
          
          <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <Users className="w-8 h-8 text-cosmic-400 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-3">Collaborative Intelligence</h3>
            <p className="text-white/60">
              Agents that work together seamlessly, sharing knowledge and coordinating 
              complex multi-step workflows.
            </p>
          </div>
          
          <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
            <Zap className="w-8 h-8 text-cosmic-400 mb-4" />
            <h3 className="text-xl font-semibold text-white mb-3">Real-Time Adaptation</h3>
            <p className="text-white/60">
              Dynamic system that learns and adapts to your workflow, becoming more 
              efficient with every interaction.
            </p>
          </div>
        </div>

        {/* Stats Section */}
        <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 mb-16">
          <div className="grid md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-3xl font-bold text-cosmic-400 mb-2">41</div>
              <div className="text-white/60">AI Agents</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-cosmic-400 mb-2">∞</div>
              <div className="text-white/60">Possibilities</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-cosmic-400 mb-2">24/7</div>
              <div className="text-white/60">Availability</div>
            </div>
            <div>
              <div className="text-3xl font-bold text-cosmic-400 mb-2">100%</div>
              <div className="text-white/60">Innovation</div>
            </div>
          </div>
        </div>

        {/* Team Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-white mb-6">Built for the Future</h2>
          <p className="text-white/70 max-w-2xl mx-auto leading-relaxed">
            CreAItive represents the next evolution in human-AI collaboration. 
            Our platform is designed to grow with you, adapting to your needs 
            and helping you achieve what was previously impossible.
          </p>
        </div>
      </div>
    </div>
  );
}
