"use client";

import React, { useState, useEffect, useCallback } from 'react';
import Container from "@/shared/components/Container";
import Heading from "@/shared/components/Heading";
import Button from "@/shared/components/Button";
import Link from "next/link";
import { FiShoppingBag, FiTool, FiUsers, FiZap, FiTrendingUp, FiStar, FiArrowLeft } from "react-icons/fi";

// 🤖 Autonomous AI Components
import AutonomousHealthIndicator from '@/components/AutonomousCore/AutonomousHealthIndicator';
import AIDecisionStream from '@/components/AutonomousCore/AIDecisionStream';

// Trading Decision Type
interface TradingDecision {
  asset: string;
  trend: string;
  potential: string;
  recommendation: string;
}

export default function MarketplacePage() {
  // 🤖 AI Trading Assistance State
  const [aiTradingMode, setAiTradingMode] = useState(false);
  const [aiTradingTransparency, setAiTradingTransparency] = useState(0.85);
  const [tradingEfficiencyScore, setTradingEfficiencyScore] = useState(92);
  const [autonomousTradingAnalysis, setAutonomousTradingAnalysis] = useState(false);
  const [intelligentMarketTracking, setIntelligentMarketTracking] = useState(false);
  const [aiTradingDecisions, setAiTradingDecisions] = useState<TradingDecision[]>([]);
  const [currentTab, setCurrentTab] = useState('marketplace');

  // 🧠 AI Trading Analysis Functions
  const analyzeMarketOpportunities = useCallback(async () => {
    if (!aiTradingMode) return;
    
    // Simulate AI market analysis
    const opportunities = [
      { asset: 'Digital Templates', trend: 'Rising', potential: '94%', recommendation: 'Strong Buy' },
      { asset: 'AI Music Samples', trend: 'Stable', potential: '87%', recommendation: 'Hold' },
      { asset: 'Creative Presets', trend: 'Emerging', potential: '96%', recommendation: 'Buy' },
      { asset: 'Video Effects', trend: 'Hot', potential: '91%', recommendation: 'Strong Buy' }
    ];
    
    setAiTradingDecisions(opportunities);
    setTradingEfficiencyScore(Math.floor(90 + Math.random() * 10));
  }, [aiTradingMode]);

  const enableAutonomousMarketAnalysis = useCallback(() => {
    setAutonomousTradingAnalysis(true);
    analyzeMarketOpportunities();
  }, [analyzeMarketOpportunities]);

  const enableIntelligentPriceTracking = useCallback(() => {
    setIntelligentMarketTracking(true);
    // AI tracks market prices and trends
  }, []);

  const optimizeTradingStrategies = useCallback(() => {
    // AI optimizes trading strategies based on user behavior
    setTradingEfficiencyScore(prev => Math.min(100, prev + 2));
  }, []);

  const generateTradingInsights = useCallback(() => {
    // AI generates personalized trading insights
    return {
      recommendation: 'Focus on emerging digital template markets',
      timing: 'Optimal trading window: Next 48 hours',
      risk: 'Low risk profile detected',
      opportunity: 'AI music sample demand increasing 23%'
    };
  }, []);

  // 🔄 AI Trading Effect
  useEffect(() => {
    if (aiTradingMode) {
      const interval = setInterval(() => {
        analyzeMarketOpportunities();
        optimizeTradingStrategies();
      }, 8000);

      return () => clearInterval(interval);
    }
  }, [aiTradingMode, analyzeMarketOpportunities, optimizeTradingStrategies]);

  // 📊 Trading Analytics
  const tradingAnalytics = {
    marketOpportunities: aiTradingDecisions.length,
    activeAnalysis: autonomousTradingAnalysis,
    priceTracking: intelligentMarketTracking,
    tradingEfficiency: `${tradingEfficiencyScore}%`,
    aiInsights: generateTradingInsights()
  };

  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Background patterns */}
      <div className="absolute inset-0 z-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-cosmic-50/50 via-transparent to-nova-50/50" />
        <div className="absolute inset-0 bg-gradient-to-tr from-neural-50/30 via-transparent to-aura-50/30" />
      </div>
      
      <main className="relative z-10">
        <Container className="py-20">
          {/* Breadcrumb Navigation */}
          <div className="mb-6">
            <Link
              href="/creative"
              className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
            >
              <FiArrowLeft className="w-4 h-4" />
              <span>Back to Creative Hub</span>
            </Link>
          </div>

          {/* Enhanced Hero Section with AI Trading Controls */}
          <div className="text-center mb-16">
            <div className="mb-8 flex flex-col items-center gap-4">
              <span className="inline-flex items-center px-4 py-2 rounded-full bg-gradient-to-r from-cosmic-100 to-nova-100 text-cosmic-700 text-sm font-medium border border-cosmic-200/50 shadow-soft">
                <span className="w-2 h-2 bg-cosmic-500 rounded-full mr-2 animate-pulse"></span>
                Revolutionary Marketplace Preview
              </span>
              
              {/* 🤖 AI Trading Mode Controls */}
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm theme-text-secondary">AI Trading Mode</span>
                  <button
                    onClick={() => setAiTradingMode(!aiTradingMode)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      aiTradingMode ? 'bg-cosmic-500' : 'bg-gray-300'
                    }`}
                  >
                    <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                      aiTradingMode ? 'translate-x-6' : 'translate-x-1'
                    }`} />
                  </button>
                </div>
                
                {aiTradingMode && (
                  <div className="flex items-center gap-2">
                    <span className="text-sm theme-text-secondary">Transparency</span>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.05"
                      value={aiTradingTransparency}
                      onChange={(e) => setAiTradingTransparency(parseFloat(e.target.value))}
                      className="w-20 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                    />
                    <span className="text-xs theme-text-tertiary min-w-[3rem]">
                      {Math.round(aiTradingTransparency * 100)}%
                    </span>
                  </div>
                )}
              </div>
            </div>
            
            <Heading level={1} gradient size="3xl" align="center" className="mb-6">
              Universal Creative Marketplace
            </Heading>
            
            <p className="text-xl theme-text-secondary max-w-3xl mx-auto mb-8">
              The future of creative commerce: Buy, sell, and trade any creative asset, tool, or service.
              <br className="hidden sm:block" />
              <strong>Coming Soon:</strong> Unlimited tools, AI agents, and creative resources in one place.
            </p>

            {/* 🤖 AI Trading Efficiency Indicator */}
            {aiTradingMode && (
              <div className="bg-gradient-to-r from-cosmic-500/10 to-nova-500/10 border border-cosmic-300/30 rounded-lg p-4 max-w-xl mx-auto mb-8">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium theme-text-primary">🤖 AI Trading Efficiency</span>
                  <span className="text-lg font-bold text-cosmic-600">{tradingEfficiencyScore}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-cosmic-500 to-nova-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${tradingEfficiencyScore}%` }}
                  ></div>
                </div>
                <p className="text-xs theme-text-secondary mt-2">
                  AI continuously optimizes your trading strategies for maximum efficiency
                </p>
              </div>
            )}

            <div className="bg-gradient-to-r from-cosmic-500/10 to-nova-500/10 border border-cosmic-300/30 rounded-lg p-6 max-w-2xl mx-auto mb-12">
              <h3 className="text-lg font-bold theme-text-primary mb-2">🚀 Revolutionary Vision</h3>
              <p className="theme-text-secondary text-sm">
                This marketplace will replace traditional creative tool subscriptions with a unified platform 
                where you can access any creative capability, hire AI agents, and trade creative assets 
                with a global community.
              </p>
            </div>
          </div>

          {/* 📑 Tab Navigation */}
          <div className="flex justify-center mb-8">
            <div className="flex bg-white/10 backdrop-blur-md rounded-lg p-1 shadow-lg">
              <button
                onClick={() => setCurrentTab('marketplace')}
                className={`px-6 py-3 rounded-md text-sm font-medium transition-all duration-200 ${
                  currentTab === 'marketplace'
                    ? 'bg-cosmic-500 text-white shadow-md'
                    : 'text-gray-600 hover:text-cosmic-600 hover:bg-white/50'
                }`}
              >
                🏪 Marketplace
              </button>
              {aiTradingMode && (
                <button
                  onClick={() => setCurrentTab('ai-trading')}
                  className={`px-6 py-3 rounded-md text-sm font-medium transition-all duration-200 ${
                    currentTab === 'ai-trading'
                      ? 'bg-cosmic-500 text-white shadow-md'
                      : 'text-gray-600 hover:text-cosmic-600 hover:bg-white/50'
                  }`}
                >
                  🤖 AI Trading
                </button>
              )}
            </div>
          </div>

          {/* Tab Content */}
          {currentTab === 'marketplace' && (
            <>
              {/* Coming Soon Categories */}
              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
                {/* Creative Tools */}
                <div className="neo-card p-8 group hover:shadow-cosmic transition-all duration-300">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-cosmic-400 to-cosmic-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <FiTool className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-display font-semibold mb-4 theme-text-primary">
                    Unlimited Creative Tools
                  </h3>
                  <p className="theme-text-secondary mb-4">
                    Access any creative tool on-demand: design, 3D modeling, video editing, audio production, and more.
                  </p>
                  <div className="text-sm theme-text-tertiary">
                    <strong>Coming:</strong> Adobe Suite replacement, Blender, Logic Pro alternatives
                  </div>
                </div>

                {/* AI Agents */}
                <div className="neo-card p-8 group hover:shadow-nova transition-all duration-300">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-nova-400 to-nova-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <FiZap className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-display font-semibold mb-4 theme-text-primary">
                    AI Agent Services
                  </h3>
                  <p className="theme-text-secondary mb-4">
                    Hire specialized AI agents for any creative task: writing, design, music composition, editing.
                  </p>
                  <div className="text-sm theme-text-tertiary">
                    <strong>Coming:</strong> Personal creative assistants, workflow automation agents
                  </div>
                </div>

                {/* Asset Trading */}
                <div className="neo-card p-8 group hover:shadow-neural transition-all duration-300">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-neural-400 to-neural-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <FiShoppingBag className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-display font-semibold mb-4 theme-text-primary">
                    Creative Asset Trading
                  </h3>
                  <p className="theme-text-secondary mb-4">
                    Buy and sell any creative asset: templates, presets, samples, models, and finished works.
                  </p>
                  <div className="text-sm theme-text-tertiary">
                    <strong>Coming:</strong> NFT integration, smart contracts, royalty automation
                  </div>
                </div>

                {/* Skill Marketplace */}
                <div className="neo-card p-8 group hover:shadow-quantum transition-all duration-300">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-quantum-400 to-quantum-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <FiUsers className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-display font-semibold mb-4 theme-text-primary">
                    Creator Services
                  </h3>
                  <p className="theme-text-secondary mb-4">
                    Connect with creators for custom work, collaborations, and creative consulting.
                  </p>
                  <div className="text-sm theme-text-tertiary">
                    <strong>Coming:</strong> Verified creator profiles, project matching, escrow payments
                  </div>
                </div>

                {/* Creative DNA Trading */}
                <div className="neo-card p-8 group hover:shadow-aura transition-all duration-300">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-aura-400 to-aura-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <FiStar className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-display font-semibold mb-4 theme-text-primary">
                    Creative DNA Trading
                  </h3>
                  <p className="theme-text-secondary mb-4">
                    Buy and sell creative styles, techniques, and influences through AI-analyzed creative DNA.
                  </p>
                  <div className="text-sm theme-text-tertiary">
                    <strong>Revolutionary:</strong> First-ever style marketplace with blockchain verification
                  </div>
                </div>

                {/* Analytics & Trends */}
                <div className="neo-card p-8 group hover:shadow-cosmic transition-all duration-300">
                  <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-cosmic-600 to-nova-600 flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <FiTrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-xl font-display font-semibold mb-4 theme-text-primary">
                    Market Intelligence
                  </h3>
                  <p className="theme-text-secondary mb-4">
                    Real-time analytics on creative trends, pricing insights, and market opportunities.
                  </p>
                  <div className="text-sm theme-text-tertiary">
                    <strong>Coming:</strong> AI-powered market predictions, trend forecasting
                  </div>
                </div>
              </div>

              {/* Roadmap Section */}
              <div className="text-center mb-16">
                <Heading level={2} size="2xl" className="theme-text-primary mb-8">
                  Revolutionary Development Roadmap
                </Heading>
                
                <div className="grid md:grid-cols-4 gap-6 max-w-5xl mx-auto">
                  <div className="neo-card p-6 text-left">
                    <div className="text-cosmic-500 font-bold text-lg mb-2">Phase 1</div>
                    <h4 className="font-semibold theme-text-primary mb-2">Foundation</h4>
                    <p className="text-sm theme-text-secondary">Basic marketplace with asset trading and simple tool access.</p>
                    <div className="text-xs theme-text-tertiary mt-2">Q2 2025</div>
                  </div>
                  
                  <div className="neo-card p-6 text-left">
                    <div className="text-nova-500 font-bold text-lg mb-2">Phase 2</div>
                    <h4 className="font-semibold theme-text-primary mb-2">AI Integration</h4>
                    <p className="text-sm theme-text-secondary">AI agent marketplace and automated creative services.</p>
                    <div className="text-xs theme-text-tertiary mt-2">Q3 2025</div>
                  </div>
                  
                  <div className="neo-card p-6 text-left">
                    <div className="text-neural-500 font-bold text-lg mb-2">Phase 3</div>
                    <h4 className="font-semibold theme-text-primary mb-2">Blockchain Economy</h4>
                    <p className="text-sm theme-text-secondary">Decentralized transactions and smart creative contracts.</p>
                    <div className="text-xs theme-text-tertiary mt-2">Q4 2025</div>
                  </div>
                  
                  <div className="neo-card p-6 text-left">
                    <div className="text-quantum-500 font-bold text-lg mb-2">Phase 4</div>
                    <h4 className="font-semibold theme-text-primary mb-2">Global Hub</h4>
                    <p className="text-sm theme-text-secondary">Planetary-scale creative economy with unlimited tools.</p>
                    <div className="text-xs theme-text-tertiary mt-2">2026</div>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* 🤖 AI Trading Assistant Tab */}
          {currentTab === 'ai-trading' && aiTradingMode && (
            <div className="space-y-8 mb-16">
              <div className="text-center">
                <Heading level={2} size="2xl" className="theme-text-primary mb-4">
                  🤖 AI Trading Assistant
                </Heading>
                <p className="text-lg theme-text-secondary max-w-3xl mx-auto">
                  Advanced AI algorithms analyze market trends, identify opportunities, and optimize your trading strategies in real-time.
                </p>
              </div>

              {/* AI Trading Controls Panel */}
              <div className="grid md:grid-cols-2 gap-8">
                <div className="neo-card p-6">
                  <h3 className="text-lg font-semibold theme-text-primary mb-4">🎛️ AI Trading Controls</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">Autonomous Market Analysis</span>
                      <button
                        onClick={enableAutonomousMarketAnalysis}
                        className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                          autonomousTradingAnalysis 
                            ? 'bg-cosmic-500 text-white' 
                            : 'bg-gray-200 theme-text-secondary hover:bg-cosmic-100'
                        }`}
                      >
                        {autonomousTradingAnalysis ? 'Active' : 'Enable'}
                      </button>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">Intelligent Price Tracking</span>
                      <button
                        onClick={enableIntelligentPriceTracking}
                        className={`px-3 py-1 rounded text-xs font-medium transition-colors ${
                          intelligentMarketTracking 
                            ? 'bg-nova-500 text-white' 
                            : 'bg-gray-200 theme-text-secondary hover:bg-nova-100'
                        }`}
                      >
                        {intelligentMarketTracking ? 'Tracking' : 'Enable'}
                      </button>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">Strategy Optimization</span>
                      <button
                        onClick={optimizeTradingStrategies}
                        className="px-3 py-1 rounded text-xs font-medium bg-neural-500 text-white hover:bg-neural-600 transition-colors"
                      >
                        Optimize
                      </button>
                    </div>
                  </div>
                </div>

                <div className="neo-card p-6">
                  <h3 className="text-lg font-semibold theme-text-primary mb-4">📊 Trading Analytics</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">Market Opportunities</span>
                      <span className="text-lg font-bold text-cosmic-600">{tradingAnalytics.marketOpportunities}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">Trading Efficiency</span>
                      <span className="text-lg font-bold text-nova-600">{tradingAnalytics.tradingEfficiency}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">Active Analysis</span>
                      <span className={`text-sm font-medium ${tradingAnalytics.activeAnalysis ? 'text-nova-600' : 'text-gray-500'}`}>
                        {tradingAnalytics.activeAnalysis ? 'Running' : 'Inactive'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm theme-text-secondary">Price Tracking</span>
                      <span className={`text-sm font-medium ${tradingAnalytics.priceTracking ? 'text-nova-600' : 'text-gray-500'}`}>
                        {tradingAnalytics.priceTracking ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* AI Trading Analysis */}
              {aiTradingDecisions.length > 0 && (
                <div className="neo-card p-6">
                  <h3 className="text-lg font-semibold theme-text-primary mb-4">🧠 AI Market Analysis</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    {aiTradingDecisions.map((decision, index) => (
                      <div key={index} className="bg-gradient-to-r from-cosmic-50/50 to-nova-50/50 border border-cosmic-200/30 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium theme-text-primary">{decision.asset}</h4>
                          <span className={`text-xs font-bold px-2 py-1 rounded ${
                            decision.recommendation.includes('Strong') ? 'bg-nova-100 text-nova-700' :
                            decision.recommendation === 'Buy' ? 'bg-cosmic-100 text-cosmic-700' :
                            'bg-quantum-100 text-quantum-700'
                          }`}>
                            {decision.recommendation}
                          </span>
                        </div>
                        <div className="text-sm theme-text-secondary space-y-1">
                          <div className="flex justify-between">
                            <span>Trend:</span>
                            <span className="font-medium">{decision.trend}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Potential:</span>
                            <span className="font-medium text-cosmic-600">{decision.potential}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* AI Insights */}
              <div className="neo-card p-6">
                <h3 className="text-lg font-semibold theme-text-primary mb-4">💡 AI Trading Insights</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-medium theme-text-primary mb-2">Strategic Recommendation</h4>
                    <p className="text-sm theme-text-secondary">{tradingAnalytics.aiInsights.recommendation}</p>
                  </div>
                  <div>
                    <h4 className="font-medium theme-text-primary mb-2">Optimal Timing</h4>
                    <p className="text-sm theme-text-secondary">{tradingAnalytics.aiInsights.timing}</p>
                  </div>
                  <div>
                    <h4 className="font-medium theme-text-primary mb-2">Risk Assessment</h4>
                    <p className="text-sm theme-text-secondary">{tradingAnalytics.aiInsights.risk}</p>
                  </div>
                  <div>
                    <h4 className="font-medium theme-text-primary mb-2">Market Opportunity</h4>
                    <p className="text-sm theme-text-secondary">{tradingAnalytics.aiInsights.opportunity}</p>
                  </div>
                </div>
              </div>

              {/* AI Decision Stream and System Status */}
              <div className="grid md:grid-cols-2 gap-8">
                <div className="neo-card p-6">
                  <h3 className="text-lg font-semibold theme-text-primary mb-4">🔄 AI Decision Stream</h3>
                  <AIDecisionStream />
                </div>
                <div className="neo-card p-6">
                  <h3 className="text-lg font-semibold theme-text-primary mb-4">📊 System Status</h3>
                  <AutonomousHealthIndicator />
                </div>
              </div>
            </div>
          )}

          {/* CTA Section */}
          <div className="text-center bg-gradient-to-r from-cosmic-500/10 to-nova-500/10 border border-cosmic-300/30 rounded-xl p-12">
            <Heading level={2} size="2xl" className="theme-text-primary mb-6">
              Ready to Join the Revolution?
            </Heading>
            <p className="text-lg theme-text-secondary mb-8 max-w-2xl mx-auto">
              Be among the first to access the revolutionary marketplace when it launches. 
              Start creating now and be ready for the future of creative commerce.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/canvas">
                <Button 
                  variant="primary" 
                  size="lg" 
                  className="px-8 py-4 shadow-cosmic hover:shadow-neon transition-all duration-300"
                >
                  Start Creating Now
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </Button>
              </Link>
              
              <Link href="/dashboard">
                <Button 
                  variant="outline" 
                  size="lg"
                  className="px-8 py-4 group"
                >
                  View Dashboard
                  <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                  </svg>
                </Button>
              </Link>
            </div>
          </div>
        </Container>
      </main>
    </div>
  );
} 