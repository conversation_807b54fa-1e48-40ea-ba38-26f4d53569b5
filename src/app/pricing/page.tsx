'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { Check, Zap, Crown, Rocket, Users, ArrowLeft } from 'lucide-react';

interface PricingPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  description: string;
  features: string[];
  popular: boolean;
  icon: React.ComponentType<{ className?: string }>;
  buttonText: string;
  buttonStyle: string;
}

const pricingPlans: PricingPlan[] = [
  {
    id: 'starter',
    name: 'Starter',
    price: 0,
    period: 'month',
    description: 'Perfect for individuals exploring AI creativity',
    features: [
      '5 AI agents access',
      '100 generations per month',
      'Basic templates',
      'Community support',
      'Standard processing speed'
    ],
    popular: false,
    icon: Zap,
    buttonText: 'Get Started Free',
    buttonStyle: 'bg-space-700 hover:bg-space-600 text-white'
  },
  {
    id: 'pro',
    name: 'Pro',
    price: 29,
    period: 'month',
    description: 'For creators and professionals who need more power',
    features: [
      '20 AI agents access',
      'Unlimited generations',
      'Premium templates',
      'Priority support',
      'Fast processing speed',
      'Advanced customization',
      'Export in all formats',
      'Collaboration tools'
    ],
    popular: true,
    icon: Crown,
    buttonText: 'Start Pro Trial',
    buttonStyle: 'bg-cosmic-500 hover:bg-cosmic-600 text-white'
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    price: 99,
    period: 'month',
    description: 'For teams and organizations with advanced needs',
    features: [
      'All 41 AI agents',
      'Unlimited everything',
      'Custom templates',
      'Dedicated support',
      'Lightning-fast processing',
      'White-label options',
      'API access',
      'Team management',
      'Advanced analytics',
      'Custom integrations'
    ],
    popular: false,
    icon: Rocket,
    buttonText: 'Contact Sales',
    buttonStyle: 'bg-purple-500 hover:bg-purple-600 text-white'
  }
];

export default function PricingPage() {
  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>('monthly');

  const getPrice = (plan: PricingPlan) => {
    if (plan.price === 0) return 0;
    return billingPeriod === 'yearly' ? Math.floor(plan.price * 0.8) : plan.price;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900">
      <div className="container mx-auto px-4 py-12">
        {/* Breadcrumb Navigation */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Simple
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-purple-400 ml-3">
              Pricing
            </span>
          </h1>
          
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed mb-8">
            Choose the perfect plan for your AI creativity needs. 
            Start free and scale as you grow.
          </p>

          {/* Billing Toggle */}
          <div className="flex items-center justify-center gap-4 mb-8">
            <span className={`text-sm ${billingPeriod === 'monthly' ? 'text-white' : 'text-white/60'}`}>
              Monthly
            </span>
            <button
              onClick={() => setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly')}
              className={`relative w-14 h-7 rounded-full transition-all ${
                billingPeriod === 'yearly' ? 'bg-cosmic-500' : 'bg-space-700'
              }`}
            >
              <div
                className={`absolute top-1 w-5 h-5 bg-white rounded-full transition-all ${
                  billingPeriod === 'yearly' ? 'left-8' : 'left-1'
                }`}
              />
            </button>
            <span className={`text-sm ${billingPeriod === 'yearly' ? 'text-white' : 'text-white/60'}`}>
              Yearly
            </span>
            {billingPeriod === 'yearly' && (
              <span className="px-2 py-1 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full">
                Save 20%
              </span>
            )}
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          {pricingPlans.map((plan) => (
            <div
              key={plan.id}
              className={`relative bg-space-800/50 backdrop-blur-sm border rounded-2xl p-8 ${
                plan.popular
                  ? 'border-cosmic-500/50 ring-2 ring-cosmic-500/20'
                  : 'border-white/10'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-cosmic-500 text-white px-4 py-1 rounded-full text-sm font-medium">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-cosmic-500/20 rounded-lg mb-4">
                  <plan.icon className="w-6 h-6 text-cosmic-400" />
                </div>
                
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-white/60 text-sm mb-4">{plan.description}</p>
                
                <div className="flex items-baseline justify-center gap-1">
                  <span className="text-4xl font-bold text-white">
                    ${getPrice(plan)}
                  </span>
                  <span className="text-white/60">/{plan.period}</span>
                </div>
                
                {billingPeriod === 'yearly' && plan.price > 0 && (
                  <p className="text-cosmic-400 text-sm mt-1">
                    ${plan.price}/month billed annually
                  </p>
                )}
              </div>

              <ul className="space-y-3 mb-8">
                {plan.features.map((feature, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <Check className="w-4 h-4 text-cosmic-400 flex-shrink-0" />
                    <span className="text-white/70 text-sm">{feature}</span>
                  </li>
                ))}
              </ul>

              <button className={`w-full py-3 px-6 rounded-lg font-medium transition-all ${plan.buttonStyle}`}>
                {plan.buttonText}
              </button>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-bold text-white text-center mb-12">
            Frequently Asked Questions
          </h2>
          
          <div className="space-y-6">
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">
                Can I change my plan anytime?
              </h3>
              <p className="text-white/70">
                Yes, you can upgrade or downgrade your plan at any time. 
                Changes take effect immediately and we'll prorate the billing.
              </p>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">
                What happens if I exceed my limits?
              </h3>
              <p className="text-white/70">
                We'll notify you when you're approaching your limits. 
                You can upgrade your plan or wait for the next billing cycle to reset.
              </p>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">
                Is there a free trial for paid plans?
              </h3>
              <p className="text-white/70">
                Yes, we offer a 14-day free trial for all paid plans. 
                No credit card required to start.
              </p>
            </div>
            
            <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-white mb-2">
                Do you offer custom enterprise solutions?
              </h3>
              <p className="text-white/70">
                Absolutely! Contact our sales team to discuss custom pricing, 
                features, and deployment options for your organization.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 border border-white/10">
            <Users className="w-12 h-12 text-cosmic-400 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-white mb-4">
              Ready to unleash your creativity?
            </h3>
            <p className="text-white/70 mb-6 max-w-2xl mx-auto">
              Join thousands of creators who are already using CreAItive to bring 
              their ideas to life with the power of AI.
            </p>
            <button className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-8 py-3 rounded-lg transition-all">
              Start Your Free Trial
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
