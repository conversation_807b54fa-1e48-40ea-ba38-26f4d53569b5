import type { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>_<PERSON> } from "next/font/google";
import "./globals.css";
import { Toaster } from "react-hot-toast";
import { AuthProvider } from "@/contexts/AuthContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { Header } from "@/components/Header/Header";
import Footer from "@/shared/components/Footer/Footer";
import ServiceWorkerRegistration from "@/components/ServiceWorkerRegistration";
import FrontendValidator from "@/components/Accessibility/FrontendValidator";
import SystemMonitoringInitializer from "@/components/monitoring/SystemMonitoringInitializer";
import { NotificationProvider } from "@/components/ui/enhanced-notifications";
import MasterConsistencySystem from "@/systems/MasterConsistencySystem";
import ThemeControlPanel from "@/components/theme/ThemeControlPanel";
import React from "react";

const rajdhani = <PERSON><PERSON><PERSON>({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-display",
  display: "swap",
});

const barlow = Barlow({
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
  variable: "--font-body",
  display: "swap",
});

const spaceMono = Space_Mono({
  subsets: ["latin"],
  weight: ["400", "700"],
  variable: "--font-mono",
  display: "swap",
});

export const metadata: Metadata = {
  title: "CreAItive - AI-Powered Creative Platform",
  description: "Where human creativity meets artificial intelligence. Create, discover, and enhance your projects with cutting-edge AI tools.",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "#FCFCFE" },
    { media: "(prefers-color-scheme: dark)", color: "#0C0522" }
  ],
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${rajdhani.variable} ${barlow.variable} ${spaceMono.variable} scroll-smooth`} suppressHydrationWarning>
      <head>
        <meta name="theme-color" content="#080318" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              try {
                // Check for saved theme preference, default to dark
                const savedTheme = localStorage.getItem('creaitiv-theme') || 'dark';
                document.documentElement.classList.remove('light', 'dark');
                document.documentElement.classList.add(savedTheme);
              } catch (e) {
                // Default to dark theme if localStorage fails
                document.documentElement.classList.add('dark');
              }
            `,
          }}
        />
      </head>
      <body className="antialiased" suppressHydrationWarning>
        <ThemeProvider>
          <AuthProvider>
            <NotificationProvider>
              <div className="flex flex-col min-h-screen">
                <Header />
                <main className="flex-1 pt-20">
                  {children}
                </main>
                <Footer />
              </div>
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: 'var(--bg-elevated)',
                    color: 'var(--text-primary)',
                    border: '1px solid var(--border-primary)',
                    borderRadius: '8px',
                    fontFamily: 'var(--font-body)',
                  },
                  success: {
                    iconTheme: {
                      primary: '#6E7AFF',
                      secondary: '#FFFFFF',
                    },
                  },
                  error: {
                    iconTheme: {
                      primary: '#FF6B6B',
                      secondary: '#FFFFFF',
                    },
                  },
                }}
              />
            </NotificationProvider>
          </AuthProvider>
        </ThemeProvider>
        <ServiceWorkerRegistration />
        <FrontendValidator />
        <SystemMonitoringInitializer />
        <MasterConsistencySystem />
        <ThemeControlPanel />
      </body>
    </html>
  );
}
