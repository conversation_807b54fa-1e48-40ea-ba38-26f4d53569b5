'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { SystemStatusDashboard, AgentCommunicationMonitor } from '@/components/AutonomousCore';
import {
  Server, Network, Activity, Eye, Globe, Target,
  TrendingUp, Monitor, Radio, Shield, Zap, Settings,
  ChevronDown, ChevronUp, Info, CheckCircle, ArrowLeft
} from 'lucide-react';

const SystemCommunicationDemo: React.FC = () => {
  const [selectedDemo, setSelectedDemo] = useState<'dashboard' | 'communication' | 'both'>('both');
  const [expandedSection, setExpandedSection] = useState<string | null>('overview');

  const demoSections = [
    {
      id: 'overview',
      title: 'Live System Components Overview',
      icon: Info,
      content: (
        <div className="space-y-6">
          <div className="bg-gradient-to-r from-cosmic-900/40 to-nova-900/40 rounded-xl border border-cosmic-700/30 p-6">
            <h3 className="text-xl font-bold text-cosmic-100 mb-4">
              🏆 Live System: Blockchain-Ready Components
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Server className="h-6 w-6 text-cosmic-400" />
                  <div>
                    <h4 className="font-semibold text-cosmic-100">SystemStatusDashboard</h4>
                    <p className="text-sm text-cosmic-300">Comprehensive system health monitoring</p>
                  </div>
                </div>
                <ul className="text-sm text-cosmic-300 space-y-1 ml-9">
                  <li>• 4 View Modes: Overview, Detailed, Blockchain, Agents</li>
                  <li>• Real-time CPU, Memory, Disk & Network monitoring</li>
                  <li>• 28-Agent ecosystem health tracking</li>
                  <li>• Blockchain network status (Phase 4 preparation)</li>
                  <li>• Mobile-first responsive design</li>
                  <li>• System alerts and notifications</li>
                </ul>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center gap-3">
                  <Network className="h-6 w-6 text-cosmic-400" />
                  <div>
                    <h4 className="font-semibold text-cosmic-100">AgentCommunicationMonitor</h4>
                    <p className="text-sm text-cosmic-300">Consensus visualization & communication tracking</p>
                  </div>
                </div>
                <ul className="text-sm text-cosmic-300 space-y-1 ml-9">
                  <li>• 4 View Modes: Real-time, Consensus, Topology, Analytics</li>
                  <li>• Live agent communication stream</li>
                  <li>• Blockchain consensus round visualization</li>
                  <li>• Network topology mapping</li>
                  <li>• Communication analytics and metrics</li>
                  <li>• Validator preparation (75% agents as validators)</li>
                </ul>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-cosmic-800/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-5 w-5 text-green-400" />
                <span className="font-medium text-cosmic-100">AI Consensus Validated</span>
              </div>
              <p className="text-sm text-cosmic-300">
                Both components developed following R1 strategic analysis and Devstral coordination strategy, 
                with blockchain evolution readiness and mobile-first design principles.
              </p>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'features',
      title: 'Key Features & Capabilities',
      icon: Zap,
      content: (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              {
                icon: Monitor,
                title: 'Real-time Monitoring',
                desc: 'Live system metrics with 30-second updates',
                color: 'text-green-400'
              },
              {
                icon: Target,
                title: 'Consensus Visualization',
                desc: 'Blockchain validator voting and consensus tracking',
                color: 'text-purple-400'
              },
              {
                icon: Globe,
                title: 'Network Topology',
                desc: '28-agent ecosystem status and cluster analysis',
                color: 'text-blue-400'
              },
              {
                icon: TrendingUp,
                title: 'Analytics Dashboard',
                desc: 'Communication metrics and performance insights',
                color: 'text-yellow-400'
              },
              {
                icon: Shield,
                title: 'Validator Preparation',
                desc: 'Phase 4 blockchain evolution hooks integrated',
                color: 'text-red-400'
              },
              {
                icon: Radio,
                title: 'Live Communication',
                desc: 'Real-time agent message streaming',
                color: 'text-cyan-400'
              }
            ].map((feature, index) => (
              <div key={index} className="bg-cosmic-800/30 rounded-lg p-4">
                <feature.icon className={`h-8 w-8 ${feature.color} mb-3`} />
                <h4 className="font-semibold text-cosmic-100 mb-2">{feature.title}</h4>
                <p className="text-sm text-cosmic-300">{feature.desc}</p>
              </div>
            ))}
          </div>
        </div>
      )
    },
    {
      id: 'blockchain',
      title: 'Blockchain Evolution Preparation',
      icon: Globe,
      content: (
        <div className="space-y-6">
          <div className="bg-gradient-to-r from-purple-900/20 to-blue-900/20 rounded-xl border border-purple-700/30 p-6">
            <h3 className="text-xl font-bold text-cosmic-100 mb-4 flex items-center gap-2">
              <Globe className="h-6 w-6 text-purple-400" />
              Phase 4: Blockchain Network Evolution
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-semibold text-cosmic-100">Current State → Blockchain Evolution</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-cosmic-800/30 rounded-lg">
                    <span className="text-cosmic-300">28 AI Agents</span>
                    <span className="text-cosmic-400">→</span>
                    <span className="text-purple-300">28 Blockchain Validators</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-cosmic-800/30 rounded-lg">
                    <span className="text-cosmic-300">Agent Communication</span>
                    <span className="text-cosmic-400">→</span>
                    <span className="text-purple-300">Consensus Mechanisms</span>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-cosmic-800/30 rounded-lg">
                    <span className="text-cosmic-300">System Monitoring</span>
                    <span className="text-cosmic-400">→</span>
                    <span className="text-purple-300">Network Health Dashboard</span>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <h4 className="font-semibold text-cosmic-100">Blockchain Features Ready</h4>
                <div className="space-y-2">
                  {[
                    'Validator status indicators',
                    'Consensus round visualization',
                    'Block height tracking',
                    'Network latency monitoring',
                    'Staking interface hooks',
                    'DAO governance preparation'
                  ].map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-sm text-cosmic-300">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-cosmic-900 to-nova-900 text-white">
      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb Navigation */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gradient-cosmic mb-4">
            Live System & Communication Demo
          </h1>
          <p className="text-lg text-cosmic-300 max-w-3xl mx-auto">
            Blockchain-ready system monitoring and agent communication visualization with 
            AI consensus-validated architecture and mobile-first design.
          </p>
        </div>

        {/* Component Selection */}
        <div className="mb-8">
          <div className="flex flex-wrap justify-center gap-4">
            {[
              { key: 'dashboard', label: 'System Dashboard', icon: Server },
              { key: 'communication', label: 'Communication Monitor', icon: Network },
              { key: 'both', label: 'Both Components', icon: Activity }
            ].map(({ key, label, icon: Icon }) => (
              <button
                key={key}
                onClick={() => setSelectedDemo(key as any)}
                className={`flex items-center gap-2 px-6 py-3 rounded-lg font-medium transition-all ${
                  selectedDemo === key
                    ? 'bg-cosmic-600 text-white shadow-neon'
                    : 'bg-cosmic-800/50 text-cosmic-300 hover:bg-cosmic-700/50'
                }`}
              >
                <Icon className="h-5 w-5" />
                {label}
              </button>
            ))}
          </div>
        </div>

        {/* Information Sections */}
        <div className="mb-8">
          <div className="space-y-4">
            {demoSections.map((section) => (
              <div key={section.id} className="bg-cosmic-900/40 rounded-xl border border-cosmic-700/30">
                <button
                  onClick={() => setExpandedSection(expandedSection === section.id ? null : section.id)}
                  className="w-full flex items-center justify-between p-4 text-left"
                >
                  <div className="flex items-center gap-3">
                    <section.icon className="h-5 w-5 text-cosmic-400" />
                    <span className="font-medium text-cosmic-100">{section.title}</span>
                  </div>
                  {expandedSection === section.id ? (
                    <ChevronUp className="h-5 w-5 text-cosmic-400" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-cosmic-400" />
                  )}
                </button>
                
                {expandedSection === section.id && (
                  <div className="p-4 pt-0">
                    {section.content}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Component Demos */}
        <div className="space-y-8">
          {(selectedDemo === 'dashboard' || selectedDemo === 'both') && (
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Server className="h-6 w-6 text-cosmic-400" />
                <h2 className="text-2xl font-bold text-cosmic-100">SystemStatusDashboard</h2>
                <span className="px-3 py-1 bg-green-600/20 text-green-300 text-sm rounded-full">
                  Network-Ready
                </span>
              </div>
              
              <div className="bg-cosmic-900/20 rounded-xl border border-cosmic-700/30 p-6">
                <SystemStatusDashboard />
              </div>
            </div>
          )}

          {(selectedDemo === 'communication' || selectedDemo === 'both') && (
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <Network className="h-6 w-6 text-cosmic-400" />
                <h2 className="text-2xl font-bold text-cosmic-100">AgentCommunicationMonitor</h2>
                <span className="px-3 py-1 bg-purple-600/20 text-purple-300 text-sm rounded-full">
                  Consensus-Ready
                </span>
              </div>
              
              <div className="bg-cosmic-900/20 rounded-xl border border-cosmic-700/30 p-6">
                <AgentCommunicationMonitor />
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-12 text-center">
          <div className="bg-gradient-to-r from-cosmic-900/40 to-nova-900/40 rounded-xl border border-cosmic-700/30 p-6">
            <div className="flex items-center justify-center gap-2 mb-4">
              <CheckCircle className="h-6 w-6 text-green-400" />
              <h3 className="text-xl font-bold text-cosmic-100">Day 5 Complete</h3>
            </div>
            <p className="text-cosmic-300 mb-4">
              All 8/8 Autonomous Core Components successfully created with AI consensus validation,
              blockchain evolution preparation, and mobile-first responsive design.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-cosmic-400">
              <span>✅ AI Consensus (R1 + Devstral)</span>
              <span>✅ Mobile-First Design</span>
              <span>✅ Blockchain Evolution Hooks</span>
              <span>✅ Real-time Monitoring</span>
              <span>✅ TypeScript Compliance</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemCommunicationDemo; 