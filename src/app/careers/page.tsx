'use client';

import React, { useState } from 'react';
import { MapPin, Clock, Users, Zap, Heart, Globe, ArrowRight } from 'lucide-react';

interface JobPosition {
  id: string;
  title: string;
  department: string;
  location: string;
  type: string;
  description: string;
  requirements: string[];
}

const jobPositions: JobPosition[] = [
  {
    id: '1',
    title: 'Senior AI Engineer',
    department: 'Engineering',
    location: 'Remote',
    type: 'Full-time',
    description: 'Join our team to build the next generation of AI agent collaboration systems.',
    requirements: ['5+ years in AI/ML', 'Python expertise', 'Experience with LLMs', 'Distributed systems knowledge']
  },
  {
    id: '2',
    title: 'Product Designer',
    department: 'Design',
    location: 'Remote',
    type: 'Full-time',
    description: 'Design intuitive interfaces for complex AI agent interactions and workflows.',
    requirements: ['3+ years product design', 'Figma expertise', 'AI/ML product experience', 'User research skills']
  },
  {
    id: '3',
    title: 'DevOps Engineer',
    department: 'Infrastructure',
    location: 'Remote',
    type: 'Full-time',
    description: 'Scale our AI agent infrastructure to handle millions of concurrent operations.',
    requirements: ['Kubernetes expertise', 'Cloud platforms (AWS/GCP)', 'CI/CD pipelines', 'Monitoring systems']
  }
];

const benefits = [
  {
    icon: Globe,
    title: 'Remote First',
    description: 'Work from anywhere in the world with flexible hours'
  },
  {
    icon: Zap,
    title: 'Cutting Edge Tech',
    description: 'Work with the latest AI technologies and frameworks'
  },
  {
    icon: Users,
    title: 'Amazing Team',
    description: 'Collaborate with world-class engineers and designers'
  },
  {
    icon: Heart,
    title: 'Health & Wellness',
    description: 'Comprehensive health coverage and wellness programs'
  }
];

export default function CareersPage() {
  const [selectedDepartment, setSelectedDepartment] = useState('All');
  
  const departments = ['All', 'Engineering', 'Design', 'Infrastructure', 'Product'];
  
  const filteredJobs = jobPositions.filter(job => 
    selectedDepartment === 'All' || job.department === selectedDepartment
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900">
      <div className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Join the
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-purple-400 ml-3">
              AI Revolution
            </span>
          </h1>
          
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed mb-8">
            Help us build the future of AI-powered creativity. Join a team that's pushing 
            the boundaries of what's possible with artificial intelligence.
          </p>
          
          <div className="flex items-center justify-center gap-8 text-white/60">
            <div className="flex items-center gap-2">
              <Globe className="w-5 h-5 text-cosmic-400" />
              <span>Remote First</span>
            </div>
            <div className="flex items-center gap-2">
              <Users className="w-5 h-5 text-cosmic-400" />
              <span>Growing Team</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="w-5 h-5 text-cosmic-400" />
              <span>Cutting Edge</span>
            </div>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-white text-center mb-12">Why CreAItive?</h2>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <div
                key={index}
                className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 text-center"
              >
                <benefit.icon className="w-8 h-8 text-cosmic-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-white mb-2">{benefit.title}</h3>
                <p className="text-white/60 text-sm">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Job Positions */}
        <div>
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-white">Open Positions</h2>
            
            <div className="flex gap-2">
              {departments.map((dept) => (
                <button
                  key={dept}
                  onClick={() => setSelectedDepartment(dept)}
                  className={`px-4 py-2 rounded-lg transition-all ${
                    selectedDepartment === dept
                      ? 'bg-cosmic-500/20 text-cosmic-300 border border-cosmic-500/30'
                      : 'bg-space-800/50 text-white/60 hover:text-white hover:bg-space-700/50'
                  }`}
                >
                  {dept}
                </button>
              ))}
            </div>
          </div>

          <div className="space-y-6">
            {filteredJobs.map((job) => (
              <div
                key={job.id}
                className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:border-cosmic-500/30 transition-all"
              >
                <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4 mb-4">
                  <div>
                    <h3 className="text-xl font-semibold text-white mb-2">{job.title}</h3>
                    <div className="flex items-center gap-4 text-white/60">
                      <span className="px-2 py-1 bg-cosmic-500/20 text-cosmic-300 text-xs rounded-full">
                        {job.department}
                      </span>
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        <span>{job.location}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{job.type}</span>
                      </div>
                    </div>
                  </div>
                  
                  <button className="flex items-center gap-2 bg-cosmic-500/20 hover:bg-cosmic-500/30 text-cosmic-300 px-6 py-2 rounded-lg transition-all">
                    Apply Now
                    <ArrowRight className="w-4 h-4" />
                  </button>
                </div>
                
                <p className="text-white/70 mb-4">{job.description}</p>
                
                <div>
                  <h4 className="text-white font-medium mb-2">Requirements:</h4>
                  <ul className="text-white/60 space-y-1">
                    {job.requirements.map((req, index) => (
                      <li key={index} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-cosmic-400 rounded-full" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            ))}
          </div>

          {filteredJobs.length === 0 && (
            <div className="text-center py-12">
              <p className="text-white/60 text-lg">No positions available in this department.</p>
            </div>
          )}
        </div>

        {/* CTA Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 border border-white/10">
            <h3 className="text-2xl font-bold text-white mb-4">Don't see a perfect fit?</h3>
            <p className="text-white/70 mb-6">
              We're always looking for talented individuals who share our passion for AI innovation.
            </p>
            <button className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-8 py-3 rounded-lg transition-all">
              Send Us Your Resume
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
