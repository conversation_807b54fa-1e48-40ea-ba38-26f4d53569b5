'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON>, Setting<PERSON>, Shield, BarChart3, <PERSON>, ArrowLeft } from 'lucide-react';

interface CookieCategory {
  id: string;
  name: string;
  description: string;
  essential: boolean;
  enabled: boolean;
  cookies: string[];
}

export default function CookiesPage() {
  const [cookieSettings, setCookieSettings] = useState<CookieCategory[]>([
    {
      id: 'essential',
      name: 'Essential Cookies',
      description: 'Required for the website to function properly. Cannot be disabled.',
      essential: true,
      enabled: true,
      cookies: ['session_id', 'csrf_token', 'auth_token', 'user_preferences']
    },
    {
      id: 'analytics',
      name: 'Analytics Cookies',
      description: 'Help us understand how visitors interact with our website.',
      essential: false,
      enabled: true,
      cookies: ['_ga', '_gid', 'analytics_session', 'page_views']
    },
    {
      id: 'functional',
      name: 'Functional Cookies',
      description: 'Enable enhanced functionality and personalization.',
      essential: false,
      enabled: true,
      cookies: ['theme_preference', 'language_setting', 'layout_config']
    },
    {
      id: 'marketing',
      name: 'Marketing Cookies',
      description: 'Used to track visitors and display relevant advertisements.',
      essential: false,
      enabled: false,
      cookies: ['marketing_id', 'ad_preferences', 'conversion_tracking']
    }
  ]);

  const toggleCookie = (id: string) => {
    setCookieSettings(prev => 
      prev.map(category => 
        category.id === id && !category.essential
          ? { ...category, enabled: !category.enabled }
          : category
      )
    );
  };

  const saveSettings = () => {
    // Save cookie preferences
    console.log('Cookie settings saved:', cookieSettings);
    // In a real app, this would save to localStorage and update cookie consent
  };

  const acceptAll = () => {
    setCookieSettings(prev => 
      prev.map(category => ({ ...category, enabled: true }))
    );
  };

  const rejectAll = () => {
    setCookieSettings(prev => 
      prev.map(category => 
        category.essential 
          ? category 
          : { ...category, enabled: false }
      )
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900">
      <div className="container mx-auto px-4 py-12">
        {/* Breadcrumb Navigation */}
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>
        </div>

        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-cosmic-500/20 px-4 py-2 rounded-full mb-6">
            <Cookie className="w-4 h-4 text-cosmic-400" />
            <span className="text-cosmic-300 text-sm font-medium">Cookie Policy</span>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Cookie
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-cosmic-400 to-purple-400 ml-2">
              Settings
            </span>
          </h1>
          
          <p className="text-xl text-white/70 max-w-3xl mx-auto leading-relaxed">
            We use cookies to enhance your experience, analyze site usage, and assist in marketing efforts. 
            You can customize your cookie preferences below.
          </p>
        </div>

        {/* Cookie Categories */}
        <div className="space-y-6 mb-12">
          {cookieSettings.map((category) => (
            <div
              key={category.id}
              className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-cosmic-500/20 rounded-lg">
                    {category.id === 'essential' && <Shield className="w-5 h-5 text-cosmic-400" />}
                    {category.id === 'analytics' && <BarChart3 className="w-5 h-5 text-cosmic-400" />}
                    {category.id === 'functional' && <Settings className="w-5 h-5 text-cosmic-400" />}
                    {category.id === 'marketing' && <Users className="w-5 h-5 text-cosmic-400" />}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">{category.name}</h3>
                    <p className="text-white/60 text-sm">{category.description}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  {category.essential ? (
                    <span className="text-cosmic-400 text-sm font-medium">Always Active</span>
                  ) : (
                    <button
                      onClick={() => toggleCookie(category.id)}
                      className={`relative w-12 h-6 rounded-full transition-all ${
                        category.enabled ? 'bg-cosmic-500' : 'bg-space-700'
                      }`}
                    >
                      <div
                        className={`absolute top-1 w-4 h-4 bg-white rounded-full transition-all ${
                          category.enabled ? 'left-7' : 'left-1'
                        }`}
                      />
                    </button>
                  )}
                </div>
              </div>
              
              <div className="ml-11">
                <h4 className="text-white/80 font-medium mb-2">Cookies in this category:</h4>
                <div className="flex flex-wrap gap-2">
                  {category.cookies.map((cookie) => (
                    <span
                      key={cookie}
                      className="px-2 py-1 bg-space-700/50 text-white/60 text-xs rounded-full"
                    >
                      {cookie}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
          <button
            onClick={acceptAll}
            className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-8 py-3 rounded-lg transition-all"
          >
            Accept All Cookies
          </button>
          <button
            onClick={rejectAll}
            className="bg-space-700 hover:bg-space-600 text-white px-8 py-3 rounded-lg transition-all"
          >
            Reject Non-Essential
          </button>
          <button
            onClick={saveSettings}
            className="bg-purple-500 hover:bg-purple-600 text-white px-8 py-3 rounded-lg transition-all"
          >
            Save Preferences
          </button>
        </div>

        {/* Information Sections */}
        <div className="grid md:grid-cols-2 gap-8 mb-16">
          <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6">What Are Cookies?</h2>
            <p className="text-white/70 mb-4">
              Cookies are small text files that are stored on your device when you visit a website. 
              They help websites remember your preferences and provide a better user experience.
            </p>
            <p className="text-white/70">
              CreAItive uses cookies to enhance functionality, analyze usage patterns, 
              and provide personalized experiences with our AI agents.
            </p>
          </div>
          
          <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8">
            <h2 className="text-2xl font-bold text-white mb-6">Managing Cookies</h2>
            <p className="text-white/70 mb-4">
              You can control cookies through your browser settings or using the preferences above. 
              Note that disabling certain cookies may affect website functionality.
            </p>
            <p className="text-white/70">
              Your cookie preferences are saved locally and will be remembered for future visits.
            </p>
          </div>
        </div>

        {/* Third-Party Cookies */}
        <div className="bg-space-800/50 backdrop-blur-sm border border-white/10 rounded-xl p-8 mb-16">
          <h2 className="text-2xl font-bold text-white mb-6">Third-Party Services</h2>
          <p className="text-white/70 mb-6">
            We use the following third-party services that may set their own cookies:
          </p>
          
          <div className="grid md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-cosmic-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <BarChart3 className="w-6 h-6 text-cosmic-400" />
              </div>
              <h3 className="text-white font-medium mb-2">Google Analytics</h3>
              <p className="text-white/60 text-sm">Website usage analytics</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-cosmic-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Users className="w-6 h-6 text-cosmic-400" />
              </div>
              <h3 className="text-white font-medium mb-2">Intercom</h3>
              <p className="text-white/60 text-sm">Customer support chat</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-cosmic-500/20 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Shield className="w-6 h-6 text-cosmic-400" />
              </div>
              <h3 className="text-white font-medium mb-2">Cloudflare</h3>
              <p className="text-white/60 text-sm">Security and performance</p>
            </div>
          </div>
        </div>

        {/* Contact Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-cosmic-500/20 to-purple-500/20 rounded-2xl p-8 border border-white/10">
            <Cookie className="w-12 h-12 text-cosmic-400 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-white mb-4">Questions About Cookies?</h3>
            <p className="text-white/70 mb-6 max-w-2xl mx-auto">
              If you have any questions about our use of cookies or this policy, 
              please don't hesitate to contact us.
            </p>
            <button className="bg-cosmic-500 hover:bg-cosmic-600 text-white px-8 py-3 rounded-lg transition-all">
              Contact Support
            </button>
          </div>
        </div>

        {/* Last Updated */}
        <div className="mt-12 text-center text-white/60 text-sm">
          <p>This cookie policy was last updated on January 15, 2024.</p>
        </div>
      </div>
    </div>
  );
}
