/**
 * Task Management Dashboard - Main interface for the Enhanced Task Manager
 * Connects to EnhancedTaskManager backend for full task visibility and control
 * Part of Synchronous Backend-Frontend Development Protocol
 */

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Card } from '@/shared/components/Card';
import { Button } from '@/shared/components/Button';
import Container from '@/shared/components/Container';

// Task Types from EnhancedTaskManager backend
enum TaskType {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  DEPLOYMENT = 'deployment',
  MONITORING = 'monitoring',
  OPTIMIZATION = 'optimization',
  ANALYSIS = 'analysis',
  COMMUNICATION = 'communication',
  COORDINATION = 'coordination'
}

enum TaskStatus {
  PENDING = 'pending',
  ASSIGNED = 'assigned',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

interface EnhancedTask {
  id: string;
  type: TaskType;
  title: string;
  description: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  status: TaskStatus;
  assignedAgent?: string;
  requiredCapabilities: string[];
  estimatedDuration: number;
  dependencies: string[];
  createdAt: string;
  updatedAt: string;
  deadline?: string;
  retryCount: number;
  maxRetries: number;
  result?: any;
}

interface TaskManagerMetrics {
  totalTasks: number;
  activeTasks: number;
  pendingTasks: number;
  completedTasks: number;
  failedTasks: number;
  totalAgents: number;
  activeAgents: number;
  averageSuccessRate: number;
  averageResponseTime: number;
}

export default function TasksPage() {
  const router = useRouter();
  const [tasks, setTasks] = useState<EnhancedTask[]>([]);
  const [metrics, setMetrics] = useState<TaskManagerMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<'all' | TaskStatus>('all');
  const [typeFilter, setTypeFilter] = useState<'all' | TaskType>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Fetch tasks and metrics from EnhancedTaskManager
  const fetchTasks = async () => {
    try {
      setLoading(true);
      
      // Fetch tasks
      const tasksResponse = await fetch('/api/tasks');
      const tasksData = await tasksResponse.json();
      
      // Fetch metrics
      const metricsResponse = await fetch('/api/tasks/metrics');
      const metricsData = await metricsResponse.json();
      
      if (tasksData.success && metricsData.success) {
        setTasks(tasksData.data || []);
        setMetrics(metricsData.data);
        setError(null);
      } else {
        setError(tasksData.error || metricsData.error || 'Failed to fetch tasks');
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Filter tasks based on status, type, and search
  const filteredTasks = tasks.filter(task => {
    const matchesStatus = filter === 'all' || task.status === filter;
    const matchesType = typeFilter === 'all' || task.type === typeFilter;
    const matchesSearch = searchTerm === '' || 
      task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (task.assignedAgent && task.assignedAgent.toLowerCase().includes(searchTerm.toLowerCase()));
    
    return matchesStatus && matchesType && matchesSearch;
  });

  // Get status color
  const getStatusColor = (status: TaskStatus) => {
    switch (status) {
      case TaskStatus.COMPLETED: return 'text-nova-400 bg-nova-500/20';
      case TaskStatus.IN_PROGRESS: return 'text-cosmic-400 bg-cosmic-500/20';
      case TaskStatus.ASSIGNED: return 'text-nova-400 bg-nova-500/20';
      case TaskStatus.PENDING: return 'text-stardust-400 bg-stardust-500/20';
      case TaskStatus.FAILED: return 'text-red-400 bg-red-500/20';
      case TaskStatus.CANCELLED: return 'text-space-500 bg-space-600/20';
      default: return 'text-stardust-400 bg-stardust-500/20';
    }
  };

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'text-red-400 bg-red-500/20';
      case 'high': return 'text-neural-400 bg-neural-500/20';
      case 'medium': return 'text-quantum-400 bg-quantum-500/20';
      case 'low': return 'text-stardust-400 bg-stardust-500/20';
      default: return 'text-stardust-400 bg-stardust-500/20';
    }
  };

  // Format duration
  const formatDuration = (minutes: number) => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  useEffect(() => {
    fetchTasks();
    
    // Poll for updates every 10 seconds
    const interval = setInterval(fetchTasks, 10000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Background patterns */}
      <div className="absolute inset-0 z-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-cosmic-50/50 via-transparent to-nova-50/50" />
        <div className="absolute inset-0 bg-gradient-to-tr from-neural-50/30 via-transparent to-aura-50/30" />
      </div>
      
      <main className="relative z-10">
        <Container className="py-8">
          {/* Breadcrumb Navigation */}
          <div className="mb-6">
            <Link
              href="/dashboard"
              className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Dashboard Hub</span>
            </Link>
          </div>

          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-cosmic-500 via-nova-500 to-neural-500 flex items-center justify-center animate-glow">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl font-display font-bold text-gradient-multi">
                  Task Management Dashboard
                </h1>
                <p className="text-lg theme-text-secondary">
                  Enhanced Task Manager - 41-Agent Ecosystem Control
                </p>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4">
              <Button
                variant="primary"
                onClick={() => router.push('/tasks/create')}
                className="animate-glow"
              >
                + Create New Task
              </Button>
              <Button
                variant="outline"
                onClick={fetchTasks}
                disabled={loading}
              >
                {loading ? 'Refreshing...' : 'Refresh Tasks'}
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push('/agents')}
              >
                View Agents
              </Button>
            </div>
          </div>

          {/* Metrics Cards */}
          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="neo-panel p-6 backdrop-blur-md">
                <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Total Tasks</h3>
                <div className="text-3xl font-bold text-gradient-cosmic mb-2">
                  {metrics.totalTasks}
                </div>
                <div className="text-xs text-muted-enhanced">
                  {metrics.activeTasks} active
                </div>
              </div>
              
              <div className="neo-panel p-6 backdrop-blur-md">
                <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Success Rate</h3>
                <div className="text-3xl font-bold text-gradient-nova mb-2">
                  {metrics.averageSuccessRate}%
                </div>
                <div className="text-xs text-muted-enhanced">
                  {metrics.completedTasks} completed
                </div>
              </div>
              
              <div className="neo-panel p-6 backdrop-blur-md">
                <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Response Time</h3>
                <div className="text-3xl font-bold text-gradient-neural mb-2">
                  {metrics.averageResponseTime}ms
                </div>
                <div className="text-xs text-muted-enhanced">
                  Average processing
                </div>
              </div>
              
              <div className="neo-panel p-6 backdrop-blur-md">
                <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Active Agents</h3>
                <div className="text-3xl font-bold text-gradient-aura mb-2">
                  {metrics.activeAgents}/{metrics.totalAgents}
                </div>
                <div className="text-xs text-muted-enhanced">
                  {metrics.pendingTasks} pending
                </div>
              </div>
            </div>
          )}

          {/* Filters */}
          <Card className="p-6 mb-6">
            <div className="flex flex-wrap gap-4 items-center">
              <div className="flex-1 min-w-64">
                <input
                  type="text"
                  placeholder="Search tasks, descriptions, or agents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="neo-input w-full"
                />
              </div>
              
              <select
                value={filter}
                onChange={(e) => setFilter(e.target.value as any)}
                className="neo-input"
              >
                <option value="all">All Status</option>
                <option value={TaskStatus.PENDING}>Pending</option>
                <option value={TaskStatus.ASSIGNED}>Assigned</option>
                <option value={TaskStatus.IN_PROGRESS}>In Progress</option>
                <option value={TaskStatus.COMPLETED}>Completed</option>
                <option value={TaskStatus.FAILED}>Failed</option>
                <option value={TaskStatus.CANCELLED}>Cancelled</option>
              </select>
              
              <select
                value={typeFilter}
                onChange={(e) => setTypeFilter(e.target.value as any)}
                className="neo-input"
              >
                <option value="all">All Types</option>
                <option value={TaskType.DEVELOPMENT}>Development</option>
                <option value={TaskType.TESTING}>Testing</option>
                <option value={TaskType.DEPLOYMENT}>Deployment</option>
                <option value={TaskType.MONITORING}>Monitoring</option>
                <option value={TaskType.OPTIMIZATION}>Optimization</option>
                <option value={TaskType.ANALYSIS}>Analysis</option>
                <option value={TaskType.COMMUNICATION}>Communication</option>
                <option value={TaskType.COORDINATION}>Coordination</option>
              </select>
            </div>
          </Card>

          {/* Error State */}
          {error && (
            <Card className="p-6 mb-6 border-red-500/20 bg-red-500/5">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-red-500/20 flex items-center justify-center">
                  <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-red-400">Error Loading Tasks</h3>
                  <p className="text-sm text-red-300">{error}</p>
                </div>
              </div>
            </Card>
          )}

          {/* Loading State */}
          {loading && !tasks.length && (
            <Card className="p-12 text-center">
              <div className="animate-spin w-8 h-8 border-2 border-cosmic-500 border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="theme-text-secondary">Loading tasks from Enhanced Task Manager...</p>
            </Card>
          )}

          {/* Tasks List */}
          {!loading && filteredTasks.length > 0 && (
            <div className="space-y-4">
              {filteredTasks.map((task) => (
                <Card
                  key={task.id}
                  className="p-6 hover:shadow-multi transition-all duration-300 cursor-pointer group"
                  onClick={() => router.push(`/tasks/${task.id}`)}
                >
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="text-lg font-semibold theme-text-primary group-hover:text-gradient-cosmic transition-colors">
                          {task.title}
                        </h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`}>
                          {task.status.replace('_', ' ')}
                        </span>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                          {task.priority}
                        </span>
                      </div>
                      <p className="theme-text-secondary mb-3 line-clamp-2">
                        {task.description}
                      </p>
                    </div>
                    
                    <div className="text-right ml-4">
                      <p className="text-sm theme-text-secondary">
                        {formatDate(task.createdAt)}
                      </p>
                      <p className="text-xs theme-text-muted">
                        Est. {formatDuration(task.estimatedDuration)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <span className="text-xs theme-text-secondary">Type:</span>
                        <span className="px-2 py-1 bg-cosmic-500/20 text-cosmic-300 rounded text-xs">
                          {task.type}
                        </span>
                      </div>
                      
                      {task.assignedAgent && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs theme-text-secondary">Agent:</span>
                          <span className="px-2 py-1 bg-nova-500/20 text-nova-300 rounded text-xs">
                            {task.assignedAgent}
                          </span>
                        </div>
                      )}
                      
                      {task.dependencies.length > 0 && (
                        <div className="flex items-center gap-2">
                          <span className="text-xs theme-text-secondary">Dependencies:</span>
                          <span className="px-2 py-1 bg-neural-500/20 text-neural-300 rounded text-xs">
                            {task.dependencies.length}
                          </span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {task.retryCount > 0 && (
                        <span className="px-2 py-1 bg-quantum-500/20 text-quantum-300 rounded text-xs">
                          Retry {task.retryCount}/{task.maxRetries}
                        </span>
                      )}
                      
                      <svg className="w-4 h-4 theme-text-muted group-hover:text-cosmic-400 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}

          {/* Empty State */}
          {!loading && filteredTasks.length === 0 && tasks.length > 0 && (
            <Card className="p-12 text-center">
              <div className="w-16 h-16 bg-cosmic-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-cosmic-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold theme-text-primary mb-2">No tasks match your filters</h3>
              <p className="theme-text-secondary mb-4">Try adjusting your search or filter criteria</p>
              <Button
                variant="outline"
                onClick={() => {
                  setFilter('all');
                  setTypeFilter('all');
                  setSearchTerm('');
                }}
              >
                Clear Filters
              </Button>
            </Card>
          )}

          {/* No Tasks State */}
          {!loading && tasks.length === 0 && !error && (
            <Card className="p-12 text-center">
              <div className="w-16 h-16 bg-cosmic-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-cosmic-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold theme-text-primary mb-2">No tasks yet</h3>
              <p className="theme-text-secondary mb-4">Create your first task to get started with the Enhanced Task Manager</p>
              <Button
                variant="primary"
                onClick={() => router.push('/tasks/create')}
                className="animate-glow"
              >
                Create First Task
              </Button>
            </Card>
          )}
        </Container>
      </main>
    </div>
  );
} 