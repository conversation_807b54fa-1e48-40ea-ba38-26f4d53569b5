/**
 * Task Creation Wizard - Create new tasks for the Enhanced Task Manager
 * Connects to EnhancedTaskManager backend with agent capability matching
 * Part of Synchronous Backend-Frontend Development Protocol
 */

'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { ArrowLeft } from 'lucide-react';
import { Card } from '@/shared/components/Card';
import { Button } from '@/shared/components/Button';
import Container from '@/shared/components/Container';

// Task Types from EnhancedTaskManager backend
enum TaskType {
  DEVELOPMENT = 'development',
  TESTING = 'testing',
  DEPLOYMENT = 'deployment',
  MONITORING = 'monitoring',
  OPTIMIZATION = 'optimization',
  ANALYSIS = 'analysis',
  COMMUNICATION = 'communication',
  COORDINATION = 'coordination'
}

interface TaskFormData {
  title: string;
  description: string;
  type: TaskType;
  priority: 'critical' | 'high' | 'medium' | 'low';
  requiredCapabilities: string[];
  estimatedDuration: number;
  dependencies: string[];
  deadline?: string;
  maxRetries: number;
  metadata: Record<string, any>;
}

interface AgentCapability {
  name: string;
  description: string;
  agents: string[];
}

export default function CreateTaskPage() {
  const router = useRouter();
  const [formData, setFormData] = useState<TaskFormData>({
    title: '',
    description: '',
    type: TaskType.DEVELOPMENT,
    priority: 'medium',
    requiredCapabilities: [],
    estimatedDuration: 30,
    dependencies: [],
    maxRetries: 3,
    metadata: {}
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [step, setStep] = useState(1);

  // Common agent capabilities based on our 41-agent ecosystem
  const commonCapabilities = [
    { name: 'code_generation', description: 'Generate code components and functions', agents: ['DevAgent', 'UIAgent'] },
    { name: 'testing', description: 'Create and run tests', agents: ['TestAgent', 'QualityMonitor'] },
    { name: 'deployment', description: 'Deploy applications and services', agents: ['OpsAgent', 'DeploymentAgent'] },
    { name: 'monitoring', description: 'Monitor system performance and health', agents: ['PerformanceMonitoringAgent', 'HealthMonitor'] },
    { name: 'security_analysis', description: 'Analyze security vulnerabilities', agents: ['SecurityAgent'] },
    { name: 'optimization', description: 'Optimize performance and resources', agents: ['ResourceOptimizationEngine'] },
    { name: 'coordination', description: 'Coordinate between multiple agents', agents: ['MLCoordinationLayer', 'AgentStrategicCoordinator'] },
    { name: 'strategic_planning', description: 'Strategic planning and governance', agents: ['StrategicGovernanceEngine'] },
    { name: 'self_modification', description: 'Self-improvement and modification', agents: ['AdvancedSelfModificationEngine'] },
    { name: 'communication', description: 'Cross-agent communication', agents: ['CommunicationAgent'] },
    { name: 'data_analysis', description: 'Analyze data and generate insights', agents: ['AnalysisAgent'] },
    { name: 'ui_design', description: 'Design and create user interfaces', agents: ['UIAgent'] }
  ];

  const taskTypeDescriptions = {
    [TaskType.DEVELOPMENT]: 'Create new features, components, or functionality',
    [TaskType.TESTING]: 'Test existing functionality or create test suites',
    [TaskType.DEPLOYMENT]: 'Deploy applications or configure infrastructure',
    [TaskType.MONITORING]: 'Monitor system health and performance metrics',
    [TaskType.OPTIMIZATION]: 'Improve performance, efficiency, or resource usage',
    [TaskType.ANALYSIS]: 'Analyze data, code, or system behavior',
    [TaskType.COMMUNICATION]: 'Facilitate communication between agents or users',
    [TaskType.COORDINATION]: 'Coordinate multiple agents or complex workflows'
  };

  // Update form data
  const updateFormData = (field: keyof TaskFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Toggle capability selection
  const toggleCapability = (capability: string) => {
    setFormData(prev => ({
      ...prev,
      requiredCapabilities: prev.requiredCapabilities.includes(capability)
        ? prev.requiredCapabilities.filter(c => c !== capability)
        : [...prev.requiredCapabilities, capability]
    }));
  };

  // Submit task
  const submitTask = async () => {
    if (!formData.title.trim() || !formData.description.trim()) {
      setError('Title and description are required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/tasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setSuccess(true);
        setTimeout(() => {
          router.push('/tasks');
        }, 2000);
      } else {
        setError(data.error || 'Failed to create task');
      }
    } catch (err: any) {
      setError(err.message || 'An unexpected error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <div className="min-h-screen theme-bg-primary flex items-center justify-center">
        <Card className="p-12 text-center max-w-md">
          <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-green-400 mb-2">Task Created Successfully!</h2>
          <p className="theme-text-secondary mb-4">Your task has been submitted to the Enhanced Task Manager</p>
          <p className="text-sm theme-text-muted">Redirecting to tasks dashboard...</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen theme-bg-primary">
      {/* Background patterns */}
      <div className="absolute inset-0 z-0 opacity-30">
        <div className="absolute inset-0 bg-gradient-to-br from-cosmic-50/50 via-transparent to-nova-50/50" />
        <div className="absolute inset-0 bg-gradient-to-tr from-neural-50/30 via-transparent to-aura-50/30" />
      </div>
      
      <main className="relative z-10">
        <Container className="py-8">
          {/* Breadcrumb Navigation */}
          <div className="mb-8">
            <Link
              href="/tasks"
              className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Back to Tasks</span>
            </Link>
          </div>

          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-6">
              <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-cosmic-500 via-nova-500 to-neural-500 flex items-center justify-center animate-glow">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <div>
                <h1 className="text-3xl font-display font-bold text-gradient-multi">
                  Create New Task
                </h1>
                <p className="text-lg theme-text-secondary">
                  Submit tasks to the Enhanced Task Manager - 41-Agent Ecosystem
                </p>
              </div>
            </div>


          </div>

          {/* Error Display */}
          {error && (
            <Card className="p-4 mb-6 border-red-500/20 bg-red-500/5">
              <div className="flex items-center gap-3">
                <div className="w-6 h-6 rounded-full bg-red-500/20 flex items-center justify-center">
                  <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p className="text-red-400">{error}</p>
              </div>
            </Card>
          )}

          {/* Form */}
          <Card className="p-8">
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium theme-text-primary mb-2">
                  Task Title *
                </label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => updateFormData('title', e.target.value)}
                  placeholder="Enter a clear, descriptive title for your task"
                  className="neo-input w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium theme-text-primary mb-2">
                  Task Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => updateFormData('description', e.target.value)}
                  placeholder="Provide detailed requirements and expectations for this task"
                  rows={4}
                  className="neo-input w-full resize-none"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Task Type *
                  </label>
                  <select
                    value={formData.type}
                    onChange={(e) => updateFormData('type', e.target.value)}
                    className="neo-input w-full"
                  >
                    {Object.entries(taskTypeDescriptions).map(([type, description]) => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1)}
                      </option>
                    ))}
                  </select>
                  <p className="text-xs theme-text-muted mt-1">
                    {taskTypeDescriptions[formData.type]}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Priority Level *
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => updateFormData('priority', e.target.value)}
                    className="neo-input w-full"
                  >
                    <option value="critical">Critical</option>
                    <option value="high">High</option>
                    <option value="medium">Medium</option>
                    <option value="low">Low</option>
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium theme-text-primary mb-3">
                  Required Capabilities *
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {commonCapabilities.map((capability) => (
                    <div
                      key={capability.name}
                      onClick={() => toggleCapability(capability.name)}
                      className={`p-4 rounded-lg border cursor-pointer transition-all ${
                        formData.requiredCapabilities.includes(capability.name)
                          ? 'border-cosmic-400 bg-cosmic-500/10'
                          : 'border-space-600 hover:border-space-500'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium theme-text-primary">
                          {capability.name.replace('_', ' ')}
                        </h4>
                        <div className={`w-4 h-4 rounded border-2 ${
                          formData.requiredCapabilities.includes(capability.name)
                            ? 'bg-cosmic-500 border-cosmic-500'
                            : 'border-space-500'
                        }`}>
                          {formData.requiredCapabilities.includes(capability.name) && (
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                      </div>
                      <p className="text-xs theme-text-muted">{capability.description}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Estimated Duration (minutes)
                  </label>
                  <input
                    type="number"
                    value={formData.estimatedDuration}
                    onChange={(e) => updateFormData('estimatedDuration', parseInt(e.target.value) || 30)}
                    min="1"
                    className="neo-input w-full"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium theme-text-primary mb-2">
                    Max Retry Attempts
                  </label>
                  <select
                    value={formData.maxRetries}
                    onChange={(e) => updateFormData('maxRetries', parseInt(e.target.value))}
                    className="neo-input w-full"
                  >
                    <option value={1}>1 (No retries)</option>
                    <option value={2}>2 retries</option>
                    <option value={3}>3 retries</option>
                    <option value={5}>5 retries</option>
                  </select>
                </div>
              </div>

              <div className="flex justify-end gap-4">
                <Button
                  variant="outline"
                  onClick={() => router.push('/tasks')}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={submitTask}
                  disabled={loading || !formData.title.trim() || !formData.description.trim()}
                  className="animate-glow"
                >
                  {loading ? 'Creating Task...' : 'Create Task'}
                </Button>
              </div>
            </div>
          </Card>
        </Container>
      </main>
    </div>
  );
} 