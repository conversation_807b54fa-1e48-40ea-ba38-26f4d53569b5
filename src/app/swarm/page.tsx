"use client";

import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import Container from '@/shared/components/Container';

export default function SwarmPage() {
  return (
    <div className="min-h-screen bg-adaptive">
      <Container className="theme-spacing-responsive">
        {/* Breadcrumb Navigation */}
        <div className="mb-6">
          <Link
            href="/agents"
            className="inline-flex items-center gap-2 text-stardust-400 hover:text-cosmic-400 transition-colors"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Agent Hub</span>
          </Link>
        </div>

        <h1 className="heading-1 mb-responsive-md text-gradient-cosmic priority-element priority-critical text-adaptive">
          28-Agent Swarm Intelligence
        </h1>
        
        <div className="body-large mb-responsive-lg text-stardust-light priority-element priority-high text-adaptive">
          28 autonomous agents working in perfect coordination through advanced swarm intelligence protocols.
        </div>

        <div className="grid-responsive">
          {/* Agent Overview */}
          <div className="theme-card theme-spacing-responsive">
            <h3 className="heading-5 text-adaptive">Active Agent Categories</h3>
            <div className="space-y-4 mt-6">
              <div className="flex justify-between items-center">
                <span className="body-base text-adaptive">Intelligence Agents (8)</span>
                <span className="caption text-nova-400">ACTIVE</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="body-base text-adaptive">Creative Agents (7)</span>
                <span className="caption text-nova-400">ACTIVE</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="body-base text-adaptive">Coordination Agents (6)</span>
                <span className="caption text-nova-400">ACTIVE</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="body-base text-adaptive">Specialized Agents (7)</span>
                <span className="caption text-nova-400">ACTIVE</span>
              </div>
              <div className="mt-4 pt-4 border-t border-space-600">
                <div className="flex justify-between items-center font-bold">
                  <span className="body-base text-adaptive">Total Active Agents</span>
                  <span className="caption text-cosmic-400">28/28</span>
                </div>
              </div>
            </div>
          </div>

          {/* Swarm Intelligence */}
          <div className="theme-card theme-spacing-responsive">
            <span className="heading-3 mb-responsive-xs block">🧠</span>
            <h3 className="heading-5 mb-responsive-xs text-adaptive">Swarm Intelligence</h3>
            <div className="body-base text-adaptive mb-responsive-sm">
              Collective intelligence system where agents collaborate and learn from each other.
            </div>
            <div className="body-small priority-element priority-low">
              <p>• Cross-agent communication protocols</p>
              <p>• Shared knowledge base synchronization</p>
              <p>• Collaborative problem-solving mechanisms</p>
              <p>• Adaptive learning from user interactions</p>
            </div>
          </div>

          {/* Coordination Center */}
          <div className="theme-card theme-spacing-responsive">
            <span className="heading-3 mb-responsive-xs block">⚡</span>
            <h3 className="heading-5 mb-responsive-xs text-adaptive">Coordination Center</h3>
            <div className="body-base text-adaptive mb-responsive-sm">
              Central hub for managing agent tasks, priorities, and resource allocation.
            </div>
            <div className="body-small priority-element priority-low">
              <div>Queue Management: 28 agents coordinated</div>
              <div>Resource Allocation: Optimal distribution</div>
              <div>Inter-agent Communication: Real-time mesh network</div>
              <div>Performance Monitoring: All 28 agents operational</div>
              <div>Swarm Efficiency: 97.3% coordination success</div>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="theme-card theme-spacing-responsive mt-responsive-lg">
          <div className="text-center">
            <span className="heading-4 mb-responsive-sm block">💫</span>
            <h3 className="heading-5 mb-responsive-sm text-adaptive priority-element priority-high">
              Swarm System Status
            </h3>
            <div className="heading-3 font-bold text-nova-400 mb-responsive-xs">OPTIMAL</div>
            <div className="body-base text-adaptive">
              All agents are operational and collaborating effectively to support your creative workflow.
            </div>
          </div>
        </div>
      </Container>
    </div>
  );
} 