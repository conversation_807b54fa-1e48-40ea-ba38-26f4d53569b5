"use client";

import React from 'react';
import { useState } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";
import Container from "@/shared/components/Container";
import Button from "@/shared/components/Button";
import ThemeToggle from "@/shared/components/ThemeToggle";

interface NavigationLink {
  href: string;
  label: string;
  description: string;
  isPreview?: boolean;
}

interface HeaderProps {
  className?: string;
  children?: React.ReactNode;
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { user, signOut } = useAuth();

  // Strategic 4-hub navigation structure
  const getNavigationLinks = (): NavigationLink[] => {
    return [
      { href: "/", label: "Home", description: "Platform overview" },
      { href: "/intelligence", label: "Intelligence", description: "AI Analytics & Real-time Intelligence" },
      { href: "/agents", label: "Agents", description: "28 Autonomous AI Agents" },
      { href: "/creative", label: "Creative", description: "AI Canvas & Creative Tools" },
      { href: "/dashboard", label: "Dashboard", description: "Workspace & System Control" }
    ];
  };

  const navigationLinks = getNavigationLinks();

  return (
    <header className="fixed top-0 left-0 right-0 smart-glass-elevated backdrop-blur-xl border-b border-white/10 z-50 performance-layer">
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex items-center justify-between h-16 adaptive-spacing">
          {/* Logo with Smart Priority */}
          <Link 
            href="/" 
            className="flex items-center space-x-3 micro-feedback priority-element priority-critical"
          >
            <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-cosmic-400 to-nova-500 flex items-center justify-center gpu-accelerated">
              <span className="text-white font-bold text-sm">C</span>
            </div>
            <span className="text-xl font-bold text-gradient-cosmic text-adaptive">
              CreAItive
            </span>
          </Link>

          {/* Desktop Navigation with Context-Aware Density */}
          <nav className="hidden md:flex nav-contextual interface-standard">
            <Link 
              href="/canvas" 
              className="nav-item-smart priority-element priority-high text-adaptive"
            >
              🎨 Canvas
            </Link>
            <Link 
              href="/gallery" 
              className="nav-item-smart priority-element priority-medium text-adaptive"
            >
              🖼️ Gallery
            </Link>
            <Link 
              href="/ai-tools" 
              className="nav-item-smart priority-element priority-medium text-adaptive"
            >
              🤖 AI Tools
            </Link>
            <Link 
              href="/community" 
              className="nav-item-smart priority-element priority-medium text-adaptive"
            >
              👥 Community
            </Link>
            <Link 
              href="/marketplace" 
              className="nav-item-smart priority-element priority-medium text-adaptive"
            >
              🏪 Marketplace
            </Link>
          </nav>

          {/* Action Buttons with Smart Priority */}
          <div className="hidden md:flex items-center adaptive-gap">
            <Link 
              href="/login" 
              className="btn-smart priority-element priority-medium px-4 py-2"
            >
              Sign In
            </Link>
            <Link 
              href="/register" 
              className="btn-smart priority-element priority-high px-4 py-2 bg-gradient-to-r from-cosmic-500 to-nova-500 text-white"
            >
              Get Started
            </Link>
          </div>

          {/* Mobile Menu Button with Micro-Feedback */}
          <button
            className="md:hidden micro-feedback p-2 rounded-lg smart-glass priority-element priority-high"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            aria-label="Toggle menu"
          >
            <svg 
              className="w-6 h-6 text-stardust-light transition-transform duration-200" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
              style={{ transform: isMenuOpen ? 'rotate(90deg)' : 'rotate(0deg)' }}
            >
              {isMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>
      </div>

      {/* Mobile Menu with 4 Strategic Hubs */}
      <div className={`md:hidden progressive-container ${isMenuOpen ? 'progressive-expanded' : 'progressive-collapsed'} interface-simple`}>
        <div className="smart-glass border-t border-white/10 px-6 py-4 performance-layer">
          <nav className="space-y-4">
            <Link
              href="/intelligence"
              className="block nav-item-smart priority-element priority-high text-adaptive"
              onClick={() => setIsMenuOpen(false)}
            >
              🧠 Intelligence Hub
            </Link>
            <Link
              href="/agents"
              className="block nav-item-smart priority-element priority-high text-adaptive"
              onClick={() => setIsMenuOpen(false)}
            >
              🤖 Agent Hub
            </Link>
            <Link
              href="/creative"
              className="block nav-item-smart priority-element priority-high text-adaptive"
              onClick={() => setIsMenuOpen(false)}
            >
              🎨 Creative Hub
            </Link>
            <Link
              href="/dashboard"
              className="block nav-item-smart priority-element priority-high text-adaptive"
              onClick={() => setIsMenuOpen(false)}
            >
              📊 Dashboard Hub
            </Link>
            <Link 
              href="/community" 
              className="block nav-item-smart priority-element priority-medium text-adaptive"
              onClick={() => setIsMenuOpen(false)}
            >
              👥 Community
            </Link>
            <Link 
              href="/marketplace" 
              className="block nav-item-smart priority-element priority-medium text-adaptive"
              onClick={() => setIsMenuOpen(false)}
            >
              🏪 Marketplace
            </Link>
            
            {/* Mobile Action Buttons */}
            <div className="pt-4 space-y-3 border-t border-white/10">
              <Link 
                href="/login" 
                className="block btn-smart priority-element priority-medium text-center"
                onClick={() => setIsMenuOpen(false)}
              >
                Sign In
              </Link>
              <Link 
                href="/register" 
                className="block btn-smart priority-element priority-high text-center bg-gradient-to-r from-cosmic-500 to-nova-500 text-white"
                onClick={() => setIsMenuOpen(false)}
              >
                Get Started
              </Link>
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
}

Header.displayName = 'Header';