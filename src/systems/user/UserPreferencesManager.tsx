"use client";

import { useState, useEffect } from 'react';
import { useAuthContext } from '@/contexts/AuthContext';

/**
 * 👤 USER PREFERENCES MANAGER
 * 
 * Manages user-specific preferences including:
 * - Theme preferences and saved themes
 * - UI settings and customizations
 * - Accessibility preferences
 * - Sync across devices
 */

interface UserThemePreference {
  id: string;
  name: string;
  schema: any;
  isDefault: boolean;
  createdAt: string;
  lastUsed?: string;
}

interface UserPreferences {
  themes: UserThemePreference[];
  defaultThemeId?: string;
  accessibility: {
    highContrast: boolean;
    reducedMotion: boolean;
    largeText: boolean;
    colorBlindFriendly: boolean;
  };
  ui: {
    compactMode: boolean;
    showAnimations: boolean;
    autoSaveThemes: boolean;
    syncAcrossDevices: boolean;
  };
  lastUpdated: string;
}

export class UserPreferencesManager {
  private static instance: UserPreferencesManager;
  private preferences: UserPreferences;
  private userId: string | null = null;

  static getInstance(): UserPreferencesManager {
    if (!UserPreferencesManager.instance) {
      UserPreferencesManager.instance = new UserPreferencesManager();
    }
    return UserPreferencesManager.instance;
  }

  constructor() {
    this.preferences = this.getDefaultPreferences();
  }

  // DEFAULT PREFERENCES
  private getDefaultPreferences(): UserPreferences {
    return {
      themes: [],
      accessibility: {
        highContrast: false,
        reducedMotion: false,
        largeText: false,
        colorBlindFriendly: false
      },
      ui: {
        compactMode: false,
        showAnimations: true,
        autoSaveThemes: true,
        syncAcrossDevices: true
      },
      lastUpdated: new Date().toISOString()
    };
  }

  // SET USER ID
  setUserId(userId: string | null) {
    this.userId = userId;
    if (userId) {
      this.loadUserPreferences();
    } else {
      this.preferences = this.getDefaultPreferences();
    }
  }

  // LOAD USER PREFERENCES
  private async loadUserPreferences() {
    if (!this.userId) return;

    try {
      // Try to load from localStorage first (offline support)
      const localPrefs = localStorage.getItem(`user-preferences-${this.userId}`);
      if (localPrefs) {
        this.preferences = { ...this.getDefaultPreferences(), ...JSON.parse(localPrefs) };
      }

      // TODO: Load from backend API
      // const response = await fetch(`/api/users/${this.userId}/preferences`);
      // if (response.ok) {
      //   const serverPrefs = await response.json();
      //   this.preferences = { ...this.preferences, ...serverPrefs };
      //   this.saveToLocalStorage();
      // }
    } catch (error) {
      console.warn('Failed to load user preferences:', error);
    }
  }

  // SAVE USER PREFERENCES
  private async saveUserPreferences() {
    if (!this.userId) return;

    this.preferences.lastUpdated = new Date().toISOString();

    try {
      // Save to localStorage immediately
      this.saveToLocalStorage();

      // TODO: Save to backend API
      // await fetch(`/api/users/${this.userId}/preferences`, {
      //   method: 'PUT',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(this.preferences)
      // });
    } catch (error) {
      console.warn('Failed to save user preferences:', error);
    }
  }

  // SAVE TO LOCAL STORAGE
  private saveToLocalStorage() {
    if (!this.userId) return;
    localStorage.setItem(`user-preferences-${this.userId}`, JSON.stringify(this.preferences));
  }

  // THEME MANAGEMENT
  async saveTheme(theme: Omit<UserThemePreference, 'id' | 'createdAt'>) {
    if (!this.userId) return null;

    const newTheme: UserThemePreference = {
      ...theme,
      id: `theme-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString()
    };

    // If this is the first theme or marked as default, make it default
    if (this.preferences.themes.length === 0 || theme.isDefault) {
      this.preferences.themes.forEach(t => t.isDefault = false);
      newTheme.isDefault = true;
      this.preferences.defaultThemeId = newTheme.id;
    }

    this.preferences.themes.push(newTheme);
    await this.saveUserPreferences();
    
    return newTheme.id;
  }

  async deleteTheme(themeId: string) {
    if (!this.userId) return;

    const themeIndex = this.preferences.themes.findIndex(t => t.id === themeId);
    if (themeIndex === -1) return;

    const wasDefault = this.preferences.themes[themeIndex].isDefault;
    this.preferences.themes.splice(themeIndex, 1);

    // If we deleted the default theme, make the first remaining theme default
    if (wasDefault && this.preferences.themes.length > 0) {
      this.preferences.themes[0].isDefault = true;
      this.preferences.defaultThemeId = this.preferences.themes[0].id;
    } else if (this.preferences.themes.length === 0) {
      this.preferences.defaultThemeId = undefined;
    }

    await this.saveUserPreferences();
  }

  async setDefaultTheme(themeId: string) {
    if (!this.userId) return;

    this.preferences.themes.forEach(theme => {
      theme.isDefault = theme.id === themeId;
      if (theme.id === themeId) {
        theme.lastUsed = new Date().toISOString();
      }
    });

    this.preferences.defaultThemeId = themeId;
    await this.saveUserPreferences();
  }

  async updateThemeUsage(themeId: string) {
    if (!this.userId) return;

    const theme = this.preferences.themes.find(t => t.id === themeId);
    if (theme) {
      theme.lastUsed = new Date().toISOString();
      await this.saveUserPreferences();
    }
  }

  // ACCESSIBILITY PREFERENCES
  async updateAccessibilityPreferences(accessibility: Partial<UserPreferences['accessibility']>) {
    if (!this.userId) return;

    this.preferences.accessibility = { ...this.preferences.accessibility, ...accessibility };
    await this.saveUserPreferences();
    
    // Apply accessibility settings immediately
    this.applyAccessibilitySettings();
  }

  private applyAccessibilitySettings() {
    const { accessibility } = this.preferences;
    const root = document.documentElement;

    // High contrast
    root.classList.toggle('high-contrast', accessibility.highContrast);
    
    // Reduced motion
    root.classList.toggle('reduced-motion', accessibility.reducedMotion);
    
    // Large text
    root.classList.toggle('large-text', accessibility.largeText);
    
    // Color blind friendly
    root.classList.toggle('colorblind-friendly', accessibility.colorBlindFriendly);
  }

  // UI PREFERENCES
  async updateUIPreferences(ui: Partial<UserPreferences['ui']>) {
    if (!this.userId) return;

    this.preferences.ui = { ...this.preferences.ui, ...ui };
    await this.saveUserPreferences();
  }

  // GETTERS
  getThemes(): UserThemePreference[] {
    return this.preferences.themes;
  }

  getDefaultTheme(): UserThemePreference | null {
    return this.preferences.themes.find(t => t.isDefault) || null;
  }

  getAccessibilityPreferences() {
    return this.preferences.accessibility;
  }

  getUIPreferences() {
    return this.preferences.ui;
  }

  getAllPreferences(): UserPreferences {
    return this.preferences;
  }

  // EXPORT/IMPORT
  exportPreferences(): string {
    return JSON.stringify({
      ...this.preferences,
      exportedAt: new Date().toISOString(),
      exportedBy: this.userId
    }, null, 2);
  }

  async importPreferences(preferencesData: string) {
    if (!this.userId) return;

    try {
      const imported = JSON.parse(preferencesData);
      
      // Validate the imported data
      if (imported.themes && Array.isArray(imported.themes)) {
        // Merge themes (avoid duplicates by name)
        const existingNames = new Set(this.preferences.themes.map(t => t.name));
        const newThemes = imported.themes.filter((t: any) => !existingNames.has(t.name));
        
        this.preferences.themes.push(...newThemes.map((t: any) => ({
          ...t,
          id: `theme-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          isDefault: false
        })));
      }

      if (imported.accessibility) {
        this.preferences.accessibility = { ...this.preferences.accessibility, ...imported.accessibility };
      }

      if (imported.ui) {
        this.preferences.ui = { ...this.preferences.ui, ...imported.ui };
      }

      await this.saveUserPreferences();
      this.applyAccessibilitySettings();
      
      return true;
    } catch (error) {
      console.error('Failed to import preferences:', error);
      return false;
    }
  }

  // SYNC STATUS
  getLastSyncTime(): string | null {
    return this.preferences.lastUpdated;
  }

  async forceSyncWithServer() {
    if (!this.userId) return;

    try {
      // TODO: Implement server sync
      // const response = await fetch(`/api/users/${this.userId}/preferences/sync`, {
      //   method: 'POST',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(this.preferences)
      // });
      
      console.log('Sync with server completed');
    } catch (error) {
      console.error('Failed to sync with server:', error);
    }
  }
}

// REACT HOOK FOR USER PREFERENCES
export const useUserPreferences = () => {
  const { user } = useAuthContext();
  const [manager] = useState(() => UserPreferencesManager.getInstance());
  const [preferences, setPreferences] = useState(manager.getAllPreferences());

  // Update manager when user changes
  useEffect(() => {
    manager.setUserId(user?.id || null);
    setPreferences(manager.getAllPreferences());
  }, [user, manager]);

  // Refresh preferences periodically
  useEffect(() => {
    const interval = setInterval(() => {
      setPreferences(manager.getAllPreferences());
    }, 5000);

    return () => clearInterval(interval);
  }, [manager]);

  const saveTheme = async (theme: Omit<UserThemePreference, 'id' | 'createdAt'>) => {
    const themeId = await manager.saveTheme(theme);
    setPreferences(manager.getAllPreferences());
    return themeId;
  };

  const deleteTheme = async (themeId: string) => {
    await manager.deleteTheme(themeId);
    setPreferences(manager.getAllPreferences());
  };

  const setDefaultTheme = async (themeId: string) => {
    await manager.setDefaultTheme(themeId);
    setPreferences(manager.getAllPreferences());
  };

  const updateAccessibility = async (accessibility: Partial<UserPreferences['accessibility']>) => {
    await manager.updateAccessibilityPreferences(accessibility);
    setPreferences(manager.getAllPreferences());
  };

  const updateUI = async (ui: Partial<UserPreferences['ui']>) => {
    await manager.updateUIPreferences(ui);
    setPreferences(manager.getAllPreferences());
  };

  const exportPreferences = () => {
    return manager.exportPreferences();
  };

  const importPreferences = async (data: string) => {
    const success = await manager.importPreferences(data);
    if (success) {
      setPreferences(manager.getAllPreferences());
    }
    return success;
  };

  return {
    preferences,
    themes: preferences.themes,
    defaultTheme: manager.getDefaultTheme(),
    accessibility: preferences.accessibility,
    ui: preferences.ui,
    saveTheme,
    deleteTheme,
    setDefaultTheme,
    updateAccessibility,
    updateUI,
    exportPreferences,
    importPreferences,
    lastSync: manager.getLastSyncTime(),
    isLoggedIn: !!user
  };
};

export default UserPreferencesManager;
