"use client";

import { useState, useEffect } from 'react';

/**
 * 🧪 TEST ACCOUNT MANAGER
 * 
 * Manages test accounts and data for consistent testing:
 * - AGITEST user with persistent data
 * - TestGuest with saved preferences
 * - Pre-configured themes and settings
 * - Survives cache clears and browser resets
 */

interface TestUser {
  id: string;
  username: string;
  email: string;
  name: string;
  password?: string;
  type: 'admin' | 'guest';
  avatar: string;
  testAccount: boolean;
  preferences?: any;
  themes?: any[];
}

interface TestAccountData {
  users: Record<string, TestUser>;
  themes: Record<string, any>;
  preferences: Record<string, any>;
  sessions: Record<string, any>;
}

export class TestAccountManager {
  private static instance: TestAccountManager;
  private testData: TestAccountData;
  private isInitialized = false;

  static getInstance(): TestAccountManager {
    if (!TestAccountManager.instance) {
      TestAccountManager.instance = new TestAccountManager();
    }
    return TestAccountManager.instance;
  }

  constructor() {
    this.testData = {
      users: {},
      themes: {},
      preferences: {},
      sessions: {}
    };
    this.initializeTestData();
  }

  // INITIALIZE TEST DATA
  private async initializeTestData() {
    if (this.isInitialized) return;

    try {
      // Load test data from our test files
      await this.loadTestUsers();
      await this.loadTestThemes();
      await this.loadTestPreferences();
      
      this.isInitialized = true;
      console.log('🧪 Test Account Manager initialized');
    } catch (error) {
      console.warn('Failed to initialize test data:', error);
      this.createDefaultTestData();
    }
  }

  // LOAD TEST USERS
  private async loadTestUsers() {
    // AGITEST User
    this.testData.users['test-user-001'] = {
      id: 'test-user-001',
      username: 'AGITEST',
      email: '<EMAIL>',
      name: 'AGI Test User',
      password: 'AGITEST123!',
      type: 'admin',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=AGITEST',
      testAccount: true
    };

    // Guest Test User
    this.testData.users['guest-test-001'] = {
      id: 'guest-test-001',
      username: 'TestGuest',
      email: '<EMAIL>',
      name: 'Test Guest User',
      type: 'guest',
      avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=TestGuest',
      testAccount: true
    };
  }

  // LOAD TEST THEMES
  private async loadTestThemes() {
    this.testData.themes = {
      'test-theme-001': {
        id: 'test-theme-001',
        name: 'AGITEST Default',
        userId: 'test-user-001',
        schema: {
          name: 'AGITEST Default',
          description: 'Default theme for AGITEST user',
          colors: {
            cosmic: '#6E7AFF',
            nova: '#00D4AA',
            quantum: '#FFB800',
            neural: '#B794F6',
            aura: '#F093FB',
            stardust: '#94A3B8',
            space: '#0C0522'
          },
          gradients: {
            primary: 'linear-gradient(135deg, #6E7AFF 0%, #00D4AA 100%)',
            secondary: 'linear-gradient(135deg, #B794F6 0%, #F093FB 100%)',
            accent: 'linear-gradient(135deg, #FFB800 0%, #6E7AFF 100%)'
          },
          effects: {
            glow: true,
            animations: true,
            particles: true
          }
        },
        isDefault: true,
        createdAt: '2024-01-01T00:00:00.000Z'
      },
      'test-theme-002': {
        id: 'test-theme-002',
        name: 'AGITEST Professional',
        userId: 'test-user-001',
        schema: {
          name: 'AGITEST Professional',
          description: 'Professional blue theme for testing',
          colors: {
            cosmic: '#2563EB',
            nova: '#059669',
            quantum: '#D97706',
            neural: '#7C3AED',
            aura: '#DB2777',
            stardust: '#6B7280',
            space: '#111827'
          },
          gradients: {
            primary: 'linear-gradient(135deg, #2563EB 0%, #059669 100%)',
            secondary: 'linear-gradient(135deg, #7C3AED 0%, #DB2777 100%)',
            accent: 'linear-gradient(135deg, #D97706 0%, #2563EB 100%)'
          },
          effects: {
            glow: false,
            animations: true,
            particles: false
          }
        },
        isDefault: false,
        createdAt: '2024-01-01T01:00:00.000Z'
      },
      'guest-theme-001': {
        id: 'guest-theme-001',
        name: 'Guest Default',
        userId: 'guest-test-001',
        schema: {
          name: 'Guest Default',
          description: 'Default theme for guest testing',
          colors: {
            cosmic: '#3B82F6',
            nova: '#10B981',
            quantum: '#F59E0B',
            neural: '#8B5CF6',
            aura: '#EC4899',
            stardust: '#6B7280',
            space: '#1F2937'
          },
          gradients: {
            primary: 'linear-gradient(135deg, #3B82F6 0%, #10B981 100%)',
            secondary: 'linear-gradient(135deg, #8B5CF6 0%, #EC4899 100%)',
            accent: 'linear-gradient(135deg, #F59E0B 0%, #3B82F6 100%)'
          },
          effects: {
            glow: true,
            animations: true,
            particles: false
          }
        },
        isDefault: true,
        createdAt: '2024-01-01T00:00:00.000Z'
      }
    };
  }

  // LOAD TEST PREFERENCES
  private async loadTestPreferences() {
    // AGITEST preferences
    this.testData.preferences['test-user-001'] = {
      themes: Object.values(this.testData.themes).filter(t => t.userId === 'test-user-001'),
      defaultThemeId: 'test-theme-001',
      accessibility: {
        highContrast: false,
        reducedMotion: false,
        largeText: false,
        colorBlindFriendly: false
      },
      ui: {
        compactMode: false,
        showAnimations: true,
        autoSaveThemes: true,
        syncAcrossDevices: true
      },
      lastUpdated: '2024-01-01T00:00:00.000Z'
    };

    // Guest preferences
    this.testData.preferences['guest-test-001'] = {
      themes: Object.values(this.testData.themes).filter(t => t.userId === 'guest-test-001'),
      defaultThemeId: 'guest-theme-001',
      accessibility: {
        highContrast: false,
        reducedMotion: false,
        largeText: false,
        colorBlindFriendly: false
      },
      ui: {
        compactMode: false,
        showAnimations: true,
        autoSaveThemes: true,
        syncAcrossDevices: false
      },
      lastUpdated: '2024-01-01T00:00:00.000Z'
    };
  }

  // CREATE DEFAULT TEST DATA
  private createDefaultTestData() {
    console.log('🧪 Creating default test data');
    this.loadTestUsers();
    this.loadTestThemes();
    this.loadTestPreferences();
    this.isInitialized = true;
  }

  // AUTHENTICATE TEST USER
  authenticateUser(username: string, password?: string): TestUser | null {
    const user = Object.values(this.testData.users).find(u => 
      u.username === username || u.email === username
    );

    if (!user) return null;

    // For AGITEST, check password
    if (user.username === 'AGITEST') {
      if (password !== 'AGITEST123!') return null;
    }

    // For guest, no password required
    if (user.type === 'guest') {
      return user;
    }

    return user;
  }

  // GET TEST USER BY ID
  getTestUser(userId: string): TestUser | null {
    return this.testData.users[userId] || null;
  }

  // GET USER PREFERENCES
  getUserPreferences(userId: string): any {
    return this.testData.preferences[userId] || null;
  }

  // GET USER THEMES
  getUserThemes(userId: string): any[] {
    return Object.values(this.testData.themes).filter(theme => theme.userId === userId);
  }

  // SAVE USER PREFERENCES (for testing)
  saveUserPreferences(userId: string, preferences: any): void {
    this.testData.preferences[userId] = {
      ...this.testData.preferences[userId],
      ...preferences,
      lastUpdated: new Date().toISOString()
    };

    // Also save to localStorage for persistence
    localStorage.setItem(`test-preferences-${userId}`, JSON.stringify(this.testData.preferences[userId]));
    console.log(`🧪 Saved test preferences for ${userId}`);
  }

  // SAVE USER THEME (for testing)
  saveUserTheme(userId: string, theme: any): string {
    const themeId = `test-theme-${Date.now()}`;
    const newTheme = {
      ...theme,
      id: themeId,
      userId,
      createdAt: new Date().toISOString()
    };

    this.testData.themes[themeId] = newTheme;

    // Update user preferences
    if (!this.testData.preferences[userId]) {
      this.testData.preferences[userId] = { themes: [] };
    }
    
    this.testData.preferences[userId].themes = this.testData.preferences[userId].themes || [];
    this.testData.preferences[userId].themes.push(newTheme);

    // Save to localStorage
    localStorage.setItem(`test-themes-${userId}`, JSON.stringify(this.getUserThemes(userId)));
    console.log(`🧪 Saved test theme ${themeId} for ${userId}`);

    return themeId;
  }

  // LOAD PERSISTED TEST DATA
  loadPersistedTestData(userId: string): void {
    try {
      // Load preferences
      const savedPrefs = localStorage.getItem(`test-preferences-${userId}`);
      if (savedPrefs) {
        this.testData.preferences[userId] = JSON.parse(savedPrefs);
      }

      // Load themes
      const savedThemes = localStorage.getItem(`test-themes-${userId}`);
      if (savedThemes) {
        const themes = JSON.parse(savedThemes);
        themes.forEach((theme: any) => {
          this.testData.themes[theme.id] = theme;
        });
      }

      console.log(`🧪 Loaded persisted test data for ${userId}`);
    } catch (error) {
      console.warn('Failed to load persisted test data:', error);
    }
  }

  // RESET TEST DATA
  resetTestData(): void {
    // Clear localStorage
    Object.keys(this.testData.users).forEach(userId => {
      localStorage.removeItem(`test-preferences-${userId}`);
      localStorage.removeItem(`test-themes-${userId}`);
    });

    // Reset in-memory data
    this.isInitialized = false;
    this.initializeTestData();
    
    console.log('🧪 Test data reset');
  }

  // GET ALL TEST USERS
  getAllTestUsers(): TestUser[] {
    return Object.values(this.testData.users);
  }

  // CHECK IF USER IS TEST ACCOUNT
  isTestAccount(userId: string): boolean {
    const user = this.testData.users[userId];
    return user?.testAccount || false;
  }
}

// REACT HOOK FOR TEST ACCOUNTS
export const useTestAccounts = () => {
  const [manager] = useState(() => TestAccountManager.getInstance());
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const init = async () => {
      await manager['initializeTestData']();
      setIsReady(true);
    };
    init();
  }, [manager]);

  const loginAsAGITEST = () => {
    return manager.authenticateUser('AGITEST', 'AGITEST123!');
  };

  const loginAsGuest = () => {
    return manager.authenticateUser('TestGuest');
  };

  const getTestUserPreferences = (userId: string) => {
    return manager.getUserPreferences(userId);
  };

  const saveTestPreferences = (userId: string, preferences: any) => {
    manager.saveUserPreferences(userId, preferences);
  };

  const resetAllTestData = () => {
    manager.resetTestData();
  };

  return {
    isReady,
    loginAsAGITEST,
    loginAsGuest,
    getTestUserPreferences,
    saveTestPreferences,
    resetAllTestData,
    manager
  };
};

export default TestAccountManager;
