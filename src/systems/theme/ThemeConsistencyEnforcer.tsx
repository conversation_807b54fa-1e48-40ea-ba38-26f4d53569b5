"use client";

import { useEffect, useState } from 'react';

/**
 * 🎨 THEME CONSISTENCY ENFORCER
 * 
 * Automatically enforces:
 * - Theme color compliance
 * - Component pattern consistency
 * - CSS/Tailwind standardization
 * - Design system adherence
 */

interface ThemeRule {
  name: string;
  pattern: RegExp;
  replacement: string;
  severity: 'error' | 'warning' | 'info';
  description: string;
}

interface ThemeViolation {
  rule: string;
  element: Element;
  currentValue: string;
  suggestedValue: string;
  severity: 'error' | 'warning' | 'info';
  autoFixable: boolean;
}

export class ThemeConsistencyEnforcer {
  private static instance: ThemeConsistencyEnforcer;
  private rules: ThemeRule[] = [];
  private violations: ThemeViolation[] = [];

  static getInstance(): ThemeConsistencyEnforcer {
    if (!ThemeConsistencyEnforcer.instance) {
      ThemeConsistencyEnforcer.instance = new ThemeConsistencyEnforcer();
    }
    return ThemeConsistencyEnforcer.instance;
  }

  constructor() {
    this.initializeRules();
  }

  // INITIALIZE THEME RULES
  private initializeRules() {
    this.rules = [
      // COLOR STANDARDIZATION RULES
      {
        name: 'Standard Blue Colors',
        pattern: /\b(text-blue-|bg-blue-|border-blue-)\d+/g,
        replacement: 'cosmic-',
        severity: 'error',
        description: 'Replace standard blue colors with cosmic- theme colors'
      },
      {
        name: 'Standard Green Colors',
        pattern: /\b(text-green-|bg-green-|border-green-)\d+/g,
        replacement: 'nova-',
        severity: 'error',
        description: 'Replace standard green colors with nova- theme colors'
      },
      {
        name: 'Standard Yellow Colors',
        pattern: /\b(text-yellow-|bg-yellow-|border-yellow-)\d+/g,
        replacement: 'quantum-',
        severity: 'error',
        description: 'Replace standard yellow colors with quantum- theme colors'
      },
      {
        name: 'Standard Purple Colors',
        pattern: /\b(text-purple-|bg-purple-|border-purple-)\d+/g,
        replacement: 'neural-',
        severity: 'error',
        description: 'Replace standard purple colors with neural- theme colors'
      },
      {
        name: 'Deprecated Nebula Colors',
        pattern: /\b(text-nebula-|bg-nebula-|border-nebula-)/g,
        replacement: 'neural-',
        severity: 'error',
        description: 'Replace deprecated nebula- colors with neural- theme colors'
      },

      // COMPONENT PATTERN RULES
      {
        name: 'Deprecated Neo Buttons',
        pattern: /\bneo-button\b/g,
        replacement: 'bg-cosmic-500 hover:bg-cosmic-600 text-white rounded-lg font-medium transition-colors',
        severity: 'warning',
        description: 'Replace neo-button with modern button styling'
      },
      {
        name: 'Legacy Card Classes',
        pattern: /\bold-card\b/g,
        replacement: 'theme-card theme-spacing-responsive',
        severity: 'warning',
        description: 'Replace old-card with modern theme-card pattern'
      },

      // SPACING CONSISTENCY RULES
      {
        name: 'Inconsistent Padding',
        pattern: /\bp-\d+\b/g,
        replacement: 'theme-spacing-responsive',
        severity: 'info',
        description: 'Use theme-spacing-responsive for consistent padding'
      },
      {
        name: 'Inconsistent Margins',
        pattern: /\bm-\d+\b/g,
        replacement: 'theme-spacing-responsive',
        severity: 'info',
        description: 'Use theme-spacing-responsive for consistent margins'
      },

      // TYPOGRAPHY RULES
      {
        name: 'Inconsistent Text Sizes',
        pattern: /\btext-(xs|sm|base|lg|xl|2xl|3xl|4xl|5xl|6xl)\b/g,
        replacement: 'text-responsive-',
        severity: 'info',
        description: 'Use text-responsive- classes for consistent typography'
      }
    ];
  }

  // SCAN FOR VIOLATIONS
  scanForViolations(): ThemeViolation[] {
    this.violations = [];

    // Check if we're in browser environment
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return this.violations;
    }

    const elements = document.querySelectorAll('*');

    elements.forEach(element => {
      const className = element.className;
      if (typeof className === 'string') {
        this.rules.forEach(rule => {
          const matches = className.match(rule.pattern);
          if (matches) {
            matches.forEach(match => {
              this.violations.push({
                rule: rule.name,
                element,
                currentValue: match,
                suggestedValue: this.generateSuggestion(match, rule),
                severity: rule.severity,
                autoFixable: true
              });
            });
          }
        });
      }
    });

    return this.violations;
  }

  // GENERATE SUGGESTION
  private generateSuggestion(match: string, rule: ThemeRule): string {
    if (rule.name.includes('Blue')) {
      return match.replace(/blue-\d+/, 'cosmic-500');
    }
    if (rule.name.includes('Green')) {
      return match.replace(/green-\d+/, 'nova-500');
    }
    if (rule.name.includes('Yellow')) {
      return match.replace(/yellow-\d+/, 'quantum-500');
    }
    if (rule.name.includes('Purple')) {
      return match.replace(/purple-\d+/, 'neural-500');
    }
    if (rule.name.includes('Nebula')) {
      return match.replace(/nebula-/, 'neural-');
    }
    return rule.replacement;
  }

  // AUTO-FIX VIOLATIONS
  autoFixViolations(violationsToFix?: ThemeViolation[]): number {
    const violations = violationsToFix || this.violations.filter(v => v.autoFixable);
    let fixedCount = 0;

    violations.forEach(violation => {
      const element = violation.element;
      const currentClassName = element.className;
      
      if (typeof currentClassName === 'string') {
        const newClassName = currentClassName.replace(
          violation.currentValue,
          violation.suggestedValue
        );
        
        if (newClassName !== currentClassName) {
          element.className = newClassName;
          fixedCount++;
        }
      }
    });

    // Re-scan after fixes
    this.scanForViolations();
    
    return fixedCount;
  }

  // GET THEME COMPLIANCE SCORE
  getThemeComplianceScore(): number {
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return 100;
    }

    const totalElements = document.querySelectorAll('*').length;
    const violationCount = this.violations.length;
    
    if (totalElements === 0) return 100;
    
    const complianceRate = Math.max(0, (totalElements - violationCount) / totalElements);
    return Math.round(complianceRate * 100);
  }

  // GET VIOLATIONS BY SEVERITY
  getViolationsBySeverity() {
    return {
      errors: this.violations.filter(v => v.severity === 'error'),
      warnings: this.violations.filter(v => v.severity === 'warning'),
      info: this.violations.filter(v => v.severity === 'info')
    };
  }

  // GENERATE THEME REPORT
  generateThemeReport() {
    const violations = this.scanForViolations();
    const complianceScore = this.getThemeComplianceScore();
    const violationsBySeverity = this.getViolationsBySeverity();

    return {
      complianceScore,
      totalViolations: violations.length,
      autoFixableViolations: violations.filter(v => v.autoFixable).length,
      violationsBySeverity,
      topViolations: this.getTopViolations(),
      recommendations: this.generateRecommendations()
    };
  }

  // GET TOP VIOLATIONS
  private getTopViolations() {
    const violationCounts: Record<string, number> = {};
    
    this.violations.forEach(violation => {
      violationCounts[violation.rule] = (violationCounts[violation.rule] || 0) + 1;
    });

    return Object.entries(violationCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5)
      .map(([rule, count]) => ({ rule, count }));
  }

  // GENERATE RECOMMENDATIONS
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];
    const violationsBySeverity = this.getViolationsBySeverity();

    if (violationsBySeverity.errors.length > 0) {
      recommendations.push(`Fix ${violationsBySeverity.errors.length} critical theme violations`);
    }

    if (violationsBySeverity.warnings.length > 0) {
      recommendations.push(`Update ${violationsBySeverity.warnings.length} deprecated component patterns`);
    }

    if (this.getThemeComplianceScore() < 90) {
      recommendations.push('Improve theme compliance to 90%+ for better consistency');
    }

    const autoFixable = this.violations.filter(v => v.autoFixable).length;
    if (autoFixable > 0) {
      recommendations.push(`${autoFixable} violations can be auto-fixed`);
    }

    return recommendations;
  }

  // VALIDATE NEW COMPONENT
  validateComponent(componentHTML: string): ThemeViolation[] {
    if (typeof window === 'undefined' || typeof document === 'undefined') {
      return [];
    }

    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = componentHTML;
    
    const violations: ThemeViolation[] = [];
    const elements = tempDiv.querySelectorAll('*');

    elements.forEach(element => {
      const className = element.className;
      if (typeof className === 'string') {
        this.rules.forEach(rule => {
          const matches = className.match(rule.pattern);
          if (matches) {
            matches.forEach(match => {
              violations.push({
                rule: rule.name,
                element,
                currentValue: match,
                suggestedValue: this.generateSuggestion(match, rule),
                severity: rule.severity,
                autoFixable: true
              });
            });
          }
        });
      }
    });

    return violations;
  }

  // ADD CUSTOM RULE
  addCustomRule(rule: ThemeRule) {
    this.rules.push(rule);
  }

  // REMOVE RULE
  removeRule(ruleName: string) {
    this.rules = this.rules.filter(rule => rule.name !== ruleName);
  }
}

// REACT HOOK FOR THEME CONSISTENCY
export const useThemeConsistency = () => {
  const [enforcer] = useState(() => {
    if (typeof window === 'undefined') return null;
    return ThemeConsistencyEnforcer.getInstance();
  });

  const [report, setReport] = useState(() => {
    // Only generate report in browser environment
    if (typeof window === 'undefined' || !enforcer) {
      return {
        complianceScore: 100,
        totalViolations: 0,
        autoFixableViolations: 0,
        violationsBySeverity: { errors: [], warnings: [], info: [] },
        topViolations: [],
        recommendations: []
      };
    }
    return enforcer.generateThemeReport();
  });
  const [isScanning, setIsScanning] = useState(false);

  const scanAndUpdate = async () => {
    if (typeof window === 'undefined' || !enforcer) return;

    setIsScanning(true);
    await new Promise(resolve => setTimeout(resolve, 100)); // Allow UI update

    enforcer.scanForViolations();
    setReport(enforcer.generateThemeReport());
    setIsScanning(false);
  };

  const autoFixAll = () => {
    if (!enforcer) return 0;
    const fixedCount = enforcer.autoFixViolations();
    setReport(enforcer.generateThemeReport());
    return fixedCount;
  };

  useEffect(() => {
    // Only run in browser environment
    if (typeof window === 'undefined' || !enforcer) return;

    // Initial scan
    scanAndUpdate();

    // Set up mutation observer for dynamic changes
    const observer = new MutationObserver(() => {
      setTimeout(scanAndUpdate, 500);
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class']
    });

    return () => observer.disconnect();
  }, [enforcer]);

  return {
    report,
    isScanning,
    scanAndUpdate,
    autoFixAll,
    enforcer
  };
};

export default ThemeConsistencyEnforcer;
