"use client";

import { useState, useEffect, useCallback } from 'react';
import { useThemeConsistency } from './ThemeConsistencyEnforcer';

/**
 * 🎨 DYNAMIC THEME CONTROLLER
 * 
 * Provides complete real-time theme control:
 * - Live color scheme switching
 * - AI-powered theme generation
 * - Blueprint templates for agents/tools
 * - Absolute schema enforcement
 * - One-click theme application
 */

interface ThemeSchema {
  name: string;
  description: string;
  colors: {
    cosmic: string;    // Primary (Intelligence/Systems)
    nova: string;      // Success/Active
    quantum: string;   // Processing/Analysis  
    neural: string;    // AI/Advanced
    aura: string;      // Community/Social
    stardust: string;  // Secondary
    space: string;     // Background
  };
  gradients: {
    primary: string;
    secondary: string;
    accent: string;
  };
  effects: {
    glow: boolean;
    animations: boolean;
    particles: boolean;
  };
}

interface ThemeBlueprint {
  id: string;
  name: string;
  category: 'agent' | 'tool' | 'app' | 'web' | 'custom';
  schema: ThemeSchema;
  preview: string;
  aiGenerated?: boolean;
}

export class DynamicThemeController {
  private static instance: DynamicThemeController;
  private currentSchema: ThemeSchema;
  private blueprints: ThemeBlueprint[] = [];
  private cssVariables: Record<string, string> = {};

  static getInstance(): DynamicThemeController {
    if (!DynamicThemeController.instance) {
      DynamicThemeController.instance = new DynamicThemeController();
    }
    return DynamicThemeController.instance;
  }

  constructor() {
    this.currentSchema = this.getDefaultSchema();
    this.initializeBlueprints();
    this.loadSavedTheme();
  }

  // DEFAULT THEME SCHEMA
  private getDefaultSchema(): ThemeSchema {
    return {
      name: 'CreAItive Default',
      description: 'The original AI platform theme',
      colors: {
        cosmic: '#6E7AFF',    // Blue - Intelligence
        nova: '#00D4AA',      // Green - Success
        quantum: '#FFB800',   // Yellow - Processing
        neural: '#B794F6',    // Purple - AI
        aura: '#F093FB',      // Pink - Community
        stardust: '#94A3B8',  // Gray - Secondary
        space: '#0C0522'      // Dark - Background
      },
      gradients: {
        primary: 'linear-gradient(135deg, #6E7AFF 0%, #00D4AA 100%)',
        secondary: 'linear-gradient(135deg, #B794F6 0%, #F093FB 100%)',
        accent: 'linear-gradient(135deg, #FFB800 0%, #6E7AFF 100%)'
      },
      effects: {
        glow: true,
        animations: true,
        particles: true
      }
    };
  }

  // INITIALIZE THEME BLUEPRINTS
  private initializeBlueprints() {
    this.blueprints = [
      {
        id: 'ai-agent-blue',
        name: 'AI Agent Blueprint',
        category: 'agent',
        schema: {
          name: 'AI Agent Blue',
          description: 'Professional blue theme for AI agents',
          colors: {
            cosmic: '#2563EB',    // Deep Blue
            nova: '#059669',      // Emerald
            quantum: '#D97706',   // Amber
            neural: '#7C3AED',    // Violet
            aura: '#DB2777',      // Pink
            stardust: '#6B7280',  // Gray
            space: '#111827'      // Dark Blue
          },
          gradients: {
            primary: 'linear-gradient(135deg, #2563EB 0%, #059669 100%)',
            secondary: 'linear-gradient(135deg, #7C3AED 0%, #DB2777 100%)',
            accent: 'linear-gradient(135deg, #D97706 0%, #2563EB 100%)'
          },
          effects: { glow: true, animations: true, particles: false }
        },
        preview: 'bg-gradient-to-r from-blue-600 to-emerald-600'
      },
      {
        id: 'creative-tool-warm',
        name: 'Creative Tool Warm',
        category: 'tool',
        schema: {
          name: 'Creative Warm',
          description: 'Warm, inspiring theme for creative tools',
          colors: {
            cosmic: '#EA580C',    // Orange
            nova: '#16A34A',      // Green
            quantum: '#EAB308',   // Yellow
            neural: '#A855F7',    // Purple
            aura: '#EC4899',      // Pink
            stardust: '#78716C',  // Stone
            space: '#1C1917'      // Warm Dark
          },
          gradients: {
            primary: 'linear-gradient(135deg, #EA580C 0%, #16A34A 100%)',
            secondary: 'linear-gradient(135deg, #A855F7 0%, #EC4899 100%)',
            accent: 'linear-gradient(135deg, #EAB308 0%, #EA580C 100%)'
          },
          effects: { glow: true, animations: true, particles: true }
        },
        preview: 'bg-gradient-to-r from-orange-600 to-green-600'
      },
      {
        id: 'enterprise-minimal',
        name: 'Enterprise Minimal',
        category: 'app',
        schema: {
          name: 'Enterprise',
          description: 'Clean, professional theme for enterprise apps',
          colors: {
            cosmic: '#1E40AF',    // Blue
            nova: '#047857',      // Emerald
            quantum: '#B45309',   // Amber
            neural: '#6D28D9',    // Violet
            aura: '#BE185D',      // Pink
            stardust: '#52525B',  // Zinc
            space: '#18181B'      // Zinc Dark
          },
          gradients: {
            primary: 'linear-gradient(135deg, #1E40AF 0%, #047857 100%)',
            secondary: 'linear-gradient(135deg, #6D28D9 0%, #BE185D 100%)',
            accent: 'linear-gradient(135deg, #B45309 0%, #1E40AF 100%)'
          },
          effects: { glow: false, animations: false, particles: false }
        },
        preview: 'bg-gradient-to-r from-blue-700 to-emerald-700'
      }
    ];
  }

  // APPLY THEME SCHEMA
  applyThemeSchema(schema: ThemeSchema): void {
    this.currentSchema = schema;
    
    // Generate CSS variables
    this.cssVariables = {
      '--color-cosmic-50': this.lighten(schema.colors.cosmic, 95),
      '--color-cosmic-100': this.lighten(schema.colors.cosmic, 90),
      '--color-cosmic-200': this.lighten(schema.colors.cosmic, 80),
      '--color-cosmic-300': this.lighten(schema.colors.cosmic, 60),
      '--color-cosmic-400': this.lighten(schema.colors.cosmic, 40),
      '--color-cosmic-500': schema.colors.cosmic,
      '--color-cosmic-600': this.darken(schema.colors.cosmic, 20),
      '--color-cosmic-700': this.darken(schema.colors.cosmic, 40),
      '--color-cosmic-800': this.darken(schema.colors.cosmic, 60),
      '--color-cosmic-900': this.darken(schema.colors.cosmic, 80),
      
      // Repeat for all colors
      '--color-nova-500': schema.colors.nova,
      '--color-quantum-500': schema.colors.quantum,
      '--color-neural-500': schema.colors.neural,
      '--color-aura-500': schema.colors.aura,
      '--color-stardust-500': schema.colors.stardust,
      '--color-space-500': schema.colors.space,
      
      // Gradients
      '--gradient-primary': schema.gradients.primary,
      '--gradient-secondary': schema.gradients.secondary,
      '--gradient-accent': schema.gradients.accent
    };

    // Apply to document
    this.injectCSSVariables();
    this.applyEffects(schema.effects);
    this.saveTheme(schema);
    
    // Notify all components to re-render
    this.notifyThemeChange(schema);
  }

  // INJECT CSS VARIABLES
  private injectCSSVariables(): void {
    const root = document.documentElement;
    Object.entries(this.cssVariables).forEach(([property, value]) => {
      root.style.setProperty(property, value);
    });
  }

  // APPLY VISUAL EFFECTS
  private applyEffects(effects: ThemeSchema['effects']): void {
    const root = document.documentElement;
    
    // Glow effects
    root.style.setProperty('--enable-glow', effects.glow ? '1' : '0');
    
    // Animations
    root.style.setProperty('--enable-animations', effects.animations ? '1' : '0');
    
    // Particles
    root.style.setProperty('--enable-particles', effects.particles ? '1' : '0');
    
    // Apply classes
    if (effects.glow) {
      document.body.classList.add('theme-glow-enabled');
    } else {
      document.body.classList.remove('theme-glow-enabled');
    }
  }

  // COLOR MANIPULATION UTILITIES
  private lighten(color: string, percent: number): string {
    // Convert hex to RGB, lighten, convert back
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    const factor = percent / 100;
    const newR = Math.round(r + (255 - r) * factor);
    const newG = Math.round(g + (255 - g) * factor);
    const newB = Math.round(b + (255 - b) * factor);
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  }

  private darken(color: string, percent: number): string {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    
    const factor = 1 - (percent / 100);
    const newR = Math.round(r * factor);
    const newG = Math.round(g * factor);
    const newB = Math.round(b * factor);
    
    return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
  }

  // AI THEME GENERATION
  async generateAITheme(prompt: string): Promise<ThemeSchema> {
    // Simulate AI theme generation
    const aiColors = this.generateColorsFromPrompt(prompt);
    
    return {
      name: `AI Generated: ${prompt}`,
      description: `AI-generated theme based on: ${prompt}`,
      colors: aiColors,
      gradients: {
        primary: `linear-gradient(135deg, ${aiColors.cosmic} 0%, ${aiColors.nova} 100%)`,
        secondary: `linear-gradient(135deg, ${aiColors.neural} 0%, ${aiColors.aura} 100%)`,
        accent: `linear-gradient(135deg, ${aiColors.quantum} 0%, ${aiColors.cosmic} 100%)`
      },
      effects: {
        glow: prompt.includes('glow') || prompt.includes('bright'),
        animations: !prompt.includes('minimal') && !prompt.includes('static'),
        particles: prompt.includes('dynamic') || prompt.includes('particles')
      }
    };
  }

  private generateColorsFromPrompt(prompt: string): ThemeSchema['colors'] {
    // Simple AI color generation based on keywords
    const keywords = prompt.toLowerCase();
    
    if (keywords.includes('ocean') || keywords.includes('blue')) {
      return {
        cosmic: '#0EA5E9',
        nova: '#06B6D4',
        quantum: '#F59E0B',
        neural: '#8B5CF6',
        aura: '#EC4899',
        stardust: '#64748B',
        space: '#0F172A'
      };
    }
    
    if (keywords.includes('forest') || keywords.includes('green')) {
      return {
        cosmic: '#059669',
        nova: '#10B981',
        quantum: '#F59E0B',
        neural: '#8B5CF6',
        aura: '#EC4899',
        stardust: '#6B7280',
        space: '#111827'
      };
    }
    
    // Default to warm theme
    return {
      cosmic: '#DC2626',
      nova: '#16A34A',
      quantum: '#EAB308',
      neural: '#A855F7',
      aura: '#EC4899',
      stardust: '#78716C',
      space: '#1C1917'
    };
  }

  // SAVE/LOAD THEME
  private saveTheme(schema: ThemeSchema): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('dynamic-theme-schema', JSON.stringify(schema));
    }
  }

  private loadSavedTheme(): void {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('dynamic-theme-schema');
      if (saved) {
        try {
          this.currentSchema = JSON.parse(saved);
          this.applyThemeSchema(this.currentSchema);
        } catch (e) {
          console.warn('Failed to load saved theme, using default');
        }
      }
    }
  }

  // NOTIFY THEME CHANGE
  private notifyThemeChange(schema: ThemeSchema): void {
    // Dispatch custom event for components to listen to
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('themeSchemaChanged', {
        detail: { schema }
      }));
    }
  }

  // GETTERS
  getCurrentSchema(): ThemeSchema {
    return this.currentSchema;
  }

  getBlueprints(): ThemeBlueprint[] {
    return this.blueprints;
  }

  getBlueprintsByCategory(category: ThemeBlueprint['category']): ThemeBlueprint[] {
    return this.blueprints.filter(bp => bp.category === category);
  }

  // ADD CUSTOM BLUEPRINT
  addCustomBlueprint(blueprint: Omit<ThemeBlueprint, 'id'>): string {
    const id = `custom-${Date.now()}`;
    this.blueprints.push({ ...blueprint, id });
    return id;
  }
}

// REACT HOOK FOR DYNAMIC THEME CONTROL
export const useDynamicTheme = () => {
  const [controller] = useState(() => DynamicThemeController.getInstance());
  const [currentSchema, setCurrentSchema] = useState(controller.getCurrentSchema());
  const [blueprints, setBlueprints] = useState(controller.getBlueprints());
  const themeEnforcer = useThemeConsistency();

  // Listen for theme changes
  useEffect(() => {
    const handleThemeChange = (event: CustomEvent) => {
      setCurrentSchema(event.detail.schema);
      // Re-scan for consistency after theme change
      setTimeout(() => themeEnforcer.scanAndUpdate(), 500);
    };

    window.addEventListener('themeSchemaChanged', handleThemeChange as EventListener);
    return () => window.removeEventListener('themeSchemaChanged', handleThemeChange as EventListener);
  }, [themeEnforcer]);

  const applyTheme = useCallback((schema: ThemeSchema) => {
    controller.applyThemeSchema(schema);
  }, [controller]);

  const applyBlueprint = useCallback((blueprintId: string) => {
    const blueprint = blueprints.find(bp => bp.id === blueprintId);
    if (blueprint) {
      controller.applyThemeSchema(blueprint.schema);
    }
  }, [controller, blueprints]);

  const generateAITheme = useCallback(async (prompt: string) => {
    const aiSchema = await controller.generateAITheme(prompt);
    controller.applyThemeSchema(aiSchema);
    return aiSchema;
  }, [controller]);

  const createCustomBlueprint = useCallback((blueprint: Omit<ThemeBlueprint, 'id'>) => {
    const id = controller.addCustomBlueprint(blueprint);
    setBlueprints(controller.getBlueprints());
    return id;
  }, [controller]);

  return {
    currentSchema,
    blueprints,
    applyTheme,
    applyBlueprint,
    generateAITheme,
    createCustomBlueprint,
    controller
  };
};

export default DynamicThemeController;
