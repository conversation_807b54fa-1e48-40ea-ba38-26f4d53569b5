"use client";

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import DynamicConsistencyMonitor from './consistency/DynamicConsistencyMonitor';
import NavigationConnectivitySystem, { useNavigationConnectivity } from './navigation/NavigationConnectivitySystem';
import ThemeConsistencyEnforcer, { useThemeConsistency } from './theme/ThemeConsistencyEnforcer';

/**
 * 🎯 MASTER CONSISTENCY SYSTEM
 * 
 * Orchestrates all consistency systems:
 * - Dynamic Consistency Monitor
 * - Navigation Connectivity System  
 * - Theme Consistency Enforcer
 * - New Page Auto-Registration
 * - Real-time Violation Detection
 */

interface SystemHealth {
  overall: number;
  navigation: number;
  theme: number;
  consistency: number;
  timestamp: Date;
}

interface AutoCalibration {
  enabled: boolean;
  autoFix: boolean;
  notifyViolations: boolean;
  preventOrphans: boolean;
  enforceTheme: boolean;
}

export const MasterConsistencySystem = () => {
  // Only run in browser environment
  if (typeof window === 'undefined') {
    return null;
  }

  const pathname = usePathname();
  const [systemHealth, setSystemHealth] = useState<SystemHealth>({
    overall: 100,
    navigation: 100,
    theme: 100,
    consistency: 100,
    timestamp: new Date()
  });

  const [autoCalibration, setAutoCalibration] = useState<AutoCalibration>({
    enabled: true,
    autoFix: true,
    notifyViolations: true,
    preventOrphans: true,
    enforceTheme: true
  });

  const [isActive, setIsActive] = useState(false); // Start hidden
  const [showMonitoring, setShowMonitoring] = useState(false);
  const [violations, setViolations] = useState<any[]>([]);

  // Hook into subsystems
  const navigationSystem = useNavigationConnectivity();
  const themeSystem = useThemeConsistency();

  // AUTO-REGISTER CURRENT PAGE
  useEffect(() => {
    if (!autoCalibration.enabled || typeof window === 'undefined') return;

    // Auto-register current page in navigation system
    const pageTitle = document.title || 'Untitled Page';
    const metaDescription = document.querySelector('meta[name="description"]')?.getAttribute('content') || '';
    
    navigationSystem.registerCurrentPage({
      title: pageTitle,
      category: determineCategoryFromPath(pathname || '/')
    });

  }, [pathname, autoCalibration.enabled, navigationSystem]);

  // DETERMINE CATEGORY FROM PATH
  const determineCategoryFromPath = (path: string): string => {
    if (path.includes('demo')) return 'Demo';
    if (path.includes('ai') || path.includes('intelligence')) return 'AI Systems';
    if (path.includes('agent')) return 'AI Agents';
    if (path.includes('creative')) return 'Creative Tools';
    if (path.includes('community')) return 'Community';
    if (path.includes('dashboard')) return 'Dashboard';
    return 'System';
  };

  // CALCULATE OVERALL SYSTEM HEALTH
  useEffect(() => {
    const navigationHealth = navigationSystem.report.healthScore;
    const themeHealth = themeSystem.report.complianceScore;
    const consistencyHealth = Math.max(0, 100 - violations.length * 5);
    
    const overall = Math.round((navigationHealth + themeHealth + consistencyHealth) / 3);

    setSystemHealth({
      overall,
      navigation: navigationHealth,
      theme: themeHealth,
      consistency: consistencyHealth,
      timestamp: new Date()
    });
  }, [navigationSystem.report, themeSystem.report, violations]);

  // AUTO-CALIBRATION ENGINE
  useEffect(() => {
    if (!autoCalibration.enabled) return;

    const runAutoCalibration = async () => {
      let actionsPerformed = 0;

      // 1. AUTO-FIX THEME VIOLATIONS
      if (autoCalibration.autoFix && autoCalibration.enforceTheme) {
        const fixedThemeViolations = themeSystem.autoFixAll();
        actionsPerformed += fixedThemeViolations;
      }

      // 2. AUTO-CONNECT ORPHANED PAGES
      if (autoCalibration.preventOrphans) {
        const orphanedPages = navigationSystem.report.orphanedPages;
        orphanedPages.forEach(page => {
          navigationSystem.connectPage(page.path, page.hub);
          actionsPerformed++;
        });
      }

      // 3. NOTIFY ABOUT VIOLATIONS
      if (autoCalibration.notifyViolations && actionsPerformed > 0) {
        notifyCalibrationActions(actionsPerformed);
      }
    };

    // Run calibration every 30 seconds
    const interval = setInterval(runAutoCalibration, 30000);
    
    // Initial run
    setTimeout(runAutoCalibration, 2000);

    return () => clearInterval(interval);
  }, [autoCalibration, themeSystem, navigationSystem]);

  // NOTIFY CALIBRATION ACTIONS
  const notifyCalibrationActions = (actionsCount: number) => {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🎯 Master Consistency System: Performed ${actionsCount} auto-calibration actions`);
    }
  };

  // MANUAL SYSTEM CALIBRATION
  const runFullCalibration = async () => {
    let totalActions = 0;

    // 1. Theme consistency
    const themeFixed = themeSystem.autoFixAll();
    totalActions += themeFixed;

    // 2. Navigation connectivity
    const orphaned = navigationSystem.report.orphanedPages;
    orphaned.forEach(page => {
      navigationSystem.connectPage(page.path, page.hub);
      totalActions++;
    });

    // 3. Re-scan everything
    await themeSystem.scanAndUpdate();

    return totalActions;
  };

  // TOGGLE BUTTON (Always visible in development)
  if (process.env.NODE_ENV === 'development' && !showMonitoring) {
    return (
      <button
        onClick={() => setShowMonitoring(true)}
        className="bg-space-800/90 hover:bg-space-700 border border-cosmic-500/30 text-cosmic-300 p-2 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 backdrop-blur-sm"
        title="Show Consistency Monitoring"
      >
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-cosmic-400 rounded-full animate-pulse" />
          <span className="text-xs font-medium">Monitor</span>
        </div>
      </button>
    );
  }

  // SYSTEM HEALTH DASHBOARD (Development Mode)
  if (showMonitoring && process.env.NODE_ENV === 'development') {
    return (
      <>
        {/* Main Consistency Monitor */}
        <DynamicConsistencyMonitor />

        {/* Master System Dashboard */}
        <div className="fixed top-4 right-4 z-50 max-w-sm">
          <div className="bg-space-800 border border-cosmic-500/30 rounded-lg p-4 shadow-xl">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-cosmic-300 font-semibold">🎯 Master System</h3>
              <button
                onClick={() => setShowMonitoring(false)}
                className="text-xs text-white/60 hover:text-white"
              >
                Hide
              </button>
            </div>

            {/* System Health Indicators */}
            <div className="space-y-2 mb-4">
              <div className="flex items-center justify-between">
                <span className="text-xs text-white/70">Overall Health</span>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${
                    systemHealth.overall >= 90 ? 'bg-nova-400' :
                    systemHealth.overall >= 70 ? 'bg-quantum-400' : 'bg-red-400'
                  } animate-pulse`} />
                  <span className="text-xs font-bold text-white">{systemHealth.overall}%</span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs text-white/70">Navigation</span>
                <span className="text-xs text-cosmic-300">{systemHealth.navigation}%</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs text-white/70">Theme</span>
                <span className="text-xs text-neural-300">{systemHealth.theme}%</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-xs text-white/70">Consistency</span>
                <span className="text-xs text-nova-300">{systemHealth.consistency}%</span>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="space-y-1 mb-4 text-xs">
              <div className="flex justify-between">
                <span className="text-white/60">Pages</span>
                <span className="text-white">{navigationSystem.report.totalPages}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Orphaned</span>
                <span className="text-red-300">{navigationSystem.report.orphanedPages.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-white/60">Theme Issues</span>
                <span className="text-yellow-300">{themeSystem.report.totalViolations}</span>
              </div>
            </div>

            {/* Auto-Calibration Toggle */}
            <div className="mb-3">
              <label className="flex items-center gap-2 text-xs">
                <input
                  type="checkbox"
                  checked={autoCalibration.enabled}
                  onChange={(e) => setAutoCalibration(prev => ({
                    ...prev,
                    enabled: e.target.checked
                  }))}
                  className="rounded"
                />
                <span className="text-white/70">Auto-Calibration</span>
              </label>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <button
                onClick={runFullCalibration}
                className="flex-1 bg-cosmic-500 hover:bg-cosmic-600 text-white text-xs py-2 px-3 rounded transition-colors"
              >
                Calibrate All
              </button>
              <button
                onClick={() => window.location.reload()}
                className="bg-space-700 hover:bg-space-600 text-white text-xs py-2 px-3 rounded transition-colors"
              >
                Refresh
              </button>
            </div>

            {/* Last Updated */}
            <div className="text-xs text-white/40 mt-2 text-center">
              Updated: {systemHealth.timestamp.toLocaleTimeString()}
            </div>
          </div>
        </div>
      </>
    );
  }

  // Production mode - only run background systems
  return (
    <>
      {/* Background consistency monitoring */}
      {autoCalibration.enabled && <DynamicConsistencyMonitor />}
    </>
  );
};

// HOOK FOR MANUAL SYSTEM CONTROL
export const useMasterConsistencySystem = () => {
  const navigationSystem = useNavigationConnectivity();
  const themeSystem = useThemeConsistency();

  const runFullSystemCheck = async () => {
    // Run all system checks
    await themeSystem.scanAndUpdate();
    
    return {
      navigation: navigationSystem.report,
      theme: themeSystem.report,
      timestamp: new Date()
    };
  };

  const autoFixAllIssues = async () => {
    let totalFixed = 0;
    
    // Fix theme issues
    totalFixed += themeSystem.autoFixAll();
    
    // Connect orphaned pages
    const orphaned = navigationSystem.report.orphanedPages;
    orphaned.forEach(page => {
      navigationSystem.connectPage(page.path, page.hub);
      totalFixed++;
    });

    return totalFixed;
  };

  return {
    runFullSystemCheck,
    autoFixAllIssues,
    navigationSystem,
    themeSystem
  };
};

// NEW PAGE AUTO-REGISTRATION HOOK
export const useNewPageRegistration = () => {
  const pathname = usePathname();
  const navigationSystem = useNavigationConnectivity();
  const themeSystem = useThemeConsistency();

  useEffect(() => {
    // Auto-register new page
    const registerNewPage = () => {
      const pageTitle = document.title || 'New Page';
      const isNewPage = !navigationSystem.report.totalPages ||
                       !Object.keys(navigationSystem.system.navigationMap || {}).includes(pathname || '');

      if (isNewPage) {
        console.log(`🆕 Auto-registering new page: ${pathname}`);

        navigationSystem.registerCurrentPage({
          title: pageTitle,
          category: 'New Feature'
        });

        // Validate theme compliance for new page
        setTimeout(() => {
          themeSystem.scanAndUpdate();
        }, 1000);
      }
    };

    registerNewPage();
  }, [pathname, navigationSystem, themeSystem]);
};

// PAGE CREATION HELPER
export const createNewPageWithConsistency = (
  path: string,
  metadata: {
    title: string;
    hub: 'intelligence' | 'agents' | 'creative' | 'dashboard' | 'community';
    category: string;
    description?: string;
  }
) => {
  const navigationSystem = NavigationConnectivitySystem.getInstance();
  const themeEnforcer = ThemeConsistencyEnforcer.getInstance();

  // Register page in navigation system
  navigationSystem.registerPage(path, {
    title: metadata.title,
    hub: metadata.hub,
    category: metadata.category
  });

  // Return template with consistent styling
  return {
    path,
    metadata,
    template: `
      <div className="min-h-screen bg-space-900 pt-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-6xl font-bold bg-gradient-to-r from-cosmic-400 via-nova-400 to-neural-400 bg-clip-text text-transparent">
              ${metadata.title}
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto mt-6">
              ${metadata.description || 'New feature page with automatic consistency enforcement'}
            </p>
          </div>

          <div className="theme-card theme-spacing-responsive">
            <p className="text-white/70">
              This page was automatically registered with the Master Consistency System.
              All styling and navigation connections are enforced automatically.
            </p>
          </div>
        </div>
      </div>
    `,
    isConsistent: true,
    autoConnected: true
  };
};

export default MasterConsistencySystem;
