"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

/**
 * 🔗 NAVIGATION CONNECTIVITY SYSTEM
 * 
 * Automatically ensures:
 * - All pages are connected to navigation
 * - No orphaned pages exist
 * - Consistent navigation patterns
 * - Dynamic route registration
 */

interface NavigationNode {
  path: string;
  title: string;
  hub: 'intelligence' | 'agents' | 'creative' | 'dashboard' | 'community';
  category: string;
  isConnected: boolean;
  connections: string[];
  lastAccessed?: Date;
}

interface NavigationMap {
  [path: string]: NavigationNode;
}

export class NavigationConnectivitySystem {
  private static instance: NavigationConnectivitySystem;
  public navigationMap: NavigationMap = {};
  private requiredHubs = ['intelligence', 'agents', 'creative', 'dashboard'];
  
  static getInstance(): NavigationConnectivitySystem {
    if (!NavigationConnectivitySystem.instance) {
      NavigationConnectivitySystem.instance = new NavigationConnectivitySystem();
    }
    return NavigationConnectivitySystem.instance;
  }

  // REGISTER NEW PAGE AUTOMATICALLY
  registerPage(path: string, metadata: Partial<NavigationNode>) {
    const existingNode = this.navigationMap[path];
    
    this.navigationMap[path] = {
      ...existingNode,
      path,
      title: metadata.title || this.generateTitleFromPath(path),
      hub: metadata.hub || this.determineOptimalHub(path),
      category: metadata.category || this.determineCategoryFromPath(path),
      isConnected: this.checkIfConnected(path),
      connections: metadata.connections || [],
      lastAccessed: new Date()
    };

    // Auto-connect if orphaned
    if (!this.navigationMap[path].isConnected) {
      this.autoConnectPage(path);
    }

    this.saveNavigationMap();
  }

  // DETERMINE OPTIMAL HUB FOR PAGE
  private determineOptimalHub(path: string): NavigationNode['hub'] {
    const pathSegments = path.split('/').filter(Boolean);
    const firstSegment = pathSegments[0]?.toLowerCase();

    // AI/Intelligence related
    if (firstSegment?.includes('ai') || 
        firstSegment?.includes('intelligence') ||
        firstSegment?.includes('analytics') ||
        firstSegment?.includes('autonomous') ||
        firstSegment?.includes('live-ai') ||
        firstSegment?.includes('real-ai')) {
      return 'intelligence';
    }

    // Agent related
    if (firstSegment?.includes('agent') ||
        firstSegment?.includes('ecosystem') ||
        firstSegment?.includes('communication') ||
        firstSegment?.includes('observation')) {
      return 'agents';
    }

    // Creative related
    if (firstSegment?.includes('creative') ||
        firstSegment?.includes('canvas') ||
        firstSegment?.includes('gallery') ||
        firstSegment?.includes('marketplace') ||
        firstSegment?.includes('media') ||
        firstSegment?.includes('voice')) {
      return 'creative';
    }

    // Community related
    if (firstSegment?.includes('community') ||
        firstSegment?.includes('collaboration') ||
        firstSegment?.includes('profile') ||
        firstSegment?.includes('discover')) {
      return 'community';
    }

    // Default to dashboard for system pages
    return 'dashboard';
  }

  // AUTO-CONNECT ORPHANED PAGE
  private autoConnectPage(path: string) {
    const node = this.navigationMap[path];
    if (!node) return;

    const hubPath = `/${node.hub}`;
    const connections: string[] = [];

    // Add to appropriate hub
    connections.push(hubPath);

    // Add to dashboard if it's a demo/important page
    if (this.isDemoPage(path) || this.isImportantPage(path)) {
      connections.push('/dashboard');
    }

    // Update connections
    this.navigationMap[path].connections = connections;
    this.navigationMap[path].isConnected = true;

    // Notify about auto-connection
    this.notifyAutoConnection(path, connections);
  }

  // CHECK IF PAGE IS DEMO/IMPORTANT
  private isDemoPage(path: string): boolean {
    return path.includes('demo') || 
           path.includes('live-') || 
           path.includes('real-') ||
           path.includes('test');
  }

  private isImportantPage(path: string): boolean {
    const importantPaths = [
      '/analytics', '/monitoring', '/system', 
      '/autonomous', '/intelligence', '/agents'
    ];
    return importantPaths.some(important => path.includes(important));
  }

  // GENERATE TITLE FROM PATH
  private generateTitleFromPath(path: string): string {
    const segments = path.split('/').filter(Boolean);
    const lastSegment = segments[segments.length - 1];
    
    return lastSegment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // DETERMINE CATEGORY FROM PATH
  private determineCategoryFromPath(path: string): string {
    if (path.includes('demo')) return 'Demo';
    if (path.includes('analytics')) return 'Analytics';
    if (path.includes('monitoring')) return 'Monitoring';
    if (path.includes('agent')) return 'AI Agents';
    if (path.includes('creative')) return 'Creative Tools';
    if (path.includes('community')) return 'Community';
    return 'System';
  }

  // CHECK IF PAGE IS CONNECTED
  private checkIfConnected(path: string): boolean {
    // Check if page is referenced in any navigation
    const navigationFiles = [
      '/intelligence', '/agents', '/creative', '/dashboard', '/community'
    ];
    
    // In a real implementation, this would check actual navigation files
    // For now, we'll use a heuristic based on common patterns
    return this.navigationMap[path]?.connections?.length > 0 || false;
  }

  // GET ORPHANED PAGES
  getOrphanedPages(): NavigationNode[] {
    return Object.values(this.navigationMap).filter(node => !node.isConnected);
  }

  // GET NAVIGATION SUGGESTIONS
  getNavigationSuggestions(path: string): string[] {
    const node = this.navigationMap[path];
    if (!node) return [];

    const suggestions: string[] = [];
    
    // Suggest hub connection
    suggestions.push(`Add to ${node.hub.charAt(0).toUpperCase() + node.hub.slice(1)} Hub`);
    
    // Suggest dashboard connection for demos
    if (this.isDemoPage(path)) {
      suggestions.push('Add to Dashboard Demo Section');
    }

    // Suggest related pages
    const relatedPages = this.findRelatedPages(path);
    relatedPages.forEach(related => {
      suggestions.push(`Connect to ${related.title}`);
    });

    return suggestions;
  }

  // FIND RELATED PAGES
  private findRelatedPages(path: string): NavigationNode[] {
    const node = this.navigationMap[path];
    if (!node) return [];

    return Object.values(this.navigationMap).filter(other => 
      other.path !== path &&
      (other.hub === node.hub || 
       other.category === node.category ||
       this.hasCommonKeywords(path, other.path))
    ).slice(0, 3);
  }

  // CHECK COMMON KEYWORDS
  private hasCommonKeywords(path1: string, path2: string): boolean {
    const keywords1 = path1.split(/[-\/]/).filter(Boolean);
    const keywords2 = path2.split(/[-\/]/).filter(Boolean);
    
    return keywords1.some(keyword => 
      keywords2.includes(keyword) && keyword.length > 3
    );
  }

  // NOTIFY AUTO-CONNECTION
  private notifyAutoConnection(path: string, connections: string[]) {
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔗 Auto-connected orphaned page: ${path}`);
      console.log(`   Added to: ${connections.join(', ')}`);
    }
  }

  // SAVE NAVIGATION MAP
  private saveNavigationMap() {
    if (typeof window !== 'undefined') {
      localStorage.setItem('navigationMap', JSON.stringify(this.navigationMap));
    }
  }

  // LOAD NAVIGATION MAP
  loadNavigationMap() {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem('navigationMap');
      if (saved) {
        this.navigationMap = JSON.parse(saved);
      }
    }
  }

  // GET NAVIGATION HEALTH SCORE
  getNavigationHealthScore(): number {
    const totalPages = Object.keys(this.navigationMap).length;
    if (totalPages === 0) return 100;

    const connectedPages = Object.values(this.navigationMap).filter(node => node.isConnected).length;
    return Math.round((connectedPages / totalPages) * 100);
  }

  // GENERATE NAVIGATION REPORT
  generateNavigationReport() {
    const orphaned = this.getOrphanedPages();
    const healthScore = this.getNavigationHealthScore();
    
    return {
      healthScore,
      totalPages: Object.keys(this.navigationMap).length,
      connectedPages: Object.values(this.navigationMap).filter(node => node.isConnected).length,
      orphanedPages: orphaned,
      hubDistribution: this.getHubDistribution(),
      suggestions: orphaned.map(page => ({
        page: page.path,
        suggestions: this.getNavigationSuggestions(page.path)
      }))
    };
  }

  // GET HUB DISTRIBUTION
  private getHubDistribution() {
    const distribution: Record<string, number> = {};
    Object.values(this.navigationMap).forEach(node => {
      distribution[node.hub] = (distribution[node.hub] || 0) + 1;
    });
    return distribution;
  }
}

// REACT HOOK FOR NAVIGATION CONNECTIVITY
export const useNavigationConnectivity = () => {
  const [system] = useState(() => NavigationConnectivitySystem.getInstance());
  const [report, setReport] = useState(system.generateNavigationReport());
  const router = useRouter();

  useEffect(() => {
    system.loadNavigationMap();
    setReport(system.generateNavigationReport());
  }, [system]);

  const registerCurrentPage = (metadata: Partial<NavigationNode>) => {
    const currentPath = window.location.pathname;
    system.registerPage(currentPath, metadata);
    setReport(system.generateNavigationReport());
  };

  const connectPage = (path: string, targetHub: string) => {
    system.registerPage(path, { 
      hub: targetHub as NavigationNode['hub'],
      connections: [`/${targetHub}`]
    });
    setReport(system.generateNavigationReport());
  };

  return {
    report,
    registerCurrentPage,
    connectPage,
    system
  };
};

export default NavigationConnectivitySystem;
