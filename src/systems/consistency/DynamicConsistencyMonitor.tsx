"use client";

import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';

/**
 * 🔄 DYNAMIC CONSISTENCY MONITOR SYSTEM
 * 
 * Automatically detects and prevents:
 * - Disconnected pages/buttons
 * - Inconsistent styling
 * - Missing navigation links
 * - Theme color violations
 * - Broken component patterns
 */

interface ConsistencyIssue {
  type: 'navigation' | 'styling' | 'theme' | 'component' | 'accessibility';
  severity: 'critical' | 'warning' | 'info';
  message: string;
  element?: string;
  suggestion?: string;
  autoFix?: () => void;
}

interface PageAnalysis {
  path: string;
  hasNavigation: boolean;
  themeCompliance: number;
  missingConnections: string[];
  styleInconsistencies: string[];
  componentIssues: string[];
}

export const DynamicConsistencyMonitor = () => {
  const pathname = usePathname();
  const [issues, setIssues] = useState<ConsistencyIssue[]>([]);
  const [pageAnalysis, setPageAnalysis] = useState<PageAnalysis | null>(null);
  const [isMonitoring, setIsMonitoring] = useState(true);

  // THEME COLOR VALIDATION
  const validateThemeColors = (): ConsistencyIssue[] => {
    const issues: ConsistencyIssue[] = [];
    const invalidColors = ['blue-', 'green-', 'yellow-', 'purple-', 'nebula-'];
    const validThemeColors = ['cosmic-', 'nova-', 'quantum-', 'neural-', 'aura-', 'stardust-', 'space-'];

    // Check all elements for invalid colors
    const elements = document.querySelectorAll('*');
    elements.forEach((element) => {
      const classes = element.className;
      if (typeof classes === 'string') {
        invalidColors.forEach(invalidColor => {
          if (classes.includes(invalidColor)) {
            issues.push({
              type: 'theme',
              severity: 'critical',
              message: `Invalid theme color detected: ${invalidColor}`,
              element: element.tagName,
              suggestion: `Replace with theme colors: ${validThemeColors.join(', ')}`,
              autoFix: () => autoFixThemeColor(element, invalidColor)
            });
          }
        });
      }
    });

    return issues;
  };

  // NAVIGATION CONNECTIVITY CHECK
  const validateNavigation = (): ConsistencyIssue[] => {
    const issues: ConsistencyIssue[] = [];
    const requiredNavElements = [
      'header nav',
      'footer nav', 
      'mobile navigation',
      'breadcrumbs'
    ];

    // Check if page has proper navigation
    const hasHeader = document.querySelector('header');
    const hasFooter = document.querySelector('footer');
    const hasNavLinks = document.querySelectorAll('a[href]').length > 0;

    if (!hasHeader) {
      issues.push({
        type: 'navigation',
        severity: 'critical',
        message: 'Page missing header navigation',
        suggestion: 'Add Header component to page layout'
      });
    }

    if (!hasFooter) {
      issues.push({
        type: 'navigation',
        severity: 'warning',
        message: 'Page missing footer navigation',
        suggestion: 'Add Footer component to page layout'
      });
    }

    if (!hasNavLinks) {
      issues.push({
        type: 'navigation',
        severity: 'critical',
        message: 'Page has no navigation links - potential orphaned page',
        suggestion: 'Add navigation links to connect this page to the platform'
      });
    }

    return issues;
  };

  // COMPONENT PATTERN VALIDATION
  const validateComponentPatterns = (): ConsistencyIssue[] => {
    const issues: ConsistencyIssue[] = [];

    // Check for deprecated patterns
    const deprecatedClasses = ['neo-button', 'old-card', 'legacy-'];
    const elements = document.querySelectorAll('*');
    
    elements.forEach((element) => {
      const classes = element.className;
      if (typeof classes === 'string') {
        deprecatedClasses.forEach(deprecated => {
          if (classes.includes(deprecated)) {
            issues.push({
              type: 'component',
              severity: 'warning',
              message: `Deprecated component pattern: ${deprecated}`,
              element: element.tagName,
              suggestion: 'Update to use modern component patterns',
              autoFix: () => autoFixDeprecatedPattern(element, deprecated)
            });
          }
        });
      }
    });

    return issues;
  };

  // ACCESSIBILITY VALIDATION
  const validateAccessibility = (): ConsistencyIssue[] => {
    const issues: ConsistencyIssue[] = [];

    // Check for missing alt text
    const images = document.querySelectorAll('img:not([alt])');
    if (images.length > 0) {
      issues.push({
        type: 'accessibility',
        severity: 'warning',
        message: `${images.length} images missing alt text`,
        suggestion: 'Add descriptive alt text to all images'
      });
    }

    // Check for missing button labels
    const buttons = document.querySelectorAll('button:not([aria-label]):not([title])');
    buttons.forEach(button => {
      if (!button.textContent?.trim()) {
        issues.push({
          type: 'accessibility',
          severity: 'warning',
          message: 'Button missing accessible label',
          element: 'button',
          suggestion: 'Add aria-label or visible text to button'
        });
      }
    });

    return issues;
  };

  // AUTO-FIX FUNCTIONS
  const autoFixThemeColor = (element: Element, invalidColor: string) => {
    const colorMap: Record<string, string> = {
      'blue-': 'cosmic-',
      'green-': 'nova-',
      'yellow-': 'quantum-',
      'purple-': 'neural-',
      'nebula-': 'neural-'
    };

    const replacement = colorMap[invalidColor];
    if (replacement && element.className) {
      element.className = (element.className || '').replace(new RegExp(invalidColor, 'g'), replacement);
    }
  };

  const autoFixDeprecatedPattern = (element: Element, deprecated: string) => {
    const patternMap: Record<string, string> = {
      'neo-button': 'bg-cosmic-500 hover:bg-cosmic-600 text-white rounded-lg font-medium transition-colors',
      'old-card': 'theme-card theme-spacing-responsive',
      'legacy-': 'modern-'
    };

    const replacement = patternMap[deprecated];
    if (replacement && element.className) {
      element.className = (element.className || '').replace(deprecated, replacement);
    }
  };

  // COMPREHENSIVE PAGE ANALYSIS
  const analyzePage = (): PageAnalysis => {
    const allIssues = [
      ...validateThemeColors(),
      ...validateNavigation(),
      ...validateComponentPatterns(),
      ...validateAccessibility()
    ];

    const themeIssues = allIssues.filter(issue => issue.type === 'theme');
    const themeCompliance = Math.max(0, 100 - (themeIssues.length * 10));

    const navIssues = allIssues.filter(issue => issue.type === 'navigation');
    const hasNavigation = navIssues.length === 0;

    return {
      path: pathname || '/',
      hasNavigation,
      themeCompliance,
      missingConnections: navIssues.map(issue => issue.message),
      styleInconsistencies: themeIssues.map(issue => issue.message),
      componentIssues: allIssues.filter(issue => issue.type === 'component').map(issue => issue.message)
    };
  };

  // REAL-TIME MONITORING
  useEffect(() => {
    if (!isMonitoring) return;

    const runAnalysis = () => {
      const allIssues = [
        ...validateThemeColors(),
        ...validateNavigation(),
        ...validateComponentPatterns(),
        ...validateAccessibility()
      ];

      setIssues(allIssues);
      setPageAnalysis(analyzePage());
    };

    // Initial analysis
    setTimeout(runAnalysis, 1000);

    // Set up mutation observer for dynamic changes
    const observer = new MutationObserver(() => {
      setTimeout(runAnalysis, 500);
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['class', 'style']
    });

    return () => observer.disconnect();
  }, [pathname, isMonitoring]);

  // AUTO-FIX ALL ISSUES
  const autoFixAllIssues = () => {
    issues.forEach(issue => {
      if (issue.autoFix) {
        issue.autoFix();
      }
    });
    
    // Re-run analysis after fixes
    setTimeout(() => {
      const newIssues = [
        ...validateThemeColors(),
        ...validateNavigation(),
        ...validateComponentPatterns(),
        ...validateAccessibility()
      ];
      setIssues(newIssues);
    }, 1000);
  };

  // DEVELOPMENT MODE DISPLAY
  if (process.env.NODE_ENV === 'development' && issues.length > 0) {
    return (
      <div className="fixed bottom-4 right-4 z-50 max-w-md">
        <div className="bg-space-800 border border-cosmic-500/30 rounded-lg p-4 shadow-xl">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-cosmic-300 font-semibold">🔄 Consistency Monitor</h3>
            <button
              onClick={() => setIsMonitoring(!isMonitoring)}
              className="text-xs text-white/60 hover:text-white"
            >
              {isMonitoring ? 'Pause' : 'Resume'}
            </button>
          </div>
          
          <div className="space-y-2 mb-3 max-h-40 overflow-y-auto">
            {issues.slice(0, 5).map((issue, index) => (
              <div key={index} className={`text-xs p-2 rounded ${
                issue.severity === 'critical' ? 'bg-red-500/20 text-red-300' :
                issue.severity === 'warning' ? 'bg-yellow-500/20 text-yellow-300' :
                'bg-blue-500/20 text-blue-300'
              }`}>
                <div className="font-medium">{issue.type.toUpperCase()}</div>
                <div>{issue.message}</div>
                {issue.suggestion && (
                  <div className="text-white/60 mt-1">{issue.suggestion}</div>
                )}
              </div>
            ))}
            {issues.length > 5 && (
              <div className="text-xs text-white/60">
                +{issues.length - 5} more issues...
              </div>
            )}
          </div>

          <div className="flex gap-2">
            <button
              onClick={autoFixAllIssues}
              className="flex-1 bg-cosmic-500 hover:bg-cosmic-600 text-white text-xs py-2 px-3 rounded transition-colors"
            >
              Auto-Fix ({issues.filter(i => i.autoFix).length})
            </button>
            <button
              onClick={() => setIssues([])}
              className="bg-space-700 hover:bg-space-600 text-white text-xs py-2 px-3 rounded transition-colors"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default DynamicConsistencyMonitor;
