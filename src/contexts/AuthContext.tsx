"use client";

import React, { createContext, useContext, ReactNode, useMemo } from "react";
import { useAuth as useAuthHook, User, LoginCredentials, RegisterData } from "@/hooks/useAuth";

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (emailOrCredentials: string | LoginCredentials, password?: string, isGuest?: boolean) => Promise<{ success: boolean; error?: string; fieldErrors?: Record<string, string> }>;
  register: (data: RegisterData) => Promise<{ success: boolean; error?: string; fieldErrors?: Record<string, string> }>;
  logout: () => void;
  signOut: () => void;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const auth = useAuthHook();
  
  // Wrapper function to support both old and new login signatures
  const loginWrapper = async (emailOrCredentials: string | LoginCredentials, password?: string, isGuest?: boolean) => {
    if (typeof emailOrCredentials === 'string') {
      // Old signature: login(email, password, isGuest)
      return auth.login({ email: emailOrCredentials, password: password || '', isGuest });
    } else {
      // New signature: login(credentials)
      return auth.login(emailOrCredentials);
    }
  };

  const contextValue = useMemo(() => ({
    user: auth.user,
    isAuthenticated: auth.isAuthenticated,
    isLoading: auth.isLoading,
    error: auth.error,
    login: loginWrapper,
    register: auth.register,
    logout: auth.logout,
    signOut: auth.logout,
    hasPermission: auth.hasPermission,
    hasAnyPermission: auth.hasAnyPermission,
    hasAllPermissions: auth.hasAllPermissions,
  }), [
    auth.user,
    auth.isAuthenticated,
    auth.isLoading,
    auth.error,
    auth.login,
    auth.register,
    auth.logout,
    auth.hasPermission,
    auth.hasAnyPermission,
    auth.hasAllPermissions,
  ]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuthContext() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  
  return context;
}

export const useAuth = useAuthContext; 

AuthContext.displayName = 'AuthContext';