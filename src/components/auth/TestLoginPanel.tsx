"use client";

import React, { useState } from 'react';
import { useTestAccounts } from '@/systems/testing/TestAccountManager';
import { useAuthContext } from '@/contexts/AuthContext';
import { User, TestTube, RefreshCw, Eye, EyeOff } from 'lucide-react';

/**
 * 🧪 TEST LOGIN PANEL
 * 
 * Quick login interface for testing:
 * - AGITEST user (admin with saved themes)
 * - TestGuest user (persistent guest)
 * - Auto-fill credentials
 * - Reset test data functionality
 */

export const TestLoginPanel = () => {
  const { login } = useAuthContext();
  const { 
    isReady, 
    loginAsAGITEST, 
    loginAsGuest, 
    resetAllTestData 
  } = useTestAccounts();
  
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');

  // LOGIN AS AGITEST
  const handleAGITESTLogin = async () => {
    setIsLoading(true);
    try {
      const testUser = loginAsAGITEST();
      if (testUser) {
        await login(testUser.email, 'AGITEST123!');
        console.log('🧪 Logged in as AGITEST');
      }
    } catch (error) {
      console.error('AGITEST login failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // LOGIN AS GUEST
  const handleGuestLogin = async () => {
    setIsLoading(true);
    try {
      const guestUser = loginAsGuest();
      if (guestUser) {
        // For guest, we'll use a special guest login method
        await login('<EMAIL>', '', true); // true for guest mode
        console.log('🧪 Logged in as TestGuest');
      }
    } catch (error) {
      console.error('Guest login failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // AUTO-FILL AGITEST CREDENTIALS
  const fillAGITESTCredentials = () => {
    setUsername('AGITEST');
    setPassword('AGITEST123!');
  };

  // RESET TEST DATA
  const handleResetTestData = () => {
    if (confirm('Reset all test data? This will clear saved themes and preferences.')) {
      resetAllTestData();
      alert('Test data reset successfully!');
    }
  };

  if (!isReady) {
    return (
      <div className="bg-space-800/50 border border-cosmic-500/30 rounded-lg p-4">
        <div className="flex items-center gap-2 text-cosmic-300">
          <div className="w-4 h-4 border-2 border-cosmic-500/30 border-t-cosmic-500 rounded-full animate-spin" />
          <span className="text-sm">Loading test accounts...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Test Account Quick Login */}
      <div className="bg-space-800/50 border border-cosmic-500/30 rounded-lg p-6">
        <div className="flex items-center gap-2 mb-4">
          <TestTube className="w-5 h-5 text-cosmic-400" />
          <h3 className="text-lg font-semibold text-white">Test Accounts</h3>
        </div>
        
        <div className="space-y-3">
          {/* AGITEST Login */}
          <button
            onClick={handleAGITESTLogin}
            disabled={isLoading}
            className="w-full bg-cosmic-500 hover:bg-cosmic-600 disabled:opacity-50 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
            ) : (
              <User className="w-4 h-4" />
            )}
            Login as AGITEST (Admin)
          </button>
          
          {/* Guest Login */}
          <button
            onClick={handleGuestLogin}
            disabled={isLoading}
            className="w-full bg-nova-500 hover:bg-nova-600 disabled:opacity-50 text-white py-3 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
            ) : (
              <Eye className="w-4 h-4" />
            )}
            Login as TestGuest (Persistent)
          </button>
        </div>
        
        <div className="mt-4 pt-4 border-t border-white/10">
          <div className="text-xs text-white/60 space-y-1">
            <div><strong>AGITEST:</strong> Full admin access, 3 saved themes, all features</div>
            <div><strong>TestGuest:</strong> Guest access, theme customization, persistent data</div>
          </div>
        </div>
      </div>

      {/* Manual Login with Auto-Fill */}
      <div className="bg-space-800/50 border border-white/10 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-white mb-4">Manual Login</h3>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-white/80 mb-2">
              Username/Email
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter username or email"
                className="flex-1 bg-space-700 border border-white/20 rounded-lg px-3 py-2 text-white placeholder-white/40 focus:outline-none focus:border-cosmic-400"
              />
              <button
                onClick={fillAGITESTCredentials}
                className="bg-space-600 hover:bg-space-500 text-white px-3 py-2 rounded-lg text-sm transition-colors"
                title="Auto-fill AGITEST credentials"
              >
                Auto-fill
              </button>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-white/80 mb-2">
              Password
            </label>
            <div className="relative">
              <input
                type={showPassword ? 'text' : 'password'}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter password"
                className="w-full bg-space-700 border border-white/20 rounded-lg px-3 py-2 pr-10 text-white placeholder-white/40 focus:outline-none focus:border-cosmic-400"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/40 hover:text-white/80"
              >
                {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
            </div>
          </div>
          
          <button
            onClick={() => login(username, password)}
            disabled={!username || !password || isLoading}
            className="w-full bg-neural-500 hover:bg-neural-600 disabled:opacity-50 text-white py-3 px-4 rounded-lg font-medium transition-colors"
          >
            Login
          </button>
        </div>
      </div>

      {/* Test Data Management */}
      <div className="bg-space-800/50 border border-yellow-500/30 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="text-sm font-medium text-white">Test Data Management</h4>
            <p className="text-xs text-white/60">Reset saved themes and preferences</p>
          </div>
          <button
            onClick={handleResetTestData}
            className="bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-300 px-3 py-2 rounded-lg text-sm transition-colors flex items-center gap-2"
          >
            <RefreshCw className="w-4 h-4" />
            Reset
          </button>
        </div>
      </div>

      {/* Test Account Info */}
      <div className="bg-space-800/30 border border-white/10 rounded-lg p-4">
        <h4 className="text-sm font-medium text-white mb-2">Test Account Details</h4>
        <div className="text-xs text-white/60 space-y-1">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="font-medium text-cosmic-300">AGITEST</div>
              <div>Username: AGITEST</div>
              <div>Password: AGITEST123!</div>
              <div>Type: Admin</div>
              <div>Themes: 3 saved</div>
            </div>
            <div>
              <div className="font-medium text-nova-300">TestGuest</div>
              <div>Username: TestGuest</div>
              <div>Password: None</div>
              <div>Type: Guest</div>
              <div>Themes: 1 saved</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestLoginPanel;
