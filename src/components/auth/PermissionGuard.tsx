'use client';

import React, { ReactNode } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Shield, Lock, AlertTriangle } from 'lucide-react';

interface PermissionGuardProps {
  children: ReactNode;
  permission?: string;
  permissions?: string[];
  requireAll?: boolean; // If true, requires ALL permissions. If false, requires ANY permission
  fallback?: ReactNode;
  showError?: boolean;
  className?: string;
}

export default function PermissionGuard({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback,
  showError = true,
  className = ''
}: PermissionGuardProps) {
  const { user, isAuthenticated, hasPermission, hasAnyPermission, hasAllPermissions } = useAuth();

  // If not authenticated, show login required message
  if (!isAuthenticated || !user) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (!showError) {
      return null;
    }

    return (
      <div className={`p-6 ${className}`}>
        <div className="neo-card p-8 text-center">
          <Lock className="w-12 h-12 text-cosmic-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold theme-text-primary mb-2">
            Authentication Required
          </h3>
          <p className="theme-text-secondary mb-4">
            Please log in to access this feature.
          </p>
          <button className="neo-button neo-button-primary">
            Log In
          </button>
        </div>
      </div>
    );
  }

  // Build permission list
  const permissionList = permission ? [permission] : permissions;

  if (permissionList.length === 0) {
    // No permissions required, show content
    return <>{children}</>;
  }

  // Check permissions
  const hasRequiredPermissions = requireAll 
    ? hasAllPermissions(permissionList)
    : hasAnyPermission(permissionList);

  if (hasRequiredPermissions) {
    return <>{children}</>;
  }

  // Permission denied
  if (fallback) {
    return <>{fallback}</>;
  }

  if (!showError) {
    return null;
  }

  return (
    <div className={`p-6 ${className}`}>
      <div className="neo-card p-8 text-center">
        <Shield className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold theme-text-primary mb-2">
          Insufficient Permissions
        </h3>
        <p className="theme-text-secondary mb-4">
          You don't have the required permissions to access this feature.
        </p>
        
        {/* Show required permissions */}
        <div className="bg-gradient-to-r from-yellow-50/50 to-orange-50/50 border border-yellow-200/30 rounded-lg p-4 mb-4">
          <h4 className="text-sm font-semibold theme-text-primary mb-2">Required Permissions:</h4>
          <ul className="text-xs theme-text-secondary space-y-1">
            {permissionList.map((perm, index) => (
              <li key={index} className="flex items-center gap-2">
                <AlertTriangle className="w-3 h-3 text-yellow-500" />
                {formatPermissionName(perm)}
              </li>
            ))}
          </ul>
          {requireAll && permissionList.length > 1 && (
            <p className="text-xs theme-text-tertiary mt-2">
              All permissions above are required.
            </p>
          )}
        </div>

        {/* Show current user permissions */}
        <div className="bg-gradient-to-r from-cosmic-50/50 to-nova-50/50 border border-cosmic-200/30 rounded-lg p-4">
          <h4 className="text-sm font-semibold theme-text-primary mb-2">Your Current Role:</h4>
          <div className="text-sm theme-text-secondary">
            <div className="font-medium text-cosmic-600">{formatRoleName(user.role)}</div>
            <div className="text-xs mt-1">
              {user.permissions.length} permission{user.permissions.length !== 1 ? 's' : ''} granted
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

// Helper function to format permission names for display
function formatPermissionName(permission: string): string {
  return permission
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Helper function to format role names for display
function formatRoleName(role: string): string {
  const roleNames = {
    observer: 'Basic Observer',
    admin_observer: 'Admin Observer',
    developer_observer: 'Developer Observer'
  };
  
  return roleNames[role as keyof typeof roleNames] || role;
}

// Convenience components for common permission checks
export function ObserverOnly({ children, fallback, showError = true }: { 
  children: ReactNode; 
  fallback?: ReactNode; 
  showError?: boolean; 
}) {
  return (
    <PermissionGuard
      permission="observe_ai_decisions"
      fallback={fallback}
      showError={showError}
    >
      {children}
    </PermissionGuard>
  );
}

export function AdminObserverOnly({ children, fallback, showError = true }: { 
  children: ReactNode; 
  fallback?: ReactNode; 
  showError?: boolean; 
}) {
  return (
    <PermissionGuard
      permission="export_observation_data"
      fallback={fallback}
      showError={showError}
    >
      {children}
    </PermissionGuard>
  );
}

export function DeveloperObserverOnly({ children, fallback, showError = true }: { 
  children: ReactNode; 
  fallback?: ReactNode; 
  showError?: boolean; 
}) {
  return (
    <PermissionGuard
      permission="debug_logs"
      fallback={fallback}
      showError={showError}
    >
      {children}
    </PermissionGuard>
  );
}
