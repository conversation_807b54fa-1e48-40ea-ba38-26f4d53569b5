'use client';

import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Eye, EyeOff, Lock, Mail, AlertCircle, CheckCircle } from 'lucide-react';

interface ObservationLoginProps {
  onSuccess?: () => void;
  className?: string;
}

export default function ObservationLogin({ onSuccess, className = '' }: ObservationLoginProps) {
  const { login, isLoading } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [showDemoCredentials, setShowDemoCredentials] = useState(true);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!formData.email || !formData.password) {
      setError('Please enter both email and password');
      return;
    }

    const result = await login(formData);
    
    if (result.success) {
      onSuccess?.();
    } else {
      setError(result.error || 'Login failed');
    }
  };

  const handleDemoLogin = (role: 'observer' | 'admin_observer' | 'developer_observer') => {
    const credentials = {
      observer: { email: '<EMAIL>', password: 'demo123' },
      admin_observer: { email: '<EMAIL>', password: 'admin123' },
      developer_observer: { email: '<EMAIL>', password: 'dev123' }
    };

    setFormData(credentials[role]);
  };

  return (
    <div className={`max-w-md mx-auto ${className}`}>
      <div className="neo-card p-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <Lock className="w-8 h-8 text-white" />
          </div>
          <h2 className="text-2xl font-bold theme-text-primary mb-2">
            Observation Access
          </h2>
          <p className="theme-text-secondary text-sm">
            Secure access to autonomous AI monitoring platform
          </p>
        </div>

        {/* Demo Credentials */}
        {showDemoCredentials && (
          <div className="bg-gradient-to-r from-cosmic-50/50 to-nova-50/50 border border-cosmic-200/30 rounded-lg p-4 mb-6">
            <div className="flex items-center justify-between mb-3">
              <h3 className="text-sm font-semibold theme-text-primary">Demo Accounts</h3>
              <button
                onClick={() => setShowDemoCredentials(false)}
                className="text-xs theme-text-tertiary hover:theme-text-secondary"
              >
                Hide
              </button>
            </div>
            <div className="space-y-2">
              <button
                onClick={() => handleDemoLogin('observer')}
                className="w-full text-left p-2 rounded bg-white/50 hover:bg-white/70 transition-colors"
              >
                <div className="text-xs font-medium text-cosmic-700">Basic Observer</div>
                <div className="text-xs text-cosmic-600"><EMAIL> / demo123</div>
              </button>
              <button
                onClick={() => handleDemoLogin('admin_observer')}
                className="w-full text-left p-2 rounded bg-white/50 hover:bg-white/70 transition-colors"
              >
                <div className="text-xs font-medium text-nova-700">Admin Observer</div>
                <div className="text-xs text-nova-600"><EMAIL> / admin123</div>
              </button>
              <button
                onClick={() => handleDemoLogin('developer_observer')}
                className="w-full text-left p-2 rounded bg-white/50 hover:bg-white/70 transition-colors"
              >
                <div className="text-xs font-medium text-neural-700">Developer Observer</div>
                <div className="text-xs text-neural-600"><EMAIL> / dev123</div>
              </button>
            </div>
          </div>
        )}

        {/* Login Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium theme-text-primary mb-2">
              Email Address
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 theme-text-tertiary" />
              <input
                type="email"
                id="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cosmic-500 focus:border-cosmic-500 theme-bg-primary theme-text-primary"
                placeholder="Enter your email"
                disabled={isLoading}
              />
            </div>
          </div>

          {/* Password Field */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium theme-text-primary mb-2">
              Password
            </label>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 theme-text-tertiary" />
              <input
                type={showPassword ? 'text' : 'password'}
                id="password"
                value={formData.password}
                onChange={(e) => setFormData(prev => ({ ...prev, password: e.target.value }))}
                className="w-full pl-10 pr-12 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-cosmic-500 focus:border-cosmic-500 theme-bg-primary theme-text-primary"
                placeholder="Enter your password"
                disabled={isLoading}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 theme-text-tertiary hover:theme-text-secondary"
                disabled={isLoading}
              >
                {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
              <AlertCircle className="w-5 h-5 text-red-500 flex-shrink-0" />
              <span className="text-sm text-red-700">{error}</span>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading || !formData.email || !formData.password}
            className="w-full py-3 px-4 bg-gradient-to-r from-cosmic-500 to-nova-500 text-white font-medium rounded-lg hover:from-cosmic-600 hover:to-nova-600 focus:ring-2 focus:ring-cosmic-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
          >
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                Authenticating...
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2">
                <CheckCircle className="w-5 h-5" />
                Access Observation Platform
              </div>
            )}
          </button>
        </form>

        {/* Security Notice */}
        <div className="mt-6 p-4 bg-gradient-to-r from-quantum-50/50 to-aura-50/50 border border-quantum-200/30 rounded-lg">
          <h4 className="text-sm font-semibold theme-text-primary mb-2">🔒 Observation-Only Access</h4>
          <ul className="text-xs theme-text-secondary space-y-1">
            <li>• View AI decisions and agent status</li>
            <li>• Monitor system performance and health</li>
            <li>• Export observation data (admin+)</li>
            <li>• No control or intervention capabilities</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
