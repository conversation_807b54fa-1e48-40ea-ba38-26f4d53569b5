/**
 * Team Collaboration Interface UI - Following Sequential Development Mandate  
 * Multi-user collaboration interface (Final UI Component)
 *
 * Features:
 * - Multi-user collaboration interface
 * - Team management components
 * - Integration with navigation organization
 * - Real-time team communication
 * - Collaborative workspace management
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Card } from '@/shared/components/Card';
import { Button } from '@/shared/components/Button';
import { 
  Users,
  UserPlus,
  MessageSquare,
  Video,
  Phone,
  Settings,
  Crown,
  Shield,
  Eye,
  Edit,
  Share2,
  Bell,
  BellOff,
  Clock,
  MapPin,
  Calendar,
  FileText,
  Folder,
  GitBranch,
  Activity,
  CheckCircle,
  AlertCircle,
  Circle,
  Send,
  Paperclip,
  Smile,
  MoreHorizontal,
  Search,
  Filter,
  Plus,
  X,
  Globe,
  Lock,
  Unlock,
  Star,
  Hash,
  UserCheck,
  UserX
} from 'lucide-react';

export interface TeamMember {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member' | 'viewer';
  status: 'online' | 'away' | 'busy' | 'offline';
  lastSeen: string;
  permissions: {
    canEdit: boolean;
    canInvite: boolean;
    canManage: boolean;
    canDelete: boolean;
  };
  currentProject?: string;
  currentPage?: string;
}

interface CollaborationSession {
  id: string;
  type: 'project' | 'page' | 'chat' | 'meeting';
  title: string;
  participants: string[];
  startTime: string;
  duration?: number;
  isActive: boolean;
  lastActivity: string;
}

interface TeamMessage {
  id: string;
  sender: TeamMember;
  content: string;
  timestamp: string;
  type: 'text' | 'file' | 'system' | 'reaction';
  attachments?: Array<{ name: string; type: string; url: string }>;
  reactions?: Array<{ emoji: string; users: string[]; count: number }>;
  threadReplies?: number;
}

interface TeamProject {
  id: string;
  name: string;
  description: string;
  members: string[];
  visibility: 'public' | 'private' | 'team';
  status: 'active' | 'paused' | 'completed';
  lastModified: string;
  progress: number;
  dueDate?: string;
}

interface TeamCollaborationInterfaceProps {
  className?: string;
  currentUser?: TeamMember;
  teamId?: string;
}

export default function TeamCollaborationInterface({ 
  className = '',
  currentUser,
  teamId = 'default' 
}: TeamCollaborationInterfaceProps) {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [activeSessions, setActiveSessions] = useState<CollaborationSession[]>([]);
  const [teamMessages, setTeamMessages] = useState<TeamMessage[]>([]);
  const [teamProjects, setTeamProjects] = useState<TeamProject[]>([]);
  const [activeTab, setActiveTab] = useState<'overview' | 'members' | 'chat' | 'projects' | 'sessions'>('overview');
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState('');
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [selectedRole, setSelectedRole] = useState<TeamMember['role']>('member');

  // Real-time updates
  const [lastUpdate, setLastUpdate] = useState<string>('');

  // Load collaboration data
  useEffect(() => {
    const loadCollaborationData = async () => {
      try {
        // Generate mock collaboration data
        const mockMembers: TeamMember[] = [
          {
            id: 'user-1',
            name: 'Sebastian Markiewicz',
            email: '<EMAIL>',
            role: 'owner',
            status: 'online',
            lastSeen: new Date().toISOString(),
            permissions: {
              canEdit: true,
              canInvite: true,
              canManage: true,
              canDelete: true
            },
            currentProject: 'CreAItive Platform',
            currentPage: 'Team Collaboration Interface'
          },
          {
            id: 'user-2',
            name: 'AI Assistant Alpha',
            email: '<EMAIL>',
            role: 'admin',
            status: 'online',
            lastSeen: new Date(Date.now() - 300000).toISOString(),
            permissions: {
              canEdit: true,
              canInvite: true,
              canManage: true,
              canDelete: false
            },
            currentProject: 'Agent Development',
            currentPage: 'Model Management'
          },
          {
            id: 'user-3',
            name: 'DevStral Coordinator',
            email: '<EMAIL>',
            role: 'member',
            status: 'busy',
            lastSeen: new Date(Date.now() - 120000).toISOString(),
            permissions: {
              canEdit: true,
              canInvite: false,
              canManage: false,
              canDelete: false
            },
            currentProject: 'UI Development',
            currentPage: 'Voice Interface'
          },
          {
            id: 'user-4',
            name: 'R1 Analyst',
            email: '<EMAIL>',
            role: 'member',
            status: 'away',
            lastSeen: new Date(Date.now() - 1800000).toISOString(),
            permissions: {
              canEdit: false,
              canInvite: false,
              canManage: false,
              canDelete: false
            }
          }
        ];

        const mockSessions: CollaborationSession[] = [
          {
            id: 'session-1',
            type: 'project',
            title: 'UI Component Development Sprint',
            participants: ['user-1', 'user-2', 'user-3'],
            startTime: new Date(Date.now() - 3600000).toISOString(),
            duration: 120,
            isActive: true,
            lastActivity: new Date(Date.now() - 180000).toISOString()
          },
          {
            id: 'session-2',
            type: 'chat',
            title: 'Daily Standup',
            participants: ['user-1', 'user-2'],
            startTime: new Date(Date.now() - 900000).toISOString(),
            isActive: false,
            lastActivity: new Date(Date.now() - 900000).toISOString()
          }
        ];

        const mockMessages: TeamMessage[] = [
          {
            id: 'msg-1',
            sender: mockMembers[1],
            content: 'Team Collaboration Interface implementation is looking great! The real-time updates are working perfectly.',
            timestamp: new Date(Date.now() - 1800000).toISOString(),
            type: 'text',
            reactions: [
              { emoji: '👍', users: ['user-1', 'user-3'], count: 2 },
              { emoji: '🎉', users: ['user-1'], count: 1 }
            ]
          },
          {
            id: 'msg-2',
            sender: mockMembers[2],
            content: 'All 6 UI components are now complete. We achieved 100% of Track 10 UI Implementation goals!',
            timestamp: new Date(Date.now() - 900000).toISOString(),
            type: 'text',
            reactions: [
              { emoji: '🚀', users: ['user-1', 'user-2'], count: 2 }
            ]
          },
          {
            id: 'msg-3',
            sender: mockMembers[0],
            content: 'Excellent work everyone! The Sequential Development Mandate approach has been incredibly effective.',
            timestamp: new Date(Date.now() - 300000).toISOString(),
            type: 'text'
          }
        ];

        const mockProjects: TeamProject[] = [
          {
            id: 'project-1',
            name: 'CreAItive Platform',
            description: 'Complete intelligent creative platform with 28-agent ecosystem',
            members: ['user-1', 'user-2', 'user-3', 'user-4'],
            visibility: 'team',
            status: 'active',
            lastModified: new Date().toISOString(),
            progress: 95,
            dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 'project-2',
            name: 'Track 10 UI Implementation',
            description: '6 major UI components for complete user interface',
            members: ['user-1', 'user-2', 'user-3'],
            visibility: 'team',
            status: 'completed',
            lastModified: new Date().toISOString(),
            progress: 100
          }
        ];

        setTeamMembers(mockMembers);
        setActiveSessions(mockSessions);
        setTeamMessages(mockMessages);
        setTeamProjects(mockProjects);
        setLastUpdate(new Date().toLocaleTimeString());
        
      } catch (error) {
        console.error('Failed to load collaboration data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadCollaborationData();
    
    // Set up real-time updates
    const interval = setInterval(() => {
      setLastUpdate(new Date().toLocaleTimeString());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: TeamMember['status']) => {
    switch (status) {
      case 'online': return 'bg-green-400';
      case 'away': return 'bg-yellow-400';
      case 'busy': return 'bg-red-400';
      case 'offline': return 'bg-gray-400';
    }
  };

  const getStatusIcon = (status: TeamMember['status']) => {
    switch (status) {
      case 'online': return <CheckCircle className="w-3 h-3 text-green-400" />;
      case 'away': return <Clock className="w-3 h-3 text-yellow-400" />;
      case 'busy': return <AlertCircle className="w-3 h-3 text-red-400" />;
      case 'offline': return <Circle className="w-3 h-3 text-gray-400" />;
    }
  };

  const getRoleIcon = (role: TeamMember['role']) => {
    switch (role) {
      case 'owner': return <Crown className="w-4 h-4 text-yellow-400" />;
      case 'admin': return <Shield className="w-4 h-4 text-blue-400" />;
      case 'member': return <UserCheck className="w-4 h-4 text-green-400" />;
      case 'viewer': return <Eye className="w-4 h-4 text-gray-400" />;
    }
  };

  const sendMessage = () => {
    if (newMessage.trim() && currentUser) {
      const newMsg: TeamMessage = {
        id: `msg-${Date.now()}`,
        sender: currentUser,
        content: newMessage.trim(),
        timestamp: new Date().toISOString(),
        type: 'text'
      };
      
      setTeamMessages(prev => [...prev, newMsg]);
      setNewMessage('');
    }
  };

  const inviteTeamMember = () => {
    if (inviteEmail && selectedRole) {
      console.log(`Inviting ${inviteEmail} as ${selectedRole}`);
      setInviteEmail('');
      setShowInviteModal(false);
    }
  };

  const formatLastSeen = (lastSeen: string) => {
    const diff = Date.now() - new Date(lastSeen).getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Users className="w-8 h-8 animate-pulse text-cosmic-400 mx-auto mb-2" />
          <p className="text-stardust-300">Loading collaboration...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gradient-cosmic mb-1">
            Team Collaboration
          </h2>
          <p className="text-stardust-400">
            Multi-user collaboration and team management
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <span className="text-sm text-stardust-500">
            {teamMembers.filter(m => m.status === 'online').length} online
          </span>
          <Button variant="primary" onClick={() => setShowInviteModal(true)}>
            <UserPlus className="w-4 h-4 mr-2" />
            Invite
          </Button>
        </div>
      </div>

      {/* Team Status Overview */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-green-900/40 to-green-800/40">
          <div className="text-2xl font-bold text-green-300">
            {teamMembers.filter(m => m.status === 'online').length}
          </div>
          <div className="text-sm text-stardust-400">Online Now</div>
        </Card>
        <Card className="p-4 bg-gradient-to-br from-blue-900/40 to-blue-800/40">
          <div className="text-2xl font-bold text-blue-300">{activeSessions.filter(s => s.isActive).length}</div>
          <div className="text-sm text-stardust-400">Active Sessions</div>
        </Card>
        <Card className="p-4 bg-gradient-to-br from-purple-900/40 to-purple-800/40">
          <div className="text-2xl font-bold text-purple-300">{teamProjects.filter(p => p.status === 'active').length}</div>
          <div className="text-sm text-stardust-400">Active Projects</div>
        </Card>
        <Card className="p-4 bg-gradient-to-br from-cosmic-900/40 to-cosmic-800/40">
          <div className="text-2xl font-bold text-cosmic-300">{teamMessages.length}</div>
          <div className="text-sm text-stardust-400">Team Messages</div>
        </Card>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-space-800 p-1 rounded-lg">
        {[
          { id: 'overview', label: 'Overview', icon: Activity },
          { id: 'members', label: 'Members', icon: Users },
          { id: 'chat', label: 'Team Chat', icon: MessageSquare },
          { id: 'projects', label: 'Projects', icon: Folder },
          { id: 'sessions', label: 'Sessions', icon: Video }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 flex items-center justify-center gap-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === tab.id
                ? 'bg-cosmic-600 text-white'
                : 'text-stardust-400 hover:text-stardust-300'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            <span className="hidden sm:inline">{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card className="p-6">
            <h3 className="text-lg font-semibold text-stardust-200 mb-4">Recent Activity</h3>
            <div className="space-y-3">
              {teamMessages.slice(-3).map(message => (
                <div key={message.id} className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-cosmic-600 rounded-full flex items-center justify-center">
                    <span className="text-xs font-medium text-white">
                      {message.sender.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm text-stardust-200">{message.content}</div>
                    <div className="text-xs text-stardust-500">
                      {message.sender.name} • {formatLastSeen(message.timestamp)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Card className="p-6">
            <h3 className="text-lg font-semibold text-stardust-200 mb-4">Active Team Members</h3>
            <div className="space-y-3">
              {teamMembers.filter(m => m.status === 'online' || m.status === 'busy').map(member => (
                <div key={member.id} className="flex items-center gap-3">
                  <div className="relative">
                    <div className="w-8 h-8 bg-cosmic-600 rounded-full flex items-center justify-center">
                      <span className="text-xs font-medium text-white">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-space-900 ${getStatusColor(member.status)}`}></div>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm text-stardust-200">{member.name}</div>
                    <div className="text-xs text-stardust-500">
                      {member.currentProject || 'Available'}
                    </div>
                  </div>
                  {getRoleIcon(member.role)}
                </div>
              ))}
            </div>
          </Card>
        </div>
      )}

      {activeTab === 'members' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {teamMembers.map(member => (
            <Card key={member.id} className="p-4">
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="relative">
                    <div className="w-10 h-10 bg-cosmic-600 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-white">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </span>
                    </div>
                    <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-space-900 ${getStatusColor(member.status)}`}></div>
                  </div>
                  <div>
                    <h3 className="font-medium text-stardust-200">{member.name}</h3>
                    <div className="text-xs text-stardust-500">{member.email}</div>
                  </div>
                </div>
                {getRoleIcon(member.role)}
              </div>

              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-stardust-400">Status</span>
                  <div className="flex items-center gap-1">
                    {getStatusIcon(member.status)}
                    <span className="capitalize text-stardust-300">{member.status}</span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-stardust-400">Last seen</span>
                  <span className="text-stardust-300">{formatLastSeen(member.lastSeen)}</span>
                </div>
                {member.currentProject && (
                  <div className="flex justify-between">
                    <span className="text-stardust-400">Working on</span>
                    <span className="text-stardust-300 text-xs">{member.currentProject}</span>
                  </div>
                )}
              </div>

              <div className="flex gap-2 mt-4">
                <Button variant="outline" size="sm">
                  <MessageSquare className="w-4 h-4 mr-1" />
                  Chat
                </Button>
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="w-4 h-4" />
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}

      {activeTab === 'chat' && (
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-stardust-200">Team Chat</h3>
            <Button variant="outline" size="sm">
              <Hash className="w-4 h-4 mr-1" />
              General
            </Button>
          </div>

          <div className="space-y-4 mb-6 max-h-96 overflow-y-auto">
            {teamMessages.map(message => (
              <div key={message.id} className="flex items-start gap-3">
                <div className="w-8 h-8 bg-cosmic-600 rounded-full flex items-center justify-center">
                  <span className="text-xs font-medium text-white">
                    {message.sender.name.split(' ').map(n => n[0]).join('')}
                  </span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-medium text-stardust-200">{message.sender.name}</span>
                    <span className="text-xs text-stardust-500">{formatLastSeen(message.timestamp)}</span>
                  </div>
                  <div className="text-sm text-stardust-300">{message.content}</div>
                  {message.reactions && (
                    <div className="flex gap-1 mt-2">
                      {message.reactions.map((reaction, i) => (
                        <button
                          key={i}
                          className="flex items-center gap-1 px-2 py-1 bg-space-700 rounded-full text-xs hover:bg-space-600"
                        >
                          <span>{reaction.emoji}</span>
                          <span className="text-stardust-400">{reaction.count}</span>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="flex gap-2">
            <input
              type="text"
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
              placeholder="Type a message..."
              className="flex-1 px-4 py-2 bg-space-800 border border-cosmic-500/30 rounded-lg text-stardust-200 focus:border-cosmic-400 focus:outline-none"
            />
            <Button variant="outline">
              <Paperclip className="w-4 h-4" />
            </Button>
            <Button variant="outline">
              <Smile className="w-4 h-4" />
            </Button>
            <Button variant="primary" onClick={sendMessage}>
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </Card>
      )}

      {activeTab === 'projects' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {teamProjects.map(project => (
            <Card key={project.id} className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="text-lg font-semibold text-stardust-200">{project.name}</h3>
                  <p className="text-sm text-stardust-400">{project.description}</p>
                </div>
                <div className={`px-2 py-1 rounded text-xs ${
                  project.status === 'completed' ? 'bg-green-900/30 text-green-400' :
                  project.status === 'active' ? 'bg-blue-900/30 text-blue-400' :
                  'bg-yellow-900/30 text-yellow-400'
                }`}>
                  {project.status}
                </div>
              </div>

              <div className="space-y-3 mb-4">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-stardust-400">Progress</span>
                    <span className="text-stardust-300">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-space-700 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-cosmic-500 to-nova-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-stardust-400">Team</span>
                  <span className="text-stardust-300">{project.members.length} members</span>
                </div>

                <div className="flex justify-between text-sm">
                  <span className="text-stardust-400">Last modified</span>
                  <span className="text-stardust-300">{formatLastSeen(project.lastModified)}</span>
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="primary" size="sm">
                  <Eye className="w-4 h-4 mr-1" />
                  View
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="w-4 h-4 mr-1" />
                  Share
                </Button>
              </div>
            </Card>
          ))}
        </div>
      )}

      {activeTab === 'sessions' && (
        <div className="space-y-4">
          {activeSessions.map(session => (
            <Card key={session.id} className="p-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className={`w-3 h-3 rounded-full ${session.isActive ? 'bg-green-400 animate-pulse' : 'bg-gray-400'}`}></div>
                  <div>
                    <h3 className="text-lg font-semibold text-stardust-200">{session.title}</h3>
                    <div className="flex items-center gap-4 text-sm text-stardust-400">
                      <span>{session.participants.length} participants</span>
                      <span>{session.type}</span>
                      <span>Started {formatLastSeen(session.startTime)}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  {session.isActive ? (
                    <>
                      <Button variant="primary" size="sm">
                        <Video className="w-4 h-4 mr-1" />
                        Join
                      </Button>
                      <Button variant="outline" size="sm">
                        <Phone className="w-4 h-4" />
                      </Button>
                    </>
                  ) : (
                    <Button variant="outline" size="sm">
                      <Activity className="w-4 h-4 mr-1" />
                      Review
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <Card className="max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-bold text-stardust-200">Invite Team Member</h3>
                <Button variant="outline" onClick={() => setShowInviteModal(false)}>
                  <X className="w-4 h-4" />
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-stardust-300 mb-2">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={inviteEmail}
                    onChange={(e) => setInviteEmail(e.target.value)}
                    placeholder="<EMAIL>"
                    className="w-full px-3 py-2 bg-space-800 border border-cosmic-500/30 rounded-lg text-stardust-200 focus:border-cosmic-400 focus:outline-none"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-stardust-300 mb-2">
                    Role
                  </label>
                  <select
                    value={selectedRole}
                    onChange={(e) => setSelectedRole(e.target.value as TeamMember['role'])}
                    className="w-full px-3 py-2 bg-space-800 border border-cosmic-500/30 rounded-lg text-stardust-200 focus:border-cosmic-400 focus:outline-none"
                  >
                    <option value="viewer">Viewer - Read only access</option>
                    <option value="member">Member - Edit content</option>
                    <option value="admin">Admin - Manage team</option>
                  </select>
                </div>

                <div className="flex gap-3 pt-4">
                  <Button variant="outline" onClick={() => setShowInviteModal(false)}>
                    Cancel
                  </Button>
                  <Button variant="primary" onClick={inviteTeamMember}>
                    Send Invitation
                  </Button>
                </div>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  );
} 