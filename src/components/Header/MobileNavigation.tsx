import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import { useRouter } from 'next/navigation';
import { 
  Brain, Rocket, Zap, Users, Palette, Sparkles, Home, 
  Search, ChevronRight, Grid3X3, MessageSquare, Settings,
  BarChart3, Camera, Mic, Monitor, ArrowLeft, AlertTriangle,
  Image, ShoppingCart, Edit, FileText, CheckSquare, User,
  Calendar, MessageSquare as MessageSquareIcon, Shield, HelpCircle,
  Info, LogIn, UserPlus, Key
} from 'lucide-react';
import { NavigationAnalysisIndicator } from '@/components/Navigation/NavigationAnalysisIndicator';

interface MobileNavigationProps {
  isOpen: boolean;
  onClose: () => void;
}

const NAVIGATION_SECTIONS = {
  intelligence: {
    title: '🧠 Intelligence Hub',
    icon: Brain,
    description: 'AI Analytics • Real-time Intelligence',
    href: '/intelligence',
    features: [
      'Real-time AI Analytics',
      'Autonomous Decision Making',
      'AI Model Management',
      'Intelligence Insights'
    ]
  },
  agents: {
    title: '🤖 Agent Hub',
    icon: Users,
    description: '28 Agents • Orchestration • Control',
    href: '/agents',
    features: [
      '28 Autonomous Agents',
      'Agent Orchestration',
      'Swarm Intelligence',
      'Real-time Coordination'
    ]
  },
  creative: {
    title: '🎨 Creative Hub',
    icon: Palette,
    description: 'AI Canvas • Creative Tools • Gallery',
    href: '/creative',
    features: [
      'AI-Powered Canvas',
      'Creative Asset Gallery',
      'Voice Interface',
      'Global Marketplace'
    ]
  },
  dashboard: {
    title: '📊 Dashboard Hub',
    icon: BarChart3,
    description: 'Workspace • Monitoring • System Control',
    href: '/dashboard',
    features: [
      'Personal Workspace',
      'System Monitoring',
      'Performance Analytics',
      'Project Management'
    ]
  }
};

export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  isOpen,
  onClose
}) => {
  const router = useRouter();
  const [currentView, setCurrentView] = useState<'main' | keyof typeof NAVIGATION_SECTIONS>('main');
  const [searchTerm, setSearchTerm] = useState('');

  // Filter hubs based on search
  const getFilteredHubs = () => {
    if (!searchTerm) return [];

    return Object.values(NAVIGATION_SECTIONS).filter(hub =>
      hub.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hub.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hub.features.some(feature => feature.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const handleHubSelect = (hubHref: string) => {
    onClose();
    router.push(hubHref);
  };

  const handleBackToMain = () => {
    setCurrentView('main');
    setSearchTerm('');
  };

  const filteredHubs = getFilteredHubs();

  return (
    <>
      {/* Backdrop */}
      <div
        className={`
          fixed inset-0 z-40 bg-black/60 backdrop-blur-sm
          transition-opacity duration-300
          ${isOpen 
            ? 'opacity-100 pointer-events-auto' 
            : 'opacity-0 pointer-events-none'
          }
        `}
        onClick={onClose}
        aria-hidden="true"
      />

      {/* Navigation Panel */}
      <nav
        className={`
          fixed top-0 right-0 h-full w-80 max-w-[85vw] z-50
          bg-space-900/95 backdrop-blur-xl border-l border-cosmic-500/20
          transform transition-transform duration-300 ease-out
          ${isOpen 
            ? 'translate-x-0' 
            : 'translate-x-full'
          }
        `}
        aria-label="Mobile navigation"
      >
        {/* Header */}
        <div className="p-6 border-b border-cosmic-500/20">
          <div className="flex items-center gap-3 mb-4">
            {currentView !== 'main' && (
              <button
                onClick={handleBackToMain}
                className="p-2 -ml-2 rounded-lg hover:bg-cosmic-500/10 transition-colors"
              >
                <ArrowLeft className="w-5 h-5 text-white/70" />
              </button>
            )}
            <div className="w-8 h-8 bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">C</span>
            </div>
            <span className="text-xl font-bold text-gradient-cosmic">
              CreAItive
            </span>
          </div>

          {/* Search - Enhanced for mobile */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40" />
            <input
              type="text"
              placeholder="Search platform hubs..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="
                w-full pl-10 pr-4 py-3 
                bg-space-800/50 border border-cosmic-500/20 rounded-xl 
                text-white placeholder-white/40 
                focus:outline-none focus:border-cosmic-400
                min-h-[44px]
              "
            />
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 overflow-y-auto">
          {/* Search Results */}
          {searchTerm && (
            <div className="p-6">
              <h3 className="text-sm font-medium text-white/60 mb-3">
                Search Results ({filteredHubs.length})
              </h3>
              <div className="space-y-3">
                {filteredHubs.map((hub) => {
                  const Icon = hub.icon;
                  return (
                    <Link
                      key={hub.href}
                      href={hub.href}
                      onClick={onClose}
                      className="
                        flex items-center gap-4 p-4 rounded-xl
                        text-white hover:text-cosmic-300
                        hover:bg-cosmic-500/10
                        transition-all duration-200
                        active:bg-cosmic-500/20 active:scale-95
                        focus:outline-none focus:ring-2 focus:ring-cosmic-500/50
                        min-h-[56px] w-full
                      "
                    >
                      <div className="flex-shrink-0">
                        <Icon className="w-6 h-6" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-base">{hub.title}</div>
                        <div className="text-sm text-white/60">{hub.description}</div>
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          )}

          {/* Main Navigation Hubs */}
          {!searchTerm && currentView === 'main' && (
            <div className="p-6 space-y-4">
              <h3 className="text-lg font-medium text-white mb-4">Platform Hubs</h3>
              <div className="space-y-3">
                {Object.entries(NAVIGATION_SECTIONS).map(([key, hub]) => {
                  const Icon = hub.icon;
                  return (
                    <Link
                      key={key}
                      href={hub.href}
                      onClick={onClose}
                      className="
                        flex items-center gap-4 p-4 rounded-xl w-full text-left
                        text-white hover:text-cosmic-300
                        hover:bg-cosmic-500/10
                        transition-all duration-200
                        active:bg-cosmic-500/20 active:scale-95
                        focus:outline-none focus:ring-2 focus:ring-cosmic-500/50
                        min-h-[56px] border border-white/10 hover:border-cosmic-500/30
                      "
                    >
                      <div className="flex-shrink-0">
                        <Icon className="w-6 h-6" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-base">{hub.title}</div>
                        <div className="text-sm text-white/60">{hub.description}</div>
                        <div className="flex flex-wrap gap-1 mt-2">
                          {hub.features.slice(0, 2).map((feature, index) => (
                            <span key={index} className="text-xs bg-cosmic-500/10 text-cosmic-300 px-2 py-1 rounded">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                      <ChevronRight className="w-5 h-5 text-white/40" />
                    </Link>
                  );
                })}
              </div>
            </div>
          )}


        </div>

        {/* Footer */}
        <div className="p-6 border-t border-cosmic-500/20 space-y-4">
          {/* Dynamic Navigation Analysis Indicator */}
          <NavigationAnalysisIndicator compact />
          
          <Link
            href="/dashboard"
            onClick={onClose}
            className="
              w-full bg-cosmic-500 hover:bg-cosmic-600 text-white rounded-lg font-medium transition-colors
              flex items-center justify-center gap-2
              min-h-[48px]
            "
          >
            <Rocket className="w-5 h-5" />
            <span>Start Creating</span>
          </Link>
          
          <div className="flex items-center justify-center gap-3 px-4 py-2 rounded-lg bg-emerald-500/10 border border-emerald-500/20">
            <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
            <span className="text-sm text-emerald-300 font-medium">System Active</span>
          </div>
        </div>
      </nav>
    </>
  );
};

MobileNavigation.displayName = 'MobileNavigation'; 