"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { <PERSON>, <PERSON>, Zap, <PERSON>, <PERSON><PERSON>, <PERSON>rk<PERSON>, Menu } from 'lucide-react';
import { HamburgerMenu } from './HamburgerMenu';
import { MobileNavigation } from './MobileNavigation';
import { QuickAccessMenu } from '../Navigation/QuickAccessMenu';

interface HeaderProps {
  className?: string;
  children?: React.ReactNode;
  isAuthenticated?: boolean;
  user?: {
    name: string;
    avatar?: string;
    email?: string;
  };
  navItems?: Array<{
    href: string;
    label: string;
    icon?: React.ReactNode;
  }>;
  variant?: string;
  currentPath?: string;
  onNavigate?: (path: string) => void;
  onSignOut?: () => void;
  enableSearch?: boolean;
  onSearch?: (query: string) => void;
  searchSuggestions?: string[];
  onSearchSubmit?: (query: string) => void;
  enableThemeToggle?: boolean;
  onThemeToggle?: () => void;
  currentTheme?: string;
  enableSkipNav?: boolean;
  searchProvider?: any;
  themeProvider?: {
    theme: string;
    toggleTheme: () => void;
  };
}

export type { HeaderProps };

export const Header: React.FC<HeaderProps> = ({ 
  className,
  children,
  isAuthenticated = false,
  user,
  navItems,
  variant,
  currentPath,
  onNavigate,
  onSignOut,
  enableSearch = false,
  onSearch,
  searchSuggestions = [],
  onSearchSubmit,
  enableThemeToggle = false,
  onThemeToggle,
  currentTheme = 'dark',
  enableSkipNav = false,
  searchProvider,
  themeProvider
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isQuickAccessOpen, setIsQuickAccessOpen] = useState(false);

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <>
      <header className={`fixed top-0 left-0 right-0 z-50 bg-space-900/90 backdrop-blur-xl border-b border-cosmic-500/30 shadow-lg ${className || ''}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 py-3 sm:py-4">
          <div className="flex items-center justify-between">
            {/* Enhanced Logo with Glow Effect */}
            <Link href="/" className="flex items-center gap-2 sm:gap-3 group">
              <div className="relative w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-cosmic-500 to-nova-500 rounded-xl flex items-center justify-center group-hover:scale-105 transition-all duration-300 shadow-lg group-hover:shadow-cosmic-500/50">
                <div className="absolute inset-0 bg-gradient-to-br from-cosmic-400 to-nova-400 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse" />
                <span className="relative text-white font-bold text-lg sm:text-xl">C</span>
              </div>
              <span className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-cosmic-400 via-nebula-400 to-nova-400 bg-clip-text text-transparent group-hover:from-cosmic-300 group-hover:via-nebula-300 group-hover:to-nova-300 transition-all duration-300">
                CreAItive
              </span>
            </Link>

            {/* Desktop Navigation - 4 Strategic Sections */}
            <nav className="hidden lg:flex items-center gap-2">
              {/* Intelligence Section */}
              <Link
                href="/intelligence"
                className="flex items-center gap-2 text-white hover:text-cosmic-300 transition-all duration-300 px-4 py-2 rounded-lg hover:bg-cosmic-500/20 hover:shadow-lg hover:shadow-cosmic-500/25 font-medium relative group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-cosmic-500/0 via-cosmic-500/10 to-cosmic-500/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
                <Brain className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                <span>Intelligence</span>
                <div className="w-2 h-2 bg-cosmic-400 rounded-full animate-pulse group-hover:bg-cosmic-300 group-hover:scale-125 transition-all duration-300"></div>
              </Link>

              {/* Agents Section */}
              <Link
                href="/agents"
                className="flex items-center gap-2 text-white hover:text-nova-300 transition-all duration-300 px-4 py-2 rounded-lg hover:bg-nova-500/20 hover:shadow-lg hover:shadow-nova-500/25 font-medium relative group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-nova-500/0 via-nova-500/10 to-nova-500/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
                <Users className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                <span>Agents</span>
                <div className="w-2 h-2 bg-nova-400 rounded-full animate-pulse group-hover:bg-nova-300 group-hover:scale-125 transition-all duration-300"></div>
              </Link>

              {/* Creative Section */}
              <Link
                href="/creative"
                className="flex items-center gap-2 text-white hover:text-nebula-300 transition-all duration-300 px-4 py-2 rounded-lg hover:bg-nebula-500/20 hover:shadow-lg hover:shadow-nebula-500/25 font-medium relative group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-nebula-500/0 via-nebula-500/10 to-nebula-500/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
                <Palette className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                <span>Creative</span>
                <div className="w-2 h-2 bg-nebula-400 rounded-full animate-pulse group-hover:bg-nebula-300 group-hover:scale-125 transition-all duration-300"></div>
              </Link>

              {/* Dashboard Section */}
              <Link
                href="/dashboard"
                className="flex items-center gap-2 text-white hover:text-aura-300 transition-all duration-300 px-4 py-2 rounded-lg hover:bg-aura-500/20 hover:shadow-lg hover:shadow-aura-500/25 font-medium relative group overflow-hidden"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-aura-500/0 via-aura-500/10 to-aura-500/0 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-700" />
                <Sparkles className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                <span>Dashboard</span>
                <div className="w-2 h-2 bg-aura-400 rounded-full animate-pulse group-hover:bg-aura-300 group-hover:scale-125 transition-all duration-300"></div>
              </Link>
            </nav>

            {/* Right Side Actions */}
            <div className="flex items-center gap-2 sm:gap-4">
              {/* Quick Access Menu - Desktop */}
              <div className="hidden lg:block">
                <button
                  onClick={() => setIsQuickAccessOpen(!isQuickAccessOpen)}
                  className="flex items-center gap-2 px-3 py-2 rounded-lg bg-space-700/50 hover:bg-space-600/50 transition-colors text-white/70 hover:text-white"
                  title="Quick Access Menu"
                >
                  <Menu className="w-4 h-4" />
                  <span className="text-sm">Menu</span>
                </button>
              </div>

              {/* Mobile Menu Button */}
              <button
                onClick={toggleMobileMenu}
                className="lg:hidden p-2 rounded-lg hover:bg-cosmic-500/10 transition-colors"
                aria-label="Open navigation menu"
              >
                <Menu className="w-6 h-6 text-white" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Navigation */}
      <MobileNavigation 
        isOpen={isMobileMenuOpen} 
        onClose={closeMobileMenu} 
      />
      
      {/* Quick Access Menu */}
      <QuickAccessMenu
        isOpen={isQuickAccessOpen}
        onClose={() => setIsQuickAccessOpen(false)}
        currentPath={currentPath}
      />
    </>
  );
};

Header.displayName = 'Header';