interface FooterProps {
  className?: string;
  children?: React.ReactNode;
  onNewsletterSubscribe?: (email: string) => void;
  onSocialClick?: (platform: string) => void;
  enableLanguageSelector?: boolean;
  onLanguageChange?: (language: string) => void;
  currentLanguage?: string;
  enableRegionSelector?: boolean;
  onRegionChange?: (region: string) => void;
  currentRegion?: string;
  socialLinks?: Array<{ platform: string; url: string; }>;
  enableSkipLinks?: boolean;
  customNavItems?: Array<{ href: string; label: string; }>;
  onLinkClick?: (href: string) => void;
  onNotification?: (message: string) => void;
  theme?: string;
  navigationData?: any;
  variant?: string;
  enableNewsletter?: boolean;
  includeStructuredData?: boolean;
}

import React from 'react';
import Link from 'next/link';

export const Footer: React.FC<FooterProps> = ({ 
  className,
  children,
  onNewsletterSubscribe,
  onSocialClick,
  enableLanguageSelector = false,
  onLanguageChange,
  currentLanguage = 'en',
  enableRegionSelector = false,
  onRegionChange,
  currentRegion = 'us',
  socialLinks = [],
  enableSkipLinks = false,
  customNavItems = [],
  onLinkClick,
  onNotification,
  theme,
  navigationData,
  variant,
  enableNewsletter,
  includeStructuredData
}) => {
  return (
    <footer className="neo-component bg-glass border-t border-cosmic-500/20 backdrop-blur-sm mt-20">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-gradient-cosmic rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">C</span>
              </div>
              <span className="text-xl font-orbitron font-bold text-gradient-cosmic">
                CreAItive
              </span>
            </div>
            <p className="text-stardust-400 text-sm leading-relaxed">
              Autonomous AI platform with 28 intelligent agents working together.
              Experience the future where AI and creativity merge seamlessly.
            </p>
          </div>

          {/* Platform Hubs */}
          <div className="space-y-4">
            <h3 className="font-orbitron text-lg text-stardust-200">Platform Hubs</h3>
            <div className="space-y-2">
              <Link href="/intelligence" className="block text-stardust-400 hover:text-cosmic-400 transition-colors text-sm">
                🧠 Intelligence Hub
              </Link>
              <Link href="/agents" className="block text-stardust-400 hover:text-cosmic-400 transition-colors text-sm">
                🤖 Agent Hub
              </Link>
              <Link href="/creative" className="block text-stardust-400 hover:text-cosmic-400 transition-colors text-sm">
                🎨 Creative Hub
              </Link>
              <Link href="/dashboard" className="block text-stardust-400 hover:text-cosmic-400 transition-colors text-sm">
                📊 Dashboard Hub
              </Link>
            </div>
          </div>

          {/* Features */}
          <div className="space-y-4">
            <h3 className="font-orbitron text-lg text-stardust-200">Features</h3>
            <div className="space-y-2">
              <span className="block text-stardust-400 text-sm">Autonomous Development</span>
              <span className="block text-stardust-400 text-sm">AI-Powered Testing</span>
              <span className="block text-stardust-400 text-sm">Security Monitoring</span>
              <span className="block text-stardust-400 text-sm">Performance Optimization</span>
            </div>
          </div>

          {/* Status */}
          <div className="space-y-4">
            <h3 className="font-orbitron text-lg text-stardust-200">System Status</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                <span className="text-stardust-400 text-sm">Agent Swarm: Active</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                <span className="text-stardust-400 text-sm">Security: Protected</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-emerald-400 rounded-full"></div>
                <span className="text-stardust-400 text-sm">Performance: Optimal</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-cosmic-500/20 mt-12 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <p className="text-stardust-400 text-sm">
              © 2025 CreAItive. Powered by autonomous AI agents.
            </p>
            <div className="flex items-center gap-4 text-sm text-stardust-400">
              <span>🤖 28 Agents Active</span>
              <span>⚡ 100% Autonomous</span>
              <span>🛡️ Secure</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}; 

Footer.displayName = 'Footer';