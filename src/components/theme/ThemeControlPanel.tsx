"use client";

import { useState, useEffect } from 'react';
import { useDynamicTheme } from '@/systems/theme/DynamicThemeController';
import { Palette, Wand2, Save, Download, Upload, Sparkles } from 'lucide-react';

/**
 * 🎨 THEME CONTROL PANEL
 * 
 * Complete visual theme control interface:
 * - Real-time color adjustments
 * - Blueprint selection
 * - AI theme generation
 * - Live preview
 * - One-click application
 */

export const ThemeControlPanel = () => {
  const {
    currentSchema,
    blueprints,
    applyTheme,
    applyBlueprint,
    generateAITheme,
    createCustomBlueprint
  } = useDynamicTheme();

  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'colors' | 'blueprints' | 'ai' | 'effects'>('colors');
  const [customColors, setCustomColors] = useState(currentSchema.colors);
  const [aiPrompt, setAiPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  // Update custom colors when schema changes
  useEffect(() => {
    setCustomColors(currentSchema.colors);
  }, [currentSchema]);

  // APPLY CUSTOM COLORS
  const applyCustomColors = () => {
    const newSchema = {
      ...currentSchema,
      name: 'Custom Theme',
      colors: customColors,
      gradients: {
        primary: `linear-gradient(135deg, ${customColors.cosmic} 0%, ${customColors.nova} 100%)`,
        secondary: `linear-gradient(135deg, ${customColors.neural} 0%, ${customColors.aura} 100%)`,
        accent: `linear-gradient(135deg, ${customColors.quantum} 0%, ${customColors.cosmic} 100%)`
      }
    };
    applyTheme(newSchema);
  };

  // GENERATE AI THEME
  const handleAIGeneration = async () => {
    if (!aiPrompt.trim()) return;
    
    setIsGenerating(true);
    try {
      await generateAITheme(aiPrompt);
      setAiPrompt('');
    } catch (error) {
      console.error('AI theme generation failed:', error);
    } finally {
      setIsGenerating(false);
    }
  };

  // SAVE CURRENT THEME AS BLUEPRINT
  const saveAsBlueprint = () => {
    const name = prompt('Enter blueprint name:');
    if (name) {
      createCustomBlueprint({
        name,
        category: 'custom',
        schema: currentSchema,
        preview: `bg-gradient-to-r from-[${currentSchema.colors.cosmic}] to-[${currentSchema.colors.nova}]`
      });
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="bg-cosmic-500/90 hover:bg-cosmic-500 text-white p-2 rounded-lg shadow-lg transition-all duration-300 hover:scale-105 backdrop-blur-sm"
        title="Theme Settings"
      >
        <Palette className="w-5 h-5" />
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 bg-space-800 border border-cosmic-500/30 rounded-xl shadow-2xl">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10">
        <div className="flex items-center gap-2">
          <Palette className="w-5 h-5 text-cosmic-400" />
          <h3 className="text-white font-semibold">Theme Control</h3>
        </div>
        <button
          onClick={() => setIsOpen(false)}
          className="text-white/60 hover:text-white transition-colors"
        >
          ✕
        </button>
      </div>

      {/* Tabs */}
      <div className="flex border-b border-white/10">
        {[
          { id: 'colors', label: 'Colors', icon: '🎨' },
          { id: 'blueprints', label: 'Blueprints', icon: '📋' },
          { id: 'ai', label: 'AI Generate', icon: '🤖' },
          { id: 'effects', label: 'Effects', icon: '✨' }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex-1 p-3 text-xs font-medium transition-colors ${
              activeTab === tab.id
                ? 'text-cosmic-300 border-b-2 border-cosmic-500'
                : 'text-white/60 hover:text-white'
            }`}
          >
            <span className="mr-1">{tab.icon}</span>
            {tab.label}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="p-4 max-h-96 overflow-y-auto">
        {/* Colors Tab */}
        {activeTab === 'colors' && (
          <div className="space-y-4">
            <div className="text-xs text-white/70 mb-3">
              Adjust colors and see changes instantly across the entire platform
            </div>
            
            {Object.entries(customColors).map(([colorName, colorValue]) => (
              <div key={colorName} className="space-y-2">
                <label className="text-xs text-white/80 capitalize">
                  {colorName} ({colorName === 'cosmic' ? 'Intelligence' : 
                             colorName === 'nova' ? 'Success' :
                             colorName === 'quantum' ? 'Processing' :
                             colorName === 'neural' ? 'AI Features' :
                             colorName === 'aura' ? 'Community' :
                             colorName === 'stardust' ? 'Secondary' : 'Background'})
                </label>
                <div className="flex items-center gap-2">
                  <input
                    type="color"
                    value={colorValue}
                    onChange={(e) => setCustomColors(prev => ({
                      ...prev,
                      [colorName]: e.target.value
                    }))}
                    className="w-8 h-8 rounded border border-white/20 cursor-pointer"
                  />
                  <input
                    type="text"
                    value={colorValue}
                    onChange={(e) => setCustomColors(prev => ({
                      ...prev,
                      [colorName]: e.target.value
                    }))}
                    className="flex-1 bg-space-700 text-white text-xs px-2 py-1 rounded border border-white/20"
                  />
                </div>
              </div>
            ))}
            
            <button
              onClick={applyCustomColors}
              className="w-full bg-cosmic-500 hover:bg-cosmic-600 text-white py-2 px-4 rounded text-sm font-medium transition-colors"
            >
              Apply Colors
            </button>
          </div>
        )}

        {/* Blueprints Tab */}
        {activeTab === 'blueprints' && (
          <div className="space-y-3">
            <div className="text-xs text-white/70 mb-3">
              Pre-designed themes for different use cases
            </div>
            
            {blueprints.map(blueprint => (
              <div
                key={blueprint.id}
                className="bg-space-700 rounded-lg p-3 cursor-pointer hover:bg-space-600 transition-colors"
                onClick={() => applyBlueprint(blueprint.id)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h4 className="text-white text-sm font-medium">{blueprint.name}</h4>
                  <span className="text-xs text-white/60 bg-space-600 px-2 py-1 rounded">
                    {blueprint.category}
                  </span>
                </div>
                <p className="text-xs text-white/70 mb-2">{blueprint.schema.description}</p>
                <div className="flex gap-1">
                  {Object.values(blueprint.schema.colors).slice(0, 5).map((color, index) => (
                    <div
                      key={index}
                      className="w-4 h-4 rounded"
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>
            ))}
            
            <button
              onClick={saveAsBlueprint}
              className="w-full bg-neural-500 hover:bg-neural-600 text-white py-2 px-4 rounded text-sm font-medium transition-colors flex items-center justify-center gap-2"
            >
              <Save className="w-4 h-4" />
              Save Current as Blueprint
            </button>
          </div>
        )}

        {/* AI Generate Tab */}
        {activeTab === 'ai' && (
          <div className="space-y-4">
            <div className="text-xs text-white/70 mb-3">
              Describe your desired theme and let AI create it
            </div>
            
            <div className="space-y-2">
              <label className="text-xs text-white/80">Theme Description</label>
              <textarea
                value={aiPrompt}
                onChange={(e) => setAiPrompt(e.target.value)}
                placeholder="e.g., 'Ocean-inspired with blue tones and gentle animations' or 'Dark cyberpunk with neon accents'"
                className="w-full bg-space-700 text-white text-xs px-3 py-2 rounded border border-white/20 resize-none h-20"
              />
            </div>
            
            <button
              onClick={handleAIGeneration}
              disabled={!aiPrompt.trim() || isGenerating}
              className="w-full bg-gradient-to-r from-cosmic-500 to-neural-500 hover:from-cosmic-600 hover:to-neural-600 text-white py-2 px-4 rounded text-sm font-medium transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isGenerating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Wand2 className="w-4 h-4" />
                  Generate AI Theme
                </>
              )}
            </button>
            
            <div className="text-xs text-white/60">
              <strong>Examples:</strong>
              <ul className="mt-1 space-y-1">
                <li>• "Professional blue corporate theme"</li>
                <li>• "Warm sunset colors with glow effects"</li>
                <li>• "Minimal dark theme for focus"</li>
                <li>• "Vibrant gaming theme with particles"</li>
              </ul>
            </div>
          </div>
        )}

        {/* Effects Tab */}
        {activeTab === 'effects' && (
          <div className="space-y-4">
            <div className="text-xs text-white/70 mb-3">
              Control visual effects and animations
            </div>
            
            {[
              { key: 'glow', label: 'Glow Effects', description: 'Subtle glow on interactive elements' },
              { key: 'animations', label: 'Animations', description: 'Smooth transitions and micro-interactions' },
              { key: 'particles', label: 'Particle Effects', description: 'Background particle animations' }
            ].map(effect => (
              <div key={effect.key} className="flex items-center justify-between">
                <div>
                  <div className="text-sm text-white">{effect.label}</div>
                  <div className="text-xs text-white/60">{effect.description}</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={currentSchema.effects[effect.key as keyof typeof currentSchema.effects]}
                    onChange={(e) => {
                      const newSchema = {
                        ...currentSchema,
                        effects: {
                          ...currentSchema.effects,
                          [effect.key]: e.target.checked
                        }
                      };
                      applyTheme(newSchema);
                    }}
                    className="sr-only"
                  />
                  <div className="w-11 h-6 bg-space-600 rounded-full peer peer-checked:bg-cosmic-500 transition-colors">
                    <div className="w-5 h-5 bg-white rounded-full shadow transform transition-transform peer-checked:translate-x-5 translate-x-0.5 translate-y-0.5" />
                  </div>
                </label>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-3 border-t border-white/10 bg-space-900/50">
        <div className="text-xs text-white/60 text-center">
          Current: <span className="text-cosmic-300">{currentSchema.name}</span>
        </div>
      </div>
    </div>
  );
};

export default ThemeControlPanel;
