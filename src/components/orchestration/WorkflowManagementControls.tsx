'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card } from '@/shared/components/Card';
import { Button } from '@/shared/components/Button';

interface WorkflowManagementControlsProps {
  className?: string;
}

interface WorkflowAction {
  id: string;
  name: string;
  description: string;
  icon: string;
  action: () => void;
}

export default function WorkflowManagementControls({ className = '' }: WorkflowManagementControlsProps) {
  const router = useRouter();
  const [isProcessing, setIsProcessing] = useState(false);

  const workflowActions: WorkflowAction[] = [
    {
      id: 'create',
      name: 'Create Workflow',
      description: 'Design a new multi-agent workflow',
      icon: 'M12 4v16m8-8H4',
      action: () => {
        router.push('/tasks/create');
      }
    },
    {
      id: 'pause-all',
      name: 'Pause All',
      description: 'Pause all active workflows',
      icon: 'M10 9v6m4-6v6',
      action: async () => {
        setIsProcessing(true);
        try {
          await fetch('/api/orchestration/pause-all', { method: 'POST' });
          // Handle success
        } catch (error) {
          console.error('Failed to pause workflows:', error);
        } finally {
          setIsProcessing(false);
        }
      }
    },
    {
      id: 'resume-all',
      name: 'Resume All',
      description: 'Resume all paused workflows',
      icon: 'M8 5v14l11-7z',
      action: async () => {
        setIsProcessing(true);
        try {
          await fetch('/api/orchestration/resume-all', { method: 'POST' });
          // Handle success
        } catch (error) {
          console.error('Failed to resume workflows:', error);
        } finally {
          setIsProcessing(false);
        }
      }
    },
    {
      id: 'emergency-stop',
      name: 'Emergency Stop',
      description: 'Immediately stop all workflows',
      icon: 'M6 18L18 6M6 6l12 12',
      action: async () => {
        if (confirm('Are you sure you want to emergency stop all workflows?')) {
          setIsProcessing(true);
          try {
            await fetch('/api/orchestration/emergency-stop', { method: 'POST' });
            // Handle success
          } catch (error) {
            console.error('Failed to emergency stop workflows:', error);
          } finally {
            setIsProcessing(false);
          }
        }
      }
    }
  ];

  const performAction = (action: WorkflowAction) => {
    if (isProcessing) return;
    action.action();
  };

  return (
    <Card className={`p-6 ${className}`}>
      <div className="flex items-center gap-3 mb-6">
        <div className="w-12 h-12 rounded-lg bg-gradient-cosmic flex items-center justify-center">
          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gradient-cosmic">
            Workflow Management
          </h3>
          <p className="text-sm theme-text-secondary">
            Control multi-agent workflow execution
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {workflowActions.map((action) => (
          <Button
            key={action.id}
            variant={action.id === 'emergency-stop' ? 'error' : 'outline'}
            onClick={() => performAction(action)}
            disabled={isProcessing}
            className="p-4 h-auto flex flex-col items-start gap-2 text-left hover:scale-105 transition-transform"
          >
            <div className="flex items-center gap-2 w-full">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={action.icon} />
              </svg>
              <span className="font-semibold">{action.name}</span>
            </div>
            <span className="text-xs opacity-75">{action.description}</span>
          </Button>
        ))}
      </div>

      {isProcessing && (
        <div className="mt-4 p-3 bg-blue-500/20 rounded-lg flex items-center gap-2">
          <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"></div>
          <span className="text-sm text-blue-400">Processing workflow action...</span>
        </div>
      )}
    </Card>
  );
} 