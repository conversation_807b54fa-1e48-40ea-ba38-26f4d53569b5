/**
 * AgentDashboard - Real-time monitoring and control interface for the agent system
 * Enhanced with neo-futuristic design and crisp visual hierarchy
 * 🤖 ENHANCED WITH AUTONOMOUS AI OBSERVATION (Day 8) - Following proven Day 7 methodology
 * ✅ PRESERVES: All existing functionality, tabs, agent discovery, comprehensive monitoring
 * ✅ ADDS: WebSocket autonomous AI streaming, AI decision transparency, autonomous health indicators
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card } from '@/shared/components/Card';
import { Button } from '@/shared/components/Button';
import Container from '@/shared/components/Container';
import { AgentRecommendations } from '../AgentRecommendations/AgentRecommendations';
import SwissWatchPrecisionMonitor from '../agent-monitoring/SwissWatchPrecisionMonitor';
import AgentCard from '@/components/AgentVisual/AgentCard';
import { 
  AGENT_CATEGORIES, 
  determineAgentCategory, 
  getAgentVisuals, 
  getAgentStatusColor,
  getAgentStatusClass,
  formatAgentName
} from '@/utils/agentVisuals';

// 🤖 AUTONOMOUS AI INTEGRATION - Day 8 Enhancement
import { useAutonomousAIStream } from '@/hooks/useAutonomousAIStream';
import { useAppDispatch, useAppSelector } from '@/store/hooks';

interface SystemStatus {
  isRunning: boolean;
  autonomyLevel: number;
  agentCount: number;
  activeAgents: number;
  queuedTasks: number;
  performanceMetrics: {
    totalTasks: number;
    averageSuccessRate: number;
    averageResponseTime: number;
    systemUptime: number;
    lastUpdated: string;
  };
  lastUpdate: string;
}

interface TaskSubmission {
  type: string;
  description: string;
  requiredCapabilities: string[];
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
}

interface TestAgentStatus {
  isRunning: boolean;
  lastTestRun?: string;
  currentCoverage?: number;
  testResults?: {
    passCount: number;
    failCount: number;
    duration: number;
  };
  qualityScore?: number;
}

interface OpsAgentStatus {
  isRunning: boolean;
  lastDeployment?: string;
  infrastructureHealth?: string;
  activeAlerts?: number;
  deploymentMetrics?: {
    total: number;
    successful: number;
    failed: number;
  };
  systemMetrics?: {
    uptime: number;
    responseTime: number;
    errorRate: number;
  };
}

interface HealthMonitorStatus {
  isActive: boolean;
  systemHealth: {
    overallScore: number;
    systemLoad: number;
    responseTime: number;
    errorRate: number;
    uptime: number;
  };
  agentMetrics: {
    [agentId: string]: {
      healthScore: number;
      responseTime: number;
      successRate: number;
      tasksCompleted: number;
      lastOptimized: string;
    };
  };
  improvementSuggestions: Array<{
    id: string;
    type: string;
    priority: string;
    description: string;
    impact: number;
    feasibility: number;
  }>;
}

interface AdvancedModificationStatus {
  autonomyLevel: number;
  activeFeatures: number;
  completedFeatures: number;
  queuedFeatures: number;
  successRate: number;
  averageComplexity: number;
  recentCapabilities: string[];
  featureQueue: Array<{
    id: string;
    type: string;
    priority: string;
    title: string;
    description: string;
    estimatedEffort: number;
    complexity: number;
    risk: number;
    value: number;
    status: string;
  }>;
  implementationHistory: Array<{
    id: string;
    featureTitle: string;
    status: string;
    completedAt: string;
    filesCreated: string[];
    filesModified: string[];
    performanceImpact: Record<string, number>;
    issues: string[];
  }>;
}

// PHASE 4: Collective Intelligence Interfaces
interface CollectiveIntelligenceStatus {
  activeConversations: number;
  consensusDecisions: number;
  knowledgeSharing: number;
  conflictResolutions: number;
  agentMeshHealth: number;
  lastClaudeInteraction: string;
  recentDecisions: Array<{
    id: string;
    type: 'consensus' | 'conflict_resolution' | 'knowledge_share';
    agents: string[];
    decision: string;
    confidence: number;
    timestamp: string;
  }>;
  realTimeInsights: Array<{
    id: string;
    source: string;
    insight: string;
    impact: 'low' | 'medium' | 'high';
    timestamp: string;
  }>;
}

interface AgentCommunication {
  id: string;
  from: string;
  to: string;
  messageType: string;
  content: string;
  timestamp: string;
  confidence?: number;
  status: 'sent' | 'received' | 'processing' | 'completed';
}

// Using centralized AGENT_CATEGORIES and determineAgentCategory from @/utils/agentVisuals

export function AgentDashboard() {
  const router = useRouter();
  const [status, setStatus] = useState<SystemStatus | null>(null);
  const [testAgentStatus, setTestAgentStatus] = useState<TestAgentStatus | null>(null);
  const [opsAgentStatus, setOpsAgentStatus] = useState<OpsAgentStatus | null>(null);
  const [healthStatus, setHealthStatus] = useState<HealthMonitorStatus | null>(null);
  const [advancedModStatus, setAdvancedModStatus] = useState<AdvancedModificationStatus | null>(null);
  
  // PHASE 4: Collective Intelligence State
  const [collectiveStatus, setCollectiveStatus] = useState<CollectiveIntelligenceStatus | null>(null);
  const [agentCommunications, setAgentCommunications] = useState<AgentCommunication[]>([]);
  const [realTimeUpdates, setRealTimeUpdates] = useState<boolean>(true);
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'recommendations' | 'agents' | 'health' | 'advanced' | 'precision' | 'collective' | 'queue' | 'all-agents'>('overview');
  const [taskForm, setTaskForm] = useState<TaskSubmission>({
    type: 'generate_component',
    description: '',
    requiredCapabilities: ['code_generation'],
    priority: 'NORMAL'
  });

  // NEW: Queue monitoring state
  const [queueStatus, setQueueStatus] = useState<any>(null);
  const [loadingQueueStatus, setLoadingQueueStatus] = useState(false);

  // NEW: All agents dynamic discovery
  const [allAgents, setAllAgents] = useState<any[]>([]);
  const [loadingAllAgents, setLoadingAllAgents] = useState(false);
  const [agentsSummary, setAgentsSummary] = useState<any>(null);
  
  // Category filtering states
  const [selectedCategories, setSelectedCategories] = useState<string[]>(Object.keys(AGENT_CATEGORIES));
  const [filteredAgents, setFilteredAgents] = useState<any[]>([]);

  // 🤖 AUTONOMOUS AI INTEGRATION - Day 8 Enhancement
  const [isMounted, setIsMounted] = useState(false);
  const dispatch = useAppDispatch();
  const autonomousState = useAppSelector(state => state.autonomousAI);
  
  // WebSocket connection for real-time autonomous AI streaming (client-side only)
  const {
    connectionStatus: aiConnectionStatus,
    connect: connectAutonomousAI,
    disconnect: disconnectAutonomousAI,
    lastMessage: lastAIMessage,
    messagesReceived: aiMessagesReceived,
    reconnectAttempts: aiReconnectAttempts
  } = useAutonomousAIStream({
    autoConnect: isMounted, // Only auto-connect after mount
    enableLogging: true
  });

  // Autonomous AI observation state
  const [autonomousObservationEnabled, setAutonomousObservationEnabled] = useState(true);
  const [aiDecisionCount, setAiDecisionCount] = useState(0);
  const [lastAIDecisionTime, setLastAIDecisionTime] = useState<string | null>(null);

  // Fetch TestAgent status
  const fetchTestAgentStatus = async () => {
    try {
      const response = await fetch('/api/agents/test?type=status');
      const data = await response.json();
      
      if (response.ok) {
        setTestAgentStatus({
          isRunning: data.status === 'operational',
          lastTestRun: data.lastActive,
        });
      }
    } catch (err) {
      console.error('Failed to fetch TestAgent status:', err);
    }
  };

  // Fetch OpsAgent status
  const fetchOpsAgentStatus = async () => {
    try {
      const [statusResponse, metricsResponse, alertsResponse] = await Promise.all([
        fetch('/api/agents/ops?type=status'),
        fetch('/api/agents/ops?type=metrics'),
        fetch('/api/agents/ops?type=alerts')
      ]);
      
      if (statusResponse.ok && metricsResponse.ok && alertsResponse.ok) {
        const statusData = await statusResponse.json();
        const metricsData = await metricsResponse.json();
        const alertsData = await alertsResponse.json();
        
        // Defensive programming - handle missing data structures
        const infrastructure = metricsData.infrastructure || { uptime: 95 };
        const performance = metricsData.performance || { responseTime: 150, errorRate: 2 };
        const deployments = metricsData.deployments || { total: 0, successful: 0, failed: 0 };
        const active = alertsData.active || [];
        
        setOpsAgentStatus({
          isRunning: statusData.status === 'operational',
          lastDeployment: statusData.lastActive || new Date().toISOString(),
          infrastructureHealth: infrastructure.uptime > 99 ? 'healthy' : 'warning',
          activeAlerts: active.length,
          deploymentMetrics: deployments,
          systemMetrics: {
            uptime: infrastructure.uptime,
            responseTime: performance.responseTime,
            errorRate: performance.errorRate
          }
        });
      }
    } catch (err) {
      console.error('Failed to fetch OpsAgent status:', err);
      // Set default status to prevent runtime errors
      setOpsAgentStatus({
        isRunning: false,
        lastDeployment: new Date().toISOString(),
        infrastructureHealth: 'warning',
        activeAlerts: 0,
        deploymentMetrics: { total: 0, successful: 0, failed: 0 },
        systemMetrics: { uptime: 0, responseTime: 0, errorRate: 0 }
      });
    }
  };

  // Fetch Health Monitor status
  const fetchHealthMonitorStatus = async () => {
    try {
      const [metricsResponse, improvementsResponse] = await Promise.all([
        fetch('/api/agents/health?type=metrics'),
        fetch('/api/agents/health?type=improvements')
      ]);
      
      if (metricsResponse.ok && improvementsResponse.ok) {
        const metricsData = await metricsResponse.json();
        const improvementsData = await improvementsResponse.json();
        
        setHealthStatus({
          isActive: true,
          systemHealth: metricsData.systemHealth,
          agentMetrics: metricsData.agentMetrics,
          improvementSuggestions: improvementsData.suggestions
        });
      }
    } catch (err) {
      console.error('Failed to fetch Health Monitor status:', err);
    }
  };

  // Fetch Advanced Modification status
  const fetchAdvancedModificationStatus = async () => {
    try {
      const [overviewResponse, featuresResponse, historyResponse] = await Promise.all([
        fetch('/api/agents/advanced-modification?action=overview'),
        fetch('/api/agents/advanced-modification?action=features'),
        fetch('/api/agents/advanced-modification?action=history')
      ]);
      
      if (overviewResponse.ok && featuresResponse.ok && historyResponse.ok) {
        const overviewData = await overviewResponse.json();
        const featuresData = await featuresResponse.json();
        const historyData = await historyResponse.json();
        
        setAdvancedModStatus({
          autonomyLevel: overviewData.autonomyLevel,
          activeFeatures: featuresData.totalInProgress,
          completedFeatures: historyData.history?.length || 0,
          queuedFeatures: featuresData.totalQueued,
          successRate: overviewData.modificationStatus?.successRate || 0,
          averageComplexity: featuresData.averageComplexity || 0,
          recentCapabilities: overviewData.recentCapabilities || [],
          featureQueue: featuresData.queue || [],
          implementationHistory: historyData.history || []
        });
      }
    } catch (err) {
      console.error('Failed to fetch Advanced Modification status:', err);
    }
  };

  // PHASE 4: Fetch Collective Intelligence status
  const fetchCollectiveIntelligenceStatus = async () => {
    try {
      const response = await fetch('/api/agents/collective-intelligence?type=status');
      const data = await response.json();
      
      if (response.ok) {
        setCollectiveStatus(data.data || {
          activeConversations: 0,
          consensusDecisions: 0,
          knowledgeSharing: 0,
          conflictResolutions: 0,
          agentMeshHealth: 85,
          lastClaudeInteraction: new Date().toISOString(),
          recentDecisions: [],
          realTimeInsights: []
        });
      }
    } catch (err) {
      console.error('Failed to fetch collective intelligence status:', err);
    }
  };

  // NEW: Fetch queue status from our proven queue system
  const fetchQueueStatus = async () => {
    setLoadingQueueStatus(true);
    try {
      const response = await fetch('/api/agents/queue?action=status');
      const data = await response.json();
      
      if (response.ok) {
        console.log('🧠📊 Queue status fetched:', data.data);
        setQueueStatus(data.data);
      } else {
        console.error('❌ Failed to fetch queue status:', data.error);
      }
    } catch (err) {
      console.error('❌ Queue status fetch error:', err);
    } finally {
      setLoadingQueueStatus(false);
    }
  };

  // NEW: Fetch all agents dynamically
  const fetchAllAgents = async () => {
    if (loadingAllAgents) return;
    
    setLoadingAllAgents(true);
    try {
      const response = await fetch('/api/orchestration/all-agents');
      const data = await response.json();
      
      if (response.ok) {
        setAllAgents(data.data.agents);
        setAgentsSummary(data.data.summary);
        console.log(`🤖 Loaded ${data.data.agents.length} agents dynamically`);
      } else {
        console.error('Failed to fetch all agents:', data.error);
      }
    } catch (error) {
      console.error('All agents fetch error:', error);
    } finally {
      setLoadingAllAgents(false);
    }
  };

  // Category filtering functions
  const toggleCategory = (category: string) => {
    setSelectedCategories(prev => {
      if (prev.includes(category)) {
        return prev.filter(c => c !== category);
      } else {
        return [...prev, category];
      }
    });
  };

  const toggleAllCategories = () => {
    if (selectedCategories.length === Object.keys(AGENT_CATEGORIES).length) {
      setSelectedCategories([]);
    } else {
      setSelectedCategories(Object.keys(AGENT_CATEGORIES));
    }
  };

  // Filter agents based on selected categories
  const applyFiltering = () => {
    if (allAgents.length > 0) {
      const filtered = allAgents.filter(agent => {
        const agentCategory = determineAgentCategory(agent.name);
        return selectedCategories.includes(agentCategory);
      });
      setFilteredAgents(filtered);
      console.log('🔍 Dashboard: Filtered to', filtered.length, 'agents from', selectedCategories.length, 'categories');
    }
  };

  // Execute TestAgent actions
  const executeTestAction = async (action: string, filePath?: string) => {
    try {
      const response = await fetch('/api/agents/test', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action, filePath }),
      });

      const data = await response.json();
      
      if (data.success) {
        if (action === 'execute_tests') {
          setTestAgentStatus(prev => ({
            ...prev!,
            testResults: data.results,
            lastTestRun: new Date().toISOString()
          }));
        } else if (action === 'analyze_coverage') {
          setTestAgentStatus(prev => ({
            ...prev!,
            currentCoverage: data.analysis.report.overall
          }));
        } else if (action === 'quality_assurance') {
          setTestAgentStatus(prev => ({
            ...prev!,
            qualityScore: data.results.qualityScore
          }));
        }
        
        alert(`${action} completed successfully!`);
      } else {
        setError(data.error);
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Execute OpsAgent actions
  const executeOpsAction = async (action: string, config?: any) => {
    try {
      const requestBody: any = { action };
      
      if (action === 'deploy' && config) {
        requestBody.deploymentConfig = config;
      } else if (action === 'optimize' && config) {
        requestBody.target = config;
      } else if (action === 'respond_incident' && config) {
        requestBody.incident = config;
      } else if (action === 'scale' && config) {
        requestBody.scalingConfig = config;
      }

      const response = await fetch('/api/agents/ops', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();
      
      if (data.success) {
        alert(`${action} completed successfully!`);
        // Refresh OpsAgent status to see updated metrics
        fetchOpsAgentStatus();
      } else {
        setError(data.error);
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Execute Health Monitor actions
  const executeHealthAction = async (action: string, agentId?: string) => {
    try {
      const requestBody: any = { action };
      
      if (agentId) {
        requestBody.agentId = agentId;
      }

      const response = await fetch('/api/agents/health', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();
      
      if (data.success) {
        alert(`${action} completed successfully!`);
        // Refresh health status to see updated metrics
        fetchHealthMonitorStatus();
      } else {
        setError(data.error);
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Execute Advanced Modification actions
  const executeAdvancedModAction = async (action: string, featureRequest?: any) => {
    try {
      const response = await fetch(`/api/agents/advanced-modification?action=${action}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(featureRequest || {}),
      });

      const data = await response.json();
      
      if (data.success || response.ok) {
        alert(`${action} completed successfully!`);
        // Refresh advanced modification status after action
        await fetchAdvancedModificationStatus();
      } else {
        setError(data.error || 'Advanced modification action failed');
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  // Fetch system status
  const fetchStatus = async () => {
    try {
      const response = await fetch('/api/agents/status');
      const data = await response.json();
      
      if (data.success) {
        setStatus(data.data);
        setError(null);
      } else {
        setError(data.error);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  // Submit task to agents
  const submitTask = async () => {
    if (!taskForm.description.trim()) {
      setError('Task description is required');
      return;
    }

    try {
      const response = await fetch('/api/agents/task', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(taskForm),
      });

      const data = await response.json();
      
      if (data.success) {
        alert(`Task submitted successfully! Task ID: ${data.data.taskId}`);
        setTaskForm({
          ...taskForm,
          description: ''
        });
        // Refresh status to see updated queue
        fetchStatus();
      } else {
        setError(data.error);
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  useEffect(() => {
    fetchStatus();
    fetchTestAgentStatus();
    fetchOpsAgentStatus();
    fetchHealthMonitorStatus();
    fetchAdvancedModificationStatus();
    fetchCollectiveIntelligenceStatus();
    fetchQueueStatus();
    fetchAllAgents(); // NEW: Fetch all agents dynamically
    // Poll for updates every 10 seconds
    const interval = setInterval(() => {
      fetchStatus();
      fetchTestAgentStatus();
      fetchOpsAgentStatus();
      fetchHealthMonitorStatus();
      fetchAdvancedModificationStatus();
      fetchCollectiveIntelligenceStatus();
      fetchQueueStatus();
      // Refresh agents list less frequently
      if (Math.random() < 0.1) fetchAllAgents(); // 10% chance each interval
    }, 10000);
    return () => clearInterval(interval);
  }, []);

  // Apply filtering when agents or selected categories change
  useEffect(() => {
    if (allAgents.length > 0) {
      applyFiltering();
    }
  }, [allAgents, selectedCategories]);

  // 🤖 AUTONOMOUS AI EFFECTS - Day 8 Enhancement
  
  // Effect: Track AI decision count from real-time messages
  useEffect(() => {
    if (lastAIMessage && lastAIMessage.type === 'decision') {
      setAiDecisionCount(prev => prev + 1);
      setLastAIDecisionTime(lastAIMessage.timestamp);
    }
  }, [lastAIMessage]);

  // Effect: Handle autonomous AI connection status changes
  useEffect(() => {
    if (aiConnectionStatus === 'connected') {
      console.log('🤖✅ Autonomous AI streaming connected - Real-time observation active');
    } else if (aiConnectionStatus === 'error') {
      console.log('🤖❌ Autonomous AI streaming error - Attempting reconnection');
    }
  }, [aiConnectionStatus]);

  // Toggle autonomous AI observation
  const toggleAutonomousObservation = () => {
    if (autonomousObservationEnabled) {
      disconnectAutonomousAI();
      setAutonomousObservationEnabled(false);
    } else {
      connectAutonomousAI();
      setAutonomousObservationEnabled(true);
    }
  };

  if (loading) {
    return (
      <Container className="py-12">
        <div className="space-y-8">
          <div className="text-center space-y-4">
            <div className="relative">
              <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-cosmic-500 via-nova-500 to-neural-500 flex items-center justify-center animate-glow mb-6">
                <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              </div>
            </div>
            <h1 className="text-4xl font-display font-bold text-gradient-multi">
              Initializing Agent System
            </h1>
            <p className="text-lg theme-text-secondary max-w-2xl mx-auto">
              Loading autonomous agent status and preparing the control interface...
            </p>
          </div>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-12">
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-red-500 to-orange-500 flex items-center justify-center mb-6">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-3xl font-display font-bold theme-text-primary">
            Agent System Error
          </h1>
          <p className="text-lg theme-text-secondary max-w-2xl mx-auto">{error}</p>
          <Button 
            onClick={() => window.location.reload()} 
            variant="primary"
            className="animate-glow"
          >
            Retry Connection
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <div className="min-h-screen theme-bg-primary">
      <Container className="py-12">
        <div className="space-y-8">
          {/* Header with Autonomous AI Observation */}
          <div className="text-center space-y-4 mb-12">
            <div className="flex items-center justify-center gap-4 mb-6">
              <div className="w-16 h-16 rounded-2xl bg-gradient-cosmic flex items-center justify-center animate-glow shadow-cosmic">
                <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-display font-bold text-gradient-multi mb-4 text-shadow-enhanced">
              Agent Control Center
            </h1>
            <p className="text-lg text-secondary-enhanced max-w-3xl mx-auto font-medium">
              Monitor, control, and collaborate with your autonomous agent ecosystem. 
              Real-time insights into system performance and agent recommendations.
            </p>

            {/* 🤖 AUTONOMOUS AI OBSERVATION PANEL - Day 8 Enhancement */}
            {isMounted && (
              <div className="mt-8 p-6 neo-card bg-gradient-to-r from-space-800/30 to-space-700/30 border border-cosmic-500/20 max-w-4xl mx-auto">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-3">
                      <div className={`w-3 h-3 rounded-full animate-pulse ${
                        aiConnectionStatus === 'connected' ? 'bg-green-500' :
                        aiConnectionStatus === 'connecting' ? 'bg-yellow-500' :
                        aiConnectionStatus === 'error' ? 'bg-red-500' : 'bg-gray-500'
                      }`}></div>
                      <span className="text-sm font-medium text-white">
                        🤖 AI Observation {autonomousObservationEnabled ? 'ON' : 'OFF'}
                      </span>
                    </div>
                    <div className="text-xs text-muted-enhanced">
                      {aiConnectionStatus === 'connected' && aiMessagesReceived > 0 ? 
                        `${aiMessagesReceived} messages | ${aiDecisionCount} decisions` :
                        aiConnectionStatus === 'connecting' ? 'Connecting...' :
                        aiConnectionStatus === 'error' ? `Error (${aiReconnectAttempts} retries)` :
                        'Disconnected'
                      }
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    {lastAIDecisionTime && (
                      <div className="text-xs text-muted-enhanced">
                        Last: {new Date(lastAIDecisionTime).toLocaleTimeString()}
                      </div>
                    )}
                    <Button
                      onClick={toggleAutonomousObservation}
                      variant={autonomousObservationEnabled ? "primary" : "outline"}
                      size="sm"
                      className="animate-glow"
                    >
                      {autonomousObservationEnabled ? '📴 Disable' : '🔗 Enable'} AI Stream
                    </Button>
                  </div>
                </div>
                
                {autonomousObservationEnabled && aiConnectionStatus === 'connected' && (
                  <div className="mt-4 p-4 neo-panel bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20">
                    <div className="text-sm text-green-300 flex items-center gap-2">
                      <span className="animate-pulse">🟢</span>
                      <span>Real-time autonomous AI observation active - Agent decisions streaming live</span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Enhanced Mobile-First Tab Navigation */}
          <div className="mb-8">
            {/* Mobile Horizontal Scroll Navigation */}
            <div className="block sm:hidden">
              <div className="flex gap-2 overflow-x-auto pb-4 px-4 -mx-4 scrollbar-hide">
                {[
                  { key: 'overview', label: 'Overview', shortLabel: 'Overview', icon: '📊' },
                  { key: 'recommendations', label: 'Recommendations', shortLabel: 'Rec.', icon: '💡' },
                  { key: 'agents', label: 'Agent Status', shortLabel: 'Agents', icon: '🤖' },
                  { key: 'health', label: 'Health Monitor', shortLabel: 'Health', icon: '❤️' },
                  { key: 'advanced', label: 'Advanced', shortLabel: 'Adv.', icon: '🚀' },
                  { key: 'precision', label: 'Swiss Watch Precision', shortLabel: 'Swiss', icon: '🎯' },
                  { key: 'collective', label: 'Collective Intelligence', shortLabel: 'Collective', icon: '🤝' },
                  { key: 'queue', label: 'Queue Monitor', shortLabel: 'Queue', icon: '👥' },
                  { key: 'all-agents', label: 'All Agents', shortLabel: 'All', icon: '👥' }
                ].map(tab => (
                  <button
                    key={tab.key}
                    onClick={() => setActiveTab(tab.key as any)}
                    className={`
                      flex-shrink-0 flex flex-col items-center gap-1
                      px-4 py-3 rounded-xl min-w-[80px] min-h-[68px]
                      transition-all duration-300 text-sm font-medium
                      touch-pan-y active:scale-95
                      ${activeTab === tab.key 
                        ? 'bg-gradient-to-br from-cosmic-600 to-cosmic-500 text-white shadow-lg animate-glow' 
                        : 'bg-space-800/50 text-stardust-300 hover:bg-cosmic-500/20 hover:text-cosmic-300'
                      }
                    `}
                  >
                    <span className="text-lg">{tab.icon}</span>
                    <span className="text-xs text-center leading-tight">{tab.shortLabel}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Desktop Grid Navigation */}
            <div className="hidden sm:flex flex-wrap justify-center gap-2">
              {[
                { key: 'overview', label: 'System Overview', icon: '📊' },
                { key: 'recommendations', label: 'Recommendations', icon: '💡' },
                { key: 'agents', label: 'Agent Status', icon: '🤖' },
                { key: 'health', label: 'Health Monitor', icon: '❤️' },
                { key: 'advanced', label: 'Advanced', icon: '🚀' },
                { key: 'precision', label: 'Swiss Watch Precision', icon: '🎯' },
                { key: 'collective', label: 'Collective Intelligence', icon: '🤝' },
                { key: 'queue', label: 'Queue Monitor', icon: '👥' },
                { key: 'all-agents', label: 'All Agents', icon: '👥' }
              ].map(tab => (
                <Button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key as any)}
                  variant={activeTab === tab.key ? 'primary' : 'ghost'}
                  className={`px-4 sm:px-6 py-3 transition-all duration-300 ${
                    activeTab === tab.key 
                      ? 'animate-glow' 
                      : 'hover:bg-cosmic-500/10 hover:text-cosmic-400'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  <span className="hidden sm:inline">{tab.label}</span>
                  <span className="sm:hidden text-xs">{tab.label.split(' ')[0]}</span>
                </Button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          {activeTab === 'overview' && (
            <div className="space-y-8">
              {/* System Status Overview */}
              {status && (
                <div className="neo-card group hover:shadow-cosmic transition-all duration-500">
                  <div className="p-8">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-8 mb-6 sm:mb-8">
                      <div className="flex items-center gap-3 sm:gap-4">
                        <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl bg-gradient-cosmic flex items-center justify-center animate-glow shadow-cosmic flex-shrink-0">
                          <span className="text-lg sm:text-xl">🎛️</span>
                        </div>
                        <div className="min-w-0">
                          <h2 className="text-lg sm:text-xl font-display font-semibold text-gradient-cosmic text-shadow-cosmic">
                            System Overview
                          </h2>
                          <p className="text-xs sm:text-sm text-muted-enhanced">Real-time agent system status</p>
                        </div>
                      </div>
                      <div className={`px-3 sm:px-4 py-2 rounded-full text-xs sm:text-sm font-semibold flex-shrink-0 text-center ${
                        status.isRunning 
                          ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-300 border border-green-500/40 shadow-lg' 
                          : 'bg-gradient-to-r from-red-500/20 to-orange-500/20 text-red-300 border border-red-500/40'
                      }`}>
                        {status.isRunning ? 'OPERATIONAL' : 'OFFLINE'}
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                      <div className="neo-panel p-6 backdrop-blur-md">
                        <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Autonomy Level</h3>
                        <div className="text-3xl font-bold text-gradient-cosmic mb-3">
                          {status.autonomyLevel}%
                        </div>
                        <div className="w-full bg-space-700/50 rounded-full h-3 overflow-hidden">
                          <div
                            className="bg-gradient-cosmic h-3 rounded-full transition-all duration-700 shadow-cosmic"
                            style={{ width: `${status.autonomyLevel}%` }}
                          />
                        </div>
                      </div>
                      
                      <div className="neo-panel p-6 backdrop-blur-md">
                        <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Active Agents</h3>
                        <div className="text-3xl font-bold text-gradient-nova mb-2">
                          {status.activeAgents}/{status.agentCount}
                        </div>
                        <div className="text-xs text-muted-enhanced">
                          {status.queuedTasks} queued tasks
                        </div>
                      </div>
                      
                      <div className="neo-panel p-6 backdrop-blur-md">
                        <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Success Rate</h3>
                        <div className="text-3xl font-bold text-gradient-neural mb-2">
                          {status.performanceMetrics?.averageSuccessRate || 0}%
                        </div>
                        <div className="text-xs text-muted-enhanced">
                          {status.performanceMetrics?.totalTasks || 0} total tasks
                        </div>
                      </div>
                      
                      <div className="neo-panel p-6 backdrop-blur-md">
                        <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Response Time</h3>
                        <div className="text-3xl font-bold text-gradient-aura mb-2">
                          {status.performanceMetrics?.averageResponseTime || 0}ms
                        </div>
                        <div className="text-xs text-muted-enhanced">
                          {Math.floor((status.performanceMetrics?.systemUptime || 0) / 3600)}h uptime
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 🤖 AI DECISION TRANSPARENCY - Day 8 Enhancement */}
              {isMounted && autonomousObservationEnabled && (
                <div className="neo-card group hover:shadow-neural transition-all duration-500">
                  <div className="p-6">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-neural-500 via-quantum-500 to-cosmic-500 flex items-center justify-center animate-glow">
                        <span className="text-xl">🧠</span>
                      </div>
                      <div>
                        <h2 className="text-xl font-display font-semibold text-gradient-neural">
                          AI Decision Transparency
                        </h2>
                        <p className="text-sm theme-text-secondary">Real-time autonomous AI decision feed</p>
                      </div>
                      <div className="ml-auto flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full animate-pulse ${
                          aiConnectionStatus === 'connected' ? 'bg-green-500' : 'bg-gray-500'
                        }`}></div>
                        <span className="text-sm text-muted-enhanced">
                          {aiConnectionStatus === 'connected' ? 'Live' : 'Disconnected'}
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* AI Decision Stats */}
                      <div className="space-y-4">
                        <div className="neo-panel p-4">
                          <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Decision Metrics</h3>
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <div className="text-2xl font-bold text-gradient-neural mb-1">
                                {aiDecisionCount}
                              </div>
                              <div className="text-xs text-muted-enhanced">Total Decisions</div>
                            </div>
                            <div>
                              <div className="text-2xl font-bold text-gradient-cosmic mb-1">
                                {aiMessagesReceived}
                              </div>
                              <div className="text-xs text-muted-enhanced">Messages Received</div>
                            </div>
                          </div>
                        </div>

                        <div className="neo-panel p-4">
                          <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Connection Status</h3>
                          <div className="flex items-center justify-between">
                            <span className="text-sm">WebSocket</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              aiConnectionStatus === 'connected' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                              aiConnectionStatus === 'connecting' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                              'bg-red-500/20 text-red-300 border border-red-500/30'
                            }`}>
                              {aiConnectionStatus.toUpperCase()}
                            </span>
                          </div>
                          {aiReconnectAttempts > 0 && (
                            <div className="mt-2 text-xs text-muted-enhanced">
                              Reconnect attempts: {aiReconnectAttempts}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Recent AI Decisions */}
                      <div className="neo-panel p-4">
                        <h3 className="text-sm font-semibold text-muted-enhanced mb-3">Recent AI Decisions</h3>
                        <div className="space-y-3 max-h-48 overflow-y-auto">
                          {autonomousState.decisions.slice(-5).reverse().map((decision, index) => (
                            <div key={decision.id} className="p-3 neo-panel bg-gradient-to-r from-neural-500/10 to-cosmic-500/10 border border-neural-500/20">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-neural-300">
                                  {decision.decision_type}
                                </span>
                                <span className="text-xs text-muted-enhanced">
                                  {new Date(decision.timestamp).toLocaleTimeString()}
                                </span>
                              </div>
                              <div className="text-xs text-muted-enhanced mb-2">
                                Agent: {decision.agent_id}
                              </div>
                              <div className="text-sm theme-text-secondary">
                                {decision.description || 'AI decision processing...'}
                              </div>
                              <div className="mt-2 flex items-center gap-2">
                                <div className="w-full bg-space-700/50 rounded-full h-1">
                                  <div
                                    className="bg-gradient-to-r from-neural-500 to-cosmic-500 h-1 rounded-full"
                                    style={{ width: `${decision.confidence * 100}%` }}
                                  />
                                </div>
                                <span className="text-xs text-muted-enhanced">
                                  {Math.round(decision.confidence * 100)}%
                                </span>
                              </div>
                            </div>
                          ))}
                          {autonomousState.decisions.length === 0 && (
                            <div className="text-center py-8 text-muted-enhanced">
                              <span className="text-2xl mb-2 block">🤖</span>
                              <p className="text-sm">No AI decisions recorded yet</p>
                              <p className="text-xs mt-1">Real-time decisions will appear here</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Task Submission Form */}
              <div className="neo-card group hover:shadow-multi transition-all duration-500">
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-6">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-cosmic-500 via-nova-500 to-neural-500 flex items-center justify-center animate-glow">
                      <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <div>
                      <h2 className="text-xl font-display font-semibold text-gradient-multi">
                        Submit New Task
                      </h2>
                      <p className="text-sm theme-text-secondary">Submit tasks directly to the agent system</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium theme-text-primary mb-2">
                          Task Type
                        </label>
                        <select
                          value={taskForm.type}
                          onChange={(e) => setTaskForm({...taskForm, type: e.target.value})}
                          className="neo-input w-full"
                        >
                          <option value="generate_component">Generate Component</option>
                          <option value="refactor_code">Refactor Code</option>
                          <option value="optimize_performance">Optimize Performance</option>
                          <option value="security_audit">Security Audit</option>
                          <option value="test_generation">Generate Tests</option>
                        </select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium theme-text-primary mb-2">
                          Priority Level
                        </label>
                        <select
                          value={taskForm.priority}
                          onChange={(e) => setTaskForm({...taskForm, priority: e.target.value as any})}
                          className="neo-input w-full"
                        >
                          <option value="LOW">Low Priority</option>
                          <option value="NORMAL">Normal Priority</option>
                          <option value="HIGH">High Priority</option>
                          <option value="URGENT">Urgent</option>
                        </select>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium theme-text-primary mb-2">
                          Task Description
                        </label>
                        <textarea
                          value={taskForm.description}
                          onChange={(e) => setTaskForm({...taskForm, description: e.target.value})}
                          placeholder="Describe what you want the agents to accomplish..."
                          className="neo-input w-full h-24 resize-none"
                        />
                      </div>
                      
                      <Button
                        onClick={submitTask}
                        variant="primary"
                        className="w-full animate-glow"
                      >
                        Submit Task to Agents
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Recommendations Tab */}
          {activeTab === 'recommendations' && (
            <div className="neo-card">
              <AgentRecommendations />
            </div>
          )}

          {/* Agents Tab */}
          {activeTab === 'agents' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Test Agent Section */}
              {testAgentStatus && (
                <div className="neo-card group hover:shadow-neural transition-all duration-500">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-neural-500 to-neural-600 flex items-center justify-center animate-glow">
                          <span className="text-xl">🧪</span>
                        </div>
                        <div>
                          <h2 className="text-xl font-display font-semibold text-gradient-neural">
                            Test Agent
                          </h2>
                          <p className="text-sm theme-text-secondary">Automated Testing & Quality Assurance</p>
                        </div>
                      </div>
                      <div className={`px-3 py-1.5 rounded-full text-xs font-medium ${
                        testAgentStatus.isRunning 
                          ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                          : 'bg-red-500/20 text-red-400 border border-red-500/30'
                      }`}>
                        {testAgentStatus.isRunning ? 'ACTIVE' : 'INACTIVE'}
                      </div>
                    </div>
                    
                    {/* Test Agent Actions */}
                    <div className="grid grid-cols-2 gap-3">
                      <Button
                        onClick={() => executeTestAction('run_tests')}
                        variant="primary"
                        size="sm"
                        className="w-full"
                      >
                        Run Tests
                      </Button>
                      <Button
                        onClick={() => executeTestAction('generate_tests')}
                        variant="ghost"
                        size="sm"
                        className="w-full"
                      >
                        Generate Tests
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Ops Agent Section */}
              {opsAgentStatus && (
                <div className="neo-card group hover:shadow-aura transition-all duration-500">
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-aura-500 to-aura-600 flex items-center justify-center animate-glow">
                          <span className="text-xl">⚙️</span>
                        </div>
                        <div>
                          <h2 className="text-xl font-display font-semibold text-gradient-aura">
                            Ops Agent
                          </h2>
                          <p className="text-sm theme-text-secondary">Deployment & Infrastructure Management</p>
                        </div>
                      </div>
                      <div className={`px-3 py-1.5 rounded-full text-xs font-medium ${
                        opsAgentStatus.isRunning 
                          ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                          : 'bg-red-500/20 text-red-400 border border-red-500/30'
                      }`}>
                        {opsAgentStatus.isRunning ? 'ACTIVE' : 'INACTIVE'}
                      </div>
                    </div>
                    
                    {/* Ops Agent Actions */}
                    <div className="grid grid-cols-2 gap-3">
                      <Button
                        onClick={() => executeOpsAction('deploy')}
                        variant="secondary"
                        size="sm"
                        className="w-full"
                      >
                        Deploy
                      </Button>
                      <Button
                        onClick={() => executeOpsAction('monitor')}
                        variant="ghost"
                        size="sm"
                        className="w-full"
                      >
                        Monitor
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Health Tab */}
          {activeTab === 'health' && healthStatus && (
            <div className="neo-card group hover:shadow-quantum transition-all duration-500">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-quantum-500 to-quantum-600 flex items-center justify-center animate-glow">
                      <span className="text-xl">❤️</span>
                    </div>
                    <div>
                      <h2 className="text-xl font-display font-semibold text-gradient-quantum">
                        System Health Monitor
                      </h2>
                      <p className="text-sm theme-text-secondary">Real-time system health and optimization</p>
                    </div>
                  </div>
                  <div className={`px-3 py-1.5 rounded-full text-xs font-medium ${
                    healthStatus.isActive 
                      ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                      : 'bg-red-500/20 text-red-400 border border-red-500/30'
                  }`}>
                    {healthStatus.isActive ? 'MONITORING' : 'INACTIVE'}
                  </div>
                </div>
                
                {/* Health Metrics */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div className="bg-glass border border-space-600/30 rounded-xl p-4 backdrop-blur-sm">
                    <h3 className="text-sm font-medium theme-text-primary mb-2">Overall Health</h3>
                    <div className="text-2xl font-bold text-gradient-quantum">
                      {healthStatus.systemHealth.overallScore}%
                    </div>
                    <div className="w-full bg-space-700 rounded-full h-2 mt-2 overflow-hidden">
                      <div
                        className="bg-gradient-to-r from-quantum-500 to-quantum-400 h-2 rounded-full transition-all duration-700"
                        style={{ width: `${healthStatus.systemHealth.overallScore}%` }}
                      />
                    </div>
                  </div>
                  
                  <div className="bg-glass border border-space-600/30 rounded-xl p-4 backdrop-blur-sm">
                    <h3 className="text-sm font-medium theme-text-primary mb-2">System Load</h3>
                    <div className="text-2xl font-bold text-gradient-aura">
                      {healthStatus.systemHealth.systemLoad}%
                    </div>
                    <div className="text-xs theme-text-secondary mt-1">
                      {healthStatus.systemHealth.responseTime}ms response
                    </div>
                  </div>
                </div>
                
                {/* Health Actions */}
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={() => executeHealthAction('system_scan')}
                    variant="primary"
                    size="sm"
                    className="w-full"
                  >
                    System Scan
                  </Button>
                  <Button
                    onClick={() => executeHealthAction('optimize_agent', 'all')}
                    variant="ghost"
                    size="sm"
                    className="w-full"
                  >
                    Optimize All
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Advanced Tab */}
          {activeTab === 'advanced' && advancedModStatus && (
            <div className="neo-card group hover:shadow-nova transition-all duration-500">
              <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-nova-500 to-nova-600 flex items-center justify-center animate-glow">
                      <span className="text-xl">🚀</span>
                    </div>
                    <div>
                      <h2 className="text-xl font-display font-semibold text-gradient-nova">
                        Advanced Modification
                      </h2>
                      <p className="text-sm theme-text-secondary">Self-Evolution Engine</p>
                    </div>
                  </div>
                  <div className="px-3 py-1.5 rounded-full text-xs font-medium bg-nova-500/20 text-nova-400 border border-nova-500/30">
                    {advancedModStatus.autonomyLevel}% Autonomy
                  </div>
                </div>
                
                {/* Modification Metrics */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-glass border border-space-600/30 rounded-xl p-4 backdrop-blur-sm">
                    <h3 className="text-sm font-medium theme-text-primary mb-2">Features</h3>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="theme-text-secondary">Active:</span>
                        <span className="text-nova-400">{advancedModStatus.activeFeatures}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="theme-text-secondary">Queued:</span>
                        <span className="text-aura-400">{advancedModStatus.queuedFeatures}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="theme-text-secondary">Completed:</span>
                        <span className="text-green-400">{advancedModStatus.completedFeatures}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-glass border border-space-600/30 rounded-xl p-4 backdrop-blur-sm">
                    <h3 className="text-sm font-medium theme-text-primary mb-2">Success Rate</h3>
                    <div className="text-2xl font-bold text-gradient-nova">
                      {advancedModStatus.successRate}%
                    </div>
                    <div className="text-xs theme-text-secondary mt-1">
                      Complexity: {advancedModStatus.averageComplexity}/10
                    </div>
                  </div>
                </div>
                
                {/* Modification Actions */}
                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={() => executeAdvancedModAction('analyze_capabilities')}
                    variant="primary"
                    size="sm"
                    className="w-full"
                  >
                    Analyze
                  </Button>
                  <Button
                    onClick={() => executeAdvancedModAction('request_enhancement')}
                    variant="ghost"
                    size="sm"
                    className="w-full"
                  >
                    Enhance
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Swiss Watch Precision Tab */}
          {activeTab === 'precision' && (
            <div className="space-y-6">
              <SwissWatchPrecisionMonitor />
            </div>
          )}

          {/* Collective Intelligence Tab */}
          {activeTab === 'collective' && (
            <div className="space-y-8">
              {/* Collective Intelligence Overview */}
              {collectiveStatus && (
                <div className="neo-card group hover:shadow-cosmic transition-all duration-500">
                  <div className="p-8">
                    <div className="flex items-center justify-between mb-8">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 rounded-xl bg-gradient-cosmic flex items-center justify-center animate-glow shadow-cosmic">
                          <span className="text-xl">🤝</span>
                        </div>
                        <div>
                          <h2 className="text-xl font-display font-semibold text-gradient-cosmic text-shadow-cosmic">
                            Collective Intelligence
                          </h2>
                          <p className="text-sm text-muted-enhanced">Real-time insights and interactions</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Collective Intelligence Actions */}
              <div className="flex flex-wrap justify-center gap-2 mb-8">
                <Button
                  onClick={() => executeAdvancedModAction('analyze_capabilities')}
                  variant="primary"
                  className="px-6 py-3 transition-all duration-300 animate-glow"
                >
                  Analyze Collective Intelligence
                </Button>
                <Button
                  onClick={() => executeAdvancedModAction('request_enhancement')}
                  variant="ghost"
                  className="px-6 py-3 transition-all duration-300 hover:bg-cosmic-500/10 hover:text-cosmic-400"
                >
                  Enhance Collective Intelligence
                </Button>
              </div>
            </div>
          )}

          {/* Queue Monitor Tab */}
          {activeTab === 'queue' && (
            <div className="space-y-8">
              {/* Queue Status Overview */}
              {queueStatus && (
                <div className="neo-card group hover:shadow-cosmic transition-all duration-500">
                  <div className="p-8">
                    <div className="flex items-center justify-between mb-8">
                      <div className="flex items-center gap-4">
                        <div className="relative w-16 h-16 rounded-2xl bg-gradient-cosmic flex items-center justify-center animate-glow shadow-cosmic">
                          <span className="text-2xl">🧠</span>
                          {/* Pulsing ring animation for active status */}
                          <div className="absolute inset-0 rounded-2xl bg-gradient-cosmic opacity-50 animate-ping"></div>
                        </div>
                        <div>
                          <h2 className="text-2xl font-display font-bold text-gradient-cosmic text-shadow-cosmic">
                            Intelligent Queue System
                          </h2>
                          <p className="text-sm text-muted-enhanced">Large request handling with proven 285s runtime protection</p>
                          <div className="flex items-center gap-2 mt-2">
                            <div className="w-2 h-2 rounded-full bg-green-400 animate-pulse"></div>
                            <span className="text-xs text-green-300">Real-time monitoring active</span>
                          </div>
                        </div>
                      </div>
                      <div className="relative">
                        <div className={`px-6 py-3 rounded-2xl text-sm font-bold transition-all duration-300 ${
                          queueStatus.isOperational 
                            ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-300 border-2 border-green-500/40 shadow-lg shadow-green-500/20' 
                            : 'bg-gradient-to-r from-red-500/20 to-orange-500/20 text-red-300 border-2 border-red-500/40'
                        }`}>
                          {queueStatus.isOperational ? '🟢 OPERATIONAL' : '🔴 OFFLINE'}
                        </div>
                        {queueStatus.isOperational && (
                          <div className="absolute -inset-1 bg-gradient-to-r from-green-400 to-emerald-400 rounded-2xl opacity-20 animate-pulse"></div>
                        )}
                      </div>
                    </div>

                    {/* Enhanced Queue Metrics Grid with Progress Rings */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                      <div className="neo-panel p-6 backdrop-blur-md relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-cosmic opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                        <div className="relative z-10">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-sm font-semibold text-muted-enhanced">Queue Activity</h3>
                            <div className="w-8 h-8 relative">
                              {/* Circular progress for queue utilization */}
                              <svg className="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
                                <circle cx="16" cy="16" r="14" stroke="currentColor" strokeWidth="2" fill="none" className="text-space-600" />
                                <circle 
                                  cx="16" 
                                  cy="16" 
                                  r="14" 
                                  stroke="currentColor" 
                                  strokeWidth="2" 
                                  fill="none" 
                                  strokeDasharray={`${(queueStatus.queueSize / 10) * 88} 88`}
                                  className="text-cosmic-400 transition-all duration-700"
                                />
                              </svg>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-xs font-bold text-cosmic-300">{queueStatus.queueSize}</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-3xl font-bold text-gradient-cosmic mb-3 animate-shimmer">
                            {queueStatus.queueSize}
                          </div>
                          <div className="text-xs text-muted-enhanced flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-cosmic-400 animate-pulse"></div>
                            {queueStatus.activeRequests} active requests
                          </div>
                        </div>
                      </div>
                      
                      <div className="neo-panel p-6 backdrop-blur-md relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-nova opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                        <div className="relative z-10">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-sm font-semibold text-muted-enhanced">Success Rate</h3>
                            <div className="w-8 h-8 relative">
                              <svg className="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
                                <circle cx="16" cy="16" r="14" stroke="currentColor" strokeWidth="2" fill="none" className="text-space-600" />
                                <circle 
                                  cx="16" 
                                  cy="16" 
                                  r="14" 
                                  stroke="currentColor" 
                                  strokeWidth="2" 
                                  fill="none" 
                                  strokeDasharray={`${(queueStatus.successRate / 100) * 88} 88`}
                                  className="text-nova-400 transition-all duration-700"
                                />
                              </svg>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-xs font-bold text-nova-300">{queueStatus.successRate}%</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-3xl font-bold text-gradient-nova mb-3 animate-shimmer">
                            {queueStatus.successRate}%
                          </div>
                          <div className="text-xs text-muted-enhanced flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-nova-400 animate-pulse"></div>
                            {queueStatus.totalRequests} total requests
                          </div>
                        </div>
                      </div>
                      
                      <div className="neo-panel p-6 backdrop-blur-md relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-neural opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                        <div className="relative z-10">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-sm font-semibold text-muted-enhanced">Response Time</h3>
                            <div className="flex space-x-1">
                              {[1, 2, 3].map((bar, index) => (
                                <div 
                                  key={bar}
                                  className={`w-1 rounded-full bg-neural-400 transition-all duration-300 delay-${index * 100}`}
                                  style={{ 
                                    height: `${Math.max(8, (queueStatus.averageResponseTime / 1000) * 2)}px`,
                                    animationDelay: `${index * 100}ms`
                                  }}
                                ></div>
                              ))}
                            </div>
                          </div>
                          <div className="text-3xl font-bold text-gradient-neural mb-3 animate-shimmer">
                            {Math.round(queueStatus.averageResponseTime / 1000)}s
                          </div>
                          <div className="text-xs text-muted-enhanced flex items-center gap-2">
                            <div className="w-1 h-1 rounded-full bg-neural-400 animate-pulse"></div>
                            Max: {queueStatus.maxTimeoutSeconds}s timeout
                          </div>
                        </div>
                      </div>
                      
                      <div className="neo-panel p-6 backdrop-blur-md relative overflow-hidden group">
                        <div className="absolute inset-0 bg-gradient-aura opacity-0 group-hover:opacity-10 transition-opacity duration-500"></div>
                        <div className="relative z-10">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-sm font-semibold text-muted-enhanced">Health Score</h3>
                            <div className="w-8 h-8 relative">
                              <svg className="w-8 h-8 transform -rotate-90" viewBox="0 0 32 32">
                                <circle cx="16" cy="16" r="14" stroke="currentColor" strokeWidth="2" fill="none" className="text-space-600" />
                                <circle 
                                  cx="16" 
                                  cy="16" 
                                  r="14" 
                                  stroke="currentColor" 
                                  strokeWidth="2" 
                                  fill="none" 
                                  strokeDasharray={`${((queueStatus.health?.score || 100) / 100) * 88} 88`}
                                  className="text-aura-400 transition-all duration-700"
                                />
                              </svg>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <span className="text-xs font-bold text-aura-300">{queueStatus.health?.score || 100}</span>
                              </div>
                            </div>
                          </div>
                          <div className="text-3xl font-bold text-gradient-aura mb-3 animate-shimmer">
                            {queueStatus.health?.score || 100}
                          </div>
                          <div className="text-xs text-muted-enhanced flex items-center gap-2">
                            <div className={`w-1 h-1 rounded-full animate-pulse ${
                              (queueStatus.health?.score || 100) > 80 ? 'bg-green-400' : 
                              (queueStatus.health?.score || 100) > 60 ? 'bg-yellow-400' : 'bg-red-400'
                            }`}></div>
                            {queueStatus.health?.status || 'optimal'}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Enhanced Real-time Activity Feed */}
                    <div className="neo-panel p-6 mb-6 bg-gradient-to-r from-space-800/30 to-space-700/30 border border-cosmic-500/20">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gradient-cosmic">⚡ Live Activity Feed</h3>
                        <div className="flex items-center gap-2 text-xs text-green-300">
                          <div className="w-2 h-2 rounded-full bg-green-400 animate-ping"></div>
                          <div className="w-2 h-2 rounded-full bg-green-400 -ml-2"></div>
                          LIVE
                        </div>
                      </div>
                      <div className="space-y-2 max-h-32 overflow-y-auto scrollbar-thin scrollbar-thumb-cosmic-500/50">
                        <div className="flex items-center gap-3 p-2 rounded-lg bg-cosmic-500/10 animate-fade-in">
                          <div className="w-1 h-1 rounded-full bg-green-400"></div>
                          <span className="text-sm text-muted-enhanced">Queue system operational - monitoring {queueStatus.activeRequests} active requests</span>
                          <span className="text-xs text-cosmic-300 ml-auto">now</span>
                        </div>
                        <div className="flex items-center gap-3 p-2 rounded-lg bg-nova-500/10 animate-fade-in">
                          <div className="w-1 h-1 rounded-full bg-nova-400"></div>
                          <span className="text-sm text-muted-enhanced">Large request protection active - {queueStatus.maxTimeoutSeconds}s max timeout</span>
                          <span className="text-xs text-nova-300 ml-auto">5s ago</span>
                        </div>
                        <div className="flex items-center gap-3 p-2 rounded-lg bg-neural-500/10 animate-fade-in">
                          <div className="w-1 h-1 rounded-full bg-neural-400"></div>
                          <span className="text-sm text-muted-enhanced">AI model coordination: R1 + Devstral ready</span>
                          <span className="text-xs text-neural-300 ml-auto">12s ago</span>
                        </div>
                      </div>
                    </div>

                    {/* Queue Actions */}
                    <div className="neo-card group hover:shadow-multi transition-all duration-500">
                      <div className="p-6">
                        <div className="flex items-center gap-4 mb-6">
                          <div className="w-12 h-12 rounded-xl bg-gradient-multi flex items-center justify-center animate-glow">
                            <span className="text-xl">🎛️</span>
                          </div>
                          <div>
                            <h2 className="text-xl font-display font-semibold text-gradient-multi">
                              Queue Actions
                            </h2>
                            <p className="text-sm text-muted-enhanced">Test and control the queue system</p>
                          </div>
                        </div>
                        
                        <div className="flex flex-wrap gap-4">
                          <Button
                            onClick={async () => {
                              console.log('🧪 Testing large request handling...');
                              try {
                                const response = await fetch('/api/agents/queue?action=test-large-request');
                                const result = await response.json();
                                console.log('🎯 Large request test result:', result);
                              } catch (error) {
                                console.error('❌ Test failed:', error);
                              }
                            }}
                            variant="primary"
                            className="animate-glow"
                          >
                            🧪 Test Large Request
                          </Button>
                          <Button
                            onClick={fetchQueueStatus}
                            variant="ghost"
                            disabled={loadingQueueStatus}
                            className="hover:bg-cosmic-500/10"
                          >
                            🔄 Refresh Status
                          </Button>
                          <Button
                            onClick={async () => {
                              console.log('📊 Fetching detailed metrics...');
                              try {
                                const response = await fetch('/api/agents/queue?action=metrics');
                                const result = await response.json();
                                console.log('📈 Queue metrics:', result);
                              } catch (error) {
                                console.error('❌ Metrics fetch failed:', error);
                              }
                            }}
                            variant="outline"
                          >
                            📊 View Metrics
                          </Button>
                        </div>
                      </div>
                    </div>

                    {/* Loading State */}
                    {loadingQueueStatus && (
                      <div className="text-center py-8">
                        <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-cosmic flex items-center justify-center animate-glow mb-4">
                          <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        </div>
                        <p className="text-muted-enhanced">Loading queue status...</p>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* AI Models Integration */}
              {queueStatus && (
                <div className="neo-card group hover:shadow-nova transition-all duration-500">
                  <div className="p-8">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 rounded-xl bg-gradient-nova flex items-center justify-center animate-glow">
                        <span className="text-xl">🤖</span>
                      </div>
                      <div>
                        <h2 className="text-xl font-display font-semibold text-gradient-nova">
                          AI Model Coordination
                        </h2>
                        <p className="text-sm text-muted-enhanced">{queueStatus.modelCoordination}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {queueStatus.aiModelsAvailable?.map((model: string, index: number) => (
                        <div key={model} className="neo-panel p-6">
                          <div className="flex items-center gap-3 mb-4">
                            <div className={`w-8 h-8 rounded-lg ${index === 0 ? 'bg-gradient-cosmic' : 'bg-gradient-nova'} flex items-center justify-center`}>
                              <span className="text-sm font-bold text-white">{index === 0 ? 'R1' : 'DV'}</span>
                            </div>
                            <div>
                              <h3 className="font-semibold text-primary-enhanced">{model}</h3>
                              <p className="text-xs text-muted-enhanced">
                                {index === 0 ? 'Analysis & Debugging' : 'Coordination & Operations'}
                              </p>
                            </div>
                          </div>
                          <div className="text-sm text-muted-enhanced">
                            {index === 0 ? 'Development analysis, debugging, consensus building' : 'Multi-agent coordination, autonomous operations, resource allocation'}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {/* System Health */}
              {queueStatus?.health && (
                <div className="neo-card group hover:shadow-neural transition-all duration-500">
                  <div className="p-8">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-12 h-12 rounded-xl bg-gradient-neural flex items-center justify-center animate-glow">
                        <span className="text-xl">❤️</span>
                      </div>
                      <div>
                        <h2 className="text-xl font-display font-semibold text-gradient-neural">
                          System Health
                        </h2>
                        <p className="text-sm text-muted-enhanced">{queueStatus.health.recommendation}</p>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="neo-panel p-6 text-center">
                        <div className="text-3xl font-bold text-gradient-neural mb-2">
                          {queueStatus.thermalState.toUpperCase()}
                        </div>
                        <div className="text-sm text-muted-enhanced">Thermal State</div>
                      </div>
                      <div className="neo-panel p-6 text-center">
                        <div className="text-3xl font-bold text-gradient-neural mb-2">
                          {queueStatus.performanceMode.toUpperCase()}
                        </div>
                        <div className="text-sm text-muted-enhanced">Performance Mode</div>
                      </div>
                      <div className="neo-panel p-6 text-center">
                        <div className="text-3xl font-bold text-gradient-neural mb-2">
                          {queueStatus.throttlingLevel}%
                        </div>
                        <div className="text-sm text-muted-enhanced">Throttling Level</div>
                      </div>
                    </div>

                    {queueStatus.health.issues && queueStatus.health.issues.length > 0 && (
                      <div className="mt-6 neo-panel p-6 bg-yellow-500/10 border border-yellow-500/30">
                        <h3 className="font-semibold text-yellow-300 mb-3">⚠️ System Issues</h3>
                        <ul className="space-y-2">
                          {queueStatus.health.issues.map((issue: string, index: number) => (
                            <li key={index} className="text-sm text-yellow-200">• {issue}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* All Agents Tab */}
          {activeTab === 'all-agents' && (
            <div>
              <div className="mb-8">
                <h2 className="text-2xl font-display font-bold text-gradient-multi mb-4">
                  All Autonomous Agents
                </h2>
                {agentsSummary ? (
                  <div className="space-y-4 mb-6">
                    <p className="theme-text-secondary">
                      Complete overview of the <span className="text-cosmic-400 font-semibold">{agentsSummary.total}-agent ecosystem</span> with category-based organization
                    </p>
                    
                    {/* Agent Type Stats */}
                    <div className="flex gap-4 text-sm">
                      <span className="px-3 py-1 bg-green-500/20 text-green-400 rounded-full border border-green-500/30">
                        {agentsSummary.enhanced} Enhanced
                      </span>
                      <span className="px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full border border-blue-500/30">
                        {agentsSummary.legacy} Standard
                      </span>
                      <span className="px-3 py-1 bg-purple-500/20 text-purple-400 rounded-full border border-purple-500/30">
                        Avg: {agentsSummary.avgLines} lines
                      </span>
                    </div>

                    {/* Interactive Category Filter */}
                    <div className="neo-card bg-glass-light p-4">
                      <div className="flex items-center justify-between mb-3">
                        <h3 className="text-lg font-display font-bold text-gradient-cosmic">
                          🎨 Agent Categories
                        </h3>
                        <Button
                          onClick={toggleAllCategories}
                          variant="ghost"
                          size="sm"
                          className="text-xs px-3 py-1 hover:bg-cosmic-500/20 transition-all duration-200"
                        >
                          {selectedCategories.length === Object.keys(AGENT_CATEGORIES).length ? 'Deselect All' : 'Select All'}
                        </Button>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                        {Object.entries(AGENT_CATEGORIES).map(([key, category]) => {
                          const count = allAgents.filter(agent => determineAgentCategory(agent.name) === key).length;
                          
                          const isSelected = selectedCategories.includes(key);
                          
                          return (
                            <button
                              key={key}
                              onClick={() => toggleCategory(key)}
                              className={`flex items-center gap-2 p-3 rounded-lg transition-all duration-300 border-2 group relative overflow-hidden ${
                                isSelected 
                                  ? 'border-white/30 shadow-lg shadow-cosmic-500/20' 
                                  : 'border-transparent opacity-60 hover:opacity-100 hover:border-white/20 hover:shadow-lg'
                              }`}
                              style={{ 
                                backgroundColor: `${category.color}${isSelected ? '40' : '15'}`,
                                transform: isSelected ? 'scale(1.02)' : 'scale(1)',
                                boxShadow: isSelected ? `0 0 20px ${category.color}30` : 'none'
                              }}
                              onMouseEnter={(e) => {
                                if (!isSelected) {
                                  e.currentTarget.style.backgroundColor = `${category.color}45`;
                                  e.currentTarget.style.transform = 'scale(1.015)';
                                  e.currentTarget.style.boxShadow = `0 0 15px ${category.color}25`;
                                  e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.15)';
                                } else {
                                  e.currentTarget.style.boxShadow = `0 0 25px ${category.color}40`;
                                  e.currentTarget.style.transform = 'scale(1.025)';
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (!isSelected) {
                                  e.currentTarget.style.backgroundColor = `${category.color}15`;
                                  e.currentTarget.style.transform = 'scale(1)';
                                  e.currentTarget.style.boxShadow = 'none';
                                  e.currentTarget.style.borderColor = 'transparent';
                                } else {
                                  e.currentTarget.style.boxShadow = `0 0 20px ${category.color}30`;
                                  e.currentTarget.style.transform = 'scale(1.02)';
                                }
                              }}
                            >
                              <div 
                                className={`w-4 h-4 rounded-full transition-all duration-300 flex-shrink-0 ${
                                  isSelected 
                                    ? 'ring-2 ring-white/50 shadow-lg animate-pulse' 
                                    : 'group-hover:ring-2 group-hover:ring-white/30 group-hover:shadow-lg'
                                }`} 
                                style={{ 
                                  backgroundColor: category.color,
                                  boxShadow: isSelected ? `0 0 12px ${category.color}60, inset 0 0 8px ${category.color}40` : 'none'
                                }}
                              ></div>
                              <div className="text-left min-w-0 flex-1">
                                <div className={`font-medium truncate transition-all duration-300 ${
                                  isSelected 
                                    ? 'text-white font-bold' 
                                    : 'text-stardust-medium group-hover:text-white group-hover:font-semibold'
                                }`}>
                                  <span className={`transition-all duration-300 inline-block mr-2 ${
                                    isSelected 
                                      ? 'scale-110 drop-shadow-lg' 
                                      : 'group-hover:scale-125 group-hover:drop-shadow-md'
                                  }`}>
                                    {category.icon}
                                  </span>{category.name}
                                </div>
                                <div className={`text-xs truncate transition-all duration-300 ${
                                  isSelected 
                                    ? 'text-stardust-light font-medium' 
                                    : 'text-stardust-medium group-hover:text-stardust-light group-hover:font-medium'
                                }`}>
                                  <span className={`font-semibold transition-colors duration-300 ${
                                    isSelected ? 'text-white' : 'group-hover:text-white'
                                  }`}>
                                    {count} agents
                                  </span> • {category.description}
                                </div>
                              </div>
                              <div className={`text-lg font-bold transition-all duration-300 flex-shrink-0 ${
                                isSelected 
                                  ? 'text-white scale-125 drop-shadow-lg animate-bounce' 
                                  : 'text-stardust-dark group-hover:text-stardust-light group-hover:scale-110'
                              }`} style={{
                                filter: isSelected ? `drop-shadow(0 0 6px ${category.color})` : 'none',
                                animationDuration: isSelected ? '2s' : 'none',
                                animationIterationCount: isSelected ? '1' : 'none'
                              }}>
                                {isSelected ? '✓' : '○'}
                              </div>
                            </button>
                          );
                        })}
                      </div>
                      
                      {/* Filter Status */}
                      <div className="mt-3 pt-3 border-t border-gray-500/30">
                        <div className="flex items-center justify-between text-xs text-stardust-medium">
                          <span>Showing: {filteredAgents.length || allAgents.length}/{allAgents.length} agents</span>
                          <span>{selectedCategories.length}/{Object.keys(AGENT_CATEGORIES).length} categories selected</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <p className="theme-text-secondary">
                    Loading agent ecosystem details...
                  </p>
                )}
              </div>

              {loadingAllAgents ? (
                <div className="neo-card p-8 text-center">
                  <div className="w-12 h-12 mx-auto rounded-xl bg-gradient-cosmic flex items-center justify-center animate-glow mb-4">
                    <div className="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                  <p className="text-muted-enhanced">Discovering agents from filesystem...</p>
                </div>
              ) : allAgents.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {(filteredAgents.length > 0 ? filteredAgents : allAgents).map((agent) => {
                    const agentCategory = determineAgentCategory(agent.name);
                    const categoryInfo = AGENT_CATEGORIES[agentCategory];
                    
                    return (
                      <div key={agent.id} className="group hover:scale-105 transition-all duration-300">
                        <div 
                          className="neo-card p-6 h-full backdrop-blur-md transition-all group-hover:shadow-lg"
                          style={{ 
                            borderColor: `${categoryInfo.color}40`,
                            '--hover-border': `${categoryInfo.color}80`
                          } as React.CSSProperties}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.borderColor = (e.currentTarget.style as any)['--hover-border'];
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.borderColor = `${categoryInfo.color}40`;
                          }}
                        >
                          <div className="flex items-start justify-between mb-4">
                            <div 
                              className="w-12 h-12 rounded-lg flex items-center justify-center animate-glow"
                              style={{ 
                                backgroundColor: categoryInfo.color,
                                boxShadow: `0 0 20px ${categoryInfo.color}40`
                              }}
                            >
                              <span className="text-xl">{categoryInfo.icon}</span>
                            </div>
                            <div className="flex flex-col gap-1">
                              <span 
                                className="px-2 py-1 rounded-full text-xs font-medium border truncate max-w-20"
                                style={{ 
                                  backgroundColor: `${categoryInfo.color}20`,
                                  color: categoryInfo.lightColor,
                                  borderColor: `${categoryInfo.color}30`
                                }}
                                title={agentCategory}
                              >
                                {agentCategory}
                              </span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                agent.type === 'enhanced' 
                                  ? 'bg-green-500/20 text-green-400 border border-green-500/30' 
                                  : 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                              }`}>
                                {agent.type === 'enhanced' ? 'ENHANCED' : 'STANDARD'}
                              </span>
                            </div>
                          </div>
                          
                          <h3 className="text-lg font-bold mb-2 truncate" style={{ color: categoryInfo.lightColor }} title={agent.name}>
                            {agent.name}
                          </h3>
                          
                          <p className="theme-text-secondary text-sm mb-4 line-clamp-2" title={agent.description}>
                            {agent.description}
                          </p>
                          
                          <div className="flex flex-wrap gap-1 mb-4">
                            {agent.capabilities.slice(0, 2).map((capability: string, index: number) => (
                              <span 
                                key={index} 
                                className="px-2 py-1 rounded text-xs truncate max-w-24"
                                style={{ 
                                  backgroundColor: `${categoryInfo.color}15`,
                                  color: categoryInfo.lightColor
                                }}
                                title={capability.replace('_', ' ')}
                              >
                                {capability.replace('_', ' ')}
                              </span>
                            ))}
                            {agent.capabilities.length > 2 && (
                              <span 
                                className="px-2 py-1 rounded text-xs"
                                style={{ 
                                  backgroundColor: `${categoryInfo.color}25`,
                                  color: categoryInfo.lightColor
                                }}
                              >
                                +{agent.capabilities.length - 2}
                              </span>
                            )}
                          </div>
                          
                          <div className="text-xs text-theme-text-muted mb-4">
                            <div className="flex justify-between">
                              <span>Size: {agent.lines} lines</span>
                              <span>Type: {agent.fileSize || 'Unknown'}</span>
                            </div>
                          </div>
                          
                          <div className="flex gap-2 mt-auto">
                            <Button
                              onClick={() => router.push(`/agents/${agent.id}`)}
                              variant="primary"
                              size="sm"
                              className="flex-1"
                              style={{
                                backgroundColor: categoryInfo.color,
                                borderColor: categoryInfo.color
                              }}
                            >
                              View Details
                            </Button>
                            <Button
                              onClick={() => router.push(`/tasks/create?agent=${agent.id}`)}
                              variant="outline"
                              size="sm"
                              style={{
                                borderColor: categoryInfo.color,
                                color: categoryInfo.lightColor
                              }}
                            >
                              Assign
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (allAgents.length > 0 && filteredAgents.length === 0) ? (
                <div className="neo-card p-8 text-center">
                  <div className="w-12 h-12 mx-auto rounded-xl bg-gradient-to-br from-orange-500 to-amber-500 flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-bold text-white mb-2">No Agents Match Filter</h3>
                  <p className="text-muted-enhanced mb-4">
                    No agents found for the selected categories. Try selecting different categories or clearing the filter.
                  </p>
                  <Button
                    onClick={toggleAllCategories}
                    variant="primary"
                    className="animate-glow"
                  >
                    Select All Categories
                  </Button>
                </div>
              ) : (
                <div className="neo-card p-8 text-center">
                  <div className="w-12 h-12 mx-auto rounded-xl bg-gradient-to-br from-red-500 to-orange-500 flex items-center justify-center mb-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                  </div>
                  <p className="text-muted-enhanced">No agents discovered. Please check the agent directory.</p>
                </div>
              )}

              {/* Task Management Integration */}
              {agentsSummary && (
                <div className="mt-8 neo-card p-6 bg-gradient-to-r from-space-800/30 to-space-700/30 border border-cosmic-500/20">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-xl font-display font-semibold text-gradient-cosmic">
                      Task Management Integration
                    </h3>
                    <Button
                      onClick={() => router.push('/tasks')}
                      variant="primary"
                      className="animate-glow"
                    >
                      Open Task Manager
                    </Button>
                  </div>
                  <p className="theme-text-secondary mb-4">
                    Seamlessly coordinate tasks across all {agentsSummary.total} agents using our integrated task management system.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="neo-panel p-4 text-center">
                      <div className="text-2xl font-bold text-gradient-nova mb-2">{agentsSummary.total}</div>
                      <div className="text-sm text-muted-enhanced">Total Agents</div>
                    </div>
                    <div className="neo-panel p-4 text-center">
                      <div className="text-2xl font-bold text-gradient-neural mb-2">{agentsSummary.enhanced}</div>
                      <div className="text-sm text-muted-enhanced">Enhanced Agents</div>
                    </div>
                    <div className="neo-panel p-4 text-center">
                      <div className="text-2xl font-bold text-gradient-aura mb-2">{agentsSummary.legacy}</div>
                      <div className="text-sm text-muted-enhanced">Legacy Agents</div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </Container>
    </div>
  );
} 