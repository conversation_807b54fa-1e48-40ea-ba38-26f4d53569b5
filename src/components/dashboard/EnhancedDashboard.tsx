'use client';

import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  Cpu, 
  Activity, 
  Users, 
  Zap, 
  TrendingUp, 
  Shield, 
  Rocket,
  Eye,
  Settings
} from 'lucide-react';
import { EnhancedLoading } from '../ui/enhanced-loading';
import { Animated<PERSON>utton, GlowCard, FloatingElement } from '../ui/interactive-animations';
import { MetricCard, CircularProgress, AnimatedProgressBar } from '../ui/advanced-charts';
import { useNotifications, useAINotification } from '../ui/enhanced-notifications';
import { useSystemMetrics } from '../monitoring/SystemMonitoringInitializer';

interface DashboardData {
  systemHealth: {
    cpu: number;
    memory: number;
    disk: number;
    score: number;
    status: string;
  };
  agents: {
    total: number;
    active: number;
    performance: number;
  };
  intelligence: {
    decisions: number;
    accuracy: number;
    learning: number;
  };
  projects: {
    total: number;
    active: number;
    completed: number;
  };
}

export default function EnhancedDashboard() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const { getLatestMetrics } = useSystemMetrics();
  const addAINotification = useAINotification();

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Get real system metrics
        const systemMetrics = getLatestMetrics();
        
        // Simulate other data (in production, fetch from APIs)
        const dashboardData: DashboardData = {
          systemHealth: systemMetrics || {
            cpu: 45,
            memory: 68,
            disk: 55,
            score: 85,
            status: 'healthy'
          },
          agents: {
            total: 28,
            active: 26,
            performance: 94
          },
          intelligence: {
            decisions: 1247,
            accuracy: 96.8,
            learning: 89
          },
          projects: {
            total: 12,
            active: 8,
            completed: 4
          }
        };

        setData(dashboardData);
        setLoading(false);

        // Show AI notification for system status
        if (systemMetrics && systemMetrics.health !== 'healthy') {
          addAINotification(
            'System Health Alert',
            `System health is ${systemMetrics.health} with score ${systemMetrics.score}/100`,
            {
              action: {
                label: 'View Details',
                onClick: () => window.open('/monitoring', '_blank')
              }
            }
          );
        }
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
        setLoading(false);
      }
    };

    fetchDashboardData();
    const interval = setInterval(fetchDashboardData, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [getLatestMetrics, addAINotification]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900 p-6">
        <EnhancedLoading 
          variant="cosmic" 
          size="lg" 
          text="Loading CreAItive Dashboard..." 
          fullScreen 
        />
      </div>
    );
  }

  if (!data) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900 p-6 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Dashboard Unavailable</h2>
          <p className="text-stardust-300 mb-6">Unable to load dashboard data</p>
          <AnimatedButton onClick={() => window.location.reload()}>
            Retry
          </AnimatedButton>
        </div>
      </div>
    );
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: <Eye className="w-4 h-4" /> },
    { id: 'agents', label: 'Agents', icon: <Users className="w-4 h-4" /> },
    { id: 'intelligence', label: 'Intelligence', icon: <Brain className="w-4 h-4" /> },
    { id: 'system', label: 'System', icon: <Settings className="w-4 h-4" /> }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-space-900 via-space-800 to-cosmic-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <FloatingElement delay={0} duration={4}>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-cosmic-400 via-neural-400 to-nova-400 bg-clip-text text-transparent mb-2">
              CreAItive Dashboard
            </h1>
          </FloatingElement>
          <FloatingElement delay={0.2} duration={4}>
            <p className="text-stardust-300 text-lg">
              Real-time insights into your AI-powered creative platform
            </p>
          </FloatingElement>
        </div>

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="flex space-x-1 bg-glass border border-cosmic-500/30 rounded-lg p-1 backdrop-blur-md">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all duration-200 ${
                  activeTab === tab.id
                    ? 'bg-cosmic-500 text-white shadow-lg'
                    : 'text-stardust-300 hover:text-white hover:bg-cosmic-500/20'
                }`}
              >
                {tab.icon}
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <FloatingElement delay={0.1}>
                <GlowCard>
                  <MetricCard
                    title="System Health"
                    value={`${data.systemHealth.score}/100`}
                    change={5.2}
                    trend="up"
                    icon={<Shield className="w-5 h-5" />}
                  />
                </GlowCard>
              </FloatingElement>

              <FloatingElement delay={0.2}>
                <GlowCard>
                  <MetricCard
                    title="Active Agents"
                    value={`${data.agents.active}/${data.agents.total}`}
                    change={2.1}
                    trend="up"
                    icon={<Users className="w-5 h-5" />}
                  />
                </GlowCard>
              </FloatingElement>

              <FloatingElement delay={0.3}>
                <GlowCard>
                  <MetricCard
                    title="AI Decisions"
                    value={data.intelligence.decisions.toLocaleString()}
                    change={12.8}
                    trend="up"
                    icon={<Brain className="w-5 h-5" />}
                  />
                </GlowCard>
              </FloatingElement>

              <FloatingElement delay={0.4}>
                <GlowCard>
                  <MetricCard
                    title="Active Projects"
                    value={data.projects.active}
                    change={-1.2}
                    trend="down"
                    icon={<Rocket className="w-5 h-5" />}
                  />
                </GlowCard>
              </FloatingElement>
            </div>

            {/* Performance Overview */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <FloatingElement delay={0.5}>
                <GlowCard className="lg:col-span-2">
                  <div className="bg-glass border border-cosmic-500/30 rounded-xl p-6 backdrop-blur-md">
                    <h3 className="text-xl font-semibold text-white mb-6">System Performance</h3>
                    <div className="space-y-6">
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-stardust-300">CPU Usage</span>
                          <span className="text-cosmic-400 font-medium">{data.systemHealth.cpu.toFixed(1)}%</span>
                        </div>
                        <AnimatedProgressBar 
                          value={data.systemHealth.cpu} 
                          color={data.systemHealth.cpu > 80 ? 'red' : data.systemHealth.cpu > 60 ? 'yellow' : 'green'}
                        />
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-stardust-300">Memory Usage</span>
                          <span className="text-cosmic-400 font-medium">{data.systemHealth.memory.toFixed(1)}%</span>
                        </div>
                        <AnimatedProgressBar 
                          value={data.systemHealth.memory} 
                          color={data.systemHealth.memory > 85 ? 'red' : data.systemHealth.memory > 75 ? 'yellow' : 'green'}
                        />
                      </div>
                      <div>
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-stardust-300">Disk Usage</span>
                          <span className="text-cosmic-400 font-medium">{data.systemHealth.disk.toFixed(1)}%</span>
                        </div>
                        <AnimatedProgressBar 
                          value={data.systemHealth.disk} 
                          color={data.systemHealth.disk > 90 ? 'red' : data.systemHealth.disk > 80 ? 'yellow' : 'green'}
                        />
                      </div>
                    </div>
                  </div>
                </GlowCard>
              </FloatingElement>

              <FloatingElement delay={0.6}>
                <GlowCard>
                  <div className="bg-glass border border-cosmic-500/30 rounded-xl p-6 backdrop-blur-md">
                    <h3 className="text-xl font-semibold text-white mb-6">AI Performance</h3>
                    <div className="space-y-6">
                      <div className="text-center">
                        <CircularProgress 
                          value={data.intelligence.accuracy} 
                          size={120}
                          color="cosmic"
                        />
                        <p className="text-stardust-300 mt-2">Decision Accuracy</p>
                      </div>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-stardust-300">Learning Rate</span>
                          <span className="text-cosmic-400">{data.intelligence.learning}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-stardust-300">Agent Performance</span>
                          <span className="text-cosmic-400">{data.agents.performance}%</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </GlowCard>
              </FloatingElement>
            </div>

            {/* Quick Actions */}
            <FloatingElement delay={0.7}>
              <div className="bg-glass border border-cosmic-500/30 rounded-xl p-6 backdrop-blur-md">
                <h3 className="text-xl font-semibold text-white mb-6">Quick Actions</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <AnimatedButton
                    variant="cosmic"
                    className="w-full"
                    onClick={() => window.open('/agents', '_blank')}
                  >
                    <Users className="w-4 h-4 mr-2" />
                    Agent Hub
                  </AnimatedButton>
                  <AnimatedButton
                    variant="primary"
                    className="w-full"
                    onClick={() => window.open('/intelligence', '_blank')}
                  >
                    <Brain className="w-4 h-4 mr-2" />
                    Intelligence Hub
                  </AnimatedButton>
                  <AnimatedButton
                    variant="secondary"
                    className="w-full"
                    onClick={() => window.open('/creative', '_blank')}
                  >
                    <Zap className="w-4 h-4 mr-2" />
                    Creative Hub
                  </AnimatedButton>
                </div>
              </div>
            </FloatingElement>
          </div>
        )}

        {/* Other tabs would be implemented similarly */}
        {activeTab !== 'overview' && (
          <div className="text-center py-12">
            <h3 className="text-2xl font-semibold text-white mb-4">
              {tabs.find(t => t.id === activeTab)?.label} Dashboard
            </h3>
            <p className="text-stardust-300 mb-6">
              Detailed {activeTab} metrics and controls coming soon...
            </p>
            <AnimatedButton onClick={() => setActiveTab('overview')}>
              Back to Overview
            </AnimatedButton>
          </div>
        )}
      </div>
    </div>
  );
}
