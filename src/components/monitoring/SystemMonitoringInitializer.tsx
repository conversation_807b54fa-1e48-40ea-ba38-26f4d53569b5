'use client';

import { useEffect } from 'react';

export default function SystemMonitoringInitializer() {
  useEffect(() => {
    // Initialize system monitoring on the client side
    console.log('🔍 Initializing client-side system monitoring...');
    
    // Start periodic health checks
    const healthCheckInterval = setInterval(async () => {
      try {
        const response = await fetch('/api/monitoring/real-time-metrics?format=compact');
        const data = await response.json();
        
        if (data.success) {
          // Store latest metrics in sessionStorage for components to access
          sessionStorage.setItem('latest-system-metrics', JSON.stringify({
            timestamp: data.timestamp,
            cpu: data.metrics.cpu,
            memory: data.metrics.memory,
            disk: data.metrics.disk,
            health: data.metrics.health,
            score: data.metrics.score
          }));
          
          // Dispatch custom event for components listening to metrics updates
          window.dispatchEvent(new CustomEvent('system-metrics-updated', {
            detail: data.metrics
          }));
          
          // Log health warnings
          if (data.metrics.health !== 'healthy') {
            console.warn(`⚠️ System health: ${data.metrics.health} (score: ${data.metrics.score})`);
          }
        }
      } catch (error) {
        console.error('❌ Health check failed:', error);
      }
    }, 30000); // Check every 30 seconds
    
    // Initial health check
    const initialHealthCheck = async () => {
      try {
        const response = await fetch('/api/monitoring/real-time-metrics?format=compact');
        const data = await response.json();
        
        if (data.success) {
          console.log(`✅ Initial system health: ${data.metrics.health} (CPU: ${data.metrics.cpu}%, Memory: ${data.metrics.memory}%)`);
          
          // Store initial metrics
          sessionStorage.setItem('latest-system-metrics', JSON.stringify({
            timestamp: data.timestamp,
            cpu: data.metrics.cpu,
            memory: data.metrics.memory,
            disk: data.metrics.disk,
            health: data.metrics.health,
            score: data.metrics.score
          }));
        }
      } catch (error) {
        console.error('❌ Initial health check failed:', error);
      }
    };
    
    // Run initial check
    initialHealthCheck();
    
    // Cleanup interval on unmount
    return () => {
      clearInterval(healthCheckInterval);
    };
  }, []);

  // This component doesn't render anything
  return null;
}

// Hook for components to access real-time system metrics
export function useSystemMetrics() {
  const getLatestMetrics = () => {
    try {
      const stored = sessionStorage.getItem('latest-system-metrics');
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Failed to get latest metrics:', error);
      return null;
    }
  };

  const subscribeToMetrics = (callback: (metrics: any) => void) => {
    const handleUpdate = (event: CustomEvent) => {
      callback(event.detail);
    };

    window.addEventListener('system-metrics-updated', handleUpdate as EventListener);
    
    return () => {
      window.removeEventListener('system-metrics-updated', handleUpdate as EventListener);
    };
  };

  return {
    getLatestMetrics,
    subscribeToMetrics
  };
}
