"use client";

import React, { useState, useEffect } from 'react';
import { Al<PERSON><PERSON>riangle, CheckCircle, Activity, Cpu, HardDrive, Wifi, Database } from 'lucide-react';
import { useSystemMetrics } from './SystemMonitoringInitializer';

interface SystemHealthProps {
  className?: string;
  children?: React.ReactNode;
}

interface SystemMetrics {
  cpu: number;
  memory: number;
  disk: number;
  health: 'healthy' | 'warning' | 'critical';
  score: number;
  timestamp: string;
}

export default function SystemHealth() {
  const { getLatestMetrics, subscribeToMetrics } = useSystemMetrics();
  const [metrics, setMetrics] = useState<SystemMetrics | null>(null);
  const [isReal, setIsReal] = useState(false);

  useEffect(() => {
    // Get initial metrics
    const initialMetrics = getLatestMetrics();
    if (initialMetrics) {
      setMetrics(initialMetrics);
      setIsReal(true);
    }

    // Subscribe to real-time updates
    const unsubscribe = subscribeToMetrics((newMetrics) => {
      setMetrics({
        cpu: newMetrics.cpu,
        memory: newMetrics.memory,
        disk: newMetrics.disk,
        health: newMetrics.health,
        score: newMetrics.score,
        timestamp: new Date().toISOString()
      });
      setIsReal(true);
    });

    // Fallback: fetch directly if no metrics available
    if (!initialMetrics) {
      const fetchMetrics = async () => {
        try {
          const response = await fetch('/api/monitoring/real-time-metrics?format=compact');
          if (response.ok) {
            const data = await response.json();
            if (data.success) {
              setMetrics({
                cpu: data.metrics.cpu,
                memory: data.metrics.memory,
                disk: data.metrics.disk,
                health: data.metrics.health,
                score: data.metrics.score,
                timestamp: data.timestamp
              });
              setIsReal(true);
            }
          }
        } catch (error) {
          console.log('Using fallback - real monitoring not available yet');
          // Set fallback metrics
          setMetrics({
            cpu: 45,
            memory: 68,
            disk: 60,
            health: 'warning',
            score: 75,
            timestamp: new Date().toISOString()
          });
        }
      };

      fetchMetrics();
    }

    return unsubscribe;
  }, [getLatestMetrics, subscribeToMetrics]);

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-400 border-green-400 bg-green-400/10';
      case 'warning': return 'text-yellow-400 border-yellow-400 bg-yellow-400/10';
      case 'critical': return 'text-red-400 border-red-400 bg-red-400/10';
      default: return 'text-gray-400 border-gray-400 bg-gray-400/10';
    }
  };

  const getMetricColor = (value: number, thresholds: { good: number; warning: number }) => {
    if (value <= thresholds.good) return 'text-green-400';
    if (value <= thresholds.warning) return 'text-yellow-400';
    return 'text-red-400';
  };

  if (!metrics) {
    return (
      <div className="neo-component fixed bottom-4 right-4 bg-glass border border-cosmic-500/30 rounded-xl p-4 shadow-cosmic backdrop-blur-md z-50">
        <div className="flex items-center gap-2">
          <Activity className="w-4 h-4 text-cosmic-400 animate-pulse" />
          <span className="text-sm font-medium text-stardust-300">Loading...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="neo-component fixed bottom-4 right-4 bg-glass border border-cosmic-500/30 rounded-xl p-4 shadow-cosmic backdrop-blur-md z-50">
      <div className="flex items-center gap-2 mb-3">
        <Activity className="w-4 h-4 text-cosmic-400" />
        <span className="text-sm font-medium text-stardust-300">System Health</span>
        {isReal && (
          <span className="text-xs text-cosmic-400 bg-cosmic-500/20 px-2 py-1 rounded-full">
            Real-Time
          </span>
        )}
        <span className="text-xs text-gray-400">
          {metrics.score}/100
        </span>
      </div>

      <div className="space-y-2">
        {/* Overall Health */}
        <div className={`flex items-center gap-2 px-2 py-1 rounded-lg border ${getHealthColor(metrics.health)}`}>
          {metrics.health === 'critical' ? (
            <AlertTriangle className="w-3 h-3" />
          ) : (
            <CheckCircle className="w-3 h-3" />
          )}
          <span className="text-xs font-medium capitalize">{metrics.health}</span>
        </div>

        {/* Real System Metrics */}
        <div className="grid grid-cols-1 gap-1 text-xs">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <Cpu className="w-3 h-3 text-gray-400" />
              <span className="text-gray-300">CPU</span>
            </div>
            <span className={getMetricColor(metrics.cpu, { good: 70, warning: 85 })}>
              {metrics.cpu.toFixed(1)}%
            </span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <HardDrive className="w-3 h-3 text-gray-400" />
              <span className="text-gray-300">Memory</span>
            </div>
            <span className={getMetricColor(metrics.memory, { good: 75, warning: 85 })}>
              {metrics.memory.toFixed(1)}%
            </span>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-1">
              <Database className="w-3 h-3 text-gray-400" />
              <span className="text-gray-300">Disk</span>
            </div>
            <span className={getMetricColor(metrics.disk, { good: 80, warning: 90 })}>
              {metrics.disk.toFixed(1)}%
            </span>
          </div>
        </div>

        <div className="text-xs text-gray-500 mt-2 pt-2 border-t border-gray-700">
          Updated: {new Date(metrics.timestamp).toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
} 

SystemHealth.displayName = 'SystemHealth';