'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Eye, EyeOff, Search, X, Check, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface EnhancedInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  success?: string;
  hint?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  loading?: boolean;
}

export function EnhancedInput({
  label,
  error,
  success,
  hint,
  leftIcon,
  rightIcon,
  loading,
  className,
  type,
  ...props
}: EnhancedInputProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const isPassword = type === 'password';
  const inputType = isPassword && showPassword ? 'text' : type;

  const getStatusIcon = () => {
    if (loading) {
      return <div className="w-4 h-4 border-2 border-cosmic-400 border-t-transparent rounded-full animate-spin" />;
    }
    if (error) {
      return <AlertCircle className="w-4 h-4 text-red-400" />;
    }
    if (success) {
      return <Check className="w-4 h-4 text-green-400" />;
    }
    return null;
  };

  const getStatusColor = () => {
    if (error) return 'border-red-500/50 focus:border-red-400';
    if (success) return 'border-green-500/50 focus:border-green-400';
    return 'border-cosmic-500/30 focus:border-cosmic-400';
  };

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <label className="block text-sm font-medium text-stardust-300">
          {label}
        </label>
      )}
      
      <div className="relative">
        {leftIcon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-stardust-400">
            {leftIcon}
          </div>
        )}
        
        <input
          ref={inputRef}
          type={inputType}
          className={cn(
            'w-full px-4 py-3 bg-glass border rounded-lg',
            'text-white placeholder-stardust-400',
            'transition-all duration-200',
            'focus:outline-none focus:ring-2 focus:ring-cosmic-400/50',
            leftIcon && 'pl-10',
            (rightIcon || isPassword || getStatusIcon()) && 'pr-10',
            getStatusColor(),
            isFocused && 'shadow-lg shadow-cosmic-500/20'
          )}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          {...props}
        />
        
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
          {getStatusIcon()}
          {isPassword && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="text-stardust-400 hover:text-white transition-colors"
            >
              {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </button>
          )}
          {rightIcon && !isPassword && !getStatusIcon() && (
            <div className="text-stardust-400">{rightIcon}</div>
          )}
        </div>
      </div>
      
      {(error || success || hint) && (
        <div className="text-sm">
          {error && <p className="text-red-400">{error}</p>}
          {success && <p className="text-green-400">{success}</p>}
          {hint && !error && !success && <p className="text-stardust-400">{hint}</p>}
        </div>
      )}
    </div>
  );
}

interface SearchInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  onClear?: () => void;
  loading?: boolean;
}

export function SearchInput({
  value,
  onChange,
  placeholder = "Search...",
  className,
  onClear,
  loading
}: SearchInputProps) {
  return (
    <div className={cn('relative', className)}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-stardust-400" />
      <input
        type="text"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder={placeholder}
        className="w-full pl-10 pr-10 py-3 bg-glass border border-cosmic-500/30 rounded-lg text-white placeholder-stardust-400 focus:outline-none focus:ring-2 focus:ring-cosmic-400/50 focus:border-cosmic-400 transition-all duration-200"
      />
      <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center space-x-2">
        {loading && (
          <div className="w-4 h-4 border-2 border-cosmic-400 border-t-transparent rounded-full animate-spin" />
        )}
        {value && onClear && (
          <button
            onClick={onClear}
            className="text-stardust-400 hover:text-white transition-colors"
          >
            <X className="w-4 h-4" />
          </button>
        )}
      </div>
    </div>
  );
}

interface TagInputProps {
  tags: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
  className?: string;
  maxTags?: number;
}

export function TagInput({
  tags,
  onChange,
  placeholder = "Add tags...",
  className,
  maxTags
}: TagInputProps) {
  const [inputValue, setInputValue] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !tags.includes(trimmedTag) && (!maxTags || tags.length < maxTags)) {
      onChange([...tags, trimmedTag]);
      setInputValue('');
    }
  };

  const removeTag = (index: number) => {
    onChange(tags.filter((_, i) => i !== index));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addTag(inputValue);
    } else if (e.key === 'Backspace' && !inputValue && tags.length > 0) {
      removeTag(tags.length - 1);
    }
  };

  return (
    <div className={cn('space-y-2', className)}>
      <div className="flex flex-wrap gap-2 p-3 bg-glass border border-cosmic-500/30 rounded-lg min-h-[48px] focus-within:border-cosmic-400 focus-within:ring-2 focus-within:ring-cosmic-400/50 transition-all duration-200">
        {tags.map((tag, index) => (
          <span
            key={index}
            className="inline-flex items-center px-2 py-1 bg-cosmic-500/20 text-cosmic-300 text-sm rounded-md"
          >
            {tag}
            <button
              onClick={() => removeTag(index)}
              className="ml-1 text-cosmic-400 hover:text-white"
            >
              <X className="w-3 h-3" />
            </button>
          </span>
        ))}
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={tags.length === 0 ? placeholder : ''}
          className="flex-1 min-w-[120px] bg-transparent text-white placeholder-stardust-400 outline-none"
          disabled={maxTags ? tags.length >= maxTags : false}
        />
      </div>
      {maxTags && (
        <p className="text-xs text-stardust-400">
          {tags.length}/{maxTags} tags
        </p>
      )}
    </div>
  );
}

interface SliderProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  label?: string;
  showValue?: boolean;
  className?: string;
}

export function EnhancedSlider({
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  label,
  showValue = true,
  className
}: SliderProps) {
  const percentage = ((value - min) / (max - min)) * 100;

  return (
    <div className={cn('space-y-2', className)}>
      {label && (
        <div className="flex justify-between items-center">
          <label className="text-sm font-medium text-stardust-300">{label}</label>
          {showValue && (
            <span className="text-sm text-cosmic-400 font-medium">{value}</span>
          )}
        </div>
      )}
      
      <div className="relative">
        <input
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          className="w-full h-2 bg-stardust-800 rounded-lg appearance-none cursor-pointer slider"
          style={{
            background: `linear-gradient(to right, rgb(110, 122, 255) 0%, rgb(110, 122, 255) ${percentage}%, rgb(55, 65, 81) ${percentage}%, rgb(55, 65, 81) 100%)`
          }}
        />
      </div>
    </div>
  );
}

interface ToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  description?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

export function EnhancedToggle({
  checked,
  onChange,
  label,
  description,
  className,
  size = 'md'
}: ToggleProps) {
  const sizeClasses = {
    sm: 'w-8 h-4',
    md: 'w-10 h-5',
    lg: 'w-12 h-6'
  };

  const thumbSizeClasses = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  return (
    <div className={cn('flex items-start space-x-3', className)}>
      <button
        type="button"
        onClick={() => onChange(!checked)}
        className={cn(
          'relative inline-flex items-center rounded-full transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-cosmic-400/50',
          sizeClasses[size],
          checked ? 'bg-cosmic-500' : 'bg-stardust-700'
        )}
      >
        <span
          className={cn(
            'inline-block rounded-full bg-white transition-transform duration-200',
            thumbSizeClasses[size],
            checked ? 'translate-x-5' : 'translate-x-0.5'
          )}
        />
      </button>
      
      {(label || description) && (
        <div className="flex-1">
          {label && (
            <p className="text-sm font-medium text-white">{label}</p>
          )}
          {description && (
            <p className="text-sm text-stardust-400">{description}</p>
          )}
        </div>
      )}
    </div>
  );
}
