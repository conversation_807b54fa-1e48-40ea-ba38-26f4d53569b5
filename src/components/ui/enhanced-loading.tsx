'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface EnhancedLoadingProps {
  variant?: 'spinner' | 'pulse' | 'skeleton' | 'dots' | 'wave' | 'cosmic';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  text?: string;
  fullScreen?: boolean;
}

export function EnhancedLoading({ 
  variant = 'cosmic', 
  size = 'md', 
  className,
  text,
  fullScreen = false 
}: EnhancedLoadingProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  };

  const containerClasses = cn(
    'flex items-center justify-center',
    fullScreen && 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50',
    className
  );

  const renderSpinner = () => (
    <div className={cn(
      'animate-spin rounded-full border-2 border-cosmic-500/30 border-t-cosmic-400',
      sizeClasses[size]
    )} />
  );

  const renderPulse = () => (
    <div className={cn(
      'animate-pulse bg-cosmic-400/50 rounded-full',
      sizeClasses[size]
    )} />
  );

  const renderSkeleton = () => (
    <div className="space-y-3 w-full max-w-sm">
      <div className="h-4 bg-cosmic-500/30 rounded animate-pulse" />
      <div className="h-4 bg-cosmic-500/30 rounded animate-pulse w-3/4" />
      <div className="h-4 bg-cosmic-500/30 rounded animate-pulse w-1/2" />
    </div>
  );

  const renderDots = () => (
    <div className="flex space-x-1">
      {[0, 1, 2].map((i) => (
        <div
          key={i}
          className={cn(
            'bg-cosmic-400 rounded-full animate-bounce',
            size === 'sm' ? 'w-1 h-1' : size === 'md' ? 'w-2 h-2' : 'w-3 h-3'
          )}
          style={{ animationDelay: `${i * 0.1}s` }}
        />
      ))}
    </div>
  );

  const renderWave = () => (
    <div className="flex items-end space-x-1">
      {[0, 1, 2, 3, 4].map((i) => (
        <div
          key={i}
          className={cn(
            'bg-cosmic-400 animate-pulse',
            size === 'sm' ? 'w-1' : size === 'md' ? 'w-2' : 'w-3',
            'h-2'
          )}
          style={{ 
            animationDelay: `${i * 0.1}s`,
            height: `${Math.random() * 20 + 10}px`
          }}
        />
      ))}
    </div>
  );

  const renderCosmic = () => (
    <div className="relative">
      <div className={cn(
        'absolute inset-0 rounded-full bg-gradient-to-r from-cosmic-400 to-nebula-400 animate-spin',
        sizeClasses[size]
      )} style={{ animationDuration: '2s' }} />
      <div className={cn(
        'absolute inset-1 rounded-full bg-black/80 backdrop-blur-sm',
        size === 'sm' ? 'inset-0.5' : size === 'md' ? 'inset-1' : 'inset-2'
      )} />
      <div className={cn(
        'absolute inset-0 rounded-full bg-gradient-to-r from-cosmic-400/50 to-nebula-400/50 animate-pulse',
        sizeClasses[size]
      )} />
    </div>
  );

  const renderVariant = () => {
    switch (variant) {
      case 'spinner': return renderSpinner();
      case 'pulse': return renderPulse();
      case 'skeleton': return renderSkeleton();
      case 'dots': return renderDots();
      case 'wave': return renderWave();
      case 'cosmic': return renderCosmic();
      default: return renderCosmic();
    }
  };

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center space-y-3">
        {renderVariant()}
        {text && (
          <p className="text-sm text-stardust-300 animate-pulse">
            {text}
          </p>
        )}
      </div>
    </div>
  );
}

// Skeleton components for different content types
export function SkeletonCard({ className }: { className?: string }) {
  return (
    <div className={cn('p-4 space-y-3', className)}>
      <div className="h-4 bg-cosmic-500/30 rounded animate-pulse" />
      <div className="h-4 bg-cosmic-500/30 rounded animate-pulse w-3/4" />
      <div className="h-20 bg-cosmic-500/30 rounded animate-pulse" />
      <div className="flex space-x-2">
        <div className="h-8 bg-cosmic-500/30 rounded animate-pulse flex-1" />
        <div className="h-8 bg-cosmic-500/30 rounded animate-pulse w-20" />
      </div>
    </div>
  );
}

export function SkeletonTable({ rows = 5, className }: { rows?: number; className?: string }) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4">
          <div className="h-4 bg-cosmic-500/30 rounded animate-pulse w-1/4" />
          <div className="h-4 bg-cosmic-500/30 rounded animate-pulse w-1/3" />
          <div className="h-4 bg-cosmic-500/30 rounded animate-pulse w-1/6" />
          <div className="h-4 bg-cosmic-500/30 rounded animate-pulse w-1/4" />
        </div>
      ))}
    </div>
  );
}

export function SkeletonChart({ className }: { className?: string }) {
  return (
    <div className={cn('p-4', className)}>
      <div className="h-4 bg-cosmic-500/30 rounded animate-pulse mb-4 w-1/3" />
      <div className="h-40 bg-cosmic-500/30 rounded animate-pulse mb-2" />
      <div className="flex justify-between">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="h-3 bg-cosmic-500/30 rounded animate-pulse w-8" />
        ))}
      </div>
    </div>
  );
}
