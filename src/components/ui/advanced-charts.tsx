'use client';

import React, { useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';

interface DataPoint {
  label: string;
  value: number;
  color?: string;
}

interface ChartProps {
  data: DataPoint[];
  className?: string;
  animated?: boolean;
  showLabels?: boolean;
  showValues?: boolean;
}

export function AnimatedProgressBar({ 
  value, 
  max = 100, 
  className, 
  color = 'cosmic',
  showPercentage = true,
  animated = true 
}: {
  value: number;
  max?: number;
  className?: string;
  color?: 'cosmic' | 'nebula' | 'green' | 'red' | 'yellow';
  showPercentage?: boolean;
  animated?: boolean;
}) {
  const [animatedValue, setAnimatedValue] = useState(0);
  const percentage = Math.min((value / max) * 100, 100);

  useEffect(() => {
    if (animated) {
      const timer = setTimeout(() => {
        setAnimatedValue(percentage);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setAnimatedValue(percentage);
    }
  }, [percentage, animated]);

  const colorClasses = {
    cosmic: 'bg-gradient-to-r from-cosmic-500 to-nebula-500',
    nebula: 'bg-gradient-to-r from-nebula-500 to-cosmic-500',
    green: 'bg-gradient-to-r from-green-500 to-emerald-500',
    red: 'bg-gradient-to-r from-red-500 to-rose-500',
    yellow: 'bg-gradient-to-r from-yellow-500 to-amber-500'
  };

  return (
    <div className={cn('relative', className)}>
      <div className="w-full bg-stardust-800 rounded-full h-2 overflow-hidden">
        <div
          className={cn(
            'h-full transition-all duration-1000 ease-out rounded-full',
            colorClasses[color]
          )}
          style={{ width: `${animatedValue}%` }}
        />
      </div>
      {showPercentage && (
        <span className="absolute right-0 -top-6 text-xs text-stardust-300">
          {Math.round(percentage)}%
        </span>
      )}
    </div>
  );
}

export function CircularProgress({
  value,
  max = 100,
  size = 80,
  strokeWidth = 8,
  className,
  color = 'cosmic',
  showValue = true,
  animated = true
}: {
  value: number;
  max?: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
  color?: 'cosmic' | 'nebula' | 'green' | 'red' | 'yellow';
  showValue?: boolean;
  animated?: boolean;
}) {
  const [animatedValue, setAnimatedValue] = useState(0);
  const percentage = Math.min((value / max) * 100, 100);
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const offset = circumference - (animatedValue / 100) * circumference;

  useEffect(() => {
    if (animated) {
      const timer = setTimeout(() => {
        setAnimatedValue(percentage);
      }, 100);
      return () => clearTimeout(timer);
    } else {
      setAnimatedValue(percentage);
    }
  }, [percentage, animated]);

  const colorClasses = {
    cosmic: 'stroke-cosmic-500',
    nebula: 'stroke-nebula-500',
    green: 'stroke-green-500',
    red: 'stroke-red-500',
    yellow: 'stroke-yellow-500'
  };

  return (
    <div className={cn('relative inline-flex items-center justify-center', className)}>
      <svg width={size} height={size} className="transform -rotate-90">
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          className="text-stardust-800"
        />
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={circumference}
          strokeDashoffset={offset}
          strokeLinecap="round"
          className={cn('transition-all duration-1000 ease-out', colorClasses[color])}
        />
      </svg>
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center">
          <span className="text-sm font-medium text-white">
            {Math.round(percentage)}%
          </span>
        </div>
      )}
    </div>
  );
}

export function MiniBarChart({ data, className, height = 40 }: ChartProps & { height?: number }) {
  const maxValue = Math.max(...data.map(d => d.value));

  return (
    <div className={cn('flex items-end space-x-1', className)} style={{ height }}>
      {data.map((item, index) => (
        <div
          key={index}
          className="flex-1 bg-gradient-to-t from-cosmic-500 to-nebula-500 rounded-t-sm transition-all duration-500 hover:from-cosmic-400 hover:to-nebula-400"
          style={{
            height: `${(item.value / maxValue) * 100}%`,
            animationDelay: `${index * 100}ms`
          }}
          title={`${item.label}: ${item.value}`}
        />
      ))}
    </div>
  );
}

export function SparklineChart({ data, className, color = 'cosmic' }: ChartProps & { color?: string }) {
  const svgRef = useRef<SVGSVGElement>(null);
  const [path, setPath] = useState('');

  useEffect(() => {
    if (!svgRef.current || data.length === 0) return;

    const svg = svgRef.current;
    const rect = svg.getBoundingClientRect();
    const width = rect.width || 200;
    const height = rect.height || 40;

    const maxValue = Math.max(...data.map(d => d.value));
    const minValue = Math.min(...data.map(d => d.value));
    const range = maxValue - minValue || 1;

    const points = data.map((item, index) => {
      const x = (index / (data.length - 1)) * width;
      const y = height - ((item.value - minValue) / range) * height;
      return `${x},${y}`;
    });

    setPath(`M ${points.join(' L ')}`);
  }, [data]);

  return (
    <svg
      ref={svgRef}
      className={cn('w-full h-10', className)}
      viewBox="0 0 200 40"
      preserveAspectRatio="none"
    >
      <path
        d={path}
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        className={`text-${color}-500`}
      />
    </svg>
  );
}

export function MetricCard({
  title,
  value,
  change,
  trend,
  className,
  icon
}: {
  title: string;
  value: string | number;
  change?: number;
  trend?: 'up' | 'down' | 'neutral';
  className?: string;
  icon?: React.ReactNode;
}) {
  const getTrendColor = () => {
    switch (trend) {
      case 'up': return 'text-green-400';
      case 'down': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getTrendIcon = () => {
    switch (trend) {
      case 'up': return '↗';
      case 'down': return '↘';
      default: return '→';
    }
  };

  return (
    <div className={cn(
      'bg-glass border border-cosmic-500/30 rounded-xl p-4 backdrop-blur-md',
      'hover:border-cosmic-400/50 transition-all duration-200',
      className
    )}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-stardust-300">{title}</h3>
        {icon && <div className="text-cosmic-400">{icon}</div>}
      </div>
      
      <div className="flex items-end justify-between">
        <div className="text-2xl font-bold text-white">{value}</div>
        {change !== undefined && (
          <div className={cn('text-sm font-medium', getTrendColor())}>
            {getTrendIcon()} {Math.abs(change)}%
          </div>
        )}
      </div>
    </div>
  );
}

export function HeatmapGrid({
  data,
  rows,
  cols,
  className
}: {
  data: number[][];
  rows: string[];
  cols: string[];
  className?: string;
}) {
  const maxValue = Math.max(...data.flat());
  const minValue = Math.min(...data.flat());
  const range = maxValue - minValue || 1;

  const getIntensity = (value: number) => {
    return (value - minValue) / range;
  };

  return (
    <div className={cn('inline-block', className)}>
      <div className="grid gap-1" style={{ gridTemplateColumns: `auto repeat(${cols.length}, 1fr)` }}>
        {/* Header */}
        <div></div>
        {cols.map((col, index) => (
          <div key={index} className="text-xs text-stardust-300 text-center p-1">
            {col}
          </div>
        ))}
        
        {/* Rows */}
        {rows.map((row, rowIndex) => (
          <React.Fragment key={rowIndex}>
            <div className="text-xs text-stardust-300 p-1 flex items-center">
              {row}
            </div>
            {data[rowIndex]?.map((value, colIndex) => (
              <div
                key={colIndex}
                className="w-6 h-6 rounded-sm transition-all duration-200 hover:scale-110"
                style={{
                  backgroundColor: `rgba(110, 122, 255, ${getIntensity(value) * 0.8 + 0.1})`
                }}
                title={`${row} - ${cols[colIndex]}: ${value}`}
              />
            ))}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
}
