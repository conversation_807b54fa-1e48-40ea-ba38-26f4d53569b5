"use client";

import React from 'react';

/**
 * 🎯 FLOATING BUTTON CONTAINER
 * 
 * Manages positioning of floating action buttons to prevent overlap
 * and ensure consistent alignment across the platform
 */

interface FloatingButtonContainerProps {
  children: React.ReactNode;
  position?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
  spacing?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const FloatingButtonContainer: React.FC<FloatingButtonContainerProps> = ({
  children,
  position = 'bottom-right',
  spacing = 'md',
  className = ''
}) => {
  const positionClasses = {
    'bottom-left': 'bottom-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'top-left': 'top-4 left-4',
    'top-right': 'top-4 right-4'
  };

  const spacingClasses = {
    'sm': 'gap-2',
    'md': 'gap-3',
    'lg': 'gap-4'
  };

  return (
    <div className={`
      fixed ${positionClasses[position]} z-40
      flex ${position.includes('left') ? 'flex-row' : 'flex-row-reverse'} 
      ${spacingClasses[spacing]}
      ${className}
    `}>
      {children}
    </div>
  );
};

export default FloatingButtonContainer;
