'use client';

import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { 
  <PERSON>, 
  Zap, 
  Brain, 
  Rocket, 
  BarChart3, 
  Settings, 
  Palette,
  Grid3X3,
  Activity,
  Shield,
  Search,
  Plus
} from 'lucide-react';

interface QuickAccessMenuItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  category: 'agents' | 'tools' | 'monitoring' | 'creation';
  priority: 'high' | 'medium' | 'low';
}

const quickAccessItems: QuickAccessMenuItem[] = [
  // High Priority - Strategic Hubs
  {
    href: '/intelligence',
    label: 'Intelligence Hub',
    icon: Brain,
    description: 'AI Analytics & Real-time Intelligence',
    category: 'agents',
    priority: 'high'
  },
  {
    href: '/agents',
    label: 'Agent Hub',
    icon: Users,
    description: '28 Autonomous AI Agents',
    category: 'agents',
    priority: 'high'
  },
  {
    href: '/creative',
    label: 'Creative Hub',
    icon: Palette,
    description: 'AI Canvas & Creative Tools',
    category: 'creation',
    priority: 'high'
  },
  {
    href: '/dashboard',
    label: 'Dashboard Hub',
    icon: BarChart3,
    description: 'Workspace & System Control',
    category: 'monitoring',
    priority: 'high'
  },

  // Medium Priority - Essential Tools (accessed through hubs)
  {
    href: '/canvas',
    label: 'AI Canvas',
    icon: Palette,
    description: 'Primary creative workspace',
    category: 'creation',
    priority: 'medium'
  },
  {
    href: '/agent-ecosystem',
    label: 'Agent Ecosystem',
    icon: Grid3X3,
    description: '28-agent visualization',
    category: 'agents',
    priority: 'medium'
  },
  {
    href: '/gallery',
    label: 'Gallery',
    icon: Search,
    description: 'Browse AI-generated content',
    category: 'creation',
    priority: 'medium'
  },
  {
    href: '/monitoring',
    label: 'System Monitor',
    icon: Activity,
    description: 'Real-time system monitoring',
    category: 'monitoring',
    priority: 'medium'
  }
];

interface QuickAccessMenuProps {
  isOpen: boolean;
  onClose: () => void;
  currentPath?: string;
}

export const QuickAccessMenu: React.FC<QuickAccessMenuProps> = ({
  isOpen,
  onClose,
  currentPath = ''
}) => {
  const [activeCategory, setActiveCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = [
    { id: 'all', label: 'All' },
    { id: 'agents', label: 'Intelligence & Agents' },
    { id: 'creation', label: 'Creative' },
    { id: 'monitoring', label: 'Dashboard & Monitor' }
  ];

  const filteredItems = quickAccessItems.filter(item => {
    const matchesCategory = activeCategory === 'all' || item.category === activeCategory;
    const matchesSearch = searchTerm === '' || 
      item.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const groupedItems = {
    high: filteredItems.filter(item => item.priority === 'high'),
    medium: filteredItems.filter(item => item.priority === 'medium'),
    low: filteredItems.filter(item => item.priority === 'low')
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
        onClick={onClose}
      />
      
      {/* Menu Panel */}
      <div className="fixed top-20 right-4 w-96 max-h-[80vh] bg-space-900/95 backdrop-blur-xl border border-cosmic-500/30 rounded-xl shadow-2xl z-50 overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-cosmic-500/20">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-white">Quick Access</h3>
            <button
              onClick={onClose}
              className="p-1 rounded-lg hover:bg-space-700/50 transition-colors"
            >
              <span className="text-white/60 hover:text-white text-lg">✕</span>
            </button>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/40" />
            <input
              type="text"
              placeholder="Search pages..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-space-800/50 border border-cosmic-500/20 rounded-lg text-white placeholder-white/40 focus:outline-none focus:border-cosmic-400"
            />
          </div>
        </div>
        
        {/* Category Tabs */}
        <div className="flex overflow-x-auto scrollbar-none border-b border-cosmic-500/20">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex-shrink-0 px-4 py-3 text-sm font-medium transition-colors ${
                activeCategory === category.id
                  ? 'text-cosmic-300 border-b-2 border-cosmic-400'
                  : 'text-white/60 hover:text-white/80'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>
        
        {/* Menu Items */}
        <div className="max-h-96 overflow-y-auto p-4 space-y-1">
          {/* High Priority Items */}
          {groupedItems.high.length > 0 && (
            <>
              <div className="text-xs font-medium text-white/40 uppercase tracking-wider px-2 py-1">
                Essential
              </div>
              {groupedItems.high.map((item) => (
                <MenuItemComponent 
                  key={item.href}
                  item={item}
                  currentPath={currentPath}
                  onClose={onClose}
                />
              ))}
            </>
          )}
          
          {/* Medium Priority Items */}
          {groupedItems.medium.length > 0 && (
            <>
              <div className="text-xs font-medium text-white/40 uppercase tracking-wider px-2 py-1 mt-4">
                Features
              </div>
              {groupedItems.medium.map((item) => (
                <MenuItemComponent 
                  key={item.href}
                  item={item}
                  currentPath={currentPath}
                  onClose={onClose}
                />
              ))}
            </>
          )}
          
          {/* Low Priority Items */}
          {groupedItems.low.length > 0 && (
            <>
              <div className="text-xs font-medium text-white/40 uppercase tracking-wider px-2 py-1 mt-4">
                Additional
              </div>
              {groupedItems.low.map((item) => (
                <MenuItemComponent 
                  key={item.href}
                  item={item}
                  currentPath={currentPath}
                  onClose={onClose}
                />
              ))}
            </>
          )}
          
          {filteredItems.length === 0 && (
            <div className="text-center py-8 text-white/40">
              No items found matching your search
            </div>
          )}
        </div>
        
        {/* Footer */}
        <div className="p-4 border-t border-cosmic-500/20 bg-space-800/30">
          <div className="text-xs text-white/40 text-center">
            {filteredItems.length} pages available
          </div>
        </div>
      </div>
    </>
  );
};

const MenuItemComponent: React.FC<{
  item: QuickAccessMenuItem;
  currentPath: string;
  onClose: () => void;
}> = ({ item, currentPath, onClose }) => {
  const isActive = currentPath === item.href;
  const Icon = item.icon;
  
  return (
    <Link
      href={item.href}
      onClick={onClose}
      className={`flex items-center gap-3 px-3 py-3 rounded-lg transition-all group ${
        isActive
          ? 'bg-cosmic-500/20 border border-cosmic-500/30'
          : 'hover:bg-space-700/50'
      }`}
    >
      <div className={`p-2 rounded-lg ${
        isActive
          ? 'bg-cosmic-500/30 text-cosmic-300'
          : 'bg-space-700/50 text-white/60 group-hover:text-white group-hover:bg-space-600/50'
      }`}>
        <Icon className="w-4 h-4" />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className={`font-medium ${
          isActive ? 'text-cosmic-300' : 'text-white group-hover:text-white'
        }`}>
          {item.label}
        </div>
        <div className="text-xs text-white/40 truncate">
          {item.description}
        </div>
      </div>
      
      {isActive && (
        <div className="w-2 h-2 bg-cosmic-400 rounded-full animate-pulse" />
      )}
    </Link>
  );
};

export default QuickAccessMenu; 