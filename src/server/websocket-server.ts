/**
 * 🌐 WebSocket Server for Real-Time AI Streaming
 * Integrates with Next.js to provide live AI decision streaming
 */

import { createServer } from 'http';
import { parse } from 'url';
import next from 'next';
import { RealTimeWebSocketService } from '../services/RealTimeWebSocketService';

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = parseInt(process.env.PORT || '3000', 10);

// Prepare Next.js app
const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

export async function startWebSocketServer() {
  try {
    console.log('🚀 Starting WebSocket-enabled Next.js server...');
    
    await app.prepare();
    
    // Create HTTP server
    const server = createServer(async (req, res) => {
      try {
        const parsedUrl = parse(req.url!, true);
        await handle(req, res, parsedUrl);
      } catch (err) {
        console.error('Error occurred handling', req.url, err);
        res.statusCode = 500;
        res.end('internal server error');
      }
    });

    // Initialize WebSocket service
    const wsService = RealTimeWebSocketService.getInstance();
    wsService.initialize(server);

    // Start server
    server.listen(port, () => {
      console.log(`✅ Server ready on http://${hostname}:${port}`);
      console.log('🌐 WebSocket service ready for real-time AI streaming');
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('🔌 Shutting down WebSocket server...');
      wsService.cleanup();
      server.close(() => {
        console.log('✅ WebSocket server shutdown complete');
        process.exit(0);
      });
    });

  } catch (error) {
    console.error('❌ Failed to start WebSocket server:', error);
    process.exit(1);
  }
}

// Start server if this file is run directly
if (require.main === module) {
  startWebSocketServer();
}
