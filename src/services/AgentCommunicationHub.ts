/**
 * 🌐 Agent Communication Hub
 * 
 * Enables real-time communication between all 28 agents
 * with intelligent message routing and coordination
 */

import { EventEmitter } from 'events';

export interface AgentMessage {
  id: string;
  fromAgent: string;
  toAgent: string | 'broadcast';
  messageType: 'task_request' | 'status_update' | 'collaboration' | 'knowledge_share' | 'emergency';
  content: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  requiresResponse: boolean;
  context?: any;
}

export interface AgentResponse {
  id: string;
  originalMessageId: string;
  fromAgent: string;
  toAgent: string;
  content: string;
  success: boolean;
  timestamp: Date;
  executionTime: number;
}

export interface AgentCapability {
  agentId: string;
  capabilities: string[];
  currentLoad: number;
  status: 'idle' | 'busy' | 'offline';
  lastSeen: Date;
}

export class AgentCommunicationHub extends EventEmitter {
  private static instance: AgentCommunicationHub;
  private agentCapabilities: Map<string, AgentCapability> = new Map();
  private messageQueue: Map<string, AgentMessage[]> = new Map();
  private activeConversations: Map<string, AgentMessage[]> = new Map();
  private messageHistory: AgentMessage[] = [];
  private responseHistory: AgentResponse[] = [];

  private constructor() {
    super();
    this.initializeAgentCapabilities();
  }

  public static getInstance(): AgentCommunicationHub {
    if (!AgentCommunicationHub.instance) {
      AgentCommunicationHub.instance = new AgentCommunicationHub();
    }
    return AgentCommunicationHub.instance;
  }

  /**
   * Initialize capabilities for all 28 agents
   */
  private initializeAgentCapabilities(): void {
    const agentCapabilityMap = {
      // Tier 1: Critical System Agents
      'SecurityAgentIntelligenceEnhanced': ['security_analysis', 'threat_detection', 'vulnerability_scanning'],
      'ErrorMonitorAgentIntelligenceEnhanced': ['error_monitoring', 'debugging', 'system_health'],
      
      // Tier 2: Core Development Agents
      'DevAgentIntelligenceEnhanced': ['development', 'code_generation', 'architecture'],
      'AutonomousDevAgentIntelligenceEnhanced': ['autonomous_development', 'self_directed_tasks'],
      'ConversationalDevAgentIntelligenceEnhanced': ['conversational_ai', 'code_review'],
      'TestAgentIntelligenceEnhanced': ['testing', 'quality_assurance', 'test_generation'],
      
      // Tier 3: Intelligence & Control Agents
      'UIAgentIntelligenceEnhanced': ['ui_design', 'component_creation', 'styling'],
      'OpsAgentIntelligenceEnhanced': ['deployment', 'monitoring', 'infrastructure'],
      'ConfigAgentIntelligenceEnhanced': ['configuration', 'settings_management'],
      'ProactiveAutonomyAgentIntelligenceEnhanced': ['autonomous_decisions', 'strategic_planning'],
      
      // Tier 4: Enhancement & Discovery Agents
      'FeatureDiscoveryAgentIntelligenceEnhanced': ['feature_discovery', 'enhancement_suggestions'],
      'WorkflowEnhancementAgentIntelligenceEnhanced': ['workflow_optimization', 'process_improvement'],
      'PrecisionPerformanceEngineIntelligenceEnhanced': ['performance_monitoring', 'optimization'],
      
      // Tier 5: Communication & Interface Agents
      'ChatResponseParserAgentIntelligenceEnhanced': ['chat_parsing', 'response_analysis'],
      'UserBehaviorAgentIntelligenceEnhanced': ['user_behavior', 'analytics'],
      'ProcessWatcherAgentIntelligenceEnhanced': ['process_monitoring', 'resource_tracking'],
      'LivingUIAgentIntelligenceEnhanced': ['adaptive_ui', 'dynamic_interfaces'],
      
      // Tier 6: Specialized System Agents
      'PerformanceMonitoringAgentIntelligenceEnhanced': ['performance_monitoring', 'metrics'],
      'DataProcessingAgentIntelligenceEnhanced': ['data_processing', 'data_analysis'],
      'ErrorHandlingAgentIntelligenceEnhanced': ['error_handling', 'error_resolution'],
      'NotificationAgentIntelligenceEnhanced': ['notifications', 'alerts'],
      'CommunicationAgentIntelligenceEnhanced': ['communication', 'messaging'],
      'UserInputAgentIntelligenceEnhanced': ['user_input', 'input_processing'],
      'SystemHealthAgentIntelligenceEnhanced': ['system_health', 'health_monitoring'],
      'LocalIntelligenceEngineIntelligenceEnhanced': ['intelligence', 'ai_integration'],
      'SelfImprovementEngineIntelligenceEnhanced': ['self_improvement', 'optimization'],
      'AutonomyProgressionEngineIntelligenceEnhanced': ['autonomy_progression', 'evolution'],
      'AdvancedSelfModificationEngineIntelligenceEnhanced': ['self_modification', 'adaptation']
    };

    Object.entries(agentCapabilityMap).forEach(([agentId, capabilities]) => {
      this.agentCapabilities.set(agentId, {
        agentId,
        capabilities,
        currentLoad: 0,
        status: 'idle',
        lastSeen: new Date()
      });
      
      // Initialize message queue for each agent
      this.messageQueue.set(agentId, []);
    });

    console.log(`🌐 Initialized communication hub for ${this.agentCapabilities.size} agents`);
  }

  /**
   * Send message between agents
   */
  public async sendMessage(message: Omit<AgentMessage, 'id' | 'timestamp'>): Promise<string> {
    const fullMessage: AgentMessage = {
      ...message,
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date()
    };

    console.log(`📨 Message from ${message.fromAgent} to ${message.toAgent}: ${message.messageType}`);

    // Store in history
    this.messageHistory.push(fullMessage);

    // Handle broadcast messages
    if (message.toAgent === 'broadcast') {
      return this.broadcastMessage(fullMessage);
    }

    // Route to specific agent
    return this.routeMessage(fullMessage);
  }

  /**
   * Broadcast message to all agents
   */
  private async broadcastMessage(message: AgentMessage): Promise<string> {
    const broadcastPromises: Promise<void>[] = [];

    this.agentCapabilities.forEach((capability, agentId) => {
      if (agentId !== message.fromAgent && capability.status !== 'offline') {
        const agentMessage = { ...message, toAgent: agentId };
        broadcastPromises.push(this.deliverMessage(agentMessage));
      }
    });

    await Promise.all(broadcastPromises);
    this.emit('message:broadcast', message);
    
    return message.id;
  }

  /**
   * Route message to specific agent
   */
  private async routeMessage(message: AgentMessage): Promise<string> {
    const targetAgent = this.agentCapabilities.get(message.toAgent as string);
    
    if (!targetAgent) {
      throw new Error(`Agent ${message.toAgent} not found`);
    }

    if (targetAgent.status === 'offline') {
      throw new Error(`Agent ${message.toAgent} is offline`);
    }

    await this.deliverMessage(message);
    this.emit('message:sent', message);
    
    return message.id;
  }

  /**
   * Deliver message to agent's queue
   */
  private async deliverMessage(message: AgentMessage): Promise<void> {
    const agentQueue = this.messageQueue.get(message.toAgent as string);
    if (agentQueue) {
      agentQueue.push(message);
      
      // Update agent load
      const capability = this.agentCapabilities.get(message.toAgent as string);
      if (capability) {
        capability.currentLoad += message.priority === 'critical' ? 3 : 
                                  message.priority === 'high' ? 2 : 1;
      }
      
      this.emit('message:delivered', message);
    }
  }

  /**
   * Find best agent for a capability
   */
  public findBestAgentForCapability(capability: string): string | null {
    let bestAgent: string | null = null;
    let lowestLoad = Infinity;

    this.agentCapabilities.forEach((agentCap, agentId) => {
      if (agentCap.capabilities.includes(capability) && 
          agentCap.status === 'idle' && 
          agentCap.currentLoad < lowestLoad) {
        bestAgent = agentId;
        lowestLoad = agentCap.currentLoad;
      }
    });

    return bestAgent;
  }

  /**
   * Request collaboration between agents
   */
  public async requestCollaboration(
    initiatorAgent: string,
    requiredCapabilities: string[],
    task: string,
    priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'
  ): Promise<string[]> {
    const collaborators: string[] = [];

    for (const capability of requiredCapabilities) {
      const bestAgent = this.findBestAgentForCapability(capability);
      if (bestAgent && !collaborators.includes(bestAgent)) {
        collaborators.push(bestAgent);
        
        // Send collaboration request
        await this.sendMessage({
          fromAgent: initiatorAgent,
          toAgent: bestAgent,
          messageType: 'collaboration',
          content: `Collaboration request for: ${task}`,
          priority,
          requiresResponse: true,
          context: { task, requiredCapabilities }
        });
      }
    }

    console.log(`🤝 Collaboration initiated by ${initiatorAgent} with ${collaborators.length} agents`);
    return collaborators;
  }

  /**
   * Get communication statistics
   */
  public getCommunicationStats(): {
    totalMessages: number;
    activeConversations: number;
    agentLoadDistribution: Record<string, number>;
    messagesByType: Record<string, number>;
  } {
    const messagesByType: Record<string, number> = {};
    const agentLoadDistribution: Record<string, number> = {};

    this.messageHistory.forEach(msg => {
      messagesByType[msg.messageType] = (messagesByType[msg.messageType] || 0) + 1;
    });

    this.agentCapabilities.forEach((capability, agentId) => {
      agentLoadDistribution[agentId] = capability.currentLoad;
    });

    return {
      totalMessages: this.messageHistory.length,
      activeConversations: this.activeConversations.size,
      agentLoadDistribution,
      messagesByType
    };
  }

  /**
   * Update agent status
   */
  public updateAgentStatus(agentId: string, status: 'idle' | 'busy' | 'offline'): void {
    const capability = this.agentCapabilities.get(agentId);
    if (capability) {
      capability.status = status;
      capability.lastSeen = new Date();
      this.emit('agent:status_updated', { agentId, status });
    }
  }

  /**
   * Get recent messages for an agent
   */
  public getRecentMessages(agentId: string, limit: number = 10): AgentMessage[] {
    return this.messageHistory
      .filter(msg => msg.toAgent === agentId || msg.fromAgent === agentId)
      .slice(-limit)
      .reverse();
  }
}
