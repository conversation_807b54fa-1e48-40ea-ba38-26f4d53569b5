/**
 * 🌐 Real-Time WebSocket Service
 * Streams live AI decisions and agent communications
 * 
 * Features:
 * - Live agent decision streaming
 * - Real-time system metrics
 * - Cross-agent communication broadcasting
 * - Autonomous AI observation interface
 */

import { Server as SocketIOServer } from 'socket.io';
import { Server as HTTPServer } from 'http';
import { RealTimeOllamaService, RealAIDecision } from './RealTimeOllamaService';

export interface WebSocketMessage {
  type: 'decision' | 'metrics' | 'agent_status' | 'system_health' | 'cross_agent_communication';
  data: any;
  timestamp: Date;
  source: string;
}

export interface ConnectedClient {
  id: string;
  connectedAt: Date;
  subscriptions: string[];
  lastActivity: Date;
}

export class RealTimeWebSocketService {
  private static instance: RealTimeWebSocketService;
  private io: SocketIOServer | null = null;
  private ollamaService: RealTimeOllamaService;
  private connectedClients: Map<string, ConnectedClient> = new Map();
  private metricsInterval: NodeJS.Timeout | null = null;
  private agentCommunicationInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.ollamaService = RealTimeOllamaService.getInstance();
    this.setupOllamaEventListeners();
  }

  public static getInstance(): RealTimeWebSocketService {
    if (!RealTimeWebSocketService.instance) {
      RealTimeWebSocketService.instance = new RealTimeWebSocketService();
    }
    return RealTimeWebSocketService.instance;
  }

  /**
   * Initialize WebSocket server
   */
  public initialize(httpServer: HTTPServer): void {
    console.log('🌐 Initializing Real-Time WebSocket Service...');

    this.io = new SocketIOServer(httpServer, {
      cors: {
        origin: "*",
        methods: ["GET", "POST"]
      },
      transports: ['websocket', 'polling']
    });

    this.setupSocketHandlers();
    this.startMetricsStreaming();
    this.startAgentCommunicationSimulation();

    console.log('✅ Real-Time WebSocket Service initialized');
  }

  /**
   * Setup socket connection handlers
   */
  private setupSocketHandlers(): void {
    if (!this.io) return;

    this.io.on('connection', (socket) => {
      const clientId = socket.id;
      console.log(`🔌 Client connected: ${clientId}`);

      // Register client
      this.connectedClients.set(clientId, {
        id: clientId,
        connectedAt: new Date(),
        subscriptions: [],
        lastActivity: new Date()
      });

      // Handle subscription requests
      socket.on('subscribe', (channels: string[]) => {
        const client = this.connectedClients.get(clientId);
        if (client) {
          client.subscriptions = channels;
          client.lastActivity = new Date();
          console.log(`📡 Client ${clientId} subscribed to: ${channels.join(', ')}`);
        }
      });

      // Handle agent decision requests
      socket.on('request_agent_decision', async (data: { agentId: string; prompt: string; context?: any }) => {
        try {
          console.log(`🤖 Processing real AI decision request for ${data.agentId}`);
          const decision = await this.ollamaService.makeRealAIDecision(
            data.agentId,
            data.prompt,
            data.context
          );
          
          // Send decision back to requesting client
          socket.emit('agent_decision_response', {
            success: true,
            decision
          });

        } catch (error) {
          console.error('❌ Agent decision request failed:', error);
          socket.emit('agent_decision_response', {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      });

      // Handle real-time metrics requests
      socket.on('request_metrics', () => {
        const metrics = this.ollamaService.getRealTimeAgentStatus();
        socket.emit('metrics_update', {
          type: 'metrics',
          data: metrics,
          timestamp: new Date(),
          source: 'ollama_service'
        });
      });

      // Handle recent decisions requests
      socket.on('request_recent_decisions', (limit: number = 10) => {
        const decisions = this.ollamaService.getRecentDecisions(limit);
        socket.emit('recent_decisions', {
          type: 'decision_history',
          data: decisions,
          timestamp: new Date(),
          source: 'ollama_service'
        });
      });

      // Handle disconnect
      socket.on('disconnect', () => {
        console.log(`🔌 Client disconnected: ${clientId}`);
        this.connectedClients.delete(clientId);
      });

      // Send initial data
      this.sendInitialData(socket);
    });
  }

  /**
   * Setup Ollama service event listeners
   */
  private setupOllamaEventListeners(): void {
    // Listen for real AI decisions
    this.ollamaService.on('decision:made', (decision: RealAIDecision) => {
      this.broadcastToSubscribers('decisions', {
        type: 'decision',
        data: decision,
        timestamp: new Date(),
        source: 'real_ai'
      });
    });

    // Listen for service events
    this.ollamaService.on('service:initialized', (data) => {
      this.broadcastToAll({
        type: 'system_health',
        data: { status: 'ollama_ready', ...data },
        timestamp: new Date(),
        source: 'ollama_service'
      });
    });

    this.ollamaService.on('service:error', (error) => {
      this.broadcastToAll({
        type: 'system_health',
        data: { status: 'ollama_error', error },
        timestamp: new Date(),
        source: 'ollama_service'
      });
    });
  }

  /**
   * Start streaming real-time metrics
   */
  private startMetricsStreaming(): void {
    // Stream metrics every 2 seconds
    this.metricsInterval = setInterval(() => {
      const metrics = this.ollamaService.getRealTimeAgentStatus();
      
      this.broadcastToSubscribers('metrics', {
        type: 'metrics',
        data: {
          ...metrics,
          connectedClients: this.connectedClients.size,
          timestamp: new Date()
        },
        timestamp: new Date(),
        source: 'websocket_service'
      });
    }, 2000);
  }

  /**
   * Start agent communication simulation
   */
  private startAgentCommunicationSimulation(): void {
    // Simulate cross-agent communication every 5 seconds
    this.agentCommunicationInterval = setInterval(async () => {
      const agentAssignments = this.ollamaService.getAgentModelAssignments();
      
      if (agentAssignments.length >= 2) {
        // Pick two random agents for communication
        const agent1 = agentAssignments[Math.floor(Math.random() * agentAssignments.length)];
        const agent2 = agentAssignments[Math.floor(Math.random() * agentAssignments.length)];
        
        if (agent1.agentId !== agent2.agentId) {
          const communicationTopics = [
            'system optimization strategy',
            'resource allocation coordination',
            'performance improvement suggestions',
            'security protocol validation',
            'workflow enhancement proposals'
          ];
          
          const topic = communicationTopics[Math.floor(Math.random() * communicationTopics.length)];
          
          try {
            // Generate real AI communication
            const communication = await this.ollamaService.makeRealAIDecision(
              agent1.agentId,
              `Communicate with ${agent2.agentId} about ${topic}. Provide a brief, professional message.`,
              { recipient: agent2.agentId, topic }
            );

            this.broadcastToSubscribers('agent_communication', {
              type: 'cross_agent_communication',
              data: {
                from: agent1.agentId,
                to: agent2.agentId,
                topic,
                message: communication.response,
                confidence: communication.confidence,
                timestamp: new Date()
              },
              timestamp: new Date(),
              source: 'agent_ecosystem'
            });

          } catch (error) {
            console.error('❌ Agent communication simulation failed:', error);
          }
        }
      }
    }, 5000);
  }

  /**
   * Send initial data to newly connected client
   */
  private sendInitialData(socket: any): void {
    // Send current metrics
    const metrics = this.ollamaService.getRealTimeAgentStatus();
    socket.emit('initial_data', {
      metrics,
      agentAssignments: this.ollamaService.getAgentModelAssignments(),
      recentDecisions: this.ollamaService.getRecentDecisions(5),
      systemStatus: {
        ollamaAvailable: true,
        connectedClients: this.connectedClients.size,
        uptime: process.uptime()
      }
    });
  }

  /**
   * Broadcast message to all clients subscribed to a channel
   */
  private broadcastToSubscribers(channel: string, message: WebSocketMessage): void {
    if (!this.io) return;

    this.connectedClients.forEach((client, clientId) => {
      if (client.subscriptions.includes(channel) || client.subscriptions.includes('all')) {
        this.io!.to(clientId).emit('real_time_update', message);
      }
    });
  }

  /**
   * Broadcast message to all connected clients
   */
  private broadcastToAll(message: WebSocketMessage): void {
    if (!this.io) return;
    this.io.emit('real_time_update', message);
  }

  /**
   * Get connection statistics
   */
  public getConnectionStats(): {
    connectedClients: number;
    totalConnections: number;
    activeSubscriptions: Record<string, number>;
  } {
    const activeSubscriptions: Record<string, number> = {};
    
    this.connectedClients.forEach(client => {
      client.subscriptions.forEach(sub => {
        activeSubscriptions[sub] = (activeSubscriptions[sub] || 0) + 1;
      });
    });

    return {
      connectedClients: this.connectedClients.size,
      totalConnections: this.connectedClients.size, // Could track historical total
      activeSubscriptions
    };
  }

  /**
   * Cleanup resources
   */
  public cleanup(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }
    
    if (this.agentCommunicationInterval) {
      clearInterval(this.agentCommunicationInterval);
    }
    
    if (this.io) {
      this.io.close();
    }
    
    console.log('🧹 Real-Time WebSocket Service cleaned up');
  }
}
