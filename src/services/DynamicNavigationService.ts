/**
 * 🧠 DYNAMIC NAVIGATION SERVICE
 * Intelligent page discovery, organization, and missing page detection
 */

import { promises as fs } from 'fs';
import path from 'path';

export interface PageInfo {
  id: string;
  name: string;
  route: string;
  filePath: string;
  category?: string;
  suggestedCategory?: string;
  description?: string;
  imports: string[];
  components: string[];
  hasAuth: boolean;
  isAPI: boolean;
  isLayout: boolean;
  isPage: boolean;
  lastModified: Date;
  size: number;
  lines: number;
  missingDependencies?: string[];
  suggestedConnections?: string[];
}

export interface NavigationGroup {
  id: string;
  name: string;
  icon: string;
  pages: PageInfo[];
  description: string;
  completeness: number; // 0-100% based on expected vs actual pages
  missingPages?: string[];
  suggestedEnhancements?: string[];
}

export interface NavigationAnalysis {
  groups: NavigationGroup[];
  orphanedPages: PageInfo[];
  missingPages: string[];
  suggestedConnections: Array<{
    from: string;
    to: string;
    reason: string;
  }>;
  summary: {
    totalPages: number;
    categorizedPages: number;
    completeness: number;
    lastAnalyzed: Date;
  };
}

class DynamicNavigationService {
  private pageCache: Map<string, PageInfo> = new Map();
  private lastScan: Date | null = null;
  
  /**
   * 🔍 DISCOVER ALL PAGES
   * Scan filesystem for all Next.js pages and analyze them
   */
  async discoverAllPages(): Promise<PageInfo[]> {
    console.log('🔍 Discovering all pages dynamically...');
    
    const pages: PageInfo[] = [];
    const appDir = path.join(process.cwd(), 'src/app');
    
    try {
      await this.scanDirectory(appDir, '', pages);
      
      // Cache results
      pages.forEach(page => this.pageCache.set(page.route, page));
      this.lastScan = new Date();
      
      console.log(`✅ Discovered ${pages.length} pages`);
      return pages;
      
    } catch (error) {
      console.error('❌ Page discovery failed:', error);
      return [];
    }
  }
  
  /**
   * 📁 SCAN DIRECTORY RECURSIVELY
   */
  private async scanDirectory(dir: string, baseRoute: string, pages: PageInfo[]): Promise<void> {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const routeSegment = entry.name.startsWith('(') || entry.name.startsWith('[') 
          ? entry.name 
          : entry.name;
        const currentRoute = path.join(baseRoute, routeSegment).replace(/\\/g, '/');
        
        if (entry.isDirectory()) {
          // Skip certain directories
          if (!['api', '_components', '_lib', 'globals.css'].includes(entry.name)) {
            await this.scanDirectory(fullPath, currentRoute, pages);
          }
        } else if (entry.name === 'page.tsx' || entry.name === 'page.ts') {
          // This is a page file - use the directory path as the route
          const route = baseRoute === '' ? '/' : baseRoute.startsWith('/') ? baseRoute : `/${baseRoute}`;
          const pageInfo = await this.analyzePage(fullPath, route);
          if (pageInfo) {
            pages.push(pageInfo);
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️ Could not scan directory ${dir}:`, error);
    }
  }
  
  /**
   * 🔍 ANALYZE INDIVIDUAL PAGE
   */
  private async analyzePage(filePath: string, route: string): Promise<PageInfo | null> {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const stats = await fs.stat(filePath);
      const lines = content.split('\n').length;
      
      // Extract page name from route
      const name = route === '/' ? 'Home' : 
        route.split('/').filter(Boolean).pop()?.replace(/[-_]/g, ' ') || 'Unknown';
      
      // Analyze imports
      const imports = this.extractImports(content);
      
      // Analyze components used
      const components = this.extractComponents(content);
      
      // Check authentication requirements
      const hasAuth = content.includes('auth') || content.includes('login') || 
                     content.includes('protected') || content.includes('session');
      
      // Extract description from comments
      const descriptionMatch = content.match(/\/\*\*\s*\n\s*\*\s*([^*\n]+)/);
      const description = descriptionMatch ? descriptionMatch[1].trim() : 
        `${name.charAt(0).toUpperCase() + name.slice(1)} page`;
      
      // Suggest category based on content analysis
      const suggestedCategory = this.suggestCategory(route, content, imports, components);
      
      // Detect missing dependencies
      const missingDependencies = this.detectMissingDependencies(content, imports);
      
      // Suggest connections to other pages
      const suggestedConnections = this.suggestConnections(route, content, imports);
      
      return {
        id: route,
        name: name.charAt(0).toUpperCase() + name.slice(1),
        route,
        filePath,
        suggestedCategory,
        description,
        imports,
        components,
        hasAuth,
        isAPI: false,
        isLayout: false,
        isPage: true,
        lastModified: stats.mtime,
        size: stats.size,
        lines,
        missingDependencies,
        suggestedConnections
      };
      
    } catch (error) {
      console.warn(`⚠️ Could not analyze page ${filePath}:`, error);
      return null;
    }
  }
  
  /**
   * 🧠 INTELLIGENT CATEGORY SUGGESTION
   */
  private suggestCategory(route: string, content: string, imports: string[], components: string[]): string {
    const routeLower = route.toLowerCase();
    const contentLower = content.toLowerCase();

    // Core Platform (FIRST - most specific)
    if (routeLower === '/' || routeLower === '/login' || routeLower === '/register' ||
        routeLower === '/about' || routeLower === '/contact' || routeLower === '/pricing') {
      return 'core-platform';
    }

    // Agent Hub (SECOND - specific agent routes)
    if (routeLower === '/agents' || routeLower === '/agent-ecosystem' ||
        routeLower === '/swarm' || routeLower === '/orchestration' ||
        routeLower === '/autonomous-core-demo' || routeLower.includes('/agents/') ||
        (routeLower.includes('agent') && !routeLower.includes('intelligence'))) {
      return 'agent-hub';
    }

    // Creative Hub (THIRD - specific creative routes)
    if (routeLower === '/creative' || routeLower === '/canvas' ||
        routeLower === '/ai-tools' || routeLower === '/gallery' ||
        routeLower === '/marketplace' || routeLower === '/voice' ||
        routeLower.includes('design') || routeLower.includes('creative')) {
      return 'creative-hub';
    }

    // Dashboard Hub (FOURTH - specific dashboard routes)
    if (routeLower === '/dashboard' || routeLower === '/monitoring' ||
        routeLower === '/chat' || routeLower === '/community' ||
        routeLower === '/tasks' || routeLower === '/profile' ||
        routeLower.includes('monitor') || routeLower.includes('task')) {
      return 'dashboard-hub';
    }

    // Intelligence Hub (FIFTH - specific intelligence routes only)
    if (routeLower === '/intelligence' || routeLower === '/intelligence-analytics' ||
        routeLower === '/omniscient' || routeLower === '/models' ||
        (routeLower.includes('intelligence') && !routeLower.includes('agent'))) {
      return 'intelligence-hub';
    }

    return 'uncategorized';
  }
  
  /**
   * 🔍 EXTRACT IMPORTS FROM CONTENT
   */
  private extractImports(content: string): string[] {
    const imports: string[] = [];
    const importRegex = /import.*from ['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = importRegex.exec(content)) !== null) {
      imports.push(match[1]);
    }
    
    return imports;
  }
  
  /**
   * 🔍 EXTRACT COMPONENTS FROM CONTENT
   */
  private extractComponents(content: string): string[] {
    const components: string[] = [];
    
    // Look for JSX component usage
    const componentRegex = /<([A-Z][a-zA-Z0-9]*)/g;
    let match;
    
    while ((match = componentRegex.exec(content)) !== null) {
      if (!components.includes(match[1])) {
        components.push(match[1]);
      }
    }
    
    return components;
  }
  
  /**
   * ⚠️ DETECT MISSING DEPENDENCIES
   */
  private detectMissingDependencies(content: string, imports: string[]): string[] {
    const missing: string[] = [];
    
    // Check for common patterns that might need imports
    if (content.includes('useState') && !imports.some(imp => imp.includes('react'))) {
      missing.push('react');
    }
    
    if (content.includes('useRouter') && !imports.some(imp => imp.includes('next/navigation'))) {
      missing.push('next/navigation');
    }
    
    if (content.includes('Agent') && !imports.some(imp => imp.includes('agent'))) {
      missing.push('agent-services');
    }
    
    return missing;
  }
  
  /**
   * 🔗 SUGGEST CONNECTIONS TO OTHER PAGES
   */
  private suggestConnections(route: string, content: string, imports: string[]): string[] {
    const connections: string[] = [];
    
    // If this is an agent page, suggest connection to agent dashboard
    if (route.includes('agent') && route !== '/agents') {
      connections.push('/agents');
    }
    
    // If this uses auth, suggest connection to login
    if (content.includes('auth') && route !== '/login') {
      connections.push('/login');
    }
    
    // If this is a tool page, suggest connection to ai-tools
    if (content.includes('tool') && route !== '/ai-tools') {
      connections.push('/ai-tools');
    }
    
    return connections;
  }
  
  /**
   * 📊 GENERATE INTELLIGENT NAVIGATION ANALYSIS
   */
  async generateNavigationAnalysis(): Promise<NavigationAnalysis> {
    const pages = await this.discoverAllPages();
    
    // Group pages by category - ALIGNED WITH EXPECTED CATEGORIES
    const groups: NavigationGroup[] = [
      {
        id: 'core-platform',
        name: 'Core Platform',
        icon: '🏠',
        pages: [],
        description: 'Essential platform functionality',
        completeness: 0,
        missingPages: []
      },
      {
        id: 'intelligence-hub',
        name: 'Intelligence Hub',
        icon: '🧠',
        pages: [],
        description: 'AI Analytics & Real-time Intelligence',
        completeness: 0,
        missingPages: []
      },
      {
        id: 'agent-hub',
        name: 'Agent Hub',
        icon: '🤖',
        pages: [],
        description: '28 Autonomous AI Agents',
        completeness: 0,
        missingPages: []
      },
      {
        id: 'creative-hub',
        name: 'Creative Hub',
        icon: '🎨',
        pages: [],
        description: 'AI Canvas & Creative Tools',
        completeness: 0,
        missingPages: []
      },
      {
        id: 'dashboard-hub',
        name: 'Dashboard Hub',
        icon: '📊',
        pages: [],
        description: 'Workspace & System Control',
        completeness: 0,
        missingPages: []
      },
      {
        id: 'uncategorized',
        name: 'Uncategorized',
        icon: '📋',
        pages: [],
        description: 'Pages needing categorization',
        completeness: 0,
        missingPages: []
      }
    ];
    
    // Categorize pages
    const orphanedPages: PageInfo[] = [];
    
    for (const page of pages) {
      const category = page.suggestedCategory || 'uncategorized';
      const group = groups.find(g => g.id === category);
      
      if (group) {
        group.pages.push(page);
      } else {
        orphanedPages.push(page);
      }
    }
    
    // Calculate completeness and detect missing pages
    for (const group of groups) {
      const expectedPages = this.getExpectedPagesForCategory(group.id);
      const actualRoutes = group.pages.map(p => p.route);
      const missing = expectedPages.filter(expected => !actualRoutes.includes(expected));
      
      group.missingPages = missing;
      group.completeness = expectedPages.length > 0 ? 
        Math.round(((expectedPages.length - missing.length) / expectedPages.length) * 100) : 100;
    }
    
    // Generate connection suggestions
    const suggestedConnections = this.generateConnectionSuggestions(pages);
    
    const categorizedPages = groups.reduce((sum, group) => sum + group.pages.length, 0);
    
    return {
      groups,
      orphanedPages,
      missingPages: groups.flatMap(g => g.missingPages || []),
      suggestedConnections,
      summary: {
        totalPages: pages.length,
        categorizedPages,
        completeness: Math.round((categorizedPages / pages.length) * 100),
        lastAnalyzed: new Date()
      }
    };
  }
  
  /**
   * 📋 GET EXPECTED PAGES FOR CATEGORY
   */
  private getExpectedPagesForCategory(categoryId: string): string[] {
    const expectedPages: Record<string, string[]> = {
      'core-platform': ['/', '/login', '/register'],
      'intelligence-hub': ['/intelligence', '/intelligence-analytics', '/omniscient', '/models'],
      'agent-hub': ['/agents', '/agent-ecosystem', '/swarm', '/orchestration', '/autonomous-core-demo'],
      'creative-hub': ['/creative', '/canvas', '/ai-tools', '/gallery', '/marketplace', '/voice'],
      'dashboard-hub': ['/dashboard', '/monitoring', '/chat', '/community', '/tasks', '/profile']
    };

    return expectedPages[categoryId] || [];
  }
  
  /**
   * 🔗 GENERATE CONNECTION SUGGESTIONS
   */
  private generateConnectionSuggestions(pages: PageInfo[]): Array<{from: string; to: string; reason: string}> {
    const suggestions: Array<{from: string; to: string; reason: string}> = [];
    
    for (const page of pages) {
      // Suggest connections based on content analysis
      if (page.suggestedConnections) {
        for (const connection of page.suggestedConnections) {
          if (pages.some(p => p.route === connection)) {
            suggestions.push({
              from: page.route,
              to: connection,
              reason: 'Content analysis suggests related functionality'
            });
          }
        }
      }
      
      // Suggest missing auth pages
      if (page.hasAuth && !pages.some(p => p.route === '/login')) {
        suggestions.push({
          from: page.route,
          to: '/login',
          reason: 'Page requires authentication but login page missing'
        });
      }
    }
    
    return suggestions;
  }
  
  /**
   * 🔄 UPDATE NAVIGATION CACHE
   */
  async updateNavigationCache(): Promise<void> {
    await this.discoverAllPages();
    console.log('🔄 Navigation cache updated');
  }
  
  /**
   * 📊 GET NAVIGATION STATUS
   */
  getNavigationStatus() {
    return {
      totalCachedPages: this.pageCache.size,
      lastScan: this.lastScan,
      cacheValid: this.lastScan ? (Date.now() - this.lastScan.getTime()) < 5 * 60 * 1000 : false // 5 minutes
    };
  }
}

export const dynamicNavigationService = new DynamicNavigationService();
export default dynamicNavigationService; 