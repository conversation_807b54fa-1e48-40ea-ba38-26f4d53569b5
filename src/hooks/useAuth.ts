"use client";

import { useState, useEffect, useCallback } from "react";
import axios from "axios";
import TestAccountManager from "@/systems/testing/TestAccountManager";

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'observer' | 'admin_observer' | 'developer_observer';
  permissions: string[];
  createdAt?: string;
  updatedAt?: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  isGuest?: boolean;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: true,
    error: null,
  });
  
  // Track if component has mounted to prevent hydration mismatches
  const [hasMounted, setHasMounted] = useState(false);

  // Initialize auth state from localStorage only after mounting
  useEffect(() => {
    setHasMounted(true);
    
    try {
      const token = localStorage.getItem("token");
      if (token) {
        fetchCurrentUser(token);
      } else {
        setAuthState((prev) => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      // Handle localStorage access errors
      console.warn('localStorage access error:', error);
      setAuthState((prev) => ({ ...prev, isLoading: false }));
    }
  }, []); // CRITICAL: Empty dependency array to prevent loops

  // Function to fetch current user with token - memoized to prevent recreating
  const fetchCurrentUser = useCallback(async (token: string) => {
    if (!token || typeof token !== 'string') {
      setAuthState({
        user: null,
        token: null,
        isLoading: false,
        error: 'Invalid token',
      });
      return;
    }

    try {
      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

      // Use our new observation auth endpoint
      const response = await axios.get("/api/auth/observation", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data.success) {
        setAuthState({
          user: response.data.user,
          token,
          isLoading: false,
          error: null,
        });

        localStorage.setItem("token", token);
      } else {
        throw new Error(response.data.error || 'Authentication failed');
      }
    } catch (error) {
      console.error("Error fetching current user:", error);

      try {
        localStorage.removeItem("token");
      } catch (e) {
        console.warn('localStorage removal error:', e);
      }

      setAuthState({
        user: null,
        token: null,
        isLoading: false,
        error: "Authentication failed. Please login again.",
      });
    }
  }, []); // FIXED: No dependencies to prevent infinite loops

  // Login function - stable reference
  const login = useCallback(async (credentials: LoginCredentials) => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));

      // Check if this is a test account login
      const testManager = TestAccountManager.getInstance();
      const testUser = testManager.authenticateUser(credentials.email, credentials.password);

      if (testUser) {
        // Handle test account login
        console.log('🧪 Test account login:', testUser.username);

        // Load test user preferences
        testManager.loadPersistedTestData(testUser.id);
        const testPreferences = testManager.getUserPreferences(testUser.id);

        // Create a mock token for test accounts
        const testToken = `test-token-${testUser.id}-${Date.now()}`;

        try {
          localStorage.setItem("token", testToken);
          localStorage.setItem("test-user", JSON.stringify(testUser));
          if (testPreferences) {
            localStorage.setItem(`user-preferences-${testUser.id}`, JSON.stringify(testPreferences));
          }
        } catch (e) {
          console.warn('localStorage write error:', e);
        }

        setAuthState({
          user: {
            id: testUser.id,
            name: testUser.name,
            email: testUser.email,
            role: testUser.type === 'admin' ? 'admin_observer' : 'observer',
            permissions: testUser.type === 'admin' ? ['admin', 'theme:create', 'theme:save', 'settings:all'] : ['theme:create', 'theme:save']
          },
          token: testToken,
          isLoading: false,
          error: null,
        });

        return { success: true };
      }

      // Regular API login for non-test accounts
      const response = await axios.post("/api/auth/observation", credentials);

      if (response.data.success) {
        const { token, user } = response.data;

        try {
          localStorage.setItem("token", token);
        } catch (e) {
          console.warn('localStorage write error:', e);
        }

        setAuthState({
          user,
          token,
          isLoading: false,
          error: null,
        });

        return { success: true };
      } else {
        throw new Error(response.data.error || 'Login failed');
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.error || error.message || "Login failed. Please try again.";

      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));

      return {
        success: false,
        error: errorMessage,
        fieldErrors: error.response?.data?.errors || {}
      };
    }
  }, []); // FIXED: Empty dependencies

  // Register function - stable reference
  const register = useCallback(async (data: RegisterData) => {
    try {
      setAuthState((prev) => ({ ...prev, isLoading: true, error: null }));
      
      const response = await axios.post("/api/auth/register", data);
      const { token, user } = response.data.data;
      
      try {
        localStorage.setItem("token", token);
      } catch (e) {
        console.warn('localStorage write error:', e);
      }
      
      setAuthState({
        user,
        token,
        isLoading: false,
        error: null,
      });
      
      return { success: true };
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || "Registration failed. Please try again.";
      
      setAuthState((prev) => ({
        ...prev,
        isLoading: false,
        error: errorMessage,
      }));
      
      return { 
        success: false, 
        error: errorMessage,
        fieldErrors: error.response?.data?.errors || {}
      };
    }
  }, []); // FIXED: Empty dependencies

  // Logout function - stable reference
  const logout = useCallback(async () => {
    try {
      // Call logout endpoint to clear server-side cookie
      await axios.post("/api/auth/logout");
    } catch (e) {
      console.warn('Logout API error:', e);
    }

    try {
      localStorage.removeItem("token");
    } catch (e) {
      console.warn('localStorage removal error:', e);
    }

    setAuthState({
      user: null,
      token: null,
      isLoading: false,
      error: null,
    });
  }, []); // FIXED: Empty dependencies

  // Permission checking functions
  const hasPermission = useCallback((permission: string): boolean => {
    return authState.user?.permissions.includes(permission) || false;
  }, [authState.user]);

  const hasAnyPermission = useCallback((permissions: string[]): boolean => {
    if (!authState.user) return false;
    return permissions.some(permission => authState.user!.permissions.includes(permission));
  }, [authState.user]);

  const hasAllPermissions = useCallback((permissions: string[]): boolean => {
    if (!authState.user) return false;
    return permissions.every(permission => authState.user!.permissions.includes(permission));
  }, [authState.user]);

  return {
    user: hasMounted ? authState.user : null, // Always return null until mounted
    isAuthenticated: hasMounted ? !!authState.user : false,
    isLoading: hasMounted ? authState.isLoading : true, // Show loading until mounted
    error: authState.error,
    login,
    register,
    logout,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  };
}