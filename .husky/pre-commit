#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 CreAItive Pre-commit Error Prevention System"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

# Run comprehensive pre-commit checks
npm run pre-commit-checks

# Check exit code and prevent commit if checks fail
if [ $? -ne 0 ]; then
  echo "❌ Pre-commit checks failed! Commit prevented."
  echo "   Fix the issues above and try committing again."
  exit 1
fi

echo "✅ All pre-commit checks passed! Proceeding with commit."
