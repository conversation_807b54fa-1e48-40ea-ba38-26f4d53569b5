# 🧪 Test Accounts System

## Overview

This folder contains test account configurations and data for consistent testing across cache clears and browser resets.

## Test Accounts

### Primary Test Account
- **Username**: `AGITEST`
- **Password**: `AGITEST123!`
- **Email**: `<EMAIL>`
- **Role**: `admin`
- **ID**: `test-user-001`

### Guest Test User
- **Username**: `TestGuest`
- **ID**: `guest-test-001`
- **Type**: `guest`
- **Persistent**: `true`

## Test Data Structure

```
testaccounts/
├── README.md                    # This file
├── users/
│   ├── test-user-001.json      # AGITEST account data
│   └── guest-test-001.json     # Guest test data
├── themes/
│   ├── test-themes.json        # Pre-saved test themes
│   └── default-themes.json     # Default theme configurations
├── preferences/
│   ├── test-user-preferences.json
│   └── guest-preferences.json
└── sessions/
    └── active-sessions.json    # Test session data
```

## Usage

1. **Login as Test User**: Use `AGITEST` / `AGITEST123!`
2. **Login as Guest**: Click "Login as Guest" → automatically uses TestGuest
3. **Reset Test Data**: Run `npm run reset-test-data`
4. **Load Test Themes**: Automatically loaded on login

## Features

- ✅ Persistent test accounts (survive cache clears)
- ✅ Pre-configured theme preferences
- ✅ Consistent user IDs for testing
- ✅ Guest user with saved preferences
- ✅ Test data reset functionality
- ✅ Isolated from production data
