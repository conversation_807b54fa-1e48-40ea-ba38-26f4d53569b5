#!/bin/sh

# Git Pre-commit Hook - Interface Conflict Prevention
# Based on Devstral's automation recommendations

echo "🔍 Pre-commit checks starting..."
echo "📋 Checking for TypeScript interface conflicts..."

# Run interface conflict checker
npm run check-interface-conflicts

# Check the exit code
if [ $? -ne 0 ]; then
    echo ""
    echo "❌ COMMIT BLOCKED: Interface conflicts detected!"
    echo "🔧 Please fix the interface naming conflicts before committing."
    echo "💡 Use the suggested interface names from the report above."
    echo ""
    echo "🤖 Tip: Run 'npm run check-interface-conflicts' to see detailed suggestions."
    exit 1
fi

echo "✅ Interface conflict check passed!"

# Run TypeScript type checking
echo "🔧 Running TypeScript type check..."
npm run type-check

if [ $? -ne 0 ]; then
    echo ""
    echo "❌ COMMIT BLOCKED: TypeScript compilation errors detected!"
    echo "🔧 Please fix TypeScript errors before committing."
    exit 1
fi

echo "✅ TypeScript type check passed!"

# Run basic linting
echo "📝 Running ESLint..."
npm run lint

if [ $? -ne 0 ]; then
    echo ""
    echo "⚠️  Warning: Linting issues detected."
    echo "🔧 Consider fixing linting issues, but commit will proceed."
fi

echo ""
echo "✅ All pre-commit checks completed successfully!"
echo "🚀 Commit proceeding..."
echo ""
echo "🤖 Automated by: Devstral AI Coordination System" 