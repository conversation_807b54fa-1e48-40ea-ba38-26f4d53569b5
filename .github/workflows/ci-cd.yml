# Generated autonomously by OpsAgent - Enhanced for Track 5 Day 2
name: CI/CD Pipeline - Production Ready (Parallel Development)

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # =============================================
  # PHASE 1: BUILD & TEST (parallel development support)
  # =============================================
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: TypeScript type checking
      run: npm run type-check
    
    - name: Run unit tests
      run: npm test -- --coverage --passWithNoTests
    
    - name: Run Track 5 specific tests
      run: npm test -- src/__tests__/track5-day1-kubernetes-deployment.test.ts
    
    - name: Build application
      run: npm run build
    
    - name: Upload coverage reports
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: coverage/

  # =============================================
  # PHASE 2: DOCKER BUILD & PUSH
  # =============================================
  docker:
    needs: test
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    steps:
    - uses: actions/checkout@v4
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: Build and push Docker image (Production)
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.production
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}

  # =============================================
  # PHASE 3: KUBERNETES DEPLOYMENT
  # =============================================
  deploy:
    needs: [test, docker]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
    
    - name: Deploy to Development
      run: |
        echo "🚀 Deploying to Development Kubernetes cluster..."
        # Update image in deployment
        sed -i 's|image: .*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}|' k8s/deployment.yaml
        
        # Apply configurations
        kubectl apply -f k8s/namespace.yaml
        kubectl apply -f k8s/configmap.yaml
        kubectl apply -f k8s/deployment.yaml
        kubectl apply -f k8s/service.yaml
        
        # Wait for deployment to complete
        kubectl rollout status deployment/creAItive-app -n development --timeout=300s
    
    - name: Verify deployment
      run: |
        echo "✅ Verifying deployment health..."
        kubectl get pods -n development
        kubectl get services -n development
        
        # Health check
        POD_NAME=$(kubectl get pods -n development -l app=creAItive-app -o jsonpath='{.items[0].metadata.name}')
        kubectl logs $POD_NAME -n development --tail=50

  # =============================================
  # PHASE 4: PRODUCTION DEPLOYMENT (Manual)
  # =============================================
  deploy-production:
    needs: [test, docker, deploy]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup kubectl
      uses: azure/setup-kubectl@v3
      with:
        version: 'v1.28.0'
    
    - name: Deploy to Production
      run: |
        echo "🎯 Deploying to Production Kubernetes cluster..."
        # Update image in deployment for production
        sed -i 's|image: .*|image: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}|' k8s/deployment.yaml
        sed -i 's|namespace: development|namespace: production|' k8s/deployment.yaml
        
        # Apply production configurations
        kubectl apply -f k8s/namespace.yaml
        kubectl apply -f k8s/configmap.yaml
        kubectl apply -f k8s/deployment.yaml
        kubectl apply -f k8s/service.yaml
        
        # Wait for production deployment
        kubectl rollout status deployment/creAItive-app -n production --timeout=600s
        
        echo "🚀 Production deployment completed successfully!"
