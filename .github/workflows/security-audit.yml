name: Security Audit

on:
  schedule:
    # Run security audit daily at 2 AM UTC
    - cron: '0 2 * * *'
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  # =============================================
  # DEPENDENCY VULNERABILITY SCANNING
  # =============================================
  dependency-audit:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run npm audit
      run: npm audit --audit-level=moderate
    
    - name: Check for vulnerabilities
      run: npm audit --audit-level=high --json > audit-results.json || true
    
    - name: Upload audit results
      uses: actions/upload-artifact@v4
      with:
        name: npm-audit-results
        path: audit-results.json

  # =============================================
  # DOCKER IMAGE SECURITY SCANNING  
  # =============================================
  docker-security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    
    - name: Build Docker image for scanning
      run: docker build -f Dockerfile.production -t security-scan:latest .
    
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'security-scan:latest'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

  # =============================================
  # SECRET SCANNING
  # =============================================
  secret-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: TruffleHog OSS
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified

  # =============================================
  # CODE QUALITY & SECURITY ANALYSIS
  # =============================================
  codeql-analysis:
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write
    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript' ]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Initialize CodeQL
      uses: github/codeql-action/init@v3
      with:
        languages: ${{ matrix.language }}
    
    - name: Autobuild
      uses: github/codeql-action/autobuild@v3
    
    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v3
      with:
        category: "/language:${{matrix.language}}"

  # =============================================
  # SECURITY SUMMARY REPORT
  # =============================================
  security-report:
    needs: [dependency-audit, docker-security, secret-scan, codeql-analysis]
    runs-on: ubuntu-latest
    if: always()
    steps:
    - name: Security Summary
      run: |
        echo "🛡️ SECURITY AUDIT COMPLETED"
        echo "================================"
        echo "✅ Dependency vulnerabilities checked"
        echo "✅ Docker image security scanned"
        echo "✅ Secret scanning completed"
        echo "✅ Code quality analysis finished"
        echo ""
        echo "📊 Review results in Security tab and artifacts" 