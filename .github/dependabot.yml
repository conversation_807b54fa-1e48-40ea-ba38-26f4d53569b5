version: 2
updates:
  # Enable version updates for npm dependencies
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
    reviewers:
      - "CreAItive-team"
    assignees:
      - "CreAItive-team"
    commit-message:
      prefix: "chore(deps):"
      include: "scope"
    open-pull-requests-limit: 10
    
  # Enable version updates for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "10:00"
    reviewers:
      - "CreAItive-team"
    assignees:
      - "CreAItive-team"
    commit-message:
      prefix: "chore(ci):"
      include: "scope"
    open-pull-requests-limit: 5

  # Enable version updates for Docker
  - package-ecosystem: "docker"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "tuesday"
      time: "09:00"
    reviewers:
      - "CreAItive-team"
    assignees:
      - "CreAItive-team"
    commit-message:
      prefix: "chore(docker):"
      include: "scope"
    open-pull-requests-limit: 5 