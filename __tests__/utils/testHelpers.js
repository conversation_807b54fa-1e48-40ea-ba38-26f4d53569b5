
import { render } from '@testing-library/react';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';

// Test store factory
export const createTestStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      // Add your reducers here
    },
    preloadedState: initialState,
  });
};

// Custom render with providers
export const renderWithProviders = (ui, options = {}) => {
  const { initialState = {}, store = createTestStore(initialState), ...renderOptions } = options;
  
  const Wrapper = ({ children }) => (
    <Provider store={store}>{children}</Provider>
  );
  
  return {
    store,
    ...render(ui, { wrapper: Wrapper, ...renderOptions }),
  };
};

// Mock API responses
export const mockApiResponse = (data, status = 200) => ({
  ok: status >= 200 && status < 300,
  status,
  json: () => Promise.resolve(data),
});

// Test data factories
export const createMockAgent = (overrides = {}) => ({
  agentId: 'test-agent',
  tier: 'PROBATION',
  performanceMetrics: {
    successRate: 0,
    averageResponseTime: 0,
    resourceEfficiency: 50,
  },
  ...overrides,
});
