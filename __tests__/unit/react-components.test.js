
import { render, screen } from '@testing-library/react';
import { renderWithProviders } from '../utils/testHelpers';

// Mock component for testing
const TestComponent = ({ title = 'Test Title' }) => (
  <div data-testid="test-component">
    <h1>{title}</h1>
  </div>
);

describe('React Components', () => {
  test('should render component correctly', () => {
    render(<TestComponent />);
    
    expect(screen.getByTestId('test-component')).toBeInTheDocument();
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });
  
  test('should render with custom props', () => {
    render(<TestComponent title="Custom Title" />);
    
    expect(screen.getByText('Custom Title')).toBeInTheDocument();
  });
});
