
import { createMocks } from 'node-mocks-http';
import handler from '../../src/pages/api/governance/register-agent';

describe('/api/governance/register-agent', () => {
  test('should register agent successfully', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        agentId: 'test-agent',
        capabilities: ['testing', 'monitoring']
      },
    });
    
    await handler(req, res);
    
    expect(res._getStatusCode()).toBe(201);
    const data = JSON.parse(res._getData());
    expect(data.success).toBe(true);
    expect(data.data.agentId).toBe('test-agent');
  });
  
  test('should reject invalid requests', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        // Missing required fields
      },
    });
    
    await handler(req, res);
    
    expect(res._getStatusCode()).toBe(400);
    const data = JSON.parse(res._getData());
    expect(data.error).toBe('Invalid request');
  });
  
  test('should reject non-POST requests', async () => {
    const { req, res } = createMocks({
      method: 'GET',
    });
    
    await handler(req, res);
    
    expect(res._getStatusCode()).toBe(405);
  });
});
