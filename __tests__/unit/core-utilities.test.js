
describe('Core Utilities', () => {
  describe('Memory Management', () => {
    test('should monitor memory usage', () => {
      const usage = process.memoryUsage();
      expect(usage).toHaveProperty('heapUsed');
      expect(usage).toHaveProperty('heapTotal');
      expect(usage.heapUsed).toBeGreaterThan(0);
    });
  });
  
  describe('Error Handling', () => {
    test('should handle errors gracefully', () => {
      const errorHandler = (error) => {
        return {
          success: false,
          error: error.message
        };
      };
      
      const result = errorHandler(new Error('Test error'));
      expect(result.success).toBe(false);
      expect(result.error).toBe('Test error');
    });
  });
});
