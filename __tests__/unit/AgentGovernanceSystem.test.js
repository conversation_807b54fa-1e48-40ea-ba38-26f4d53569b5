
import { AgentGovernanceSystem } from '../../src/agent-core/governance/AgentGovernanceSystem';
import { createMockAgent } from '../utils/testHelpers';

describe('AgentGovernanceSystem', () => {
  let governance;
  
  beforeEach(() => {
    governance = AgentGovernanceSystem.getInstance();
  });
  
  describe('Agent Registration', () => {
    test('should register new agent with PROBATION tier', async () => {
      const agentId = 'test-agent';
      const capabilities = ['testing', 'monitoring'];
      
      const profile = await governance.registerAgent(agentId, capabilities);
      
      expect(profile.agentId).toBe(agentId);
      expect(profile.tier).toBe('PROBATION');
      expect(profile.localAIAccess.accessLevel).toBe('restricted');
    });
    
    test('should prevent duplicate agent registration', async () => {
      const agentId = 'duplicate-agent';
      const capabilities = ['testing'];
      
      await governance.registerAgent(agentId, capabilities);
      
      // Second registration should handle gracefully
      const secondProfile = await governance.registerAgent(agentId, capabilities);
      expect(secondProfile).toBeDefined();
    });
  });
  
  describe('Access Control', () => {
    test('should restrict access for new agents', async () => {
      const agentId = 'restricted-agent';
      await governance.registerAgent(agentId, ['testing']);
      
      const accessResult = await governance.requestLocalAIAccess(
        agentId,
        'test-request',
        'test prompt',
        'low'
      );
      
      expect(accessResult.approved).toBe(false);
      expect(accessResult.denyReason).toContain('restricted');
    });
  });
  
  describe('Performance Evaluation', () => {
    test('should evaluate agent performance correctly', async () => {
      const agentId = 'performance-agent';
      await governance.registerAgent(agentId, ['testing']);
      
      const evaluation = await governance.evaluateAgentForLocalAI(agentId);
      
      expect(evaluation).toHaveProperty('approved');
      expect(evaluation).toHaveProperty('accessLevel');
      expect(evaluation).toHaveProperty('reasoning');
    });
  });
});
