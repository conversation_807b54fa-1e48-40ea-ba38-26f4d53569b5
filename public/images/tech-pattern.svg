<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <pattern id="tech-grid" patternUnits="userSpaceOnUse" width="100" height="100">
      <!-- Main grid lines -->
      <path d="M 0,50 L 100,50 M 50,0 L 50,100" stroke="#6E7AFF" stroke-width="0.5" opacity="0.2" />
      
      <!-- Secondary grid lines -->
      <path d="M 0,25 L 100,25 M 0,75 L 100,75 M 25,0 L 25,100 M 75,0 L 75,100" stroke="#6E7AFF" stroke-width="0.3" opacity="0.1" />
      
      <!-- Diagonal accents -->
      <path d="M 25,0 L 0,25 M 75,0 L 0,75 M 100,25 L 25,100 M 100,75 L 75,100" stroke="#6E7AFF" stroke-width="0.2" opacity="0.1" />
      
      <!-- Nodes -->
      <circle cx="50" cy="50" r="1.2" fill="#6E7AFF" opacity="0.4" />
      <circle cx="25" cy="25" r="0.8" fill="#6E7AFF" opacity="0.3" />
      <circle cx="25" cy="75" r="0.8" fill="#6E7AFF" opacity="0.3" />
      <circle cx="75" cy="25" r="0.8" fill="#6E7AFF" opacity="0.3" />
      <circle cx="75" cy="75" r="0.8" fill="#6E7AFF" opacity="0.3" />
      
      <!-- Intersection points -->
      <circle cx="0" cy="0" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="0" cy="25" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="0" cy="50" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="0" cy="75" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="0" cy="100" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="25" cy="0" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="50" cy="0" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="75" cy="0" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="100" cy="0" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="100" cy="25" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="100" cy="50" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="100" cy="75" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="100" cy="100" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="25" cy="100" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="50" cy="100" r="0.5" fill="#6E7AFF" opacity="0.2" />
      <circle cx="75" cy="100" r="0.5" fill="#6E7AFF" opacity="0.2" />
    </pattern>
  </defs>
  <rect width="100" height="100" fill="url(#tech-grid)" />
</svg> 