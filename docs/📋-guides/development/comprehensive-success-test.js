#!/usr/bin/env node

console.log('🎯 COMPREHENSIVE SUCCESS RATE TEST - Target: 85%+\n');

const http = require('http');

function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            data: data ? JSON.parse(data) : null,
            raw: data
          });
        } catch (e) {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            data: null,
            raw: data
          });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (postData) {
      req.write(JSON.stringify(postData));
    }
    
    req.end();
  });
}

async function testAgentEndpoint(agentPath, action, name, timeout = 30000) {
  console.log(`🧠 Testing ${name}...`);
  const startTime = Date.now();
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: `/api/${agentPath}?action=${action}`,
      method: 'GET',
      timeout: timeout
    });
    
    const duration = Date.now() - startTime;
    
    if (response.ok) {
      console.log(`  ✅ SUCCESS in ${duration}ms`);
      return { success: true, duration, status: response.status };
    } else {
      console.log(`  ❌ FAILED (${response.status}) in ${duration}ms`);
      if (response.raw && response.raw.length < 200) {
        console.log(`     Error: ${response.raw.substring(0, 100)}...`);
      }
      return { success: false, duration, status: response.status };
    }
    
  } catch (error) {
    const duration = Date.now() - startTime;
    console.log(`  ❌ ERROR: ${error.message} (${duration}ms)`);
    return { success: false, duration, status: 0 };
  }
}

async function runComprehensiveSuccessTest() {
  console.log('🚀 Starting Comprehensive Success Rate Test...\n');
  
  // Phase 1: Quick validation tests (should succeed fast)
  console.log('📊 Phase 1: Quick Validation Tests\n');
  
  const quickTests = [
    { path: 'agents/test', action: 'status', name: 'TestAgent Status' },
    { path: 'agents/ui', action: 'status', name: 'UIAgent Status' },
    { path: 'agents/ops', action: 'status', name: 'OpsAgent Status' },
    { path: 'agents/autonomous-dev', action: 'status', name: 'AutonomousDevAgent Status' }
  ];
  
  const quickResults = [];
  
  for (const test of quickTests) {
    const result = await testAgentEndpoint(test.path, test.action, test.name, 15000);
    quickResults.push({ test: test.name, ...result });
    await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
  }
  
  // Phase 2: AI-powered analysis tests (longer processing)
  console.log('\n🧠 Phase 2: AI-Powered Analysis Tests\n');
  
  const analysisTests = [
    { path: 'agents/test', action: 'analyze', name: 'TestAgent Analysis' },
    { path: 'agents/ui', action: 'analyze', name: 'UIAgent Analysis' }
  ];
  
  const analysisResults = [];
  
  for (const test of analysisTests) {
    const result = await testAgentEndpoint(test.path, test.action, test.name, 60000);
    analysisResults.push({ test: test.name, ...result });
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
  }
  
  // Phase 3: Real-time metrics
  console.log('\n📈 Phase 3: Final System Metrics\n');
  
  try {
    const metricsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET',
      timeout: 10000
    });
    
    if (metricsResponse.ok && metricsResponse.data) {
      const metrics = metricsResponse.data.performanceMetrics || {};
      const queue = metricsResponse.data.queueManagement || {};
      
      console.log('🏆 REAL-TIME SYSTEM METRICS:');
      console.log(`  🔥 Total AI Requests: ${metrics.totalRequests || 0}`);
      console.log(`  ✅ AI Success Rate: ${(metrics.successRate * 100 || 0).toFixed(1)}%`);
      console.log(`  ⚡ Avg Response Time: ${metrics.averageResponseTime || 0}ms`);
      console.log(`  📋 Queue Size: ${queue.currentQueueSize || 0}`);
      console.log(`  🔄 Active Requests: ${queue.activeRequests || 0}`);
      console.log(`  🌡️ Thermal State: ${metricsResponse.data.thermalManagement?.state || 'unknown'}`);
    }
  } catch (error) {
    console.log(`❌ Metrics Error: ${error.message}`);
  }
  
  // Calculate overall success rate
  const allResults = [...quickResults, ...analysisResults];
  const successCount = allResults.filter(r => r.success).length;
  const totalTests = allResults.length;
  const overallSuccessRate = (successCount / totalTests) * 100;
  
  console.log('\n' + '='.repeat(60));
  console.log('🎯 COMPREHENSIVE SUCCESS RATE RESULTS');
  console.log('='.repeat(60));
  
  console.log(`📊 Overall Success Rate: ${overallSuccessRate.toFixed(1)}%`);
  console.log(`✅ Successful Tests: ${successCount}/${totalTests}`);
  
  // Detailed breakdown
  console.log('\n📋 Test Breakdown:');
  quickResults.forEach(result => {
    const status = result.success ? '✅' : '❌';
    console.log(`  ${status} ${result.test}: ${result.duration}ms`);
  });
  
  if (analysisResults.length > 0) {
    console.log('\n🧠 AI Analysis Tests:');
    analysisResults.forEach(result => {
      const status = result.success ? '✅' : '❌';
      console.log(`  ${status} ${result.test}: ${result.duration}ms`);
    });
  }
  
  // Success evaluation
  console.log('\n🎯 SUCCESS EVALUATION:');
  
  if (overallSuccessRate >= 85) {
    console.log('🎉 EXCELLENT: Target 85%+ SUCCESS RATE ACHIEVED!');
    console.log('✅ Ready for production deployment');
  } else if (overallSuccessRate >= 70) {
    console.log(`🔧 GOOD PROGRESS: ${overallSuccessRate.toFixed(1)}% success rate`);
    console.log('🎯 Continue optimization to reach 85%+ target');
  } else if (overallSuccessRate >= 50) {
    console.log(`🔧 MODERATE PROGRESS: ${overallSuccessRate.toFixed(1)}% success rate`);
    console.log('🔧 Significant optimization needed');
  } else {
    console.log(`⚠️ LOW SUCCESS RATE: ${overallSuccessRate.toFixed(1)}%`);
    console.log('🚨 Major system issues need attention');
  }
  
  console.log('\n🚀 NEXT ACTIONS:');
  if (overallSuccessRate >= 85) {
    console.log('1. Document breakthrough in Memory Bank');
    console.log('2. Scale to full agent ecosystem');
    console.log('3. Production readiness validation');
  } else {
    console.log('1. Analyze failed endpoints');
    console.log('2. Optimize error handling');
    console.log('3. Retry optimization cycle');
  }
  
  return {
    successRate: overallSuccessRate,
    totalTests,
    successCount,
    ready: overallSuccessRate >= 85
  };
}

runComprehensiveSuccessTest().catch(error => {
  console.error('💥 Test Error:', error);
  process.exit(1);
}); 