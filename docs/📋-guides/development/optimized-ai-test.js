#!/usr/bin/env node

console.log('🧠🚀 Optimized AI System Test - Success Rate Validation\n');

const http = require('http');

function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            data: data ? JSON.parse(data) : null
          });
        } catch (e) {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            data: data
          });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (postData) {
      req.write(JSON.stringify(postData));
    }
    
    req.end();
  });
}

async function testStabilityImprovements() {
  console.log('🔧 Phase 1: Testing System Stability...\n');
  
  // Test server health
  try {
    const healthCheck = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/health',
      method: 'GET',
      timeout: 5000
    });
    
    if (healthCheck.ok) {
      console.log('✅ Server: Healthy and responsive');
    } else {
      console.log(`⚠️ Server: Response ${healthCheck.status}`);
    }
  } catch (error) {
    console.log(`❌ Server: ${error.message}`);
    return false;
  }
  
  // Test AI Resource Manager
  try {
    const aiStatus = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET',
      timeout: 10000
    });
    
    if (aiStatus.ok && aiStatus.data) {
      const metrics = aiStatus.data.performanceMetrics || {};
      console.log('✅ AI Resource Manager: Online');
      console.log(`  📊 Total Requests: ${metrics.totalRequests || 0}`);
      console.log(`  ✅ Success Rate: ${(metrics.successRate * 100 || 0).toFixed(1)}%`);
      console.log(`  ⏱️ Avg Response: ${metrics.averageResponseTime || 0}ms`);
    } else {
      console.log(`⚠️ AI Resource Manager: Status ${aiStatus.status}`);
    }
  } catch (error) {
    console.log(`❌ AI Resource Manager: ${error.message}`);
  }
  
  console.log('\n🧪 Phase 2: Testing AI Request Processing...\n');
  
  return true;
}

async function testSuccessRateOptimization() {
  console.log('📈 Testing Success Rate Optimization...\n');
  
  const testRequests = [
    { agent: 'agents/test', action: 'status', name: 'TestAgent Status' },
    { agent: 'agents/test', action: 'analyze', name: 'TestAgent Analysis' },
    { agent: 'agents/ui', action: 'status', name: 'UIAgent Status' },
    { agent: 'agents/ops', action: 'status', name: 'OpsAgent Status' }
  ];
  
  const results = [];
  
  for (const test of testRequests) {
    try {
      console.log(`🧠 Testing ${test.name}...`);
      const startTime = Date.now();
      
      const response = await makeRequest({
        hostname: 'localhost',
        port: 3000,
        path: `/api/${test.agent}?action=${test.action}`,
        method: 'GET',
        timeout: 45000 // 45 second timeout for real AI processing
      });
      
      const duration = Date.now() - startTime;
      
      if (response.ok) {
        console.log(`  ✅ Success in ${duration}ms`);
        results.push({ test: test.name, success: true, duration });
      } else {
        console.log(`  ⚠️ Failed (${response.status}) in ${duration}ms`);
        results.push({ test: test.name, success: false, duration });
      }
      
    } catch (error) {
      console.log(`  ❌ Error: ${error.message}`);
      results.push({ test: test.name, success: false, duration: 0 });
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // Calculate success rate
  const successCount = results.filter(r => r.success).length;
  const successRate = (successCount / results.length) * 100;
  
  console.log(`\n📊 Test Results:`);
  console.log(`  🎯 Success Rate: ${successRate.toFixed(1)}%`);
  console.log(`  ✅ Successful: ${successCount}/${results.length}`);
  
  if (successRate >= 85) {
    console.log(`  🎉 SUCCESS: Target 85%+ achieved!`);
  } else if (successRate >= 50) {
    console.log(`  🔧 PROGRESS: ${successRate.toFixed(1)}% success rate (target: 85%+)`);
  } else {
    console.log(`  🔧 OPTIMIZATION: Target 85%+ (currently ${successRate.toFixed(1)}%)`);
  }
  
  return successRate;
}

async function getFinalMetrics() {
  console.log('\n📈 Phase 3: Final System Metrics...\n');
  
  try {
    const finalStatus = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET',
      timeout: 10000
    });
    
    if (finalStatus.ok && finalStatus.data) {
      const metrics = finalStatus.data.performanceMetrics || {};
      const queue = finalStatus.data.queueManagement || {};
      
      console.log('🏆 FINAL SYSTEM STATUS:');
      console.log(`  🔥 Total Requests: ${metrics.totalRequests || 0}`);
      console.log(`  ✅ Success Rate: ${(metrics.successRate * 100 || 0).toFixed(1)}%`);
      console.log(`  ⚡ Avg Response: ${metrics.averageResponseTime || 0}ms`);
      console.log(`  📋 Queue Size: ${queue.currentQueueSize || 0}`);
      console.log(`  🔄 Active Requests: ${queue.activeRequests || 0}`);
      console.log(`  🌡️ Thermal State: ${finalStatus.data.thermalManagement?.state || 'unknown'}`);
      
      const totalRequests = metrics.totalRequests || 0;
      const successRate = (metrics.successRate * 100 || 0);
      
      if (totalRequests > 0 && successRate >= 85) {
        console.log('\n🎉 OPTIMIZATION COMPLETE: System performing at target levels!');
        return true;
      } else if (totalRequests > 0) {
        console.log(`\n🔧 PROGRESS: ${successRate.toFixed(1)}% success rate (target: 85%+)`);
        return false;
      } else {
        console.log('\n⏳ WAITING: No AI requests processed yet');
        return false;
      }
    }
  } catch (error) {
    console.log(`❌ Metrics error: ${error.message}`);
    return false;
  }
}

async function runOptimizedTest() {
  console.log('🚀 OPTIMIZED AI SYSTEM TEST STARTING...\n');
  
  // Phase 1: Stability
  const stable = await testStabilityImprovements();
  if (!stable) {
    console.log('\n🚨 CRITICAL: System not stable - aborting test');
    process.exit(1);
  }
  
  // Phase 2: Success Rate
  const successRate = await testSuccessRateOptimization();
  
  // Phase 3: Final Metrics
  const optimized = await getFinalMetrics();
  
  console.log('\n' + '='.repeat(60));
  console.log('🎯 OPTIMIZATION TEST COMPLETE');
  console.log('='.repeat(60));
  
  if (optimized) {
    console.log('✅ STATUS: OPTIMIZATION SUCCESSFUL - Target performance achieved!');
  } else {
    console.log('🔧 STATUS: OPTIMIZATION IN PROGRESS - Continue monitoring for target performance');
  }
  
  console.log(`📊 Current Success Rate: ${successRate.toFixed(1)}% (Target: 85%+)`);
  console.log('🚀 Next: Scale to all 4 agents for full system test');
}

runOptimizedTest().catch(error => {
  console.error('💥 Test error:', error);
  process.exit(1);
}); 