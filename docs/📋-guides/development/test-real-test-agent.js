#!/usr/bin/env node

/**
 * 🧠🔧 Test Real TestAgent Integration
 * 
 * Tests the updated TestAgent API endpoint to verify it makes real AI requests
 */

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ ok: false, status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }
    req.end();
  });
}

async function testRealTestAgent() {
  console.log('🧠🔧 Testing Real TestAgent Integration...\n');
  
  // Get initial metrics
  console.log('📊 Step 1: Getting initial AI metrics...');
  try {
    const initialMetrics = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET'
    });
    
    if (initialMetrics.ok) {
      const initialRequests = initialMetrics.data.performanceMetrics?.totalRequests || 0;
      console.log(`📈 Initial Total AI Requests: ${initialRequests}`);
    } else {
      console.log(`❌ Failed to get initial metrics: ${initialMetrics.status}`);
    }
  } catch (error) {
    console.log(`💥 Initial metrics error: ${error.message}`);
  }
  
  // Test different TestAgent actions
  const actions = ['run_tests', 'quality_check', 'analyze', 'status'];
  
  for (const action of actions) {
    console.log(`\n🧠🚀 Step 2.${actions.indexOf(action) + 1}: Testing TestAgent action: ${action}`);
    
    try {
      const response = await makeRequest({
        hostname: 'localhost',
        port: 3000,
        path: '/api/agents/test',
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }, { action });
      
      if (response.ok && response.data.aiProcessed) {
        console.log(`✅ ${action}: Real AI response generated`);
        console.log(`📊 Response type: ${typeof response.data.result}`);
        if (response.data.result && response.data.result.insights) {
          console.log(`🧠 AI insights: ${response.data.result.insights.length} insights generated`);
        }
      } else if (response.ok) {
        console.log(`⚠️ ${action}: Response but no AI processing flag`);
        console.log(`📄 Response: ${JSON.stringify(response.data).substring(0, 200)}...`);
      } else {
        console.log(`❌ ${action}: Failed (${response.status})`);
      }
      
    } catch (error) {
      console.log(`💥 ${action}: Error - ${error.message}`);
    }
    
    // Brief delay between requests
    await new Promise(resolve => setTimeout(resolve, 3000));
  }
  
  // Wait for AI processing
  console.log('\n⏳ Step 3: Waiting 15 seconds for AI processing...');
  await new Promise(resolve => setTimeout(resolve, 15000));
  
  // Check final metrics
  console.log('\n📈 Step 4: Checking final AI metrics...');
  try {
    const finalMetrics = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET'
    });
    
    if (finalMetrics.ok) {
      const finalRequests = finalMetrics.data.performanceMetrics?.totalRequests || 0;
      const successRate = finalMetrics.data.performanceMetrics?.successRate || 0;
      
      console.log(`📊 Final Total AI Requests: ${finalRequests}`);
      console.log(`✅ Success Rate: ${successRate}%`);
      console.log(`🔄 Active Requests: ${finalMetrics.data.queueManagement?.activeRequests || 0}`);
      console.log(`📋 Queue Size: ${finalMetrics.data.queueManagement?.currentQueueSize || 0}`);
      
      if (finalRequests > 0) {
        console.log('\n🎉 SUCCESS: Real TestAgent AI integration working!');
        console.log('✅ TestAgent is making real AI requests');
        console.log('✅ R1 system processing requests');
        return true;
      } else {
        console.log('\n⚠️ WARNING: No AI requests processed');
        console.log('🔍 TestAgent may not be triggering AI calls');
        return false;
      }
    } else {
      console.log(`❌ Failed to get final metrics: ${finalMetrics.status}`);
      return false;
    }
    
  } catch (error) {
    console.log(`💥 Final metrics error: ${error.message}`);
    return false;
  }
}

// Execute test
testRealTestAgent().then(success => {
  console.log(`\n${'='.repeat(60)}`);
  if (success) {
    console.log('🎯 RESULT: Real TestAgent AI Integration Working!');
    console.log('✅ Agents making real AI requests through R1 system');
    console.log('✅ Ollama API integration successful');
    console.log('✅ End-to-end AI pipeline operational');
  } else {
    console.log('🔧 RESULT: TestAgent needs further investigation');
    console.log('✅ API endpoints responding');
    console.log('❌ AI requests not being processed');
    console.log('🎯 Next: Check agent method implementations');
  }
  console.log(`${'='.repeat(60)}`);
}).catch(error => {
  console.error('Test error:', error);
}); 