#!/usr/bin/env node

/**
 * 🧠🔧 Test Real AI Agent Integration
 */

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ ok: false, status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }
    req.end();
  });
}

async function testRealAIAgents() {
  console.log('🧠🔧 Testing Real AI Agent Integration...');
  
  const agents = ['test', 'autonomous-dev', 'ui', 'ops'];
  const actions = ['status', 'run_tests', 'health_check', 'analyze'];
  
  for (const agent of agents) {
    console.log(`\n📊 Testing ${agent} agent:`);
    
    for (const action of actions) {
      try {
        const response = await makeRequest({
          hostname: 'localhost',
          port: 3000,
          path: `/api/agents/${agent}`,
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        }, { action });
        
        if (response.ok && response.data.aiProcessed) {
          console.log(`✅ ${agent}/${action}: Real AI response (processed)`);
        } else if (response.ok) {
          console.log(`⚠️ ${agent}/${action}: Response but no AI processing`);
        } else {
          console.log(`❌ ${agent}/${action}: Failed (${response.status})`);
        }
        
      } catch (error) {
        console.log(`💥 ${agent}/${action}: Error - ${error.message}`);
      }
      
      // Brief delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  // Check AI Resource Manager metrics
  console.log('\n📈 Checking AI Resource Manager metrics...');
  try {
    const metricsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET'
    });
    
    if (metricsResponse.ok) {
      const totalRequests = metricsResponse.data.performanceMetrics?.totalRequests || 0;
      const successRate = metricsResponse.data.performanceMetrics?.successRate || 0;
      
      console.log(`📊 Total AI Requests: ${totalRequests}`);
      console.log(`✅ Success Rate: ${successRate}%`);
      
      if (totalRequests > 0) {
        console.log('\n🎉 SUCCESS: Real AI integration working!');
      } else {
        console.log('\n⚠️ WARNING: No AI requests processed yet');
      }
    }
    
  } catch (error) {
    console.log(`💥 Metrics check error: ${error.message}`);
  }
}

testRealAIAgents().then(() => {
  console.log('\n🔧 Real AI agent integration test complete');
}).catch(error => {
  console.error('Test error:', error);
});
