#!/usr/bin/env node

/**
 * 🧠🔧 R1 AI Fix Verification Test
 * 
 * Tests if the R1 system now successfully processes AI requests
 * using the fixed Ollama API integration (instead of temp files)
 */

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ ok: false, status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }
    req.end();
  });
}

async function testDirectOllamaAPI() {
  console.log('🧠🔧 Testing Direct Ollama API Integration...\n');
  
  try {
    // Test 1: Direct Ollama API call
    console.log('📡 Test 1: Direct Ollama API Call');
    const ollamaResponse = await makeRequest({
      hostname: 'localhost',
      port: 11434,
      path: '/api/generate',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    }, {
      model: 'deepseek-r1:8b',
      prompt: 'What is 2+2? Provide a brief answer.',
      stream: false,
      options: { temperature: 0.7, num_predict: 100 }
    });

    if (ollamaResponse.ok) {
      console.log(`✅ Direct API Success: ${ollamaResponse.data.response?.length || 0} chars`);
      console.log(`📝 Response: ${(ollamaResponse.data.response || '').substring(0, 100)}...\n`);
    } else {
      console.log(`❌ Direct API Failed: ${ollamaResponse.status}\n`);
      return false;
    }

    // Test 2: Agent request through R1 system
    console.log('🤖 Test 2: Agent Request through R1 System');
    const testAgentResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/agents/test',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    }, { action: 'run_tests' });

    if (testAgentResponse.ok) {
      console.log(`✅ Agent Request Success`);
      console.log(`📊 Results: ${testAgentResponse.data.results?.total} tests, ${testAgentResponse.data.results?.passed} passed\n`);
    } else {
      console.log(`❌ Agent Request Failed: ${testAgentResponse.status}\n`);
    }

    // Test 3: Check AI Resource Manager metrics
    console.log('📊 Test 3: AI Resource Manager Metrics');
    const metricsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager',
      method: 'GET'
    });
    
    if (metricsResponse.ok) {
      console.log(`📈 Metrics Retrieved`);
      console.log(`🧠 Total Requests: ${metricsResponse.data.totalRequests || 0}`);
      console.log(`✅ Success Rate: ${metricsResponse.data.successRate ? (metricsResponse.data.successRate * 100).toFixed(1) : 0}%`);
      console.log(`📋 Queue Size: ${metricsResponse.data.queueSize || 0}`);
      console.log(`🔄 Active Requests: ${metricsResponse.data.activeRequests || 0}\n`);
    } else {
      console.log(`❌ Metrics Failed: ${metricsResponse.status}\n`);
    }

    // Wait and check again for processing
    console.log('⏳ Waiting 5 seconds for R1 processing...');
    await new Promise(resolve => setTimeout(resolve, 5000));

    const finalMetricsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager',
      method: 'GET'
    });
    
    if (finalMetricsResponse.ok) {
      console.log(`📈 Final Metrics:`);
      console.log(`🧠 Total Requests: ${finalMetricsResponse.data.totalRequests || 0}`);
      console.log(`✅ Success Rate: ${finalMetricsResponse.data.successRate ? (finalMetricsResponse.data.successRate * 100).toFixed(1) : 0}%`);
      
      if (finalMetricsResponse.data.totalRequests > 0) {
        console.log(`\n🎉 SUCCESS: R1 AI system is processing requests!`);
        return true;
      } else {
        console.log(`\n⚠️ R1 system operational but no AI requests processed yet`);
        return false;
      }
    }

    return false;

  } catch (error) {
    console.log(`💥 Test Error: ${error.message}`);
    return false;
  }
}

// Execute test
testDirectOllamaAPI().then(success => {
  console.log(`\n${'='.repeat(60)}`);
  if (success) {
    console.log('🎯 RESULT: R1 AI Fix Verified - System Working!');
  } else {
    console.log('🔧 RESULT: R1 AI Fix Needs Further Investigation');
  }
  console.log(`${'='.repeat(60)}`);
}); 