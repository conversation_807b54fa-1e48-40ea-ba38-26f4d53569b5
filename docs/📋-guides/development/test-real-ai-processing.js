/**
 * 🧠✅ R1 Recommended: Test Actual AI Processing in Application
 * Verify that our IntelligentAIResourceManager can process real AI requests
 */

// Simulate the actual AI request flow
async function testRealAIProcessing() {
  console.log('🧠✅ R1 Test: Actual AI Processing Verification');
  console.log('📋 Testing IntelligentAIResourceManager with real AI requests');
  console.log('🎯 Goal: Confirm 0% success rate crisis is resolved');
  console.log('');

  // Import our actual AI Resource Manager
  try {
    // Use dynamic import since we're in Node.js
    const { IntelligentAIResourceManager } = await import('./src/agent-core/resource-optimization/IntelligentAIResourceManager.js');
    
    console.log('🧠🚀 Initializing IntelligentAIResourceManager...');
    const aiManager = IntelligentAIResourceManager.getInstance();
    
    console.log('📊 Getting initial system status...');
    const initialStatus = await aiManager.getSystemStatus();
    console.log('🧠📋 Initial Status:', {
      thermalState: initialStatus.thermalState,
      activeRequests: initialStatus.activeRequests,
      queueSize: initialStatus.queueSize,
      maxConcurrentRequests: initialStatus.maxConcurrentRequests
    });
    
    console.log('');
    console.log('🧠🎯 Testing AI Request Processing...');
    
    // Test a simple AI request
    const testRequest = {
      agentId: 'TestAgent',
      taskType: 'verification_test',
      prompt: 'Hello, this is a test to verify AI processing is working. Please respond briefly.',
      priority: 'high'
    };
    
    console.log('🧠📤 Sending test AI request...');
    const startTime = Date.now();
    
    const result = await aiManager.requestAI(testRequest);
    
    const responseTime = Date.now() - startTime;
    console.log(`🧠📥 AI request completed in ${responseTime}ms`);
    
    console.log('');
    console.log('🧠📋 Test Results:');
    console.log('✅ Success:', result.success);
    if (result.content) {
      console.log('📝 Content length:', result.content.length, 'characters');
      console.log('💬 Content preview:', result.content.substring(0, 100) + '...');
    }
    if (result.model) console.log('🤖 Model used:', result.model);
    if (result.responseTime) console.log('⚡ Response time:', result.responseTime, 'ms');
    if (result.error) console.log('❌ Error:', result.error);
    
    // Get final metrics
    console.log('');
    console.log('📊 Getting performance metrics...');
    const metrics = aiManager.getPerformanceMetrics();
    console.log('🧠📊 Final Metrics:');
    console.log('📈 Total Requests:', metrics.totalRequests);
    console.log('✅ Success Rate:', (metrics.successRate * 100).toFixed(1) + '%');
    console.log('⚡ Average Response Time:', metrics.averageResponseTime.toFixed(0), 'ms');
    
    // Analyze results
    console.log('');
    console.log('🧠🎯 R1 Analysis Results:');
    if (result.success && metrics.successRate > 0) {
      console.log('✅ SUCCESS: AI processing is working!');
      console.log('✅ 0% success rate crisis appears to be RESOLVED');
      console.log('🎯 The timeout fix successfully restored AI functionality');
    } else {
      console.log('❌ ISSUE: AI processing still has problems');
      console.log('🔧 May need additional debugging beyond timeout fix');
    }
    
    return result.success;
    
  } catch (error) {
    console.error('🧠❌ Test failed with error:', error.message);
    console.error('🔧 Stack trace:', error.stack);
    return false;
  }
}

// Run the test
console.log('Starting R1 Real AI Processing Test...');
testRealAIProcessing()
  .then(success => {
    console.log('');
    console.log('🧠🏁 R1 Test Complete');
    if (success) {
      console.log('🎉 AI processing verification PASSED');
      console.log('🎯 Ready for Step B: Test real agent interactions');
    } else {
      console.log('⚠️ AI processing verification FAILED');
      console.log('🔧 Need to investigate further issues');
    }
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('🧠💥 Test crashed:', error);
    process.exit(1);
  }); 