#!/usr/bin/env node

/**
 * 🧠🔧 Test All Real Agent AI Integration
 * 
 * Comprehensive test of all updated agent APIs to verify they make real AI requests
 */

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ ok: false, status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({ ok: false, status: 0, error: error.message });
    });
    
    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }
    req.end();
  });
}

async function testAllRealAgents() {
  console.log('🧠🔧 Testing All Real Agent AI Integration\n');
  
  const agents = [
    { name: 'TestAgent', endpoint: '/api/agents/test' },
    { name: 'AutonomousDevAgent', endpoint: '/api/agents/autonomous-dev' },
    { name: 'UIAgent', endpoint: '/api/agents/ui' },
    { name: 'OpsAgent', endpoint: '/api/agents/ops' }
  ];
  
  const actions = ['status', 'analyze', 'optimize'];
  
  console.log('🔍 Step 1: Testing real agent AI integration...\n');
  
  for (const agent of agents) {
    console.log(`\n🧠 Testing ${agent.name}:`);
    
    for (const action of actions) {
      try {
        console.log(`  📡 ${action}:`, 'sending...');
        
        const response = await makeRequest({
          hostname: 'localhost',
          port: 3000,
          path: agent.endpoint,
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          timeout: 30000
        }, { action: action });
        
        if (response.ok) {
          const result = response.data;
          if (result.aiProcessed && result.realAgent) {
            console.log(`  ✅ ${action}: REAL AI PROCESSING (${result.intelligentDesign ? 'Design AI' : result.autonomousAI ? 'Autonomous AI' : result.operationsAI ? 'Operations AI' : 'AI'})`);
          } else {
            console.log(`  ⚠️ ${action}: Response received but no AI processing flag`);
          }
        } else {
          console.log(`  ❌ ${action}: Failed (${response.status})`);
        }
        
        // Small delay between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
        
      } catch (error) {
        console.log(`  ❌ ${action}: Error - ${error.message}`);
      }
    }
  }
  
  console.log('\n🔍 Step 2: Checking AI Resource Manager metrics...\n');
  
  // Wait a moment for AI processing to complete
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  try {
    const metricsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET',
      timeout: 10000
    });
    
    if (metricsResponse.ok) {
      const metrics = metricsResponse.data.performanceMetrics;
      console.log('📊 AI Resource Manager Status:');
      console.log(`  🔥 Total Requests: ${metrics.totalRequests || 0}`);
      console.log(`  ⚡ Success Rate: ${metrics.successRate || 0}%`);
      console.log(`  ⏱️ Average Response: ${metrics.averageResponseTime || 0}ms`);
      console.log(`  🌊 Queue Size: ${metricsResponse.data.queueManagement?.queueSize || 0}`);
      console.log(`  🔄 Active Requests: ${metricsResponse.data.queueManagement?.activeRequests || 0}`);
      
      if (metrics.totalRequests > 0) {
        console.log('\n🎉 SUCCESS: Agents are making REAL AI requests!');
        console.log(`🔥 R1 AI System operational with ${metrics.totalRequests} total requests processed`);
      } else {
        console.log('\n⚠️ WARNING: No AI requests detected yet. Check agent logs.');
      }
    } else {
      console.log('❌ Could not retrieve AI metrics');
    }
  } catch (error) {
    console.log(`❌ AI metrics error: ${error.message}`);
  }
  
  console.log('\n🔍 Step 3: Direct Ollama API verification...\n');
  
  try {
    const ollamaResponse = await makeRequest({
      hostname: 'localhost',
      port: 11434,
      path: '/api/generate',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      timeout: 15000
    }, {
      model: 'deepseek-r1:8b',
      prompt: 'Test: What is 3+3? Answer briefly.',
      stream: false,
      options: { temperature: 0.7, num_predict: 50 }
    });
    
    if (ollamaResponse.ok && ollamaResponse.data.response) {
      console.log('✅ Ollama API: Working perfectly');
      console.log(`📝 Response: "${ollamaResponse.data.response.substring(0, 100)}..."`);
    } else {
      console.log('❌ Ollama API: Not responding correctly');
    }
  } catch (error) {
    console.log(`❌ Ollama API: Error - ${error.message}`);
  }
  
  console.log('\n🎯 REAL AGENT AI INTEGRATION TEST COMPLETE');
  console.log('📊 Check the AI Resource Manager metrics above to verify success!');
}

// Run the comprehensive test
testAllRealAgents().catch(console.error); 