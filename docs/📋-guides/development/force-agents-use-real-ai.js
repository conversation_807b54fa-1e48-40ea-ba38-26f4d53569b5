#!/usr/bin/env node

/**
 * 🧠🔧 Force Agents to Use Real AI
 * 
 * This script modifies agent API endpoints to call actual agent instances
 * that make real AI requests instead of returning hardcoded responses
 */

const fs = require('fs');
const path = require('path');

console.log('🧠🔧 Forcing Agents to Use Real AI Instead of Hardcoded Responses\n');

// List of agent API files to fix
const agentAPIFiles = [
  'src/pages/api/agents/test.ts',
  'src/pages/api/agents/autonomous-dev.ts',
  'src/pages/api/agents/ui.ts',
  'src/pages/api/agents/ops.ts'
];

// Create the real agent integration template
const realAgentTemplate = `
import { NextApiRequest, NextApiResponse } from 'next';
import { {AGENT_CLASS} } from '../../../agent-core/agents/{AGENT_FILE}';

let agentInstance: {AGENT_CLASS} | null = null;

function getAgentInstance(): {AGENT_CLASS} {
  if (!agentInstance) {
    agentInstance = new {AGENT_CLASS}();
    console.log('🧠⚡ {AGENT_CLASS}: Initialized with real AI integration');
  }
  return agentInstance;
}

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  console.log('🧠🎯 {AGENT_CLASS} API: Real agent request received');
  
  try {
    const agent = getAgentInstance();
    
    switch (req.method) {
      case 'POST':
        return await handleAgentAction(req, res, agent);
      case 'GET':
        return await handleAgentQuery(req, res, agent);
      default:
        return res.status(405).json({ error: 'Method not allowed' });
    }
  } catch (error) {
    console.error('🧠❌ {AGENT_CLASS} API error:', error);
    return res.status(500).json({ 
      error: 'Real agent error',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

async function handleAgentAction(req: NextApiRequest, res: NextApiResponse, agent: {AGENT_CLASS}) {
  const { action } = req.body;
  
  console.log(\`🧠🚀 {AGENT_CLASS}: Executing real action: \${action}\`);
  
  try {
    // Force agent to make real AI requests for responses
    const result = await agent.{MAIN_METHOD}(action);
    
    console.log(\`🧠✅ {AGENT_CLASS}: Real AI response generated\`);
    
    return res.status(200).json({
      success: true,
      message: \`Real {AGENT_CLASS} response\`,
      action: action,
      result: result,
      aiProcessed: true,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error(\`🧠❌ {AGENT_CLASS}: Real action error:\`, error);
    return res.status(500).json({
      success: false,
      error: 'Real agent action failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

async function handleAgentQuery(req: NextApiRequest, res: NextApiResponse, agent: {AGENT_CLASS}) {
  const { type } = req.query;
  
  console.log(\`🧠📊 {AGENT_CLASS}: Executing real query: \${type}\`);
  
  try {
    // Get real agent status with AI insights
    const status = await agent.getStatus();
    
    return res.status(200).json({
      agentId: '{AGENT_ID}',
      type: '{AGENT_CLASS}',
      version: '3.0.0-ai',
      status: status,
      aiIntegrated: true,
      realAgent: true,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error(\`🧠❌ {AGENT_CLASS}: Real query error:\`, error);
    return res.status(500).json({
      success: false,
      error: 'Real agent query failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
`;

// Agent configuration mapping
const agentConfigs = [
  {
    file: 'src/pages/api/agents/test.ts',
    agentClass: 'TestAgent',
    agentFile: 'TestAgent',
    agentId: 'test-agent-real',
    mainMethod: 'executeTest'
  },
  {
    file: 'src/pages/api/agents/autonomous-dev.ts',
    agentClass: 'AutonomousDevAgent',
    agentFile: 'AutonomousDevAgent',
    agentId: 'autonomous-dev-real',
    mainMethod: 'performCriticalAutonomousAnalysis'
  },
  {
    file: 'src/pages/api/agents/ui.ts',
    agentClass: 'UIAgent',
    agentFile: 'UIAgent', 
    agentId: 'ui-agent-real',
    mainMethod: 'analyzeUIComponentsWithAI'
  },
  {
    file: 'src/pages/api/agents/ops.ts',
    agentClass: 'OpsAgent',
    agentFile: 'OpsAgent',
    agentId: 'ops-agent-real',
    mainMethod: 'performOperationalAnalysis'
  }
];

function updateAgentAPIFile(config) {
  const filePath = config.file;
  
  console.log(`🔧 Updating ${filePath} to use real ${config.agentClass}...`);
  
  // Create the real agent integration code
  let agentCode = realAgentTemplate
    .replace(/{AGENT_CLASS}/g, config.agentClass)
    .replace(/{AGENT_FILE}/g, config.agentFile)
    .replace(/{AGENT_ID}/g, config.agentId)
    .replace(/{MAIN_METHOD}/g, config.mainMethod);
  
  try {
    // Write the updated file
    fs.writeFileSync(filePath, agentCode, 'utf8');
    console.log(`✅ ${filePath} updated to use real AI`);
    
  } catch (error) {
    console.error(`❌ Failed to update ${filePath}:`, error.message);
  }
}

// Create a test script to verify real AI integration
const testScript = `#!/usr/bin/env node

/**
 * 🧠🔧 Test Real AI Agent Integration
 */

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ ok: false, status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }
    req.end();
  });
}

async function testRealAIAgents() {
  console.log('🧠🔧 Testing Real AI Agent Integration...');
  
  const agents = ['test', 'autonomous-dev', 'ui', 'ops'];
  const actions = ['status', 'run_tests', 'health_check', 'analyze'];
  
  for (const agent of agents) {
    console.log(\`\\n📊 Testing \${agent} agent:\`);
    
    for (const action of actions) {
      try {
        const response = await makeRequest({
          hostname: 'localhost',
          port: 3000,
          path: \`/api/agents/\${agent}\`,
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        }, { action });
        
        if (response.ok && response.data.aiProcessed) {
          console.log(\`✅ \${agent}/\${action}: Real AI response (processed)\`);
        } else if (response.ok) {
          console.log(\`⚠️ \${agent}/\${action}: Response but no AI processing\`);
        } else {
          console.log(\`❌ \${agent}/\${action}: Failed (\${response.status})\`);
        }
        
      } catch (error) {
        console.log(\`💥 \${agent}/\${action}: Error - \${error.message}\`);
      }
      
      // Brief delay between requests
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  // Check AI Resource Manager metrics
  console.log('\\n📈 Checking AI Resource Manager metrics...');
  try {
    const metricsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET'
    });
    
    if (metricsResponse.ok) {
      const totalRequests = metricsResponse.data.performanceMetrics?.totalRequests || 0;
      const successRate = metricsResponse.data.performanceMetrics?.successRate || 0;
      
      console.log(\`📊 Total AI Requests: \${totalRequests}\`);
      console.log(\`✅ Success Rate: \${successRate}%\`);
      
      if (totalRequests > 0) {
        console.log('\\n🎉 SUCCESS: Real AI integration working!');
      } else {
        console.log('\\n⚠️ WARNING: No AI requests processed yet');
      }
    }
    
  } catch (error) {
    console.log(\`💥 Metrics check error: \${error.message}\`);
  }
}

testRealAIAgents().then(() => {
  console.log('\\n🔧 Real AI agent integration test complete');
}).catch(error => {
  console.error('Test error:', error);
});
`;

// Execute the fixes
console.log('Starting agent API updates...\n');

// Update each agent API file
agentConfigs.forEach(config => {
  updateAgentAPIFile(config);
});

// Create the test script
console.log('\n📝 Creating test script...');
fs.writeFileSync('test-real-ai-agents.js', testScript, 'utf8');
console.log('✅ Created test-real-ai-agents.js');

// Create a simple trigger script for immediate testing
const triggerScript = `#!/usr/bin/env node

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ ok: false, status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }
    req.end();
  });
}

async function triggerAIRequests() {
  console.log('🧠🚀 Triggering Real AI Requests...');
  
  // Trigger multiple agents to make AI requests
  const requests = [
    { agent: 'test', action: 'run_tests' },
    { agent: 'autonomous-dev', action: 'analyze' },
    { agent: 'ui', action: 'status' },
    { agent: 'ops', action: 'health_check' }
  ];
  
  for (const req of requests) {
    try {
      console.log(\`🎯 Triggering \${req.agent} - \${req.action}\`);
      const response = await makeRequest({
        hostname: 'localhost',
        port: 3000,
        path: \`/api/agents/\${req.agent}\`,
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }, { action: req.action });
      
      if (response.ok) {
        console.log(\`✅ \${req.agent}: Success\`);
      } else {
        console.log(\`❌ \${req.agent}: Failed (\${response.status})\`);
      }
      
    } catch (error) {
      console.log(\`💥 \${req.agent}: Error - \${error.message}\`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
  }
  
  console.log('\\n⏳ Waiting 10 seconds for AI processing...');
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  // Check final metrics
  try {
    const metricsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET'
    });
    
    if (metricsResponse.ok) {
      const totalRequests = metricsResponse.data.performanceMetrics?.totalRequests || 0;
      console.log(\`\\n📊 Final Total AI Requests: \${totalRequests}\`);
      
      if (totalRequests > 0) {
        console.log('🎉 SUCCESS: Real AI requests working!');
      } else {
        console.log('⚠️ No AI requests processed - agents may need more time');
      }
    }
    
  } catch (error) {
    console.log(\`💥 Final metrics error: \${error.message}\`);
  }
}

triggerAIRequests();
`;

fs.writeFileSync('trigger-real-ai.js', triggerScript, 'utf8');
console.log('✅ Created trigger-real-ai.js');

console.log(`\n${'='.repeat(60)}`);
console.log('🎯 REAL AI INTEGRATION COMPLETE!');
console.log(`${'='.repeat(60)}`);
console.log('✅ All agent API endpoints updated to use real agents');
console.log('✅ Real AI integration templates applied');
console.log('✅ Test scripts created');
console.log('');
console.log('🚀 Next Steps:');
console.log('1. Restart your development server');
console.log('2. Run: node trigger-real-ai.js');
console.log('3. Run: node test-real-ai-agents.js (for detailed testing)');
console.log('');
console.log('📊 Expected Outcome:');
console.log('- Agents will make real AI requests through R1 system');
console.log('- AI Resource Manager totalRequests > 0');
console.log('- Success rate > 0%');
console.log(`${'='.repeat(60)}`); 