#!/usr/bin/env node

console.log('🧠📊 Real-Time AI Processing Monitor\n');

const http = require('http');

function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            data: data ? JSON.parse(data) : null
          });
        } catch (e) {
          resolve({
            ok: res.statusCode >= 200 && res.statusCode < 300,
            status: res.statusCode,
            data: data
          });
        }
      });
    });
    
    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (postData) {
      req.write(JSON.stringify(postData));
    }
    
    req.end();
  });
}

async function checkAIStatus() {
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok && response.data) {
      const metrics = response.data.performanceMetrics || {};
      const queue = response.data.queueManagement || {};
      
      console.log(`📊 AI Status (${new Date().toLocaleTimeString()})`);
      console.log(`  🔥 Total Requests: ${metrics.totalRequests || 0}`);
      console.log(`  ✅ Success Rate: ${(metrics.successRate * 100 || 0).toFixed(1)}%`);
      console.log(`  ⏱️ Avg Response: ${metrics.averageResponseTime || 0}ms`);
      console.log(`  📋 Queue Size: ${queue.currentQueueSize || 0}`);
      console.log(`  🔄 Active Requests: ${queue.activeRequests || 0}`);
      console.log(`  🌡️ Thermal State: ${response.data.thermalManagement?.state || 'unknown'}`);
      
      if (metrics.totalRequests > 0) {
        console.log('  🎉 SUCCESS: Real AI requests being processed!\n');
      } else {
        console.log('  ⏳ Waiting for AI requests...\n');
      }
      
      return true;
    } else {
      console.log(`❌ Server not responding (${response.status})\n`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Connection error: ${error.message}\n`);
    return false;
  }
}

async function triggerTestRequest() {
  try {
    console.log('🧠 Triggering test request...');
    
    const response = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/test-agent?action=status',
      method: 'GET',
      timeout: 5000
    });
    
    if (response.ok) {
      console.log('✅ Test request triggered successfully\n');
    } else {
      console.log(`⚠️ Test request returned: ${response.status}\n`);
    }
  } catch (error) {
    console.log(`⚠️ Test request error: ${error.message}\n`);
  }
}

async function monitorProgress() {
  console.log('Starting real-time monitoring...\n');
  
  // Trigger a test request
  await triggerTestRequest();
  
  // Monitor every 5 seconds
  let checks = 0;
  const maxChecks = 24; // 2 minutes total
  
  const interval = setInterval(async () => {
    const status = await checkAIStatus();
    checks++;
    
    if (checks >= maxChecks) {
      console.log('📊 Monitoring complete after 2 minutes');
      clearInterval(interval);
      process.exit(0);
    }
  }, 5000);
}

monitorProgress().catch(error => {
  console.error('Monitor error:', error);
  process.exit(1);
}); 