#!/usr/bin/env node

/**
 * 🧠🔧 Comprehensive AI Integration Test
 * 
 * Complete test of the R1 AI system integration with real agents
 */

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ ok: false, status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', (error) => {
      resolve({ ok: false, status: 0, error: error.message });
    });
    
    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }
    req.end();
  });
}

async function checkServerHealth() {
  console.log('🔍 Step 1: Checking server health...');
  
  try {
    const healthCheck = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/',
      method: 'GET',
      timeout: 5000
    });
    
    console.log(`🔍 Debug: status=${healthCheck.status}, ok=${healthCheck.ok}`);
    
    if (healthCheck.ok || healthCheck.status === 200) {
      console.log('✅ Server is running on localhost:3000');
      return true;
    } else {
      console.log(`❌ Server responded with status: ${healthCheck.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Server not accessible: ${error.message || error}`);
    console.log('🚨 Please start the development server with: npm run dev');
    return false;
  }
}

async function checkOllamaHealth() {
  console.log('\n🤖 Step 2: Checking Ollama health...');
  
  try {
    const ollamaCheck = await makeRequest({
      hostname: 'localhost',
      port: 11434,
      path: '/api/tags',
      method: 'GET',
      timeout: 5000
    });
    
    if (ollamaCheck.ok) {
      console.log('✅ Ollama is running on localhost:11434');
      if (ollamaCheck.data.models && ollamaCheck.data.models.some(m => m.name.includes('deepseek-r1'))) {
        console.log('✅ DeepSeek R1 model available');
        return true;
      } else {
        console.log('⚠️ DeepSeek R1 model not found, but Ollama is running');
        return true;
      }
    } else {
      console.log(`❌ Ollama responded with status: ${ollamaCheck.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Ollama not accessible: ${error.message}`);
    console.log('🚨 Please start Ollama and ensure deepseek-r1:8b model is available');
    return false;
  }
}

async function testDirectOllamaAPI() {
  console.log('\n🧠 Step 3: Testing direct Ollama API...');
  
  try {
    const ollamaTest = await makeRequest({
      hostname: 'localhost',
      port: 11434,
      path: '/api/generate',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    }, {
      model: 'deepseek-r1:8b',
      prompt: 'What is 2+2? Brief answer only.',
      stream: false,
      options: { temperature: 0.7, num_predict: 50 }
    });
    
    if (ollamaTest.ok && ollamaTest.data.response) {
      console.log(`✅ Ollama API working: ${ollamaTest.data.response.length} chars`);
      console.log(`📝 Response: ${ollamaTest.data.response.substring(0, 100)}...`);
      return true;
    } else {
      console.log(`❌ Ollama API failed: ${ollamaTest.status}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Ollama API error: ${error.message}`);
    return false;
  }
}

async function testAIResourceManager() {
  console.log('\n📊 Step 4: Testing AI Resource Manager...');
  
  try {
    const resourceManagerTest = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET',
      timeout: 10000
    });
    
    if (resourceManagerTest.ok) {
      const totalRequests = resourceManagerTest.data.performanceMetrics?.totalRequests || 0;
      const successRate = resourceManagerTest.data.performanceMetrics?.successRate || 0;
      
      console.log(`✅ AI Resource Manager accessible`);
      console.log(`📊 Current Total Requests: ${totalRequests}`);
      console.log(`✅ Current Success Rate: ${successRate}%`);
      return { totalRequests, successRate };
    } else {
      console.log(`❌ AI Resource Manager failed: ${resourceManagerTest.status}`);
      return null;
    }
  } catch (error) {
    console.log(`❌ AI Resource Manager error: ${error.message}`);
    return null;
  }
}

async function testRealTestAgent() {
  console.log('\n🧠 Step 5: Testing Real TestAgent Integration...');
  
  try {
    const testAgentResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/agents/test',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      timeout: 30000
    }, { action: 'run_tests' });
    
    if (testAgentResponse.ok) {
      console.log(`✅ TestAgent API accessible`);
      
      if (testAgentResponse.data.aiProcessed) {
        console.log(`🧠 AI Processing: YES`);
        console.log(`🤖 Real Agent: ${testAgentResponse.data.realAgent ? 'YES' : 'NO'}`);
        console.log(`📊 Result type: ${typeof testAgentResponse.data.result}`);
        return true;
      } else {
        console.log(`⚠️ TestAgent responded but no AI processing flag`);
        console.log(`📄 Response keys: ${Object.keys(testAgentResponse.data).join(', ')}`);
        return false;
      }
    } else {
      console.log(`❌ TestAgent failed: ${testAgentResponse.status}`);
      console.log(`📄 Error: ${testAgentResponse.data}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ TestAgent error: ${error.message}`);
    return false;
  }
}

async function runComprehensiveTest() {
  console.log('🧠🔧 Comprehensive AI Integration Test Starting...\n');
  
  // Step 1: Server Health
  const serverHealthy = await checkServerHealth();
  if (!serverHealthy) {
    console.log('\n🚨 CRITICAL: Development server not running');
    console.log('Please run: npm run dev');
    process.exit(1);
  }
  
  // Step 2: Ollama Health
  const ollamaHealthy = await checkOllamaHealth();
  if (!ollamaHealthy) {
    console.log('\n🚨 CRITICAL: Ollama not available');
    console.log('Please ensure Ollama is running with deepseek-r1:8b model');
    process.exit(1);
  }
  
  // Step 3: Direct Ollama API
  const ollamaAPI = await testDirectOllamaAPI();
  if (!ollamaAPI) {
    console.log('\n🚨 CRITICAL: Ollama API not working');
    process.exit(1);
  }
  
  // Step 4: AI Resource Manager (initial state)
  const initialRM = await testAIResourceManager();
  if (!initialRM) {
    console.log('\n🚨 CRITICAL: AI Resource Manager not accessible');
    process.exit(1);
  }
  
  // Step 5: Test Real Agent
  const testAgentWorking = await testRealTestAgent();
  
  // Step 6: Wait for processing
  console.log('\n⏳ Step 6: Waiting 15 seconds for AI processing...');
  await new Promise(resolve => setTimeout(resolve, 15000));
  
  // Step 7: Check final metrics
  console.log('\n📈 Step 7: Checking final AI metrics...');
  const finalRM = await testAIResourceManager();
  
  // Results
  console.log(`\n${'='.repeat(60)}`);
  console.log('🎯 COMPREHENSIVE TEST RESULTS');
  console.log(`${'='.repeat(60)}`);
  
  console.log(`✅ Server Health: ${serverHealthy ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Ollama Health: ${ollamaHealthy ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Ollama API: ${ollamaAPI ? 'PASS' : 'FAIL'}`);
  console.log(`✅ AI Resource Manager: ${initialRM ? 'PASS' : 'FAIL'}`);
  console.log(`✅ TestAgent Integration: ${testAgentWorking ? 'PASS' : 'FAIL'}`);
  
  if (initialRM && finalRM) {
    const requestIncrease = finalRM.totalRequests - initialRM.totalRequests;
    console.log(`📊 AI Requests Increase: ${requestIncrease}`);
    
    if (requestIncrease > 0) {
      console.log('\n🎉 SUCCESS: Complete AI integration working!');
      console.log('✅ Agents making real AI requests');
      console.log('✅ R1 system processing successfully');
      console.log('✅ End-to-end pipeline operational');
    } else {
      console.log('\n⚠️ PARTIAL SUCCESS: Infrastructure working but no AI requests processed');
      console.log('🔍 Agents may need method adjustments to trigger AI calls');
    }
  }
  
  console.log(`${'='.repeat(60)}`);
}

// Execute comprehensive test
runComprehensiveTest().catch(error => {
  console.error('\n💥 Test execution error:', error);
  process.exit(1);
}); 