#!/usr/bin/env node

/**
 * 🧠🔧 Direct R1 AI System Test
 * 
 * Directly tests the R1 AI system by making a request through the
 * IntelligentAIResourceManager to verify our Ollama API fix works
 */

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ ok: false, status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }
    req.end();
  });
}

async function testDirectR1AI() {
  console.log('🧠🔧 Testing Direct R1 AI System...\n');
  
  try {
    // Test 1: Check initial metrics
    console.log('📊 Test 1: Initial AI Resource Manager Metrics');
    const initialMetrics = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET'
    });

    if (initialMetrics.ok) {
      console.log(`📈 Initial Total Requests: ${initialMetrics.data.performanceMetrics.totalRequests}`);
      console.log(`✅ Initial Success Rate: ${initialMetrics.data.performanceMetrics.successRate}%`);
      console.log(`📋 Initial Queue Size: ${initialMetrics.data.queueManagement.currentQueueSize}`);
      console.log(`🔄 Initial Active Requests: ${initialMetrics.data.queueManagement.activeRequests}\n`);
    }

    // Test 2: Create a test endpoint that forces AI request
    console.log('🧠 Test 2: Creating Test AI Request Endpoint');
    
    // We'll create a simple test by calling the test agent with a specific action
    // that should trigger AI processing
    const testResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/agents/test',
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    }, { action: 'health_check' });

    if (testResponse.ok) {
      console.log(`✅ Test Agent Response: ${testResponse.data.message || 'Success'}\n`);
    }

    // Test 3: Wait and check metrics again
    console.log('⏳ Test 3: Waiting 15 seconds for R1 processing...');
    await new Promise(resolve => setTimeout(resolve, 15000));

    const finalMetrics = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET'
    });
    
    if (finalMetrics.ok) {
      console.log(`📈 Final Metrics:`);
      console.log(`🧠 Total Requests: ${finalMetrics.data.performanceMetrics.totalRequests}`);
      console.log(`✅ Success Rate: ${finalMetrics.data.performanceMetrics.successRate}%`);
      console.log(`📋 Queue Size: ${finalMetrics.data.queueManagement.currentQueueSize}`);
      console.log(`🔄 Active Requests: ${finalMetrics.data.queueManagement.activeRequests}`);
      console.log(`🌡️ Thermal State: ${finalMetrics.data.thermalManagement.state}`);
      console.log(`💾 Memory Usage: ${finalMetrics.data.memoryPerformance.currentUsage}GB`);
      
      const requestIncrease = finalMetrics.data.performanceMetrics.totalRequests - initialMetrics.data.performanceMetrics.totalRequests;
      
      if (requestIncrease > 0) {
        console.log(`\n🎉 SUCCESS: R1 AI system processed ${requestIncrease} requests!`);
        console.log(`🔧 Ollama API fix is working correctly!`);
        return true;
      } else {
        console.log(`\n⚠️ No AI requests processed - agents may be using hardcoded responses`);
        
        // Test 4: Direct Ollama API test to confirm it's working
        console.log(`\n🔧 Test 4: Direct Ollama API Verification`);
        const ollamaTest = await makeRequest({
          hostname: 'localhost',
          port: 11434,
          path: '/api/generate',
          method: 'POST',
          headers: { 'Content-Type': 'application/json' }
        }, {
          model: 'deepseek-r1:8b',
          prompt: 'Test: What is 1+1?',
          stream: false,
          options: { temperature: 0.7, num_predict: 50 }
        });

        if (ollamaTest.ok && ollamaTest.data.response) {
          console.log(`✅ Ollama API Working: ${ollamaTest.data.response.length} chars`);
          console.log(`📝 Response: ${ollamaTest.data.response.substring(0, 100)}...`);
          console.log(`\n🔍 DIAGNOSIS: Ollama API fixed, but agents not making AI requests`);
          return false;
        } else {
          console.log(`❌ Ollama API Failed: ${ollamaTest.status}`);
          return false;
        }
      }
    }

    return false;

  } catch (error) {
    console.log(`💥 Test Error: ${error.message}`);
    return false;
  }
}

// Execute test
testDirectR1AI().then(success => {
  console.log(`\n${'='.repeat(60)}`);
  if (success) {
    console.log('🎯 RESULT: R1 AI System Fully Operational!');
    console.log('✅ Ollama API fix successful');
    console.log('✅ Agents making real AI requests');
    console.log('✅ R1 system processing requests');
  } else {
    console.log('🔧 RESULT: R1 AI Fix Partially Complete');
    console.log('✅ Ollama API integration working');
    console.log('❌ Agents not triggering AI requests');
    console.log('🎯 Next: Modify agents to use AI instead of hardcoded responses');
  }
  console.log(`${'='.repeat(60)}`);
}); 