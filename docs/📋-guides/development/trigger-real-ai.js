#!/usr/bin/env node

const http = require('http');

function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = '';
      res.on('data', chunk => body += chunk);
      res.on('end', () => {
        try {
          const parsed = body ? JSON.parse(body) : {};
          resolve({ ok: res.statusCode >= 200 && res.statusCode < 300, status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ ok: false, status: res.statusCode, data: body });
        }
      });
    });
    
    req.on('error', reject);
    
    if (data) {
      req.write(typeof data === 'string' ? data : JSON.stringify(data));
    }
    req.end();
  });
}

async function triggerAIRequests() {
  console.log('🧠🚀 Triggering Real AI Requests...');
  
  // Trigger multiple agents to make AI requests
  const requests = [
    { agent: 'test', action: 'run_tests' },
    { agent: 'autonomous-dev', action: 'analyze' },
    { agent: 'ui', action: 'status' },
    { agent: 'ops', action: 'health_check' }
  ];
  
  for (const req of requests) {
    try {
      console.log(`🎯 Triggering ${req.agent} - ${req.action}`);
      const response = await makeRequest({
        hostname: 'localhost',
        port: 3000,
        path: `/api/agents/${req.agent}`,
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      }, { action: req.action });
      
      if (response.ok) {
        console.log(`✅ ${req.agent}: Success`);
      } else {
        console.log(`❌ ${req.agent}: Failed (${response.status})`);
      }
      
    } catch (error) {
      console.log(`💥 ${req.agent}: Error - ${error.message}`);
    }
    
    await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
  }
  
  console.log('\n⏳ Waiting 10 seconds for AI processing...');
  await new Promise(resolve => setTimeout(resolve, 10000));
  
  // Check final metrics
  try {
    const metricsResponse = await makeRequest({
      hostname: 'localhost',
      port: 3000,
      path: '/api/ai-resource-manager/status',
      method: 'GET'
    });
    
    if (metricsResponse.ok) {
      const totalRequests = metricsResponse.data.performanceMetrics?.totalRequests || 0;
      console.log(`\n📊 Final Total AI Requests: ${totalRequests}`);
      
      if (totalRequests > 0) {
        console.log('🎉 SUCCESS: Real AI requests working!');
      } else {
        console.log('⚠️ No AI requests processed - agents may need more time');
      }
    }
    
  } catch (error) {
    console.log(`💥 Final metrics error: ${error.message}`);
  }
}

triggerAIRequests();
