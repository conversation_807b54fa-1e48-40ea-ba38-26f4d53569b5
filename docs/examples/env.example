# CreAItive Environment Variables

# API URLs
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:3001/graphql
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001

# Authentication
JWT_SECRET=your-secret-key-here
JWT_EXPIRY=7d
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# OAuth Providers
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Database
MONGODB_URI=mongodb://localhost:27017/creAItive
REDIS_URL=redis://localhost:6379

# AI Services
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
PINECONE_API_KEY=your-pinecone-api-key
PINECONE_ENVIRONMENT=your-pinecone-environment
PINECONE_INDEX=your-pinecone-index

# File Storage
AWS_S3_BUCKET=your-s3-bucket-name
AWS_S3_REGION=your-s3-region
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# Media Processing
FFMPEG_PATH=/usr/local/bin/ffmpeg
IMAGEMAGICK_PATH=/usr/local/bin/convert

# Analytics and Monitoring
SENTRY_DSN=your-sentry-dsn
GOOGLE_ANALYTICS_ID=your-ga-id

# Payment Processing
STRIPE_PUBLIC_KEY=your-stripe-public-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret

# Feature Flags
ENABLE_CREATIVE_CANVAS=true
ENABLE_AI_ANALYSIS=true
ENABLE_COLLABORATION=false
ENABLE_ECONOMIC_FEATURES=false

# Performance and Scaling
REDIS_CACHE_TTL=3600
API_RATE_LIMIT=100
MEDIA_UPLOAD_SIZE_LIMIT=10485760 # 10MB

# Development
NODE_ENV=development
DEBUG=creAItive:*
NEXT_TELEMETRY_DISABLED=1
