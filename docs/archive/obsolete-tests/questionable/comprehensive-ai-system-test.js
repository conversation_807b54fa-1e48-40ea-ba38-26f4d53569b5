#!/usr/bin/env node

/**
 * 🧪 COMPREHENSIVE AI SYSTEM TEST
 * Full validation of AI-enhanced agent system functionality
 */

const http = require('http');

class ComprehensiveAISystemTest {
  constructor() {
    this.baseUrl = 'http://localhost:3000';
    this.results = {
      total: 0,
      passed: 0,
      warnings: 0,
      failed: 0,
      errors: 0,
      details: []
    };
  }

  async runAllTests() {
    console.log('🧪 COMPREHENSIVE AI SYSTEM TEST');
    console.log('===============================');
    console.log(`Testing server at: ${this.baseUrl}`);
    console.log('');

    // Check if server is running first
    const serverRunning = await this.checkServerHealth();
    if (!serverRunning) {
      console.log('❌ Server not running. Start with: npm run dev');
      return;
    }

    try {
      // Test 1: AI Resource Management
      await this.testAIResourceManagement();
      
      // Test 2: Agent AI Enhancement
      await this.testAgentAIEnhancement();
      
      // Test 3: Thermal Monitoring & Control
      await this.testThermalMonitoring();
      
      // Test 4: Agent Migration Status
      await this.testAgentMigrationStatus();
      
      // Test 5: Security Agent Functionality
      await this.testSecurityAgentFunctionality();
      
      // Test 6: System Health & Performance
      await this.testSystemHealthAndPerformance();

      // Final Summary
      this.printFinalSummary();

    } catch (error) {
      console.log(`💥 CRITICAL ERROR: ${error.message}`);
      this.results.errors++;
    }
  }

  async checkServerHealth() {
    try {
      const response = await this.makeRequest('/api/health', 'GET');
      if (response && response.success) {
        console.log('✅ Server is running');
        return true;
      }
      return false;
    } catch (error) {
      console.log('❌ Server not responding');
      return false;
    }
  }

  async testAIResourceManagement() {
    console.log('🧠 Testing AI Resource Management...');
    this.results.total++;

    try {
      const response = await this.makeRequest('/api/agents/ai-resources', 'GET');
      
      if (response && response.success) {
        const { systemStatus, metrics, management } = response.data;
        
        // Check thermal state
        const thermalState = systemStatus.thermalState;
        console.log(`  🌡️ Thermal State: ${thermalState}`);
        
        if (thermalState === 'critical') {
          console.log('  ⚠️ WARN: Critical thermal state detected');
          this.results.warnings++;
        } else {
          console.log('  ✅ Thermal state is acceptable');
        }
        
        // Check AI metrics
        console.log(`  📊 Total AI Requests: ${metrics.totalRequests}`);
        console.log(`  📈 Success Rate: ${Math.round(metrics.successRate * 100)}%`);
        console.log(`  ⏱️ Avg Response Time: ${Math.round(metrics.averageResponseTime)}ms`);
        console.log(`  🎯 Performance Score: ${management.performanceScore}%`);
        
        this.results.passed++;
        this.results.details.push({
          test: 'AI Resource Management',
          status: '✅ PASS',
          details: `Thermal: ${thermalState}, Requests: ${metrics.totalRequests}, Success: ${Math.round(metrics.successRate * 100)}%`
        });
        
      } else {
        throw new Error('AI Resource Management endpoint failed');
      }
      
    } catch (error) {
      console.log(`  ❌ ERROR: ${error.message}`);
      this.results.failed++;
      this.results.details.push({
        test: 'AI Resource Management',
        status: '❌ FAIL',
        details: error.message
      });
    }
  }

  async testAgentAIEnhancement() {
    console.log('🤖 Testing Agent AI Enhancement...');
    
    const agents = [
      { name: 'UI Agent', endpoint: '/api/agents/ui', payload: { message: 'test ui analysis' } },
      { name: 'Security Agent', endpoint: '/api/agents/security', payload: { action: 'security_scan', scope: 'system' } }
    ];

    for (const agent of agents) {
      this.results.total++;
      
      try {
        console.log(`  Testing ${agent.name}...`);
        const response = await this.makeRequest(agent.endpoint, 'POST', agent.payload);
        
        if (response && (response.success || response.agentId)) {
          console.log(`  ✅ ${agent.name}: AI response received`);
          this.results.passed++;
          this.results.details.push({
            test: `${agent.name} AI Enhancement`,
            status: '✅ PASS',
            details: 'AI response successfully generated'
          });
        } else {
          throw new Error(`${agent.name} failed to respond with AI`);
        }
        
      } catch (error) {
        console.log(`  ❌ ${agent.name}: ${error.message}`);
        this.results.failed++;
        this.results.details.push({
          test: `${agent.name} AI Enhancement`,
          status: '❌ FAIL',
          details: error.message
        });
      }
    }
  }

  async testThermalMonitoring() {
    console.log('🌡️ Testing Thermal Monitoring & Control...');
    this.results.total++;

    try {
      const response = await this.makeRequest('/api/agents/ai-resources', 'GET');
      
      if (response && response.success) {
        const { systemStatus, metrics } = response.data;
        const thermalState = systemStatus.thermalState;
        const throttlingLevel = systemStatus.throttlingLevel;
        const memoryUsage = systemStatus.memoryUsage;
        
        console.log(`  🌡️ Thermal State: ${thermalState}`);
        console.log(`  🚦 Throttling Level: ${throttlingLevel}%`);
        console.log(`  💾 Memory Usage: ${memoryUsage}GB`);
        console.log(`  🔧 Performance Mode: ${systemStatus.performanceMode}`);
        
        // Thermal safety check
        if (thermalState === 'critical') {
          console.log('  ⚠️ WARN: Critical thermal state - system under stress');
          this.results.warnings++;
        } else {
          console.log('  ✅ Thermal monitoring operational');
          this.results.passed++;
        }
        
        this.results.details.push({
          test: 'Thermal Monitoring',
          status: thermalState === 'critical' ? '⚠️ WARN' : '✅ PASS',
          details: `State: ${thermalState}, Throttling: ${throttlingLevel}%, Memory: ${memoryUsage}GB`
        });
        
      } else {
        throw new Error('Thermal monitoring data unavailable');
      }
      
    } catch (error) {
      console.log(`  ❌ ERROR: ${error.message}`);
      this.results.failed++;
      this.results.details.push({
        test: 'Thermal Monitoring',
        status: '❌ FAIL',
        details: error.message
      });
    }
  }

  async testAgentMigrationStatus() {
    console.log('🚀 Testing Agent Migration Status...');
    this.results.total++;

    try {
      const response = await this.makeRequest('/api/agents/migration', 'GET');
      
      if (response && response.success) {
        const { summary } = response.data;
        
        console.log(`  📊 Total Agents: ${summary.totalAgents}`);
        console.log(`  ✅ Migrated: ${summary.migratedAgents}`);
        console.log(`  📈 Completion: ${summary.completionPercentage}%`);
        
        if (summary.completionPercentage === 100) {
          console.log('  ✅ All agents successfully migrated with AI enhancement');
          this.results.passed++;
          this.results.details.push({
            test: 'Agent Migration Status',
            status: '✅ PASS',
            details: `${summary.migratedAgents}/${summary.totalAgents} agents migrated (${summary.completionPercentage}%)`
          });
        } else {
          console.log(`  ⚠️ WARN: Migration incomplete (${summary.completionPercentage}%)`);
          this.results.warnings++;
          this.results.details.push({
            test: 'Agent Migration Status',
            status: '⚠️ WARN',
            details: `Migration incomplete: ${summary.migratedAgents}/${summary.totalAgents} (${summary.completionPercentage}%)`
          });
        }
        
      } else {
        throw new Error('Migration status endpoint failed');
      }
      
    } catch (error) {
      console.log(`  ❌ ERROR: ${error.message}`);
      this.results.failed++;
      this.results.details.push({
        test: 'Agent Migration Status',
        status: '❌ FAIL',
        details: error.message
      });
    }
  }

  async testSecurityAgentFunctionality() {
    console.log('🛡️ Testing Security Agent Functionality...');
    this.results.total++;

    try {
      const response = await this.makeRequest('/api/agents/security', 'POST', {
        action: 'monitor_threats'
      });
      
      if (response && response.success) {
        const { result } = response;
        
        console.log(`  🔍 Threats Detected: ${result.threats.length}`);
        console.log(`  📋 Recommendations: ${result.recommendations.length}`);
        console.log('  ✅ Security monitoring operational');
        
        this.results.passed++;
        this.results.details.push({
          test: 'Security Agent Functionality',
          status: '✅ PASS',
          details: `Threats: ${result.threats.length}, Recommendations: ${result.recommendations.length}`
        });
        
      } else {
        throw new Error('Security agent threat monitoring failed');
      }
      
    } catch (error) {
      console.log(`  ❌ ERROR: ${error.message}`);
      this.results.failed++;
      this.results.details.push({
        test: 'Security Agent Functionality',
        status: '❌ FAIL',
        details: error.message
      });
    }
  }

  async testSystemHealthAndPerformance() {
    console.log('🏥 Testing System Health & Performance...');
    this.results.total++;

    try {
      const response = await this.makeRequest('/api/health', 'GET');
      
      if (response && response.success) {
        const { data } = response;
        
        console.log(`  📊 Status: ${data.status}`);
        console.log(`  ⏱️ Uptime: ${Math.round(data.uptime / 60)} minutes`);
        console.log(`  💾 Memory Used: ${data.memory.used}MB`);
        console.log(`  🔧 Environment: ${data.environment}`);
        
        if (data.status === 'healthy') {
          console.log('  ✅ System health optimal');
          this.results.passed++;
          this.results.details.push({
            test: 'System Health & Performance',
            status: '✅ PASS',
            details: `Status: ${data.status}, Uptime: ${Math.round(data.uptime / 60)}min, Memory: ${data.memory.used}MB`
          });
        } else {
          console.log(`  ⚠️ WARN: System status: ${data.status}`);
          this.results.warnings++;
          this.results.details.push({
            test: 'System Health & Performance',
            status: '⚠️ WARN',
            details: `Status: ${data.status}`
          });
        }
        
      } else {
        throw new Error('System health check failed');
      }
      
    } catch (error) {
      console.log(`  ❌ ERROR: ${error.message}`);
      this.results.failed++;
      this.results.details.push({
        test: 'System Health & Performance',
        status: '❌ FAIL',
        details: error.message
      });
    }
  }

  async makeRequest(endpoint, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
      const url = new URL(endpoint, this.baseUrl);
      const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname,
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000
      };

      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => body += chunk);
        res.on('end', () => {
          try {
            const jsonData = JSON.parse(body);
            resolve(jsonData);
          } catch (error) {
            reject(new Error(`Invalid JSON response: ${body.substring(0, 100)}`));
          }
        });
      });

      req.on('error', (error) => reject(error));
      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      if (data) {
        req.write(JSON.stringify(data));
      }
      
      req.end();
    });
  }

  printFinalSummary() {
    console.log('');
    console.log('📊 COMPREHENSIVE TEST SUMMARY');
    console.log('============================');
    console.log(`Total Tests: ${this.results.total}`);
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`⚠️ Warnings: ${this.results.warnings}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`💥 Errors: ${this.results.errors}`);
    console.log('');

    // Calculate safety score
    const successRate = this.results.total > 0 ? Math.round((this.results.passed / this.results.total) * 100) : 0;
    console.log(`🏥 AI System Health Score: ${successRate}%`);
    
    if (successRate >= 90) {
      console.log('🎉 EXCELLENT: AI system is fully operational');
    } else if (successRate >= 70) {
      console.log('✅ GOOD: AI system is mostly operational with minor issues');
    } else if (successRate >= 50) {
      console.log('⚠️ CAUTION: AI system has significant issues that need attention');
    } else {
      console.log('🚨 CRITICAL: AI system has major problems that require immediate attention');
    }

    console.log('');
    console.log('📋 DETAILED RESULTS:');
    this.results.details.forEach((detail, index) => {
      console.log(`${index + 1}. ${detail.test}: ${detail.status}`);
      console.log(`   ${detail.details}`);
    });

    console.log('');
    console.log('💡 TIP: Run this test regularly to monitor AI system health');
  }
}

// Run the test
if (require.main === module) {
  const test = new ComprehensiveAISystemTest();
  test.runAllTests().catch(console.error);
}

module.exports = ComprehensiveAISystemTest; 