/**
 * Track 7 Day 1: Enterprise Performance Optimization Tests
 * Tests enhanced PrecisionPerformanceEngine with enterprise-grade capabilities
 * Following Infrastructure Integration First Principle
 */

import { PrecisionPerformanceEngineIntelligenceEnhanced } from '../src/agent-core/agents/PrecisionPerformanceEngineIntelligenceEnhanced';

describe('Track 7 Day 1: Enterprise Performance Optimization', () => {
  let performanceEngine: PrecisionPerformanceEngineIntelligenceEnhanced;

  beforeEach(async () => {
    performanceEngine = new PrecisionPerformanceEngineIntelligenceEnhanced();
    await performanceEngine.initializeEnterprisePerformance();
  });

  describe('Enterprise Performance Infrastructure', () => {
    test('should initialize enterprise performance capabilities', async () => {
      const analytics = performanceEngine.getEnterprisePerformanceAnalytics();
      
      expect(analytics).toBeDefined();
      expect(analytics.totalProfiles).toBe(0);
      expect(analytics.resourceUtilization).toBeDefined();
      expect(analytics.optimizationOpportunities).toEqual(
        expect.arrayContaining([expect.stringContaining('optimization')])
      );
    });

    test('should maintain Expert intelligence level with enterprise capabilities', () => {
      const profile = performanceEngine.getIntelligenceProfile();
      
      expect(profile?.intelligenceLevel).toBe('expert');
      expect(profile?.businessValue).toBe(4000000);
      expect(profile?.autonomyLevel).toBe(85);
      expect(profile?.performanceRequirements?.maxResponseTimeMs).toBe(2000);
      expect(profile?.performanceRequirements?.targetThroughputPerSecond).toBe(200);
    });
  });

  describe('Agent Performance Profiling', () => {
    test('should profile agent performance with enterprise-grade analysis', async () => {
      // Mock operation to profile
      const mockOperation = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 100)); // Simulate work
        return { success: true, data: 'test result' };
      });

      const profile = await performanceEngine.profileAgentPerformance('TestAgent', mockOperation);

      expect(profile).toBeDefined();
      expect(profile.profileId).toMatch(/prof_TestAgent_\d+/);
      expect(profile.executionTime).toBeGreaterThan(0);
      expect(profile.memoryUsage).toBeDefined();
      expect(profile.cpuUtilization).toBeDefined();
      expect(profile.bottlenecks).toEqual(expect.any(Array));
      expect(profile.optimizationRecommendations).toEqual(expect.any(Array));
      expect(mockOperation).toHaveBeenCalled();
    });

    test('should identify performance bottlenecks in high-execution operations', async () => {
      // Simulate slow operation
      const slowOperation = jest.fn(async () => {
        await new Promise(resolve => setTimeout(resolve, 1200)); // > 1000ms triggers bottleneck
        return { data: 'slow result' };
      });

      const profile = await performanceEngine.profileAgentPerformance('SlowAgent', slowOperation);

      expect(profile.executionTime).toBeGreaterThan(1000);
      expect(profile.bottlenecks).toContain('High execution time');
      expect(profile.optimizationRecommendations).toEqual(
        expect.arrayContaining([
          expect.stringContaining('caching'),
          expect.stringContaining('parallel processing')
        ])
      );
    });

    test('should handle errors during profiling gracefully', async () => {
      const errorOperation = jest.fn(async () => {
        throw new Error('Simulated operation error');
      });

      await expect(performanceEngine.profileAgentPerformance('ErrorAgent', errorOperation))
        .rejects.toThrow('Simulated operation error');
      
      expect(errorOperation).toHaveBeenCalled();
    });
  });

  describe('Resource Optimization', () => {
    test('should optimize resource allocation across agent ecosystem', async () => {
      const systemLoad = {
        cpu: 75,
        memory: 60,
        activeAgents: 28,
        pendingTasks: 150
      };

      const optimization = await performanceEngine.optimizeResourceAllocation(systemLoad);

      expect(optimization).toBeDefined();
      expect(optimization.optimizationPlan).toBeDefined();
      expect(optimization.optimizationPlan.strategy).toBe('intelligent_load_balancing');
      expect(optimization.resourceReallocation).toEqual(expect.any(Array));
      expect(optimization.expectedImprovements).toBeDefined();
      expect(optimization.expectedImprovements.performanceGain).toBeGreaterThan(0.2);
      expect(optimization.expectedImprovements.performanceGain).toBeLessThan(0.5);
      expect(optimization.confidence).toBeGreaterThan(0.8);
    });

    test('should create comprehensive resource reallocation plans', async () => {
      const systemLoad = { activeAgents: 28, highLoadAgents: ['TestAgent', 'DevAgent'] };
      
      const optimization = await performanceEngine.optimizeResourceAllocation(systemLoad);
      
      expect(optimization.resourceReallocation).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            action: 'redistribute_load',
            targetAgents: expect.arrayContaining(['TestAgent', 'DevAgent', 'SecurityAgent'])
          }),
          expect.objectContaining({
            action: 'enable_caching',
            targetOperations: expect.arrayContaining(['ml_inference', 'coordination_decisions'])
          })
        ])
      );
    });
  });

  describe('Enterprise Caching System', () => {
    test('should implement intelligent caching for operations', async () => {
      let executionCount = 0;
      const expensiveOperation = jest.fn(async () => {
        executionCount++;
        await new Promise(resolve => setTimeout(resolve, 100));
        return { result: `computation_${executionCount}`, timestamp: Date.now() };
      });

      // First call should execute operation
      const result1 = await performanceEngine.implementEnterpriseCache(
        'test_cache_key', 
        expensiveOperation,
        5000 // 5 second TTL
      );
      
      // Second call should use cache
      const result2 = await performanceEngine.implementEnterpriseCache(
        'test_cache_key', 
        expensiveOperation,
        5000
      );

      expect(expensiveOperation).toHaveBeenCalledTimes(1); // Only called once
      expect(result1).toEqual(result2); // Same result from cache
      expect(result1.result).toBe('computation_1');
    });

    test('should handle cache misses and TTL expiration', async () => {
      const operation = jest.fn(async () => ({ data: 'fresh_data', id: Math.random() }));

      // First call
      const result1 = await performanceEngine.implementEnterpriseCache(
        'ttl_test_key', 
        operation,
        100 // Very short TTL (100ms)
      );
      
      // Wait for TTL to expire
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Second call should re-execute due to TTL expiration
      const result2 = await performanceEngine.implementEnterpriseCache(
        'ttl_test_key', 
        operation,
        100
      );

      expect(operation).toHaveBeenCalledTimes(2);
      expect(result1.id).not.toBe(result2.id); // Different results
    });

    test('should handle caching errors gracefully', async () => {
      const errorOperation = jest.fn(async () => {
        throw new Error('Cache operation failed');
      });

      await expect(performanceEngine.implementEnterpriseCache('error_key', errorOperation))
        .rejects.toThrow('Cache operation failed');
    });
  });

  describe('Parallel Processing Enhancement', () => {
    test('should execute tasks in parallel with efficiency analysis', async () => {
      const tasks = [
        {
          id: 'task_1',
          operation: jest.fn(async () => {
            await new Promise(resolve => setTimeout(resolve, 100));
            return { result: 'task_1_complete' };
          })
        },
        {
          id: 'task_2', 
          operation: jest.fn(async () => {
            await new Promise(resolve => setTimeout(resolve, 150));
            return { result: 'task_2_complete' };
          })
        },
        {
          id: 'task_3',
          operation: jest.fn(async () => {
            await new Promise(resolve => setTimeout(resolve, 80));
            return { result: 'task_3_complete' };
          })
        }
      ];

      const result = await performanceEngine.executeParallelProcessing(tasks);

      expect(result).toBeDefined();
      expect(result.results).toHaveLength(3);
      expect(result.results.every((r: any) => r.success)).toBe(true);
      expect(result.executionTime).toBeLessThan(350); // Should be faster than sequential (330ms)
      expect(result.parallelEfficiency).toBeGreaterThan(1); // Efficiency gain
      expect(result.coordinationMetrics.totalTasks).toBe(3);
      expect(result.coordinationMetrics.successfulTasks).toBe(3);
      expect(result.coordinationMetrics.failedTasks).toBe(0);
    });

    test('should handle mixed success/failure in parallel execution', async () => {
      const tasks = [
        {
          id: 'success_task',
          operation: jest.fn(async () => ({ result: 'success' }))
        },
        {
          id: 'failure_task',
          operation: jest.fn(async () => {
            throw new Error('Task failed');
          })
        },
        {
          id: 'another_success',
          operation: jest.fn(async () => ({ result: 'another_success' }))
        }
      ];

      const result = await performanceEngine.executeParallelProcessing(tasks);

      expect(result.results).toHaveLength(3);
      expect(result.coordinationMetrics.successfulTasks).toBe(2);
      expect(result.coordinationMetrics.failedTasks).toBe(1);
      
      const successfulResults = result.results.filter((r: any) => r.success);
      const failedResults = result.results.filter((r: any) => !r.success);
      
      expect(successfulResults).toHaveLength(2);
      expect(failedResults).toHaveLength(1);
      expect(failedResults[0].error).toBe('Task failed');
    });
  });

  describe('Enterprise Performance Analytics', () => {
    test('should provide comprehensive performance analytics', async () => {
      // Generate some profiling data
      const mockOp = jest.fn(async () => ({ test: 'data' }));
      await performanceEngine.profileAgentPerformance('AnalyticsTestAgent', mockOp);
      
      // Test caching
      await performanceEngine.implementEnterpriseCache('analytics_cache', mockOp);
      
      const analytics = performanceEngine.getEnterprisePerformanceAnalytics();
      
      expect(analytics.totalProfiles).toBeGreaterThan(0);
      expect(analytics.averageExecutionTime).toBeGreaterThan(0);
      expect(analytics.resourceUtilization).toBeDefined();
      expect(analytics.resourceUtilization.memoryPools).toBeDefined();
      expect(analytics.resourceUtilization.activeCache).toBeGreaterThan(0);
      expect(analytics.resourceUtilization.profilingData).toBeGreaterThan(0);
      expect(analytics.cacheHitRate).toBeGreaterThanOrEqual(0);
      expect(analytics.optimizationOpportunities).toEqual(expect.any(Array));
    });

    test('should identify system optimization opportunities', async () => {
      const analytics = performanceEngine.getEnterprisePerformanceAnalytics();
      
      expect(analytics.optimizationOpportunities).toEqual(
        expect.arrayContaining([
          expect.stringContaining('caching'),
          expect.stringContaining('profiling'),
          expect.stringContaining('resource scaling'),
          expect.stringContaining('load balancing')
        ])
      );
    });
  });

  describe('Enterprise Performance Integration', () => {
    test('should maintain compatibility with existing MLCoordinationLayer', async () => {
      const request = {
        type: 'performance_optimization',
        data: { agentId: 'TestAgent', operation: 'ml_inference' }
      };

      const result = await performanceEngine.processRequest(request);

      expect(result.success).toBe(true);
      expect(result.metadata.agentId).toBe('PrecisionPerformanceEngine');
      expect(result.metadata.intelligenceLevel).toBe('expert');
      expect(result.quality.score).toBeGreaterThan(0.8);
    });

    test('should coordinate with other agents for performance optimization', async () => {
      // This tests the coordination capability without breaking existing infrastructure
      await expect(performanceEngine.coordinateWithPeers('optimization', { type: 'resource_analysis' }))
        .resolves.not.toThrow();
    });
  });
}); 