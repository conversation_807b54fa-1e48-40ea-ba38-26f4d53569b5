/**
 * Track 6 Day 2: Advanced Agent Intelligence & Autonomy Tests
 * Tests enhanced autonomous decision-making capabilities in existing TestAgent
 * Following Infrastructure Integration First Principle
 */

import { TestAgentIntelligenceEnhanced } from '../src/agent-core/agents/TestAgentIntelligenceEnhanced';

describe('Track 6 Day 2: Advanced Agent Intelligence & Autonomy', () => {
  let testAgent: TestAgentIntelligenceEnhanced;

  beforeEach(async () => {
    testAgent = new TestAgentIntelligenceEnhanced();
    await testAgent.initializeTestingIntelligence();
  });

  describe('Advanced Autonomous Decision-Making Infrastructure', () => {
    test('should initialize advanced autonomous decision engine', async () => {
      expect(testAgent).toBeDefined();
      expect((testAgent as any).advancedAutonomousEngine).toBeDefined();
    });

    test('should integrate with existing MLCoordinationLayer', async () => {
      const mlCoordination = (testAgent as any).mlCoordination;
      expect(mlCoordination).toBeDefined();
      
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      expect(autonomousEngine).toBeDefined();
    });

    test('should maintain existing 4 ML systems integration', async () => {
      const intelligenceProfile = testAgent.getIntelligenceProfile();
      expect(intelligenceProfile.mlSystems).toHaveLength(4);
      expect(intelligenceProfile.mlSystems).toContain('pattern_recognizer');
      expect(intelligenceProfile.mlSystems).toContain('adaptive_optimizer');
      expect(intelligenceProfile.mlSystems).toContain('predictive_analyzer');
      expect(intelligenceProfile.mlSystems).toContain('self_learning_system');
    });
  });

  describe('Autonomous Test Strategy Selection', () => {
    test('should select optimal test strategy autonomously', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const codeContext = {
        complexity: 0.85,
        criticalAreas: ['authentication', 'payment', 'data-processing', 'api-endpoints', 'security'],
        timeConstraints: 400
      };

      const result = await autonomousEngine.selectOptimalTestStrategy(codeContext);

      expect(result).toMatchObject({
        strategy: expect.any(String),
        confidence: expect.any(Number),
        reasoning: expect.any(Array),
        autonomousDecision: true
      });

      expect(result.confidence).toBeGreaterThan(0);
      expect(result.confidence).toBeLessThanOrEqual(0.95);
      expect(result.reasoning.length).toBeGreaterThan(0);
      expect(['comprehensive', 'targeted', 'rapid', 'regression-focused']).toContain(result.strategy);
    });

    test('should provide reasoning for autonomous strategy decisions', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const highComplexityContext = {
        complexity: 0.9,
        criticalAreas: ['auth', 'payment'],
        timeConstraints: 200
      };

      const result = await autonomousEngine.selectOptimalTestStrategy(highComplexityContext);

      expect(result.reasoning).toEqual(
        expect.arrayContaining([
          expect.stringMatching(/complexity|historical|comprehensive|rapid|targeted/i)
        ])
      );
    });

    test('should learn from historical strategy success rates', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      // Simulate multiple strategy selections to build learning data
      const contexts = [
        { complexity: 0.8, criticalAreas: ['auth'], timeConstraints: 300 },
        { complexity: 0.6, criticalAreas: ['payment', 'data'], timeConstraints: 150 },
        { complexity: 0.9, criticalAreas: ['security'], timeConstraints: 500 }
      ];

      const results = await Promise.all(
        contexts.map(context => autonomousEngine.selectOptimalTestStrategy(context))
      );

      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result.reasoning.some((reason: string) => 
          reason.includes('Historical success rate')
        )).toBe(true);
      });
    });
  });

  describe('Intelligent Test Prioritization', () => {
    test('should prioritize tests autonomously with reasoning', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const testSuite = [
        { name: 'auth-test', type: 'security' },
        { name: 'payment-test', type: 'critical' },
        { name: 'ui-test', type: 'functional' },
        { name: 'performance-test', type: 'load' },
        { name: 'integration-test', type: 'system' }
      ];

      const result = await autonomousEngine.prioritizeTestsAutonomously(testSuite);

      expect(result).toMatchObject({
        prioritizedTests: expect.any(Array),
        priorityReasons: expect.any(Map),
        autonomousDecisions: expect.any(Number)
      });

      expect(result.prioritizedTests).toHaveLength(5);
      expect(result.priorityReasons.size).toBe(5);
      expect(result.autonomousDecisions).toBeGreaterThan(0);

      // Check that tests are properly prioritized (higher priority first)
      for (let i = 0; i < result.prioritizedTests.length - 1; i++) {
        expect(result.prioritizedTests[i].autonomousPriority)
          .toBeGreaterThanOrEqual(result.prioritizedTests[i + 1].autonomousPriority);
      }
    });

    test('should provide detailed reasoning for test priorities', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const testSuite = [
        { name: 'critical-auth-test', type: 'security' },
        { name: 'simple-ui-test', type: 'functional' }
      ];

      const result = await autonomousEngine.prioritizeTestsAutonomously(testSuite);

      expect(result.priorityReasons.get('critical-auth-test')).toBeDefined();
      expect(result.priorityReasons.get('simple-ui-test')).toBeDefined();

      // Check reasoning quality
      for (const [testName, reasoning] of result.priorityReasons) {
        expect(reasoning).toMatch(/Critical|risk|failure|performance|dependency/i);
      }
    });

    test('should count autonomous decisions made during prioritization', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const testSuite = Array.from({ length: 10 }, (_, i) => ({
        name: `test-${i}`,
        type: 'functional'
      }));

      const result = await autonomousEngine.prioritizeTestsAutonomously(testSuite);

      // Should make multiple autonomous decisions (one per priority factor per test)
      expect(result.autonomousDecisions).toBeGreaterThan(0);
      expect(result.autonomousDecisions).toBeLessThanOrEqual(testSuite.length * 5); // Max 5 factors per test
    });
  });

  describe('Autonomous Quality Gate Management', () => {
    test('should evaluate quality gates autonomously with confidence scoring', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const testResults = {
        coverage: 0.85,
        performanceRegression: 0.05,
        securityIssues: 0,
        flakyTests: 1
      };

      const result = await autonomousEngine.evaluateQualityGatesAutonomously(testResults);

      expect(result).toMatchObject({
        gateDecision: expect.stringMatching(/^(PASS|FAIL|CONDITIONAL_PASS)$/),
        confidence: expect.any(Number),
        autonomousReasoning: expect.any(Array),
        recommendedActions: expect.any(Array)
      });

      expect(result.confidence).toBeGreaterThan(0);
      expect(result.confidence).toBeLessThanOrEqual(0.95);
    });

    test('should make PASS decision for high-quality results', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const highQualityResults = {
        coverage: 0.95,
        performanceRegression: 0.02,
        securityIssues: 0,
        flakyTests: 0
      };

      const result = await autonomousEngine.evaluateQualityGatesAutonomously(highQualityResults);

      expect(result.gateDecision).toBe('PASS');
      expect(result.confidence).toBeGreaterThan(0.8);
      expect(result.autonomousReasoning).toEqual(
        expect.arrayContaining([
          expect.stringMatching(/ML analysis confirms quality standards/i)
        ])
      );
    });

    test('should make FAIL decision for poor quality results', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const poorQualityResults = {
        coverage: 0.4,
        performanceRegression: 0.25,
        securityIssues: 3,
        flakyTests: 5
      };

      const result = await autonomousEngine.evaluateQualityGatesAutonomously(poorQualityResults);

      expect(result.gateDecision).toBe('FAIL');
      expect(result.recommendedActions).toEqual(
        expect.arrayContaining([
          expect.stringMatching(/Do not proceed to production/i)
        ])
      );
    });

    test('should make CONDITIONAL_PASS decision for moderate quality results', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const moderateQualityResults = {
        coverage: 0.75,
        performanceRegression: 0.08,
        securityIssues: 0,
        flakyTests: 2
      };

      const result = await autonomousEngine.evaluateQualityGatesAutonomously(moderateQualityResults);

      expect(result.gateDecision).toBe('CONDITIONAL_PASS');
      expect(result.recommendedActions).toEqual(
        expect.arrayContaining([
          expect.stringMatching(/Monitor closely in production/i)
        ])
      );
    });

    test('should provide detailed autonomous reasoning for quality decisions', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      const testResults = {
        coverage: 0.6,
        performanceRegression: 0.15,
        securityIssues: 1,
        flakyTests: 3
      };

      const result = await autonomousEngine.evaluateQualityGatesAutonomously(testResults);

      expect(result.autonomousReasoning.length).toBeGreaterThan(0);
      expect(result.autonomousReasoning.some((reason: string) => 
        reason.includes('Coverage below threshold')
      )).toBe(true);
      expect(result.autonomousReasoning.some((reason: string) => 
        reason.includes('Performance regression detected')
      )).toBe(true);
      expect(result.autonomousReasoning.some((reason: string) => 
        reason.includes('Security issues found')
      )).toBe(true);
    });
  });

  describe('Autonomous Decision Learning and Metrics', () => {
    test('should track autonomous decision history', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      // Make multiple autonomous decisions
      await autonomousEngine.selectOptimalTestStrategy({ complexity: 0.8 });
      await autonomousEngine.evaluateQualityGatesAutonomously({ 
        coverage: 0.9, performanceRegression: 0.05, securityIssues: 0, flakyTests: 1 
      });

      const metrics = autonomousEngine.getAutonomousDecisionMetrics();

      expect(metrics).toMatchObject({
        totalDecisions: expect.any(Number),
        averageConfidence: expect.any(Number),
        decisionTypes: expect.any(Array),
        learningProgress: expect.any(Map)
      });

      expect(metrics.totalDecisions).toBeGreaterThan(0);
      expect(metrics.averageConfidence).toBeGreaterThan(0);
      expect(metrics.decisionTypes.length).toBeGreaterThan(0);
    });

    test('should improve learning feedback over time', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      // Initial decision
      const result1 = await autonomousEngine.selectOptimalTestStrategy({ complexity: 0.7 });
      
      // Additional decisions to build learning data
      await autonomousEngine.selectOptimalTestStrategy({ complexity: 0.8 });
      await autonomousEngine.selectOptimalTestStrategy({ complexity: 0.6 });

      const metrics = autonomousEngine.getAutonomousDecisionMetrics();
      
      expect(metrics.learningProgress.size).toBeGreaterThan(0);
      expect(metrics.averageConfidence).toBeGreaterThan(0);
    });

    test('should provide comprehensive decision analytics', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      // Perform various types of autonomous decisions
      await autonomousEngine.selectOptimalTestStrategy({ complexity: 0.8 });
      await autonomousEngine.prioritizeTestsAutonomously([
        { name: 'test1', type: 'unit' },
        { name: 'test2', type: 'integration' }
      ]);
      await autonomousEngine.evaluateQualityGatesAutonomously({
        coverage: 0.85, performanceRegression: 0.03, securityIssues: 0, flakyTests: 0
      });

      const metrics = autonomousEngine.getAutonomousDecisionMetrics();

      expect(metrics.decisionTypes).toEqual(
        expect.arrayContaining(['strategy_selection', 'quality_gate'])
      );
      expect(metrics.totalDecisions).toBeGreaterThanOrEqual(2);
    });
  });

  describe('Integration with Existing MLCoordinationLayer', () => {
    test('should maintain compatibility with existing 4 ML systems', async () => {
      const profile = testAgent.getIntelligenceProfile();
      
      expect(profile.intelligenceLevel).toBe('transcendent');
      expect(profile.autonomyLevel).toBe(95);
      expect(profile.businessValue).toBe(30000000);
      expect(profile.mlSystems).toHaveLength(4);
    });

    test('should preserve existing transcendent capabilities', async () => {
      const profile = testAgent.getIntelligenceProfile();
      
      expect(profile.strategicCapabilities).toEqual(
        expect.arrayContaining([
          'quality_engineering',
          'business_impact_analysis', 
          'deployment_readiness'
        ])
      );
    });

    test('should enhance existing intelligence without breaking changes', async () => {
      // Verify existing methods still work
      const result = await testAgent.processRequest({
        type: 'test_analysis',
        data: { testSuite: 'example' }
      });

      expect(result.success).toBe(true);
      expect(result.agentId).toBe('TestAgent');
      expect(result.intelligenceLevel).toBe('transcendent');
    });
  });

  describe('Track 6 Day 2 Success Criteria', () => {
    test('should demonstrate advanced autonomous decision-making capabilities', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      // Test all three autonomous capabilities
      const strategyResult = await autonomousEngine.selectOptimalTestStrategy({ complexity: 0.8 });
      const priorityResult = await autonomousEngine.prioritizeTestsAutonomously([
        { name: 'test1', type: 'unit' }
      ]);
      const qualityResult = await autonomousEngine.evaluateQualityGatesAutonomously({
        coverage: 0.9, performanceRegression: 0.02, securityIssues: 0, flakyTests: 0
      });

      // Verify autonomous decision-making
      expect(strategyResult.autonomousDecision).toBe(true);
      expect(priorityResult.autonomousDecisions).toBeGreaterThan(0);
      expect(qualityResult.confidence).toBeGreaterThan(0);

      // Verify learning and feedback
      const metrics = autonomousEngine.getAutonomousDecisionMetrics();
      expect(metrics.totalDecisions).toBeGreaterThan(0);
      expect(metrics.averageConfidence).toBeGreaterThan(0);
    });

    test('should integrate seamlessly with existing agent ecosystem', async () => {
      // Verify enhanced agent maintains all existing capabilities
      expect(testAgent.getIntelligenceProfile()).toBeDefined();
      expect((testAgent as any).mlCoordination).toBeDefined();
      expect((testAgent as any).intelligenceComm).toBeDefined();
      
      // Verify new autonomous capabilities added
      expect((testAgent as any).advancedAutonomousEngine).toBeDefined();
    });

    test('should provide measurable intelligence improvement', async () => {
      const autonomousEngine = (testAgent as any).advancedAutonomousEngine;
      
      // Perform autonomous operations
      await autonomousEngine.selectOptimalTestStrategy({ complexity: 0.8 });
      await autonomousEngine.evaluateQualityGatesAutonomously({
        coverage: 0.85, performanceRegression: 0.05, securityIssues: 0, flakyTests: 1
      });

      const metrics = autonomousEngine.getAutonomousDecisionMetrics();
      
      // Measurable improvement criteria
      expect(metrics.totalDecisions).toBeGreaterThan(0);
      expect(metrics.averageConfidence).toBeGreaterThan(0.5);
      expect(metrics.decisionTypes.length).toBeGreaterThan(0);
      expect(metrics.learningProgress.size).toBeGreaterThan(0);
    });
  });
}); 