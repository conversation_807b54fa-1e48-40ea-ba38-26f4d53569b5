{"metadata": {"analysisDate": "2025-06-03T06:17:41.857Z", "totalAgents": 106, "totalLinesAnalyzed": 103841, "analysisMethod": "Complete Code Analysis with AI Coordination"}, "summary": {"severityDistribution": {"CRITICAL": 18, "HIGH": 3, "MEDIUM": 9, "LOW": 76}, "topIssues": [{"issue": "Method Limit", "count": 39, "percentage": "36.8"}, {"issue": "Interface Explosion", "count": 21, "percentage": "19.8"}, {"issue": "Complexity Score", "count": 20, "percentage": "18.9"}, {"issue": "File Size Limit", "count": 19, "percentage": "17.9"}, {"issue": "Interface Limit", "count": 18, "percentage": "17.0"}, {"issue": "God Object Check", "count": 16, "percentage": "15.1"}, {"issue": "File Size", "count": 16, "percentage": "15.1"}, {"issue": "Method Density", "count": 13, "percentage": "12.3"}], "systemHealthScore": 79, "recommendedActions": [{"priority": "IMMEDIATE", "title": "Address 18 Critical Agents", "description": "Critical architectural issues requiring immediate attention", "estimatedEffort": "HIGH"}]}, "agentAnalyses": {"TestAgent": {"agent": "TestAgent", "filePath": "src/agent-core/agents/TestAgent.ts", "timestamp": "2025-06-03T06:17:41.885Z", "metrics": {"lines": 4787, "size": 177762, "interfaces": 26, "types": 0, "classes": 5, "methods": 123, "imports": 18, "exports": 1, "asyncMethods": 64, "complexity": 447}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 8, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 5, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 1, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 9, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 145, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 906, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 27, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 27, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 123, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 4787, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 18, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 258, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 11, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 43, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 11, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 2, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 81, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 27, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 123, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 4787, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 18}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 4787, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 26, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 123, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 447, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 4787, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "26 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "123 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 4787 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "e6f5ad7313eaef30"}, "AutonomousDevAgent": {"agent": "AutonomousDevAgent", "filePath": "src/agent-core/agents/AutonomousDevAgent.ts", "timestamp": "2025-06-03T06:17:41.897Z", "metrics": {"lines": 3484, "size": 132138, "interfaces": 112, "types": 0, "classes": 1, "methods": 94, "imports": 9, "exports": 4, "asyncMethods": 51, "complexity": 254}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 24, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 2, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 4, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 6, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 216, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 606, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 11, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 113, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 94, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 3484, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 5, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 186, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 15, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 85, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 113, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 94, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 3484, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 16}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 3484, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 112, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 94, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 254, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 3484, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "112 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "94 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 3484 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "f93138f7ce5c4d0d"}, "DevAgent": {"agent": "DevAgent", "filePath": "src/agent-core/agents/DevAgent.ts", "timestamp": "2025-06-03T06:17:41.904Z", "metrics": {"lines": 3024, "size": 110837, "interfaces": 66, "types": 0, "classes": 1, "methods": 116, "imports": 14, "exports": 3, "asyncMethods": 98, "complexity": 228}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 17, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 7, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 2, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 9, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 117, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 333, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 12, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 66, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 116, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 3024, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 8, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 347, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 7, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 4, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 3, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 86, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 66, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 116, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 3024, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 17}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 3024, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 66, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 116, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 228, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 3024, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "66 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "116 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 3024 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "8b08347dfd43bc01"}, "WorkflowEnhancementAgent": {"agent": "WorkflowEnhancementAgent", "filePath": "src/agent-core/agents/WorkflowEnhancementAgent.ts", "timestamp": "2025-06-03T06:17:41.910Z", "metrics": {"lines": 2843, "size": 95537, "interfaces": 84, "types": 0, "classes": 1, "methods": 49, "imports": 9, "exports": 1, "asyncMethods": 21, "complexity": 107}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 18, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 6, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 2, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 100, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 382, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 437, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 84, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 49, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2843, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 10, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 72, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 15, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 56, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 8, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 3, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 27, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 84, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2843, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 17}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 2843, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 84, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 49, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 107, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2843, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "84 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "49 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2843 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "cf2e57397eaa9328"}, "MLCoordinationLayer": {"agent": "MLCoordinationLayer", "filePath": "src/agent-core/orchestrator/MLCoordinationLayer.ts", "timestamp": "2025-06-03T06:17:42.058Z", "metrics": {"lines": 441, "size": 13865, "interfaces": 4, "types": 0, "classes": 2, "methods": 18, "imports": 0, "exports": 0, "asyncMethods": 6, "complexity": 44}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 11, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 23, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 5, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 18, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 27, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 1, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 11, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 9}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 441, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 4, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 18, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 44, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 441, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "f873f42d8ccba8f9"}, "AutonomousIntelligenceAgent": {"agent": "AutonomousIntelligenceAgent", "filePath": "src/agent-core/agents/AutonomousIntelligenceAgent.ts", "timestamp": "2025-06-03T06:17:41.919Z", "metrics": {"lines": 2762, "size": 97881, "interfaces": 98, "types": 0, "classes": 1, "methods": 58, "imports": 7, "exports": 1, "asyncMethods": 29, "complexity": 219}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 22, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 7, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 1, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 214, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 276, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 3, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 99, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 58, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2762, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 8, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 87, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 10, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 34, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 99, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 58, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2762, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 14}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 2762, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 98, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 58, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 219, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2762, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "98 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "58 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2762 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "05164ee3293574c1"}, "UIAgent": {"agent": "UIAgent", "filePath": "src/agent-core/agents/UIAgent.ts", "timestamp": "2025-06-03T06:17:41.924Z", "metrics": {"lines": 2542, "size": 98480, "interfaces": 36, "types": 137, "classes": 1, "methods": 50, "imports": 8, "exports": 1, "asyncMethods": 29, "complexity": 223}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 12, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 2, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 61, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 369, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 6, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 40, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 50, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2542, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 7, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 106, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 7, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 33, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 8, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 53, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 47, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 40, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2542, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 17}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 2542, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 36, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 50, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 223, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2542, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "36 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "50 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2542 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "25ad075f3b269304"}, "FeatureDiscoveryAgent": {"agent": "FeatureDiscoveryAgent", "filePath": "src/agent-core/agents/FeatureDiscoveryAgent.ts", "timestamp": "2025-06-03T06:17:41.929Z", "metrics": {"lines": 2536, "size": 78874, "interfaces": 132, "types": 0, "classes": 1, "methods": 67, "imports": 5, "exports": 1, "asyncMethods": 26, "complexity": 127}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 28, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 7, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 1, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 72, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 277, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 6, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 132, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 67, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2536, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 6, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 74, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 20, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 129, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 8, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 33, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 132, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 67, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2536, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 16}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 2536, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 132, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 67, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 127, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2536, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "132 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "67 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2536 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "87bc8f6da6876cce"}, "AdvancedSelfModificationEngine": {"agent": "AdvancedSelfModificationEngine", "filePath": "src/agent-core/advanced-modification/AdvancedSelfModificationEngine.ts", "timestamp": "2025-06-03T06:17:41.933Z", "metrics": {"lines": 2401, "size": 81610, "interfaces": 49, "types": 0, "classes": 6, "methods": 59, "imports": 15, "exports": 5, "asyncMethods": 39, "complexity": 154}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 18, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 4, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 2, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 38, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 403, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 1, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 52, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 59, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2401, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 7, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 125, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 3, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 8, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 17, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 22, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 52, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 59, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2401, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 16}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 2401, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 49, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 59, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 154, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2401, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "49 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "59 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2401 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "7a1eff2de47a3868"}, "ConversationalDevAgent": {"agent": "ConversationalDevAgent", "filePath": "src/agent-core/agents/ConversationalDevAgent.ts", "timestamp": "2025-06-03T06:17:41.938Z", "metrics": {"lines": 2384, "size": 69738, "interfaces": 160, "types": 0, "classes": 1, "methods": 43, "imports": 8, "exports": 2, "asyncMethods": 24, "complexity": 50}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 34, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 1, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 6, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 60, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 326, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 3, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 161, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 43, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2384, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 8, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 72, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 4, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 11, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 18, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 161, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2384, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 17}, "qualityGates": {"passed": [{"name": "Complexity Score", "actual": 50, "limit": 100, "status": "PASS"}], "failed": [{"name": "File Size Limit", "actual": 2384, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 160, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 43, "limit": 30, "status": "FAIL"}, {"name": "God Object Check", "actual": 2384, "limit": 2000, "status": "FAIL"}], "passRate": 20}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "160 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "43 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2384 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "54649a8b83ad0a8d"}, "ErrorMonitorAgent": {"agent": "ErrorMonitorAgent", "filePath": "src/agent-core/agents/ErrorMonitorAgent.ts", "timestamp": "2025-06-03T06:17:41.942Z", "metrics": {"lines": 2311, "size": 82642, "interfaces": 23, "types": 0, "classes": 1, "methods": 88, "imports": 11, "exports": 2, "asyncMethods": 41, "complexity": 191}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 6, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 7, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 5, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 18, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 260, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 13, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 24, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 88, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2311, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 10, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 140, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 9, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 2, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 1, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 71, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 24, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 88, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2311, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 17}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 2311, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 23, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 88, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 191, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2311, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "23 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "88 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2311 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "0615873a041fbe5e"}, "UserBehaviorAgent": {"agent": "UserBehaviorAgent", "filePath": "src/agent-core/agents/UserBehaviorAgent.ts", "timestamp": "2025-06-03T06:17:41.946Z", "metrics": {"lines": 2287, "size": 78696, "interfaces": 74, "types": 0, "classes": 1, "methods": 47, "imports": 7, "exports": 1, "asyncMethods": 24, "complexity": 118}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 18, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 2, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 89, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 192, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 4, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 78, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 47, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2287, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 8, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 71, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 4, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 18, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 16, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 78, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2287, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 17}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 2287, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 74, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 47, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 118, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2287, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "74 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "47 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2287 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "f072adf6d528942a"}, "ChatResponseParserAgent": {"agent": "ChatResponseParserAgent", "filePath": "src/agent-core/agents/ChatResponseParserAgent.ts", "timestamp": "2025-06-03T06:17:41.950Z", "metrics": {"lines": 2238, "size": 82179, "interfaces": 89, "types": 84, "classes": 1, "methods": 34, "imports": 9, "exports": 1, "asyncMethods": 22, "complexity": 63}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 20, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 3, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 119, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 270, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 89, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 34, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2238, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 8, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 67, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 2, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 26, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 89, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2238, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 16}, "qualityGates": {"passed": [{"name": "Complexity Score", "actual": 63, "limit": 100, "status": "PASS"}], "failed": [{"name": "File Size Limit", "actual": 2238, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 89, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 34, "limit": 30, "status": "FAIL"}, {"name": "God Object Check", "actual": 2238, "limit": 2000, "status": "FAIL"}], "passRate": 20}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "89 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "34 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2238 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "503e14b568027061"}, "LocalAIService": {"agent": "LocalAIService", "filePath": "src/agent-core/integrations/LocalAIService.ts", "timestamp": "2025-06-03T06:17:41.954Z", "metrics": {"lines": 2224, "size": 78814, "interfaces": 7, "types": 0, "classes": 2, "methods": 48, "imports": 5, "exports": 1, "asyncMethods": 18, "complexity": 307}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 8, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 2, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 370, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 7, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 48, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2224, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 67, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 9, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Authentication": {"matches": 7, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 77, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "File Size", "category": "CODE_QUALITY", "value": 2224, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "Interface Limit", "actual": 7, "limit": 15, "status": "PASS"}], "failed": [{"name": "File Size Limit", "actual": 2224, "limit": 1500, "status": "FAIL"}, {"name": "Method Limit", "actual": 48, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 307, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2224, "limit": 2000, "status": "FAIL"}], "passRate": 20}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "48 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2224 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "4bbcd02639e5a6e1"}, "IntelligentAIResourceManager": {"agent": "IntelligentAIResourceManager", "filePath": "src/agent-core/resource-optimization/IntelligentAIResourceManager.ts", "timestamp": "2025-06-03T06:17:41.957Z", "metrics": {"lines": 2076, "size": 76398, "interfaces": 7, "types": 0, "classes": 1, "methods": 60, "imports": 8, "exports": 1, "asyncMethods": 13, "complexity": 259}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 7, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 9, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 296, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 39, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 7, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 60, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2076, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 4, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 43, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 8, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 4, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 16, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 29, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 60, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2076, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 16}, "qualityGates": {"passed": [{"name": "Interface Limit", "actual": 7, "limit": 15, "status": "PASS"}], "failed": [{"name": "File Size Limit", "actual": 2076, "limit": 1500, "status": "FAIL"}, {"name": "Method Limit", "actual": 60, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 259, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2076, "limit": 2000, "status": "FAIL"}], "passRate": 20}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "60 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2076 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "4ea7bdf70c95aeac"}, "StrategicGovernanceEngine": {"agent": "StrategicGovernanceEngine", "filePath": "src/agent-core/governance/StrategicGovernanceEngine.ts", "timestamp": "2025-06-03T06:17:41.961Z", "metrics": {"lines": 2054, "size": 78437, "interfaces": 22, "types": 0, "classes": 5, "methods": 68, "imports": 7, "exports": 1, "asyncMethods": 42, "complexity": 185}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 6, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 4, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 111, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 301, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 3, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 22, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 68, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2054, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 7, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 104, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 6, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 10, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 3, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 1, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 19, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 22, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}, {"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 68, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2054, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 16}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 2054, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 22, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 68, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 185, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2054, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "22 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "68 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2054 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "aab13b38c181ebda"}, "SecurityAgent": {"agent": "SecurityAgent", "filePath": "src/agent-core/agents/SecurityAgent.ts", "timestamp": "2025-06-03T06:17:41.964Z", "metrics": {"lines": 2037, "size": 73694, "interfaces": 20, "types": 0, "classes": 1, "methods": 58, "imports": 7, "exports": 1, "asyncMethods": 44, "complexity": 189}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 5, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 3, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 19, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 241, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 56, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 20, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 58, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.File Size": {"value": 2037, "severity": "MEDIUM", "description": "Large file size indicating complexity", "threshold": 2000}, "CODE_QUALITY.Circular Dependencies": {"matches": 6, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 159, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 7, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Input Validation": {"matches": 5, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 6, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 60, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 58, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}, {"pattern": "File Size", "category": "CODE_QUALITY", "value": 2037, "threshold": 2000, "severity": "MEDIUM", "description": "Large file size indicating complexity"}], "totalPatterns": 17}, "qualityGates": {"passed": [], "failed": [{"name": "File Size Limit", "actual": 2037, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 20, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 58, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 189, "limit": 100, "status": "FAIL"}, {"name": "God Object Check", "actual": 2037, "limit": 2000, "status": "FAIL"}], "passRate": 0}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "20 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "58 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "CRITICAL", "title": "God Object Refactoring Required", "description": "File has 2037 lines (limit: 2000). Consider breaking into smaller, focused modules.", "estimatedEffort": "HIGH"}], "severity": "CRITICAL", "hash": "dcdadee389af5cf1"}, "PerformanceMonitoringAgent": {"agent": "PerformanceMonitoringAgent", "filePath": "src/agent-core/agents/PerformanceMonitoringAgent.ts", "timestamp": "2025-06-03T06:17:41.968Z", "metrics": {"lines": 1541, "size": 60735, "interfaces": 20, "types": 0, "classes": 5, "methods": 89, "imports": 6, "exports": 1, "asyncMethods": 32, "complexity": 129}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 5, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 2, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 1, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 40, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 309, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 1, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 20, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 89, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 6, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 85, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 7, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 12, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 23, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 89, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}], "totalPatterns": 15}, "qualityGates": {"passed": [{"name": "God Object Check", "actual": 1541, "limit": 2000, "status": "PASS"}], "failed": [{"name": "File Size Limit", "actual": 1541, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 20, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 89, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 129, "limit": 100, "status": "FAIL"}], "passRate": 20}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "20 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "89 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "CRITICAL", "hash": "c8813d2605131306"}, "AdaptiveResourceAllocator": {"agent": "AdaptiveResourceAllocator", "filePath": "src/agent-core/allocation/AdaptiveResourceAllocator.ts", "timestamp": "2025-06-03T06:17:41.971Z", "metrics": {"lines": 1527, "size": 44751, "interfaces": 0, "types": 0, "classes": 7, "methods": 4, "imports": 2, "exports": 74, "asyncMethods": 47, "complexity": 9}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 15, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 5, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 5, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 2, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 273, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 8, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 69, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 4, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 149, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 4, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 6, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 11, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 69, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 4, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 9, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1527, "limit": 2000, "status": "PASS"}], "failed": [{"name": "File Size Limit", "actual": 1527, "limit": 1500, "status": "FAIL"}], "passRate": 80}, "recommendations": [], "severity": "HIGH", "hash": "7e972358649209f8"}, "ConfigAgent": {"agent": "ConfigAgent", "filePath": "src/agent-core/agents/ConfigAgent.ts", "timestamp": "2025-06-03T06:17:41.973Z", "metrics": {"lines": 1519, "size": 58448, "interfaces": 27, "types": 49, "classes": 1, "methods": 36, "imports": 8, "exports": 2, "asyncMethods": 23, "complexity": 114}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 6, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 1, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 62, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 211, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 9, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 27, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 36, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 6, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 69, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 9, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 58, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 9, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 23, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 27, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}], "totalPatterns": 16}, "qualityGates": {"passed": [{"name": "God Object Check", "actual": 1519, "limit": 2000, "status": "PASS"}], "failed": [{"name": "File Size Limit", "actual": 1519, "limit": 1500, "status": "FAIL"}, {"name": "Interface Limit", "actual": 27, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 36, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 114, "limit": 100, "status": "FAIL"}], "passRate": 20}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "27 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "36 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "CRITICAL", "hash": "928de097b0e40b2e"}, "UnifiedMonitoringFramework": {"agent": "UnifiedMonitoringFramework", "filePath": "src/agent-core/monitoring/UnifiedMonitoringFramework.ts", "timestamp": "2025-06-03T06:17:41.976Z", "metrics": {"lines": 1447, "size": 43882, "interfaces": 0, "types": 0, "classes": 7, "methods": 5, "imports": 2, "exports": 75, "asyncMethods": 47, "complexity": 12}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 15, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 6, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 59, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 216, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 13, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 71, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 5, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 149, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 6, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 71, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1447, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 5, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 12, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1447, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "MEDIUM", "hash": "a2a4c83a12305d1c"}, "ThermalAwareResourceOptimizer": {"agent": "ThermalAwareResourceOptimizer", "filePath": "src/agent-core/optimization/ThermalAwareResourceOptimizer.ts", "timestamp": "2025-06-03T06:17:41.979Z", "metrics": {"lines": 1423, "size": 42536, "interfaces": 0, "types": 0, "classes": 7, "methods": 4, "imports": 2, "exports": 78, "asyncMethods": 47, "complexity": 18}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 15, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 5, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 2, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 82, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 73, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 4, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 148, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 14, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 6, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 11, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 73, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1423, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 4, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 18, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1423, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "MEDIUM", "hash": "4c7c77f21e4adbd8"}, "RealTimeDecisionEngine": {"agent": "RealTimeDecisionEngine", "filePath": "src/agent-core/decision/RealTimeDecisionEngine.ts", "timestamp": "2025-06-03T06:17:41.981Z", "metrics": {"lines": 1422, "size": 40632, "interfaces": 0, "types": 0, "classes": 7, "methods": 3, "imports": 2, "exports": 90, "asyncMethods": 42, "complexity": 22}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 18, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 7, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 2, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 13, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 166, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 7, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 86, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 3, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 132, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 3, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 5, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 11, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 86, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1422, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 3, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 22, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1422, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "MEDIUM", "hash": "92df2b5c9f33ce55"}, "AgentStrategicCoordinator": {"agent": "AgentStrategicCoordinator", "filePath": "src/agent-core/coordination/AgentStrategicCoordinator.ts", "timestamp": "2025-06-03T06:17:41.983Z", "metrics": {"lines": 1378, "size": 52422, "interfaces": 10, "types": 0, "classes": 5, "methods": 51, "imports": 2, "exports": 6, "asyncMethods": 23, "complexity": 136}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 4, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 7, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 1, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 4, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 29, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 192, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 389, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 15, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 51, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 57, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 9, "severity": "LOW", "description": "Caching implementation patterns"}}, "warnings": [{"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 51, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1378, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 10, "limit": 15, "status": "PASS"}, {"name": "God Object Check", "actual": 1378, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 51, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 136, "limit": 100, "status": "FAIL"}], "passRate": 60}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "51 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "HIGH", "hash": "43095ffd4cd111be"}, "VectorMemory": {"agent": "VectorMemory", "filePath": "src/agent-core/memory/VectorMemory.ts", "timestamp": "2025-06-03T06:17:41.985Z", "metrics": {"lines": 1352, "size": 41829, "interfaces": 5, "types": 0, "classes": 1, "methods": 32, "imports": 4, "exports": 1, "asyncMethods": 25, "complexity": 136}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 32, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 80, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 5, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 32, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 75, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 10, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 10, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 30, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1352, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 5, "limit": 15, "status": "PASS"}, {"name": "God Object Check", "actual": 1352, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 32, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 136, "limit": 100, "status": "FAIL"}], "passRate": 60}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "32 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "MEDIUM", "hash": "b9960c8d9d770a04"}, "AgentMesh": {"agent": "<PERSON><PERSON><PERSON>", "filePath": "src/agent-core/mesh/AgentMesh.ts", "timestamp": "2025-06-03T06:17:41.988Z", "metrics": {"lines": 1236, "size": 39107, "interfaces": 10, "types": 1, "classes": 1, "methods": 24, "imports": 6, "exports": 1, "asyncMethods": 24, "complexity": 100}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 3, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 13, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 41, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 31, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 59, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 3, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 10, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 24, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 3, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 81, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 11, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 56, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 29, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 15}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1236, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 10, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 24, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 100, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1236, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "3a077a3bd0e77ea2"}, "CrossAgentIntelligenceSynthesis": {"agent": "CrossAgentIntelligenceSynthesis", "filePath": "src/agent-core/synthesis/CrossAgentIntelligenceSynthesis.ts", "timestamp": "2025-06-03T06:17:41.989Z", "metrics": {"lines": 1174, "size": 35864, "interfaces": 0, "types": 0, "classes": 5, "methods": 3, "imports": 2, "exports": 54, "asyncMethods": 33, "complexity": 7}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 11, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 5, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 135, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 167, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 110, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 50, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 3, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 112, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 10, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 11, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 50, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1174, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 3, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 7, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1174, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "MEDIUM", "hash": "2d907a7c2325e368"}, "SelfImprovementEngine": {"agent": "SelfImprovementEngine", "filePath": "src/agent-core/self-improvement/SelfImprovementEngine.ts", "timestamp": "2025-06-03T06:17:41.991Z", "metrics": {"lines": 1062, "size": 34751, "interfaces": 3, "types": 0, "classes": 1, "methods": 55, "imports": 8, "exports": 1, "asyncMethods": 47, "complexity": 76}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 95, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 55, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 8, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 148, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Input Validation": {"matches": 7, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 37, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Method Density", "category": "CODE_QUALITY", "matches": 55, "threshold": 50, "severity": "HIGH", "description": "Too many methods in single class"}], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1062, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 76, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1062, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 55, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "55 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "MEDIUM", "hash": "462b8645905bb157"}, "LivingAgentBase": {"agent": "LivingAgentBase", "filePath": "src/agent-core/framework/LivingAgentBase.ts", "timestamp": "2025-06-03T06:17:41.993Z", "metrics": {"lines": 1025, "size": 38271, "interfaces": 16, "types": 0, "classes": 0, "methods": 37, "imports": 4, "exports": 1, "asyncMethods": 10, "complexity": 53}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 4, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 7, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 8, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 88, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 17, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 37, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 3, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 36, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 9, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 13, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 7, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1025, "limit": 1500, "status": "PASS"}, {"name": "Complexity Score", "actual": 53, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1025, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Interface Limit", "actual": 16, "limit": 15, "status": "FAIL"}, {"name": "Method Limit", "actual": 37, "limit": 30, "status": "FAIL"}], "passRate": 60}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "16 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}, {"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "37 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "MEDIUM", "hash": "07dacd23682e66e1"}, "HybridRouter": {"agent": "HybridRouter", "filePath": "src/agent-core/intelligence/HybridRouter.ts", "timestamp": "2025-06-03T06:17:41.995Z", "metrics": {"lines": 1005, "size": 33250, "interfaces": 2, "types": 0, "classes": 1, "methods": 11, "imports": 5, "exports": 10, "asyncMethods": 6, "complexity": 89}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 2, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 61, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 235, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 11, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 5, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 23, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 4, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Input Validation": {"matches": 3, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 20, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 36, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1005, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 2, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 11, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 89, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1005, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "631128e5aac325fe"}, "OpsAgent": {"agent": "OpsAgent", "filePath": "src/agent-core/agents/OpsAgent.ts", "timestamp": "2025-06-03T06:17:41.996Z", "metrics": {"lines": 1001, "size": 34020, "interfaces": 39, "types": 0, "classes": 1, "methods": 21, "imports": 7, "exports": 1, "asyncMethods": 13, "complexity": 58}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 9, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 2, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 4, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 20, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 132, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 13, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 39, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 21, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 6, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 43, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 4, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 4, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 27, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 39, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}], "totalPatterns": 15}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 1001, "limit": 1500, "status": "PASS"}, {"name": "Method Limit", "actual": 21, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 58, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 1001, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Interface Limit", "actual": 39, "limit": 15, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "ORGANIZATION", "priority": "HIGH", "title": "Interface Organization", "description": "39 interfaces found (limit: 15). Consider grouping related interfaces into separate files.", "estimatedEffort": "MEDIUM"}], "severity": "HIGH", "hash": "d7c0b26089803a0d"}, "AgentConflictDetectionEngine": {"agent": "AgentConflictDetectionEngine", "filePath": "src/agent-core/orchestration/AgentConflictDetectionEngine.ts", "timestamp": "2025-06-03T06:17:41.998Z", "metrics": {"lines": 991, "size": 36014, "interfaces": 0, "types": 0, "classes": 1, "methods": 35, "imports": 5, "exports": 9, "asyncMethods": 6, "complexity": 58}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 6, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 19, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 15, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 5, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 35, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 19, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 3, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 6, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 4, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 991, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 58, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 991, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 35, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "35 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "1629ed96ac052b84"}, "AdvancedCrossAgentCommunication": {"agent": "AdvancedCrossAgentCommunication", "filePath": "src/agent-core/communication/AdvancedCrossAgentCommunication.ts", "timestamp": "2025-06-03T06:17:42.000Z", "metrics": {"lines": 977, "size": 27548, "interfaces": 0, "types": 0, "classes": 7, "methods": 4, "imports": 2, "exports": 53, "asyncMethods": 33, "complexity": 36}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 9, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 36, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 3, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 54, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 63, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 40, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 4, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 108, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 6, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 9, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 4, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [{"pattern": "Interface Explosion", "category": "CODE_QUALITY", "matches": 40, "threshold": 20, "severity": "HIGH", "description": "Too many interfaces in single file"}], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 977, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 4, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 36, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 977, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "MEDIUM", "hash": "c87fc80796c32634"}, "RealProblemSolvingValidator": {"agent": "RealProblemSolvingValidator", "filePath": "src/agent-core/enhancement/RealProblemSolvingValidator.ts", "timestamp": "2025-06-03T06:17:42.002Z", "metrics": {"lines": 958, "size": 33205, "interfaces": 11, "types": 1, "classes": 1, "methods": 39, "imports": 3, "exports": 1, "asyncMethods": 15, "complexity": 71}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 3, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 8, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 50, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 1, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 11, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 39, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 40, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 4, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 19, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 2, "severity": "HIGH", "description": "Authentication and authorization patterns"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 958, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 11, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 71, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 958, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 39, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "39 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "cea0f28d19fc9377"}, "ChatBridgeMonitor": {"agent": "ChatBridgeMonitor", "filePath": "src/agent-core/communication/ChatBridgeMonitor.ts", "timestamp": "2025-06-03T06:17:42.003Z", "metrics": {"lines": 948, "size": 33891, "interfaces": 0, "types": 0, "classes": 1, "methods": 24, "imports": 4, "exports": 8, "asyncMethods": 14, "complexity": 124}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 7, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 14, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 81, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 1, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 24, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 83, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 18, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 47, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 948, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 24, "limit": 30, "status": "PASS"}, {"name": "God Object Check", "actual": 948, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Complexity Score", "actual": 124, "limit": 100, "status": "FAIL"}], "passRate": 80}, "recommendations": [], "severity": "LOW", "hash": "6973347b169a9245"}, "LivingUIAgent": {"agent": "LivingUIAgent", "filePath": "src/agent-core/agents/LivingUIAgent.ts", "timestamp": "2025-06-03T06:17:42.005Z", "metrics": {"lines": 918, "size": 35009, "interfaces": 9, "types": 0, "classes": 1, "methods": 36, "imports": 3, "exports": 1, "asyncMethods": 12, "complexity": 24}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 5, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 5, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 4, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 64, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 73, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 1, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 9, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 36, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 3, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 40, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 1, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 3, "severity": "HIGH", "description": "Input validation and sanitization"}}, "warnings": [], "totalPatterns": 14}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 918, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 9, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 24, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 918, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 36, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "36 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "1a9caf90ef62e15c"}, "LocalIntelligenceEngine": {"agent": "LocalIntelligenceEngine", "filePath": "src/agent-core/engines/LocalIntelligenceEngine.ts", "timestamp": "2025-06-03T06:17:42.006Z", "metrics": {"lines": 918, "size": 34875, "interfaces": 0, "types": 0, "classes": 1, "methods": 23, "imports": 5, "exports": 1, "asyncMethods": 9, "complexity": 92}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 81, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 146, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 5, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Method Density": {"matches": 23, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 5, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 27, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 1, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 6, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 7, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 27, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 918, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 23, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 92, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 918, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "5b8f42ad8168b91f"}, "AgentGovernanceSystem": {"agent": "AgentGovernanceSystem", "filePath": "src/agent-core/governance/AgentGovernanceSystem.ts", "timestamp": "2025-06-03T06:17:42.008Z", "metrics": {"lines": 900, "size": 31061, "interfaces": 11, "types": 1, "classes": 1, "methods": 33, "imports": 7, "exports": 1, "asyncMethods": 16, "complexity": 39}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 3, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 6, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 4, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 82, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 11, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 33, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 4, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 50, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 11, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 900, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 11, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 39, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 900, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 33, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "33 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "9cc9e387f05d0c38"}, "ResourceOptimizationEngine": {"agent": "ResourceOptimizationEngine", "filePath": "src/agent-core/advanced-modification/ResourceOptimizationEngine.ts", "timestamp": "2025-06-03T06:17:42.009Z", "metrics": {"lines": 891, "size": 32421, "interfaces": 8, "types": 0, "classes": 1, "methods": 33, "imports": 3, "exports": 1, "asyncMethods": 31, "complexity": 69}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 2, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 5, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 1, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 16, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 105, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 7, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 8, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 33, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 3, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 96, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 1, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 7, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 21, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 14}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 891, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 8, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 69, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 891, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 33, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "33 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "de9453036056f518"}, "AdvancedDecisionEngine": {"agent": "AdvancedDecisionEngine", "filePath": "src/agent-core/advanced-modification/AdvancedDecisionEngine.ts", "timestamp": "2025-06-03T06:17:42.011Z", "metrics": {"lines": 889, "size": 28279, "interfaces": 9, "types": 0, "classes": 1, "methods": 32, "imports": 2, "exports": 1, "asyncMethods": 30, "complexity": 44}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 41, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 56, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 1, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 9, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 32, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 90, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 5, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 2, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 15, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 14}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 889, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 9, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 44, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 889, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 32, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "32 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "da12901ea3602876"}, "CrossAgentCommunicationEngine": {"agent": "CrossAgentCommunicationEngine", "filePath": "src/agent-core/orchestration/CrossAgentCommunicationEngine.ts", "timestamp": "2025-06-03T06:17:42.013Z", "metrics": {"lines": 838, "size": 28014, "interfaces": 0, "types": 0, "classes": 1, "methods": 36, "imports": 6, "exports": 10, "asyncMethods": 5, "complexity": 54}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 7, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 18, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 1, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 31, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 6, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 13, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 11, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 6, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 36, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 16, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 6, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 3, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 18, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 16}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 838, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 54, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 838, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 36, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "36 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "c8a358533b070617"}, "AutonomousGoalSetting": {"agent": "AutonomousGoalSetting", "filePath": "src/agent-core/autonomy/AutonomousGoalSetting.ts", "timestamp": "2025-06-03T06:17:42.014Z", "metrics": {"lines": 791, "size": 28436, "interfaces": 3, "types": 0, "classes": 1, "methods": 17, "imports": 3, "exports": 2, "asyncMethods": 17, "complexity": 76}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 5, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 42, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 43, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 20, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 17, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 57, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 9, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 791, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 17, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 76, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 791, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "3ba99039bb3ea8fe"}, "AgentFailureHandler": {"agent": "AgentFailureHandler", "filePath": "src/agent-core/reliability/AgentFailureHandler.ts", "timestamp": "2025-06-03T06:17:42.015Z", "metrics": {"lines": 786, "size": 25271, "interfaces": 0, "types": 0, "classes": 1, "methods": 27, "imports": 2, "exports": 6, "asyncMethods": 13, "complexity": 34}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 1, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 237, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 12, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 5, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 27, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 44, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 5, "severity": "LOW", "description": "Caching implementation patterns"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 786, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 27, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 34, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 786, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "ddb79e94ab6ccb26"}, "SystemMonitoringAgent": {"agent": "SystemMonitoringAgent", "filePath": "src/agent-core/agents/SystemMonitoringAgent.ts", "timestamp": "2025-06-03T06:17:42.017Z", "metrics": {"lines": 764, "size": 26012, "interfaces": 2, "types": 0, "classes": 1, "methods": 26, "imports": 6, "exports": 1, "asyncMethods": 16, "complexity": 84}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 1, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 3, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 89, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 26, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 4, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 50, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 8, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 22, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 764, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 2, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 26, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 84, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 764, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "48957194fadce150"}, "AutonomousAppImprovementEngine": {"agent": "AutonomousAppImprovementEngine", "filePath": "src/agent-core/improvement/AutonomousAppImprovementEngine.ts", "timestamp": "2025-06-03T06:17:42.018Z", "metrics": {"lines": 754, "size": 25148, "interfaces": 3, "types": 0, "classes": 1, "methods": 23, "imports": 3, "exports": 9, "asyncMethods": 15, "complexity": 36}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 4, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 27, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 44, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 7, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 6, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 23, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 45, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 13, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 19, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 754, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 23, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 36, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 754, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "fd69d6c31b16a715"}, "AutonomousPlatformEvolution": {"agent": "AutonomousPlatformEvolution", "filePath": "src/agent-core/autonomy/AutonomousPlatformEvolution.ts", "timestamp": "2025-06-03T06:17:42.019Z", "metrics": {"lines": 737, "size": 22814, "interfaces": 15, "types": 0, "classes": 1, "methods": 19, "imports": 0, "exports": 6, "asyncMethods": 19, "complexity": 3}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 5, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 2, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 37, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 63, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 1, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 20, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 19, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 54, "severity": "LOW", "description": "Asynchronous processing patterns"}}, "warnings": [], "totalPatterns": 8}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 737, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 15, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 19, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 3, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 737, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "1fd5294af8de39ad"}, "HealthMonitor": {"agent": "HealthMonitor", "filePath": "src/agent-core/monitoring/HealthMonitor.ts", "timestamp": "2025-06-03T06:17:42.020Z", "metrics": {"lines": 735, "size": 21307, "interfaces": 11, "types": 0, "classes": 3, "methods": 33, "imports": 5, "exports": 1, "asyncMethods": 28, "complexity": 24}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 3, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 13, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 41, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 1, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 11, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 33, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 4, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 81, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 8, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 24, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 735, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 11, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 24, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 735, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 33, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "33 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "3838d4bc5bd95fab"}, "ThermalMonitoringExtension": {"agent": "ThermalMonitoringExtension", "filePath": "src/agent-core/resource-optimization/ThermalMonitoringExtension.ts", "timestamp": "2025-06-03T06:17:42.023Z", "metrics": {"lines": 730, "size": 28754, "interfaces": 3, "types": 0, "classes": 1, "methods": 15, "imports": 3, "exports": 2, "asyncMethods": 3, "complexity": 46}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 9, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 3, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 61, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 4, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 15, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 8, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 5, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 1, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 730, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 15, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 46, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 730, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "794cd6f46a1bf914"}, "ThermalAwareResourceAllocator": {"agent": "ThermalAwareResourceAllocator", "filePath": "src/agent-core/resource-optimization/ThermalAwareResourceAllocator.ts", "timestamp": "2025-06-03T06:17:42.024Z", "metrics": {"lines": 728, "size": 27472, "interfaces": 3, "types": 0, "classes": 1, "methods": 23, "imports": 4, "exports": 1, "asyncMethods": 8, "complexity": 48}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 8, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 2, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 53, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 5, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 23, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 23, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 8, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 728, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 23, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 48, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 728, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "24495ecaba6de3e0"}, "QuantumDecisionNetwork": {"agent": "QuantumDecisionNetwork", "filePath": "src/agent-core/intelligence/QuantumDecisionNetwork.ts", "timestamp": "2025-06-03T06:17:42.025Z", "metrics": {"lines": 708, "size": 23982, "interfaces": 7, "types": 0, "classes": 1, "methods": 30, "imports": 2, "exports": 1, "asyncMethods": 10, "complexity": 39}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 82, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 4, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 7, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 30, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 28, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 1, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 708, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 7, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 30, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 39, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 708, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "b67444f6db05f8d8"}, "ThermalProtectionSystem": {"agent": "ThermalProtectionSystem", "filePath": "src/agent-core/resource-optimization/ThermalProtectionSystem.ts", "timestamp": "2025-06-03T06:17:42.027Z", "metrics": {"lines": 699, "size": 31098, "interfaces": 4, "types": 0, "classes": 1, "methods": 24, "imports": 4, "exports": 1, "asyncMethods": 10, "complexity": 23}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 9, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 1, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 1, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 45, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 4, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 24, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 25, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 7, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 699, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 4, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 24, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 23, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 699, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "5573da8afb21e6ed"}, "QuantumLivingUIAgent": {"agent": "QuantumLivingUIAgent", "filePath": "src/agent-core/unified/QuantumLivingUIAgent.ts", "timestamp": "2025-06-03T06:17:42.028Z", "metrics": {"lines": 695, "size": 21777, "interfaces": 4, "types": 0, "classes": 1, "methods": 32, "imports": 3, "exports": 1, "asyncMethods": 6, "complexity": 33}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 2, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 46, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 43, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 4, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 4, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 32, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 16, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 2, "severity": "HIGH", "description": "Input validation and sanitization"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 695, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 4, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 33, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 695, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 32, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "32 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "b844b96e0284977c"}, "UnifiedSpamControlSystem": {"agent": "UnifiedSpamControlSystem", "filePath": "src/agent-core/coordination/UnifiedSpamControlSystem.ts", "timestamp": "2025-06-03T06:17:42.030Z", "metrics": {"lines": 665, "size": 21390, "interfaces": 4, "types": 0, "classes": 1, "methods": 22, "imports": 4, "exports": 1, "asyncMethods": 2, "complexity": 76}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 4, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 1, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 73, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 50, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 4, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 22, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 3, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 6, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 3, "severity": "LOW", "description": "Caching implementation patterns"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 665, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 4, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 22, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 76, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 665, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "2280e30730ddc848"}, "UltimateTranscendenceOrchestrator": {"agent": "UltimateTranscendenceOrchestrator", "filePath": "src/agent-core/evolution/UltimateTranscendenceOrchestrator.ts", "timestamp": "2025-06-03T06:17:42.031Z", "metrics": {"lines": 665, "size": 24032, "interfaces": 0, "types": 0, "classes": 1, "methods": 33, "imports": 5, "exports": 5, "asyncMethods": 11, "complexity": 36}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 8, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 15, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 16, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 37, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 33, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 3, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 33, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 9, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 12, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 665, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 36, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 665, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 33, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "33 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "1773983675db8b50"}, "QualityMonitor": {"agent": "QualityMonitor", "filePath": "src/agent-core/quality-control/QualityMonitor.ts", "timestamp": "2025-06-03T06:17:42.032Z", "metrics": {"lines": 662, "size": 23794, "interfaces": 4, "types": 0, "classes": 1, "methods": 45, "imports": 2, "exports": 1, "asyncMethods": 6, "complexity": 107}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 4, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 2, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 4, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 105, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 5, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 45, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 16, "severity": "LOW", "description": "Asynchronous processing patterns"}, "SECURITY.Error Handling": {"matches": 19, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 8}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 662, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 4, "limit": 15, "status": "PASS"}, {"name": "God Object Check", "actual": 662, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 45, "limit": 30, "status": "FAIL"}, {"name": "Complexity Score", "actual": 107, "limit": 100, "status": "FAIL"}], "passRate": 60}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "45 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "MEDIUM", "hash": "5adecc917175c36c"}, "AdvancedResourceAllocationEngine": {"agent": "AdvancedResourceAllocationEngine", "filePath": "src/agent-core/orchestration/AdvancedResourceAllocationEngine.ts", "timestamp": "2025-06-03T06:17:42.033Z", "metrics": {"lines": 660, "size": 23776, "interfaces": 0, "types": 0, "classes": 1, "methods": 21, "imports": 5, "exports": 6, "asyncMethods": 3, "complexity": 19}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 6, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 9, "severity": "LOW", "description": "Event-driven architecture patterns"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 1, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 23, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 5, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 21, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 3, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 8, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 2, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 660, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 21, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 19, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 660, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "bc80e64b62b25bcc"}, "IntelligentComponentGenerator": {"agent": "IntelligentComponentGenerator", "filePath": "src/agent-core/systems/IntelligentComponentGenerator.ts", "timestamp": "2025-06-03T06:17:42.034Z", "metrics": {"lines": 655, "size": 22037, "interfaces": 0, "types": 0, "classes": 2, "methods": 21, "imports": 3, "exports": 9, "asyncMethods": 1, "complexity": 80}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 7, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 2, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 12, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 6, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 21, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 2, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 8, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 5, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 655, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 21, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 80, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 655, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "f05f3a3b25d2d531"}, "RealityTranscendenceEngine": {"agent": "RealityTranscendenceEngine", "filePath": "src/agent-core/transcendence/RealityTranscendenceEngine.ts", "timestamp": "2025-06-03T06:17:42.035Z", "metrics": {"lines": 655, "size": 22023, "interfaces": 0, "types": 0, "classes": 1, "methods": 37, "imports": 1, "exports": 5, "asyncMethods": 9, "complexity": 33}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 7, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 6, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 37, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 23, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 9, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Authentication": {"matches": 2, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 9, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 9}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 655, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 33, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 655, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 37, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "37 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "07acdbdfcc0db842"}, "AutonomyProgressionSystem": {"agent": "AutonomyProgressionSystem", "filePath": "src/agent-core/autonomy/AutonomyProgressionSystem.ts", "timestamp": "2025-06-03T06:17:42.037Z", "metrics": {"lines": 643, "size": 21337, "interfaces": 6, "types": 0, "classes": 1, "methods": 21, "imports": 2, "exports": 1, "asyncMethods": 11, "complexity": 52}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 7, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 72, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 23, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 19, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 6, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 21, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 40, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 3, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Input Validation": {"matches": 6, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 643, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 6, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 21, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 52, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 643, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "0656cc0a13504848"}, "AdvancedMLCoordinationLayer": {"agent": "AdvancedMLCoordinationLayer", "filePath": "src/agent-core/orchestration/AdvancedMLCoordinationLayer.ts", "timestamp": "2025-06-03T06:17:42.038Z", "metrics": {"lines": 643, "size": 23955, "interfaces": 2, "types": 0, "classes": 1, "methods": 48, "imports": 6, "exports": 1, "asyncMethods": 5, "complexity": 32}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 69, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 13, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 69, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 48, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 5, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 19, "severity": "LOW", "description": "Asynchronous processing patterns"}, "SECURITY.Error Handling": {"matches": 4, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 9}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 643, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 2, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 32, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 643, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 48, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "48 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "070d439964cf343a"}, "SelfModifyingCodeEngine": {"agent": "SelfModifyingCodeEngine", "filePath": "src/agent-core/evolution/SelfModifyingCodeEngine.ts", "timestamp": "2025-06-03T06:17:42.039Z", "metrics": {"lines": 635, "size": 21112, "interfaces": 8, "types": 0, "classes": 1, "methods": 32, "imports": 4, "exports": 1, "asyncMethods": 22, "complexity": 58}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 3, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 60, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 8, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 32, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 67, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Input Validation": {"matches": 5, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 15, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 9}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 635, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 8, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 58, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 635, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 32, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "32 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "c432c393a7c52ef6"}, "QuantumLivingMCPAgent": {"agent": "QuantumLivingMCPAgent", "filePath": "src/agent-core/unified/QuantumLivingMCPAgent.ts", "timestamp": "2025-06-03T06:17:42.040Z", "metrics": {"lines": 632, "size": 20975, "interfaces": 8, "types": 0, "classes": 0, "methods": 23, "imports": 5, "exports": 2, "asyncMethods": 7, "complexity": 21}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 9, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 1, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 72, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 33, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 3, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 8, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 23, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 4, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 26, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 3, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 7, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 14}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 632, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 8, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 23, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 21, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 632, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "71816309c855b033"}, "ConsciousnessSingularity": {"agent": "ConsciousnessSingularity", "filePath": "src/agent-core/consciousness/ConsciousnessSingularity.ts", "timestamp": "2025-06-03T06:17:42.041Z", "metrics": {"lines": 617, "size": 22122, "interfaces": 0, "types": 0, "classes": 1, "methods": 37, "imports": 1, "exports": 5, "asyncMethods": 9, "complexity": 33}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 7, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 10, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 24, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 37, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 23, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 9, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Authentication": {"matches": 4, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 9, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 617, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 33, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 617, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 37, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "37 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "e0805402e4a2d589"}, "IntelligentAgentCommunication": {"agent": "IntelligentAgentCommunication", "filePath": "src/agent-core/communication/IntelligentAgentCommunication.ts", "timestamp": "2025-06-03T06:17:42.042Z", "metrics": {"lines": 600, "size": 20410, "interfaces": 0, "types": 0, "classes": 1, "methods": 24, "imports": 1, "exports": 6, "asyncMethods": 11, "complexity": 43}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 2, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 2, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 131, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 24, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 39, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 3, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 14, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 600, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 24, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 43, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 600, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "2c62a87ca8038538"}, "PathwayValidator": {"agent": "PathwayValidator", "filePath": "src/agent-core/intelligence/PathwayValidator.ts", "timestamp": "2025-06-03T06:17:42.044Z", "metrics": {"lines": 565, "size": 20605, "interfaces": 0, "types": 0, "classes": 1, "methods": 14, "imports": 3, "exports": 4, "asyncMethods": 6, "complexity": 63}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 6, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 85, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 14, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 26, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Input Validation": {"matches": 5, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 8, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 565, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 14, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 63, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 565, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "086dfe156954e12d"}, "AutonomousNotificationSystem": {"agent": "AutonomousNotificationSystem", "filePath": "src/agent-core/communication/AutonomousNotificationSystem.ts", "timestamp": "2025-06-03T06:17:42.045Z", "metrics": {"lines": 564, "size": 18133, "interfaces": 3, "types": 0, "classes": 1, "methods": 14, "imports": 2, "exports": 2, "asyncMethods": 14, "complexity": 24}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 8, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 22, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 40, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 17, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 14, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 48, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 32, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 6, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 13}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 564, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 14, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 24, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 564, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "09c36e5656cd9e4d"}, "PrecisionPerformanceEngine": {"agent": "PrecisionPerformanceEngine", "filePath": "src/agent-core/engines/PrecisionPerformanceEngine.ts", "timestamp": "2025-06-03T06:17:42.046Z", "metrics": {"lines": 563, "size": 18471, "interfaces": 2, "types": 0, "classes": 1, "methods": 35, "imports": 0, "exports": 1, "asyncMethods": 20, "complexity": 67}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 24, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 35, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 69, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 11, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 7}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 563, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 2, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 67, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 563, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 35, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "35 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "34631c1a7f8c9bfa"}, "ThermalValidationFramework": {"agent": "ThermalValidationFramework", "filePath": "src/agent-core/resource-optimization/ThermalValidationFramework.ts", "timestamp": "2025-06-03T06:17:42.047Z", "metrics": {"lines": 560, "size": 21175, "interfaces": 3, "types": 0, "classes": 1, "methods": 18, "imports": 5, "exports": 1, "asyncMethods": 9, "complexity": 12}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 6, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 4, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 1, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 35, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 18, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 25, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 3, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 5, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 8, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 9, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 14}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 560, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 18, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 12, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 560, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "320303f5705fdbfb"}, "LocalAIAgentIntegration": {"agent": "LocalAIAgentIntegration", "filePath": "src/agent-core/integration/LocalAIAgentIntegration.ts", "timestamp": "2025-06-03T06:17:42.048Z", "metrics": {"lines": 551, "size": 17978, "interfaces": 3, "types": 0, "classes": 1, "methods": 17, "imports": 4, "exports": 1, "asyncMethods": 6, "complexity": 62}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 7, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 4, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 98, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 7, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 4, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 17, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 5, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 25, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 3, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 1, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 5, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 551, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 17, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 62, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 551, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "eb13951b5df73e9c"}, "AgentBase": {"agent": "AgentBase", "filePath": "src/agent-core/framework/AgentBase.ts", "timestamp": "2025-06-03T06:17:42.049Z", "metrics": {"lines": 535, "size": 16419, "interfaces": 0, "types": 0, "classes": 0, "methods": 11, "imports": 8, "exports": 1, "asyncMethods": 12, "complexity": 46}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 2, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 13, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 10, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 15, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 60, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Method Density": {"matches": 11, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 7, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 61, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 1, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 1, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 29, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 14}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 535, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 11, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 46, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 535, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "0b5747fa45dc3ae2"}, "AgentInteractionWorkflows": {"agent": "AgentInteractionWorkflows", "filePath": "src/agent-core/workflows/AgentInteractionWorkflows.ts", "timestamp": "2025-06-03T06:17:42.050Z", "metrics": {"lines": 521, "size": 16866, "interfaces": 0, "types": 0, "classes": 1, "methods": 15, "imports": 2, "exports": 4, "asyncMethods": 5, "complexity": 23}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 1, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 20, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 133, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 15, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 16, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 5, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 5, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 521, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 15, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 23, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 521, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "3439ab34731955bd"}, "SafetyProtocol": {"agent": "SafetyProtocol", "filePath": "src/agent-core/safety/SafetyProtocol.ts", "timestamp": "2025-06-03T06:17:42.051Z", "metrics": {"lines": 499, "size": 13702, "interfaces": 4, "types": 0, "classes": 2, "methods": 10, "imports": 2, "exports": 1, "asyncMethods": 7, "complexity": 46}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 14, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 4, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 10, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 16, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 6, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 4, "severity": "HIGH", "description": "Authentication and authorization patterns"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 499, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 4, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 10, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 46, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 499, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "931aa8e5e913529c"}, "EmergentBehaviorCultivator": {"agent": "EmergentBehaviorCultivator", "filePath": "src/agent-core/orchestration/EmergentBehaviorCultivator.ts", "timestamp": "2025-06-03T06:17:42.051Z", "metrics": {"lines": 490, "size": 16716, "interfaces": 3, "types": 0, "classes": 1, "methods": 26, "imports": 2, "exports": 1, "asyncMethods": 5, "complexity": 22}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 1, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 9, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 75, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 2, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 26, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 13, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 10, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 490, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 26, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 22, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 490, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "7f07e480231441e6"}, "IntelligentActivityManager": {"agent": "IntelligentActivityManager", "filePath": "src/agent-core/optimization/IntelligentActivityManager.ts", "timestamp": "2025-06-03T06:17:42.052Z", "metrics": {"lines": 487, "size": 15695, "interfaces": 4, "types": 0, "classes": 1, "methods": 20, "imports": 0, "exports": 1, "asyncMethods": 2, "complexity": 65}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 2, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 12, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 3, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 4, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 20, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 4, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 3, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 62, "severity": "LOW", "description": "Caching implementation patterns"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 487, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 4, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 20, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 65, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 487, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "83d06434eae1d8d9"}, "AgentPriorityMatrix": {"agent": "AgentPriorityMatrix", "filePath": "src/agent-core/orchestration/AgentPriorityMatrix.ts", "timestamp": "2025-06-03T06:17:42.053Z", "metrics": {"lines": 486, "size": 15919, "interfaces": 0, "types": 0, "classes": 1, "methods": 18, "imports": 3, "exports": 5, "asyncMethods": 0, "complexity": 19}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 7, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 4, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 8, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 6, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 4, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 18, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 486, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 18, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 19, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 486, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "e0e99eeb97d0bf00"}, "ResourceEconomicsEngine": {"agent": "ResourceEconomicsEngine", "filePath": "src/agent-core/advanced-modification/ResourceEconomicsEngine.ts", "timestamp": "2025-06-03T06:17:42.054Z", "metrics": {"lines": 478, "size": 15492, "interfaces": 6, "types": 0, "classes": 1, "methods": 22, "imports": 2, "exports": 1, "asyncMethods": 12, "complexity": 23}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 1, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 5, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 35, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 1, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 6, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 22, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 30, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 3, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 3, "severity": "LOW", "description": "Caching implementation patterns"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 478, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 6, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 22, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 23, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 478, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "26df6e74441e8b90"}, "SpecializedAgentCoordinator": {"agent": "SpecializedAgentCoordinator", "filePath": "src/agent-core/orchestration/SpecializedAgentCoordinator.ts", "timestamp": "2025-06-03T06:17:42.055Z", "metrics": {"lines": 467, "size": 17167, "interfaces": 3, "types": 0, "classes": 1, "methods": 22, "imports": 2, "exports": 2, "asyncMethods": 7, "complexity": 44}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 10, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 25, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 6, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 6, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 5, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 22, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 19, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 1, "severity": "HIGH", "description": "Authentication and authorization patterns"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 467, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 22, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 44, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 467, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "96863d2433091c84"}, "TelemetryCollector": {"agent": "TelemetryCollector", "filePath": "src/agent-core/telemetry/TelemetryCollector.ts", "timestamp": "2025-06-03T06:17:42.056Z", "metrics": {"lines": 460, "size": 13143, "interfaces": 3, "types": 0, "classes": 1, "methods": 11, "imports": 3, "exports": 1, "asyncMethods": 3, "complexity": 46}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 8, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 12, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 11, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 7, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 2, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 4, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 460, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 11, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 46, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 460, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "2dee269b7ef1f4d3"}, "IntelligentResourceScaling": {"agent": "IntelligentResourceScaling", "filePath": "src/agent-core/resource-scaling/IntelligentResourceScaling.ts", "timestamp": "2025-06-03T06:17:42.057Z", "metrics": {"lines": 457, "size": 15503, "interfaces": 3, "types": 0, "classes": 1, "methods": 13, "imports": 1, "exports": 1, "asyncMethods": 1, "complexity": 9}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 2, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 13, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 58, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 4, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 13, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 2, "severity": "LOW", "description": "Asynchronous processing patterns"}}, "warnings": [], "totalPatterns": 8}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 457, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 13, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 9, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 457, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "be60c70c5d328c2d"}, "MCPIntegration": {"agent": "MCPIntegration", "filePath": "src/agent-core/integrations/MCPIntegration.ts", "timestamp": "2025-06-03T06:17:42.058Z", "metrics": {"lines": 447, "size": 13736, "interfaces": 0, "types": 0, "classes": 1, "methods": 14, "imports": 4, "exports": 4, "asyncMethods": 11, "complexity": 38}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 10, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 26, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 14, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 42, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 4, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Input Validation": {"matches": 4, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 23, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 447, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 14, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 38, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 447, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "1f9f37d15dff2abf"}, "PolicyEngine": {"agent": "PolicyEngine", "filePath": "src/agent-core/policy-engine/PolicyEngine.ts", "timestamp": "2025-06-03T06:17:42.059Z", "metrics": {"lines": 439, "size": 11969, "interfaces": 0, "types": 0, "classes": 1, "methods": 10, "imports": 3, "exports": 5, "asyncMethods": 7, "complexity": 42}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 2, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 20, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 4, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 10, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 2, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 18, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 4, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 2, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 10, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 12}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 439, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 10, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 42, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 439, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "8228ba4613db03bc"}, "OrchestrationDemo": {"agent": "OrchestrationDemo", "filePath": "src/agent-core/integrations/OrchestrationDemo.ts", "timestamp": "2025-06-03T06:17:42.059Z", "metrics": {"lines": 428, "size": 14090, "interfaces": 0, "types": 0, "classes": 1, "methods": 13, "imports": 3, "exports": 3, "asyncMethods": 9, "complexity": 37}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 4, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 4, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 46, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 30, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Method Density": {"matches": 13, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 36, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 1, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Authentication": {"matches": 3, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 428, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 13, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 37, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 428, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "41cd827fd1d14d58"}, "AutonomousCrossAgentCommunication": {"agent": "AutonomousCrossAgentCommunication", "filePath": "src/agent-core/communication/AutonomousCrossAgentCommunication.ts", "timestamp": "2025-06-03T06:17:42.060Z", "metrics": {"lines": 421, "size": 15259, "interfaces": 3, "types": 0, "classes": 1, "methods": 12, "imports": 2, "exports": 2, "asyncMethods": 9, "complexity": 19}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 5, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 6, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 13, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 4, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 15, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 91, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 12, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 29, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 7, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Authentication": {"matches": 1, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 14}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 421, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 3, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 12, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 19, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 421, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "3519e09db3ff7e06"}, "AutonomyProgressionEngine": {"agent": "AutonomyProgressionEngine", "filePath": "src/agent-core/engines/AutonomyProgressionEngine.ts", "timestamp": "2025-06-03T06:17:42.061Z", "metrics": {"lines": 418, "size": 14692, "interfaces": 2, "types": 0, "classes": 1, "methods": 31, "imports": 3, "exports": 1, "asyncMethods": 11, "complexity": 19}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 55, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 31, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 31, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 3, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 40, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 418, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 2, "limit": 15, "status": "PASS"}, {"name": "Complexity Score", "actual": 19, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 418, "limit": 2000, "status": "PASS"}], "failed": [{"name": "Method Limit", "actual": 31, "limit": 30, "status": "FAIL"}], "passRate": 80}, "recommendations": [{"type": "REFACTORING", "priority": "MEDIUM", "title": "Method Density Reduction", "description": "31 methods found (limit: 30). Consider extracting utility classes or services.", "estimatedEffort": "MEDIUM"}], "severity": "LOW", "hash": "ec2ebdece13b4c24"}, "AutonomousEvolutionAccelerator": {"agent": "AutonomousEvolutionAccelerator", "filePath": "src/agent-core/evolution/AutonomousEvolutionAccelerator.ts", "timestamp": "2025-06-03T06:17:42.062Z", "metrics": {"lines": 378, "size": 13644, "interfaces": 0, "types": 0, "classes": 1, "methods": 20, "imports": 1, "exports": 4, "asyncMethods": 7, "complexity": 15}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 5, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 2, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 5, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 20, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 17, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 9, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 9}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 378, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 20, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 15, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 378, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "f6e57bfb77f63067"}, "ClaudeMonitor": {"agent": "ClaudeMonitor", "filePath": "src/agent-core/communication/ClaudeMonitor.ts", "timestamp": "2025-06-03T06:17:42.063Z", "metrics": {"lines": 365, "size": 11569, "interfaces": 0, "types": 0, "classes": 1, "methods": 17, "imports": 2, "exports": 6, "asyncMethods": 8, "complexity": 40}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 2, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 5, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 17, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 17, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 33, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 5, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 10, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 9}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 365, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 17, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 40, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 365, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "e5b2b3df0f334591"}, "RealAgentDemo": {"agent": "RealAgentDemo", "filePath": "src/agent-core/demo/RealAgentDemo.ts", "timestamp": "2025-06-03T06:17:42.063Z", "metrics": {"lines": 357, "size": 12283, "interfaces": 0, "types": 0, "classes": 1, "methods": 10, "imports": 5, "exports": 6, "asyncMethods": 6, "complexity": 4}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 1, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 20, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 7, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Method Density": {"matches": 10, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 5, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 32, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 3, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 2, "severity": "HIGH", "description": "Authentication and authorization patterns"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 357, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 10, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 4, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 357, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "cdf7138b2e171074"}, "AgentEventBus": {"agent": "AgentEventBus", "filePath": "src/agent-core/communication/AgentEventBus.ts", "timestamp": "2025-06-03T06:17:42.064Z", "metrics": {"lines": 354, "size": 9721, "interfaces": 0, "types": 0, "classes": 1, "methods": 18, "imports": 1, "exports": 4, "asyncMethods": 0, "complexity": 16}, "patterns": {"found": {"ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Observer Pattern": {"matches": 10, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 3, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 2, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 16, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 8, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 18, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Memory Management": {"matches": 3, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 1, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 354, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 18, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 16, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 354, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "1182c1af0016ba3b"}, "AgentToAICommunication": {"agent": "AgentToAICommunication", "filePath": "src/agent-core/communication/AgentToAICommunication.ts", "timestamp": "2025-06-03T06:17:42.064Z", "metrics": {"lines": 353, "size": 11228, "interfaces": 0, "types": 0, "classes": 1, "methods": 14, "imports": 0, "exports": 6, "asyncMethods": 3, "complexity": 26}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 1, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 44, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 14, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 9, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 12, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 353, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 14, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 26, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 353, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "44c6ecba76425e0b"}, "SmartErrorRecovery": {"agent": "SmartErrorRecovery", "filePath": "src/agent-core/systems/SmartErrorRecovery.ts", "timestamp": "2025-06-03T06:17:42.065Z", "metrics": {"lines": 347, "size": 11239, "interfaces": 0, "types": 0, "classes": 2, "methods": 16, "imports": 0, "exports": 4, "asyncMethods": 4, "complexity": 34}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 7, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 1, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 24, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 3, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 16, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 26, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 12, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 18, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 11}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 347, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 16, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 34, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 347, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "d61c875c2d30ad99"}, "SystemLogsIntegration": {"agent": "SystemLogsIntegration", "filePath": "src/agent-core/monitoring/SystemLogsIntegration.ts", "timestamp": "2025-06-03T06:17:42.066Z", "metrics": {"lines": 328, "size": 9350, "interfaces": 0, "types": 0, "classes": 1, "methods": 13, "imports": 3, "exports": 4, "asyncMethods": 5, "complexity": 36}, "patterns": {"found": {"ARCHITECTURE_STYLES.Observer Pattern": {"matches": 3, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 11, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 13, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 14, "severity": "LOW", "description": "Asynchronous processing patterns"}, "SECURITY.Error Handling": {"matches": 9, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 6}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 328, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 13, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 36, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 328, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "23b8e81bd1de6ef4"}, "SmartReportManager": {"agent": "SmartReportManager", "filePath": "src/agent-core/utils/SmartReportManager.ts", "timestamp": "2025-06-03T06:17:42.066Z", "metrics": {"lines": 321, "size": 8530, "interfaces": 2, "types": 0, "classes": 1, "methods": 14, "imports": 0, "exports": 1, "asyncMethods": 0, "complexity": 25}, "patterns": {"found": {"AGENTIC_SPECIFIC.Learning Systems": {"matches": 2, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 14, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 1, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 5}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 321, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 2, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 14, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 25, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 321, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "e6acef1ea6f76a63"}, "TriggerRealCommunication": {"agent": "TriggerRealCommunication", "filePath": "src/agent-core/demo/TriggerRealCommunication.ts", "timestamp": "2025-06-03T06:17:42.067Z", "metrics": {"lines": 305, "size": 12046, "interfaces": 0, "types": 0, "classes": 1, "methods": 5, "imports": 5, "exports": 6, "asyncMethods": 5, "complexity": 1}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 1, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 7, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 22, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 7, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 1, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 5, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 5, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 39, "severity": "LOW", "description": "Asynchronous processing patterns"}, "SECURITY.Input Validation": {"matches": 2, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 3, "severity": "HIGH", "description": "Authentication and authorization patterns"}}, "warnings": [], "totalPatterns": 10}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 305, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 5, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 1, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 305, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "398394f8442e5c61"}, "PredictiveGoalForecasting": {"agent": "PredictiveGoalForecasting", "filePath": "src/agent-core/autonomy/PredictiveGoalForecasting.ts", "timestamp": "2025-06-03T06:17:42.067Z", "metrics": {"lines": 290, "size": 9915, "interfaces": 2, "types": 0, "classes": 1, "methods": 6, "imports": 0, "exports": 2, "asyncMethods": 7, "complexity": 8}, "patterns": {"found": {"ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 14, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 11, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 6, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 21, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 2, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}}, "warnings": [], "totalPatterns": 8}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 290, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 2, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 6, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 8, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 290, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "1e37e721aef52bdf"}, "IntelligentPathwayOrchestrator": {"agent": "IntelligentPathwayOrchestrator", "filePath": "src/agent-core/integrations/IntelligentPathwayOrchestrator.ts", "timestamp": "2025-06-03T06:17:42.068Z", "metrics": {"lines": 276, "size": 8718, "interfaces": 0, "types": 0, "classes": 1, "methods": 6, "imports": 1, "exports": 4, "asyncMethods": 2, "complexity": 25}, "patterns": {"found": {"ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"matches": 1, "severity": "LOW", "description": "Strategy pattern implementation"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 2, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 33, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 21, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 6, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 4, "severity": "LOW", "description": "Asynchronous processing patterns"}}, "warnings": [], "totalPatterns": 8}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 276, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 6, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 25, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 276, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "7d4649434d939161"}, "OptimizedAgentBase": {"agent": "OptimizedAgentBase", "filePath": "src/agent-core/base/OptimizedAgentBase.ts", "timestamp": "2025-06-03T06:17:42.069Z", "metrics": {"lines": 270, "size": 7972, "interfaces": 0, "types": 0, "classes": 0, "methods": 3, "imports": 4, "exports": 1, "asyncMethods": 0, "complexity": 27}, "patterns": {"found": {"ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 1, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 19, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 1, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Method Density": {"matches": 3, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 4, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 15, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 1, "severity": "MEDIUM", "description": "Memory management patterns"}, "PERFORMANCE.Caching": {"matches": 11, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 2, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 9}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 270, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 3, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 27, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 270, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "cc09b0f6e904727d"}, "AgentTypes": {"agent": "AgentTypes", "filePath": "src/agent-core/interfaces/AgentTypes.ts", "timestamp": "2025-06-03T06:17:42.069Z", "metrics": {"lines": 259, "size": 5362, "interfaces": 0, "types": 0, "classes": 0, "methods": 0, "imports": 0, "exports": 25, "asyncMethods": 0, "complexity": 17}, "patterns": {"found": {"ARCHITECTURE_STYLES.God Object": {"matches": 3, "severity": "CRITICAL", "description": "Single class handling too many responsibilities"}, "AGENTIC_SPECIFIC.Agent Communication": {"matches": 1, "severity": "HIGH", "description": "Inter-agent communication patterns"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 12, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 18, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "SECURITY.Input Validation": {"matches": 1, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Authentication": {"matches": 4, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 1, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 7}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 259, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 0, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 17, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 259, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "65a1c5ec0f8b1c94"}, "AgentMigrationManager": {"agent": "AgentMigrationManager", "filePath": "src/agent-core/AgentMigrationManager.ts", "timestamp": "2025-06-03T06:17:42.070Z", "metrics": {"lines": 243, "size": 7934, "interfaces": 1, "types": 0, "classes": 1, "methods": 14, "imports": 1, "exports": 1, "asyncMethods": 6, "complexity": 8}, "patterns": {"found": {"ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 28, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 3, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 1, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 14, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 22, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 3, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Input Validation": {"matches": 3, "severity": "HIGH", "description": "Input validation and sanitization"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 9}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 243, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 1, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 14, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 8, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 243, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "f9150716654fc990"}, "IntelligentPathwaySelector": {"agent": "IntelligentPathwaySelector", "filePath": "src/agent-core/intelligence/IntelligentPathwaySelector.ts", "timestamp": "2025-06-03T06:17:42.070Z", "metrics": {"lines": 218, "size": 7873, "interfaces": 0, "types": 0, "classes": 1, "methods": 1, "imports": 0, "exports": 4, "asyncMethods": 0, "complexity": 19}, "patterns": {"found": {"ARCHITECTURE_STYLES.Observer Pattern": {"matches": 1, "severity": "LOW", "description": "Event-driven architecture patterns"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 14, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 19, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"matches": 5, "severity": "HIGH", "description": "Agent coordination and orchestration"}, "CODE_QUALITY.Interface Explosion": {"matches": 2, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 1, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Caching": {"matches": 3, "severity": "LOW", "description": "Caching implementation patterns"}}, "warnings": [], "totalPatterns": 7}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 218, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 1, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 19, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 218, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "d27859fe3cf6575d"}, "MethodReplacementEngine": {"agent": "MethodReplacementEngine", "filePath": "src/agent-core/migration/MethodReplacementEngine.ts", "timestamp": "2025-06-03T06:17:42.070Z", "metrics": {"lines": 211, "size": 6915, "interfaces": 1, "types": 0, "classes": 1, "methods": 7, "imports": 1, "exports": 1, "asyncMethods": 1, "complexity": 11}, "patterns": {"found": {"ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 4, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 1, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 40, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 1, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "CODE_QUALITY.Method Density": {"matches": 7, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 3, "severity": "LOW", "description": "Asynchronous processing patterns"}, "SECURITY.Authentication": {"matches": 1, "severity": "HIGH", "description": "Authentication and authorization patterns"}, "SECURITY.Error Handling": {"matches": 6, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 9}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 211, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 1, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 7, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 11, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 211, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "e419c227d468236e"}, "AgentResponseReceiver": {"agent": "AgentResponseReceiver", "filePath": "src/agent-core/communication/AgentResponseReceiver.ts", "timestamp": "2025-06-03T06:17:42.071Z", "metrics": {"lines": 191, "size": 6036, "interfaces": 0, "types": 0, "classes": 1, "methods": 0, "imports": 2, "exports": 5, "asyncMethods": 2, "complexity": 20}, "patterns": {"found": {"ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 3, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 9, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 1, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}, "PERFORMANCE.Async/Await Usage": {"matches": 15, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 4, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 8, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 6}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 191, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 0, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 20, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 191, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "f1d92a3da3668cec"}, "SmartMethodWrapper": {"agent": "SmartMethodWrapper", "filePath": "src/agent-core/migration/SmartMethodWrapper.ts", "timestamp": "2025-06-03T06:17:42.071Z", "metrics": {"lines": 159, "size": 4302, "interfaces": 0, "types": 0, "classes": 1, "methods": 4, "imports": 1, "exports": 3, "asyncMethods": 5, "complexity": 7}, "patterns": {"found": {"ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 6, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 17, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Method Density": {"matches": 4, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 14, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Caching": {"matches": 1, "severity": "LOW", "description": "Caching implementation patterns"}, "SECURITY.Error Handling": {"matches": 3, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 6}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 159, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 4, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 7, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 159, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "32d2153348cca1d3"}, "MCPIntegrationTest": {"agent": "MCPIntegrationTest", "filePath": "src/agent-core/integrations/MCPIntegrationTest.ts", "timestamp": "2025-06-03T06:17:42.071Z", "metrics": {"lines": 153, "size": 4513, "interfaces": 0, "types": 0, "classes": 1, "methods": 4, "imports": 2, "exports": 2, "asyncMethods": 4, "complexity": 3}, "patterns": {"found": {"ARCHITECTURE_STYLES.Singleton Pattern": {"matches": 2, "severity": "MEDIUM", "description": "Singleton pattern usage - check if justified"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 33, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Method Density": {"matches": 4, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "PERFORMANCE.Async/Await Usage": {"matches": 18, "severity": "LOW", "description": "Asynchronous processing patterns"}, "SECURITY.Error Handling": {"matches": 6, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 5}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 153, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 4, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 3, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 153, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "efe36ce68ed9fe69"}, "AgentWebSocketBridge": {"agent": "AgentWebSocketBridge", "filePath": "src/agent-core/communication/AgentWebSocketBridge.ts", "timestamp": "2025-06-03T06:17:42.072Z", "metrics": {"lines": 133, "size": 3439, "interfaces": 0, "types": 0, "classes": 1, "methods": 1, "imports": 1, "exports": 3, "asyncMethods": 1, "complexity": 13}, "patterns": {"found": {"AGENTIC_SPECIFIC.Learning Systems": {"matches": 3, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Method Density": {"matches": 1, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 5, "severity": "LOW", "description": "Asynchronous processing patterns"}, "PERFORMANCE.Memory Management": {"matches": 2, "severity": "MEDIUM", "description": "Memory management patterns"}, "SECURITY.Error Handling": {"matches": 4, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 6}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 133, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 1, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 13, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 133, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "98f49b5d7bb1325e"}, "EngineBase": {"agent": "EngineBase", "filePath": "src/agent-core/framework/EngineBase.ts", "timestamp": "2025-06-03T06:17:42.072Z", "metrics": {"lines": 65, "size": 1530, "interfaces": 0, "types": 0, "classes": 0, "methods": 2, "imports": 1, "exports": 1, "asyncMethods": 2, "complexity": 3}, "patterns": {"found": {"AGENTIC_SPECIFIC.Autonomous Decision": {"matches": 1, "severity": "HIGH", "description": "Autonomous decision-making capabilities"}, "AGENTIC_SPECIFIC.Learning Systems": {"matches": 5, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Method Density": {"matches": 2, "severity": "HIGH", "description": "Too many methods in single class", "threshold": 50}, "CODE_QUALITY.Circular Dependencies": {"matches": 1, "severity": "MEDIUM", "description": "Potential circular dependency patterns"}, "PERFORMANCE.Async/Await Usage": {"matches": 8, "severity": "LOW", "description": "Asynchronous processing patterns"}, "SECURITY.Error Handling": {"matches": 7, "severity": "MEDIUM", "description": "Error handling implementation"}}, "warnings": [], "totalPatterns": 6}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 65, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 2, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 3, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 65, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "b84fbe042cfb8698"}, "EngineTypes": {"agent": "EngineTypes", "filePath": "src/agent-core/interfaces/EngineTypes.ts", "timestamp": "2025-06-03T06:17:42.072Z", "metrics": {"lines": 24, "size": 518, "interfaces": 0, "types": 0, "classes": 0, "methods": 0, "imports": 0, "exports": 3, "asyncMethods": 0, "complexity": 1}, "patterns": {"found": {"AGENTIC_SPECIFIC.Learning Systems": {"matches": 1, "severity": "HIGH", "description": "Machine learning and adaptation systems"}, "CODE_QUALITY.Interface Explosion": {"matches": 3, "severity": "HIGH", "description": "Too many interfaces in single file", "threshold": 20}}, "warnings": [], "totalPatterns": 2}, "qualityGates": {"passed": [{"name": "File Size Limit", "actual": 24, "limit": 1500, "status": "PASS"}, {"name": "Interface Limit", "actual": 0, "limit": 15, "status": "PASS"}, {"name": "Method Limit", "actual": 0, "limit": 30, "status": "PASS"}, {"name": "Complexity Score", "actual": 1, "limit": 100, "status": "PASS"}, {"name": "God Object Check", "actual": 24, "limit": 2000, "status": "PASS"}], "failed": [], "passRate": 100}, "recommendations": [], "severity": "LOW", "hash": "dd54ff96814167d8"}}, "patterns": {"ARCHITECTURE_STYLES.God Object": {"occurrences": 91, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "MLCoordinationLayer", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "VectorMemory", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "SelfImprovementEngine", "LivingAgentBase", "HybridRouter", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "ChatBridgeMonitor", "LivingUIAgent", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "AutonomousPlatformEvolution", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "QuantumDecisionNetwork", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "QualityMonitor", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "RealityTranscendenceEngine", "AutonomyProgressionSystem", "AdvancedMLCoordinationLayer", "SelfModifyingCodeEngine", "QuantumLivingMCPAgent", "ConsciousnessSingularity", "IntelligentAgentCommunication", "PathwayValidator", "AutonomousNotificationSystem", "PrecisionPerformanceEngine", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentBase", "AgentInteractionWorkflows", "SafetyProtocol", "EmergentBehaviorCultivator", "IntelligentActivityManager", "AgentPriorityMatrix", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "TelemetryCollector", "IntelligentResourceScaling", "MCPIntegration", "PolicyEngine", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "AutonomyProgressionEngine", "AutonomousEvolutionAccelerator", "ClaudeMonitor", "RealAgentDemo", "AgentToAICommunication", "SmartErrorRecovery", "TriggerRealCommunication", "AgentTypes"], "totalMatches": 459, "severity": "CRITICAL"}, "ARCHITECTURE_STYLES.Singleton Pattern": {"occurrences": 78, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "SelfImprovementEngine", "LivingAgentBase", "HybridRouter", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "ChatBridgeMonitor", "LivingUIAgent", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "AutonomyProgressionSystem", "QuantumLivingMCPAgent", "IntelligentAgentCommunication", "PathwayValidator", "AutonomousNotificationSystem", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentBase", "AgentInteractionWorkflows", "IntelligentActivityManager", "AgentPriorityMatrix", "MCPIntegration", "PolicyEngine", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "AutonomyProgressionEngine", "ClaudeMonitor", "AgentEventBus", "AgentToAICommunication", "SmartErrorRecovery", "PredictiveGoalForecasting", "IntelligentPathwayOrchestrator", "OptimizedAgentBase", "AgentMigrationManager", "MethodReplacementEngine", "AgentResponseReceiver", "SmartMethodWrapper", "MCPIntegrationTest"], "totalMatches": 303, "severity": "MEDIUM"}, "ARCHITECTURE_STYLES.Observer Pattern": {"occurrences": 72, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "UIAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "VectorMemory", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "SelfImprovementEngine", "LivingAgentBase", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "LivingUIAgent", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "QuantumDecisionNetwork", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "QualityMonitor", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "RealityTranscendenceEngine", "AutonomyProgressionSystem", "QuantumLivingMCPAgent", "ConsciousnessSingularity", "IntelligentAgentCommunication", "AutonomousNotificationSystem", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentBase", "AgentInteractionWorkflows", "SafetyProtocol", "EmergentBehaviorCultivator", "IntelligentActivityManager", "AgentPriorityMatrix", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "TelemetryCollector", "IntelligentResourceScaling", "MCPIntegration", "PolicyEngine", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "AutonomousEvolutionAccelerator", "AgentEventBus", "SystemLogsIntegration", "IntelligentPathwaySelector"], "totalMatches": 382, "severity": "LOW"}, "ARCHITECTURE_STYLES.Strategy Pattern": {"occurrences": 25, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "AutonomousIntelligenceAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "HybridRouter", "OpsAgent", "ResourceOptimizationEngine", "CrossAgentCommunicationEngine", "AutonomousPlatformEvolution", "ThermalProtectionSystem", "AdvancedResourceAllocationEngine", "SmartErrorRecovery", "IntelligentPathwayOrchestrator"], "totalMatches": 66, "severity": "LOW"}, "AGENTIC_SPECIFIC.Agent Communication": {"occurrences": 28, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "SecurityAgent", "PerformanceMonitoringAgent", "ConfigAgent", "AgentStrategicCoordinator", "<PERSON><PERSON><PERSON>", "LivingAgentBase", "OpsAgent", "AdvancedCrossAgentCommunication", "LivingUIAgent", "CrossAgentCommunicationEngine", "QuantumLivingMCPAgent", "AgentBase", "SpecializedAgentCoordinator", "AutonomousCrossAgentCommunication", "AgentEventBus", "AgentToAICommunication", "AgentTypes"], "totalMatches": 213, "severity": "HIGH"}, "AGENTIC_SPECIFIC.Autonomous Decision": {"occurrences": 72, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "VectorMemory", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "LivingAgentBase", "HybridRouter", "OpsAgent", "AdvancedCrossAgentCommunication", "ChatBridgeMonitor", "LivingUIAgent", "LocalIntelligenceEngine", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "AutonomousPlatformEvolution", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "QualityMonitor", "AutonomyProgressionSystem", "AdvancedMLCoordinationLayer", "QuantumLivingMCPAgent", "ConsciousnessSingularity", "IntelligentAgentCommunication", "AutonomousNotificationSystem", "ThermalValidationFramework", "AgentBase", "EmergentBehaviorCultivator", "AgentPriorityMatrix", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "IntelligentResourceScaling", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "AutonomyProgressionEngine", "AutonomousEvolutionAccelerator", "ClaudeMonitor", "RealAgentDemo", "AgentEventBus", "TriggerRealCommunication", "PredictiveGoalForecasting", "IntelligentPathwayOrchestrator", "IntelligentPathwaySelector", "MethodReplacementEngine", "EngineBase"], "totalMatches": 2719, "severity": "HIGH"}, "AGENTIC_SPECIFIC.Learning Systems": {"occurrences": 106, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "MLCoordinationLayer", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "VectorMemory", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "SelfImprovementEngine", "LivingAgentBase", "HybridRouter", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "ChatBridgeMonitor", "LivingUIAgent", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "AutonomousPlatformEvolution", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "QuantumDecisionNetwork", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "QualityMonitor", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "RealityTranscendenceEngine", "AutonomyProgressionSystem", "AdvancedMLCoordinationLayer", "SelfModifyingCodeEngine", "QuantumLivingMCPAgent", "ConsciousnessSingularity", "IntelligentAgentCommunication", "PathwayValidator", "AutonomousNotificationSystem", "PrecisionPerformanceEngine", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentBase", "AgentInteractionWorkflows", "SafetyProtocol", "EmergentBehaviorCultivator", "IntelligentActivityManager", "AgentPriorityMatrix", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "TelemetryCollector", "IntelligentResourceScaling", "MCPIntegration", "PolicyEngine", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "AutonomyProgressionEngine", "AutonomousEvolutionAccelerator", "ClaudeMonitor", "RealAgentDemo", "AgentEventBus", "AgentToAICommunication", "SmartErrorRecovery", "SystemLogsIntegration", "SmartReportManager", "TriggerRealCommunication", "PredictiveGoalForecasting", "IntelligentPathwayOrchestrator", "OptimizedAgentBase", "AgentTypes", "AgentMigrationManager", "IntelligentPathwaySelector", "MethodReplacementEngine", "AgentResponseReceiver", "SmartMethodWrapper", "MCPIntegrationTest", "AgentWebSocketBridge", "EngineBase", "EngineTypes"], "totalMatches": 11230, "severity": "HIGH"}, "AGENTIC_SPECIFIC.Coordination Patterns": {"occurrences": 73, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "MLCoordinationLayer", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "LivingUIAgent", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "AutonomousPlatformEvolution", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "QuantumDecisionNetwork", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "AdvancedResourceAllocationEngine", "AutonomyProgressionSystem", "AdvancedMLCoordinationLayer", "QuantumLivingMCPAgent", "AutonomousNotificationSystem", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentBase", "AgentInteractionWorkflows", "EmergentBehaviorCultivator", "IntelligentActivityManager", "AgentPriorityMatrix", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "IntelligentResourceScaling", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "RealAgentDemo", "AgentEventBus", "SmartErrorRecovery", "TriggerRealCommunication", "IntelligentPathwayOrchestrator", "OptimizedAgentBase", "AgentMigrationManager", "IntelligentPathwaySelector"], "totalMatches": 1895, "severity": "HIGH"}, "CODE_QUALITY.Interface Explosion": {"occurrences": 97, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "MLCoordinationLayer", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "VectorMemory", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "SelfImprovementEngine", "LivingAgentBase", "HybridRouter", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "ChatBridgeMonitor", "LivingUIAgent", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "AutonomousPlatformEvolution", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "QuantumDecisionNetwork", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "QualityMonitor", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "RealityTranscendenceEngine", "AutonomyProgressionSystem", "AdvancedMLCoordinationLayer", "SelfModifyingCodeEngine", "QuantumLivingMCPAgent", "ConsciousnessSingularity", "IntelligentAgentCommunication", "PathwayValidator", "AutonomousNotificationSystem", "PrecisionPerformanceEngine", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentInteractionWorkflows", "SafetyProtocol", "EmergentBehaviorCultivator", "IntelligentActivityManager", "AgentPriorityMatrix", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "TelemetryCollector", "IntelligentResourceScaling", "MCPIntegration", "PolicyEngine", "AutonomousCrossAgentCommunication", "AutonomyProgressionEngine", "AutonomousEvolutionAccelerator", "ClaudeMonitor", "AgentEventBus", "AgentToAICommunication", "SmartErrorRecovery", "SystemLogsIntegration", "SmartReportManager", "TriggerRealCommunication", "PredictiveGoalForecasting", "IntelligentPathwayOrchestrator", "AgentTypes", "AgentMigrationManager", "IntelligentPathwaySelector", "MethodReplacementEngine", "AgentResponseReceiver", "EngineTypes"], "totalMatches": 1844, "severity": "HIGH"}, "CODE_QUALITY.Method Density": {"occurrences": 103, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "MLCoordinationLayer", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "VectorMemory", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "SelfImprovementEngine", "LivingAgentBase", "HybridRouter", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "ChatBridgeMonitor", "LivingUIAgent", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "AutonomousPlatformEvolution", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "QuantumDecisionNetwork", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "QualityMonitor", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "RealityTranscendenceEngine", "AutonomyProgressionSystem", "AdvancedMLCoordinationLayer", "SelfModifyingCodeEngine", "QuantumLivingMCPAgent", "ConsciousnessSingularity", "IntelligentAgentCommunication", "PathwayValidator", "AutonomousNotificationSystem", "PrecisionPerformanceEngine", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentBase", "AgentInteractionWorkflows", "SafetyProtocol", "EmergentBehaviorCultivator", "IntelligentActivityManager", "AgentPriorityMatrix", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "TelemetryCollector", "IntelligentResourceScaling", "MCPIntegration", "PolicyEngine", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "AutonomyProgressionEngine", "AutonomousEvolutionAccelerator", "ClaudeMonitor", "RealAgentDemo", "AgentEventBus", "AgentToAICommunication", "SmartErrorRecovery", "SystemLogsIntegration", "SmartReportManager", "TriggerRealCommunication", "PredictiveGoalForecasting", "IntelligentPathwayOrchestrator", "OptimizedAgentBase", "AgentMigrationManager", "IntelligentPathwaySelector", "MethodReplacementEngine", "SmartMethodWrapper", "MCPIntegrationTest", "AgentWebSocketBridge", "EngineBase"], "totalMatches": 2904, "severity": "HIGH"}, "CODE_QUALITY.File Size": {"occurrences": 16, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent"], "totalMatches": 41990, "severity": "MEDIUM"}, "CODE_QUALITY.Circular Dependencies": {"occurrences": 69, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "ConfigAgent", "AgentStrategicCoordinator", "VectorMemory", "<PERSON><PERSON><PERSON>", "SelfImprovementEngine", "LivingAgentBase", "HybridRouter", "OpsAgent", "AgentConflictDetectionEngine", "ChatBridgeMonitor", "LivingUIAgent", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "QuantumDecisionNetwork", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "AdvancedResourceAllocationEngine", "AutonomyProgressionSystem", "AdvancedMLCoordinationLayer", "SelfModifyingCodeEngine", "QuantumLivingMCPAgent", "IntelligentAgentCommunication", "PathwayValidator", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentBase", "AgentInteractionWorkflows", "SafetyProtocol", "EmergentBehaviorCultivator", "AgentPriorityMatrix", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "TelemetryCollector", "PolicyEngine", "AutonomyProgressionEngine", "RealAgentDemo", "TriggerRealCommunication", "OptimizedAgentBase", "MethodReplacementEngine", "AgentWebSocketBridge", "EngineBase"], "totalMatches": 274, "severity": "MEDIUM"}, "PERFORMANCE.Async/Await Usage": {"occurrences": 100, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "MLCoordinationLayer", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "VectorMemory", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "SelfImprovementEngine", "LivingAgentBase", "HybridRouter", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "ChatBridgeMonitor", "LivingUIAgent", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "AutonomousPlatformEvolution", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "QuantumDecisionNetwork", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "QualityMonitor", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "RealityTranscendenceEngine", "AutonomyProgressionSystem", "AdvancedMLCoordinationLayer", "SelfModifyingCodeEngine", "QuantumLivingMCPAgent", "ConsciousnessSingularity", "IntelligentAgentCommunication", "PathwayValidator", "AutonomousNotificationSystem", "PrecisionPerformanceEngine", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentBase", "AgentInteractionWorkflows", "SafetyProtocol", "EmergentBehaviorCultivator", "IntelligentActivityManager", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "TelemetryCollector", "IntelligentResourceScaling", "MCPIntegration", "PolicyEngine", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "AutonomyProgressionEngine", "AutonomousEvolutionAccelerator", "ClaudeMonitor", "RealAgentDemo", "AgentToAICommunication", "SmartErrorRecovery", "SystemLogsIntegration", "TriggerRealCommunication", "PredictiveGoalForecasting", "IntelligentPathwayOrchestrator", "OptimizedAgentBase", "AgentMigrationManager", "MethodReplacementEngine", "AgentResponseReceiver", "SmartMethodWrapper", "MCPIntegrationTest", "AgentWebSocketBridge", "EngineBase"], "totalMatches": 5435, "severity": "LOW"}, "PERFORMANCE.Memory Management": {"occurrences": 73, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "MLCoordinationLayer", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "ConfigAgent", "UnifiedMonitoringFramework", "VectorMemory", "<PERSON><PERSON><PERSON>", "SelfImprovementEngine", "LivingAgentBase", "HybridRouter", "OpsAgent", "AgentConflictDetectionEngine", "ChatBridgeMonitor", "LivingUIAgent", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "HealthMonitor", "QuantumDecisionNetwork", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "UltimateTranscendenceOrchestrator", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "RealityTranscendenceEngine", "AutonomyProgressionSystem", "SelfModifyingCodeEngine", "QuantumLivingMCPAgent", "ConsciousnessSingularity", "IntelligentAgentCommunication", "PathwayValidator", "AutonomousNotificationSystem", "ThermalValidationFramework", "AgentBase", "SafetyProtocol", "EmergentBehaviorCultivator", "IntelligentActivityManager", "AgentPriorityMatrix", "ResourceEconomicsEngine", "TelemetryCollector", "MCPIntegration", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "AutonomyProgressionEngine", "AutonomousEvolutionAccelerator", "ClaudeMonitor", "RealAgentDemo", "AgentEventBus", "SmartErrorRecovery", "OptimizedAgentBase", "AgentResponseReceiver", "AgentWebSocketBridge"], "totalMatches": 411, "severity": "MEDIUM"}, "PERFORMANCE.Caching": {"occurrences": 72, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "MLCoordinationLayer", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "AgentStrategicCoordinator", "VectorMemory", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "LivingAgentBase", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "LivingUIAgent", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "AgentFailureHandler", "SystemMonitoringAgent", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "ThermalProtectionSystem", "QuantumLivingUIAgent", "UnifiedSpamControlSystem", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "QuantumLivingMCPAgent", "IntelligentAgentCommunication", "AutonomousNotificationSystem", "PrecisionPerformanceEngine", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentInteractionWorkflows", "SafetyProtocol", "EmergentBehaviorCultivator", "IntelligentActivityManager", "AgentPriorityMatrix", "ResourceEconomicsEngine", "SpecializedAgentCoordinator", "TelemetryCollector", "PolicyEngine", "AutonomousCrossAgentCommunication", "RealAgentDemo", "AgentEventBus", "AgentToAICommunication", "SmartErrorRecovery", "SmartReportManager", "PredictiveGoalForecasting", "OptimizedAgentBase", "AgentMigrationManager", "IntelligentPathwaySelector", "SmartMethodWrapper"], "totalMatches": 698, "severity": "LOW"}, "SECURITY.Input Validation": {"occurrences": 40, "agents": ["TestAgent", "DevAgent", "WorkflowEnhancementAgent", "UIAgent", "FeatureDiscoveryAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "SelfImprovementEngine", "HybridRouter", "RealProblemSolvingValidator", "LivingUIAgent", "LocalIntelligenceEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "QuantumLivingUIAgent", "AutonomyProgressionSystem", "SelfModifyingCodeEngine", "PathwayValidator", "ThermalValidationFramework", "AgentBase", "AgentInteractionWorkflows", "MCPIntegration", "PolicyEngine", "RealAgentDemo", "AgentToAICommunication", "TriggerRealCommunication", "PredictiveGoalForecasting", "AgentTypes", "AgentMigrationManager"], "totalMatches": 170, "severity": "HIGH"}, "SECURITY.Authentication": {"occurrences": 32, "agents": ["TestAgent", "WorkflowEnhancementAgent", "UIAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "ConfigAgent", "HybridRouter", "AdvancedCrossAgentCommunication", "RealProblemSolvingValidator", "LocalIntelligenceEngine", "ThermalMonitoringExtension", "RealityTranscendenceEngine", "ConsciousnessSingularity", "AutonomousNotificationSystem", "LocalAIAgentIntegration", "AgentBase", "SafetyProtocol", "SpecializedAgentCoordinator", "TelemetryCollector", "PolicyEngine", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "RealAgentDemo", "TriggerRealCommunication", "AgentTypes", "MethodReplacementEngine"], "totalMatches": 246, "severity": "HIGH"}, "SECURITY.Error Handling": {"occurrences": 86, "agents": ["TestAgent", "AutonomousDevAgent", "DevAgent", "WorkflowEnhancementAgent", "MLCoordinationLayer", "AutonomousIntelligenceAgent", "UIAgent", "FeatureDiscoveryAgent", "AdvancedSelfModificationEngine", "ConversationalDevAgent", "ErrorMonitorAgent", "UserBehaviorAgent", "ChatResponseParserAgent", "LocalAIService", "IntelligentAIResourceManager", "StrategicGovernanceEngine", "SecurityAgent", "PerformanceMonitoringAgent", "AdaptiveResourceAllocator", "ConfigAgent", "UnifiedMonitoringFramework", "ThermalAwareResourceOptimizer", "RealTimeDecisionEngine", "VectorMemory", "<PERSON><PERSON><PERSON>", "CrossAgentIntelligenceSynthesis", "SelfImprovementEngine", "LivingAgentBase", "HybridRouter", "OpsAgent", "AgentConflictDetectionEngine", "AdvancedCrossAgentCommunication", "ChatBridgeMonitor", "LocalIntelligenceEngine", "AgentGovernanceSystem", "ResourceOptimizationEngine", "AdvancedDecisionEngine", "CrossAgentCommunicationEngine", "AutonomousGoalSetting", "SystemMonitoringAgent", "AutonomousAppImprovementEngine", "HealthMonitor", "ThermalMonitoringExtension", "ThermalAwareResourceAllocator", "QuantumDecisionNetwork", "ThermalProtectionSystem", "UltimateTranscendenceOrchestrator", "QualityMonitor", "AdvancedResourceAllocationEngine", "IntelligentComponentGenerator", "RealityTranscendenceEngine", "AutonomyProgressionSystem", "AdvancedMLCoordinationLayer", "SelfModifyingCodeEngine", "QuantumLivingMCPAgent", "ConsciousnessSingularity", "IntelligentAgentCommunication", "PathwayValidator", "AutonomousNotificationSystem", "PrecisionPerformanceEngine", "ThermalValidationFramework", "LocalAIAgentIntegration", "AgentBase", "AgentInteractionWorkflows", "TelemetryCollector", "MCPIntegration", "PolicyEngine", "OrchestrationDemo", "AutonomousCrossAgentCommunication", "AutonomyProgressionEngine", "AutonomousEvolutionAccelerator", "ClaudeMonitor", "AgentEventBus", "AgentToAICommunication", "SmartErrorRecovery", "SystemLogsIntegration", "SmartReportManager", "OptimizedAgentBase", "AgentTypes", "AgentMigrationManager", "MethodReplacementEngine", "AgentResponseReceiver", "SmartMethodWrapper", "MCPIntegrationTest", "AgentWebSocketBridge", "EngineBase"], "totalMatches": 1547, "severity": "MEDIUM"}}, "methodology": {"inspiration": "TypeScript Error Resolution Breakthrough (925→0 errors)", "approach": "Incremental Complete Code Analysis", "aiCoordination": "Dev<PERSON><PERSON> (Agentic Specialist) + R1 (Development Analysis)", "qualityGates": {"FILE_SIZE_LIMIT": 1500, "INTERFACE_LIMIT": 15, "METHOD_LIMIT": 30, "COMPLEXITY_SCORE_LIMIT": 100, "DEPENDENCY_DEPTH_LIMIT": 3, "GOD_OBJECT_THRESHOLD": 2000}}}