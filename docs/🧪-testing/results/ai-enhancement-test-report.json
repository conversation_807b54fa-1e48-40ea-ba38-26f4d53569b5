{"startTime": 1748761385980, "tests": [{"test": "AI Request Throttling", "duration": 134.87645799999999, "successful": 9, "throttled": 1, "status": "PASS", "details": "9 successful, 1 throttled"}, {"test": "Concurrent Agent Activation", "duration": 264.64020800000003, "successful": 5, "failed": 0, "status": "PASS", "details": "5/5 agents activated successfully"}, {"test": "Fallback Mechanisms", "duration": 0.04716600000000426, "fallbackTriggered": true, "status": "PASS", "details": "Fallback activated when AI unavailable"}, {"test": "Resource Management", "duration": 117.25574999999998, "memoryIncrease": 10, "memoryIncreasePercent": 240, "status": "FAIL", "details": "Memory increased by 10MB (240%)"}, {"test": "Thermal Protection", "duration": 189.93333299999995, "thermalStatus": "normal", "thermalThrottling": false, "status": "PASS", "details": "Thermal status: normal, throttling: inactive"}, {"test": "Error Handling Under Load", "duration": 239.1673330000001, "errorsTriggered": 5, "errorsHandled": 5, "status": "PASS", "details": "5/5 errors handled gracefully"}, {"test": "Spam Control Systems", "duration": 188.22912500000007, "spamControlActive": true, "blockedRequests": 0, "status": "PASS", "details": "Spam control: active, blocked: 0 requests"}, {"test": "AI Cache Efficiency", "duration": 125.96741599999996, "cacheEfficiency": -18, "status": "FAIL", "details": "<PERSON><PERSON> improved performance by -18%"}], "systemMetrics": {}, "errors": [], "warnings": [], "endTime": 1748761387249, "duration": 1269, "summary": {"totalTests": 8, "passedTests": 6, "warningTests": 0, "failedTests": 2, "errorTests": 0, "healthScore": 75}}