<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Interaction Tester</title>
    <style>
        body {
            font-family: monospace;
            background: #0a0a0a;
            color: #ffffff;
            padding: 20px;
            margin: 0;
        }
        .test-section {
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }
        .test-result {
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .pass { background: #1a4d1a; color: #4ade80; }
        .fail { background: #4d1a1a; color: #f87171; }
        .pending { background: #4d4d1a; color: #fbbf24; }
        button {
            background: #2563eb;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover { background: #1d4ed8; }
        .error-log {
            background: #2d1b1b;
            border: 1px solid #red;
            border-radius: 4px;
            padding: 10px;
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: 1px solid #333;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎨 CreAItive Canvas Interaction Tester</h1>
    <p>This tool tests all canvas interactions systematically to ensure no errors.</p>
    
    <div class="test-section">
        <h2>Test Controls</h2>
        <button onclick="runAllTests()">🚀 Run All Tests</button>
        <button onclick="clearResults()">🧹 Clear Results</button>
        <button onclick="testBasicLoading()">📄 Test Page Loading</button>
        <button onclick="testToolSwitching()">🔧 Test Tool Switching</button>
        <button onclick="testDrawingInteractions()">✏️ Test Drawing</button>
        <button onclick="testShapeInteractions()">🔲 Test Shapes</button>
        <button onclick="testTextInteractions()">📝 Test Text</button>
        <button onclick="testCanvasControls()">🎛️ Test Canvas Controls</button>
        <button onclick="testErrorHandling()">⚠️ Test Error Handling</button>
    </div>

    <div class="test-section">
        <h2>📊 Test Results</h2>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h2>🚨 Error Log</h2>
        <div id="errorLog" class="error-log">No errors detected yet...</div>
    </div>

    <div class="test-section">
        <h2>🖥️ Canvas Test Frame</h2>
        <iframe id="canvasFrame" src="http://localhost:3000/canvas"></iframe>
    </div>

    <script>
        let testResults = [];
        let errorCount = 0;
        let startTime;

        // Error tracking
        window.addEventListener('error', function(event) {
            logError('Global Error: ' + event.message, event.error?.stack);
        });

        function logError(message, stack = '') {
            errorCount++;
            const errorLog = document.getElementById('errorLog');
            const errorDiv = document.createElement('div');
            errorDiv.innerHTML = `
                <div style="color: #f87171; font-weight: bold;">[${new Date().toLocaleTimeString()}] ${message}</div>
                ${stack ? `<div style="color: #9ca3af; font-size: 12px; margin-left: 10px;">${stack}</div>` : ''}
            `;
            errorLog.appendChild(errorDiv);
            errorLog.scrollTop = errorLog.scrollHeight;
        }

        function addResult(testName, status, details = '') {
            testResults.push({ testName, status, details, timestamp: new Date() });
            updateResultsDisplay();
        }

        function updateResultsDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="test-result ${result.status}">
                    <strong>${result.testName}</strong>: ${result.status.toUpperCase()}
                    ${result.details ? `<br><small>${result.details}</small>` : ''}
                    <small style="float: right;">${result.timestamp.toLocaleTimeString()}</small>
                </div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            errorCount = 0;
            document.getElementById('errorLog').innerHTML = 'No errors detected yet...';
            updateResultsDisplay();
        }

        async function runAllTests() {
            clearResults();
            startTime = Date.now();
            
            addResult('🎯 Test Suite Started', 'pending', 'Running comprehensive canvas tests...');
            
            try {
                // Test sequence
                await testBasicLoading();
                await delay(1000);
                await testToolSwitching();
                await delay(1000);
                await testDrawingInteractions();
                await delay(1000);
                await testShapeInteractions();
                await delay(1000);
                await testTextInteractions();
                await delay(1000);
                await testCanvasControls();
                await delay(1000);
                await testErrorHandling();
                
                const duration = (Date.now() - startTime) / 1000;
                const passedTests = testResults.filter(r => r.status === 'pass').length;
                const failedTests = testResults.filter(r => r.status === 'fail').length;
                
                addResult('✅ Test Suite Complete', 'pass', 
                    `Duration: ${duration}s | Passed: ${passedTests} | Failed: ${failedTests} | Errors: ${errorCount}`);
                
            } catch (error) {
                logError('Test Suite Error: ' + error.message, error.stack);
                addResult('❌ Test Suite Failed', 'fail', error.message);
            }
        }

        async function testBasicLoading() {
            addResult('🔄 Testing Page Loading', 'pending');
            
            try {
                const frame = document.getElementById('canvasFrame');
                
                // Wait for iframe to load
                await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => reject(new Error('Page load timeout')), 10000);
                    frame.onload = () => {
                        clearTimeout(timeout);
                        resolve();
                    };
                    if (frame.contentDocument && frame.contentDocument.readyState === 'complete') {
                        clearTimeout(timeout);
                        resolve();
                    }
                });

                // Check if essential elements exist
                const doc = frame.contentDocument;
                const canvas = doc.querySelector('canvas');
                const toolbar = doc.querySelector('[title*="Select"]');
                
                if (canvas && toolbar) {
                    addResult('✅ Page Loading', 'pass', 'Canvas and toolbar found');
                } else {
                    addResult('❌ Page Loading', 'fail', 'Missing essential elements');
                }
                
            } catch (error) {
                logError('Page Loading Error: ' + error.message);
                addResult('❌ Page Loading', 'fail', error.message);
            }
        }

        async function testToolSwitching() {
            addResult('🔄 Testing Tool Switching', 'pending');
            
            try {
                const frame = document.getElementById('canvasFrame');
                const doc = frame.contentDocument;
                
                // Test each tool button
                const tools = ['Select', 'Draw', 'Eraser', 'Shape', 'Text', 'Image', 'Media'];
                let toolsWorking = 0;
                
                for (const tool of tools) {
                    try {
                        const button = doc.querySelector(`[title*="${tool}"]`);
                        if (button) {
                            button.click();
                            await delay(200);
                            toolsWorking++;
                        }
                    } catch (e) {
                        logError(`Tool ${tool} error: ${e.message}`);
                    }
                }
                
                if (toolsWorking === tools.length) {
                    addResult('✅ Tool Switching', 'pass', `All ${tools.length} tools responsive`);
                } else {
                    addResult('⚠️ Tool Switching', 'fail', `Only ${toolsWorking}/${tools.length} tools working`);
                }
                
            } catch (error) {
                logError('Tool Switching Error: ' + error.message);
                addResult('❌ Tool Switching', 'fail', error.message);
            }
        }

        async function testDrawingInteractions() {
            addResult('🔄 Testing Drawing Interactions', 'pending');
            
            try {
                const frame = document.getElementById('canvasFrame');
                const doc = frame.contentDocument;
                const canvas = doc.querySelector('canvas');
                
                if (!canvas) {
                    throw new Error('Canvas not found');
                }
                
                // Select draw tool
                const drawButton = doc.querySelector('[title*="Draw"]');
                if (drawButton) drawButton.click();
                await delay(300);
                
                // Simulate drawing interactions
                const rect = canvas.getBoundingClientRect();
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                // Test mouse events
                const events = [
                    { type: 'mousedown', x: centerX - 50, y: centerY - 50 },
                    { type: 'mousemove', x: centerX, y: centerY },
                    { type: 'mousemove', x: centerX + 50, y: centerY + 50 },
                    { type: 'mouseup', x: centerX + 50, y: centerY + 50 }
                ];
                
                for (const event of events) {
                    const mouseEvent = new MouseEvent(event.type, {
                        clientX: event.x,
                        clientY: event.y,
                        bubbles: true,
                        cancelable: true
                    });
                    canvas.dispatchEvent(mouseEvent);
                    await delay(50);
                }
                
                addResult('✅ Drawing Interactions', 'pass', 'Mouse events processed successfully');
                
            } catch (error) {
                logError('Drawing Interaction Error: ' + error.message);
                addResult('❌ Drawing Interactions', 'fail', error.message);
            }
        }

        async function testShapeInteractions() {
            addResult('🔄 Testing Shape Interactions', 'pending');
            
            try {
                const frame = document.getElementById('canvasFrame');
                const doc = frame.contentDocument;
                const canvas = doc.querySelector('canvas');
                
                // Select shape tool
                const shapeButton = doc.querySelector('[title*="Shape"]');
                if (shapeButton) {
                    shapeButton.click();
                    await delay(300);
                    
                    // Test shape placement
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {
                        clientX: rect.width / 2,
                        clientY: rect.height / 2,
                        bubbles: true,
                        cancelable: true
                    });
                    canvas.dispatchEvent(clickEvent);
                    
                    addResult('✅ Shape Interactions', 'pass', 'Shape tool clicks processed');
                } else {
                    addResult('⚠️ Shape Interactions', 'fail', 'Shape button not found');
                }
                
            } catch (error) {
                logError('Shape Interaction Error: ' + error.message);
                addResult('❌ Shape Interactions', 'fail', error.message);
            }
        }

        async function testTextInteractions() {
            addResult('🔄 Testing Text Interactions', 'pending');
            
            try {
                const frame = document.getElementById('canvasFrame');
                const doc = frame.contentDocument;
                const canvas = doc.querySelector('canvas');
                
                // Select text tool
                const textButton = doc.querySelector('[title*="Text"]');
                if (textButton) {
                    textButton.click();
                    await delay(300);
                    
                    // Test text placement
                    const rect = canvas.getBoundingClientRect();
                    const clickEvent = new MouseEvent('click', {
                        clientX: rect.width / 2 + 100,
                        clientY: rect.height / 2 + 100,
                        bubbles: true,
                        cancelable: true
                    });
                    canvas.dispatchEvent(clickEvent);
                    
                    addResult('✅ Text Interactions', 'pass', 'Text tool clicks processed');
                } else {
                    addResult('⚠️ Text Interactions', 'fail', 'Text button not found');
                }
                
            } catch (error) {
                logError('Text Interaction Error: ' + error.message);
                addResult('❌ Text Interactions', 'fail', error.message);
            }
        }

        async function testCanvasControls() {
            addResult('🔄 Testing Canvas Controls', 'pending');
            
            try {
                const frame = document.getElementById('canvasFrame');
                const doc = frame.contentDocument;
                
                // Test keyboard shortcuts
                const keyEvents = ['KeyV', 'KeyB', 'KeyE', 'KeyS', 'KeyT'];
                for (const key of keyEvents) {
                    const keyEvent = new KeyboardEvent('keydown', {
                        code: key,
                        key: key.replace('Key', ''),
                        bubbles: true
                    });
                    doc.dispatchEvent(keyEvent);
                    await delay(100);
                }
                
                // Test canvas resize
                frame.style.width = '80%';
                await delay(500);
                frame.style.width = '100%';
                
                addResult('✅ Canvas Controls', 'pass', 'Keyboard shortcuts and resize tested');
                
            } catch (error) {
                logError('Canvas Controls Error: ' + error.message);
                addResult('❌ Canvas Controls', 'fail', error.message);
            }
        }

        async function testErrorHandling() {
            addResult('🔄 Testing Error Handling', 'pending');
            
            try {
                // Test error reporting to API
                const response = await fetch('http://localhost:3000/api/errors', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        message: 'Canvas Interaction Test Error',
                        stack: 'Test Stack Trace',
                        url: 'http://localhost:3000/canvas',
                        timestamp: new Date().toISOString(),
                        userAgent: navigator.userAgent,
                        additionalInfo: { testType: 'Canvas Interaction Test' }
                    })
                });
                
                if (response.ok) {
                    addResult('✅ Error Handling', 'pass', 'Error tracking API responsive');
                } else {
                    addResult('⚠️ Error Handling', 'fail', `API returned ${response.status}`);
                }
                
            } catch (error) {
                logError('Error Handling Test Error: ' + error.message);
                addResult('❌ Error Handling', 'fail', error.message);
            }
        }

        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Auto-run basic test on load
        setTimeout(() => {
            testBasicLoading();
        }, 2000);
    </script>
</body>
</html> 