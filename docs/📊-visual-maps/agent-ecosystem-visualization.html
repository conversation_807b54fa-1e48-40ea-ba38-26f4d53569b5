<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Ecosystem Visual Map</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: #ffffff;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .title {
            font-size: 2.5em;
            background: linear-gradient(45deg, #6E7AFF, #FF78F7);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .subtitle {
            font-size: 1.2em;
            color: #A0A0A0;
        }
        
        .dashboard {
            display: flex;
            gap: 20px;
            height: 80vh;
        }
        
        .visualization-container {
            flex: 1;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(110, 122, 255, 0.3);
            backdrop-filter: blur(10px);
            position: relative;
        }
        
        #network-visualization {
            width: 100%;
            height: 100%;
            border-radius: 15px;
        }
        
        .control-panel {
            width: 300px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(110, 122, 255, 0.3);
            backdrop-filter: blur(10px);
            padding: 20px;
            overflow-y: auto;
        }
        
        .metrics-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            border: 1px solid rgba(110, 122, 255, 0.2);
        }
        
        .metric-title {
            font-size: 0.9em;
            color: #A0A0A0;
            margin-bottom: 5px;
        }
        
        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #6E7AFF;
        }
        
        .category-legend {
            margin-top: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 0.9em;
        }
        
        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 3px;
            margin-right: 10px;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #6E7AFF, #FF78F7);
            transition: width 0.3s ease;
        }
        
        .filter-section {
            margin-top: 20px;
        }
        
        .filter-title {
            font-size: 1.1em;
            margin-bottom: 10px;
            color: #6E7AFF;
        }
        
        .filter-option {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .filter-option input {
            margin-right: 8px;
        }
        
        .update-timestamp {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 0.8em;
            color: #A0A0A0;
        }
        
        .layer-labels {
            position: absolute;
            left: 10px;
            top: 50px;
            pointer-events: none;
            z-index: 1;
        }
        
        .layer-label {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
            margin-bottom: 8px;
            border-left: 3px solid;
        }
        
        .foundation-label { 
            border-left-color: #FF4444; 
            top: 80px;
            position: absolute;
        }
        .core-label { 
            border-left-color: #FF8844; 
            top: 260px;
            position: absolute;
        }
        .infrastructure-label { 
            border-left-color: #FFAA44; 
            top: 440px;
            position: absolute;
        }
        .coordination-label { 
            border-left-color: #44FF44; 
            top: 620px;
            position: absolute;
        }
        .specialized-label { 
            border-left-color: #4488FF; 
            top: 800px;
            position: absolute;
            left: 80px;
        }
        .monitoring-label { 
            border-left-color: #8844FF; 
            top: 800px;
            position: absolute;
            left: 680px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">🗺️ Agent Ecosystem Visual Map</div>
        <div class="subtitle">Real-time validation tracking for 28 agents</div>
    </div>
    
    <div class="dashboard">
        <div class="visualization-container">
            <div id="network-visualization"></div>
            <div class="update-timestamp">
                Last updated: 6/4/2025, 4:55:49 PM
            </div>
            <!-- Hierarchical Layer Labels -->
            <div class="layer-labels">
                <div class="layer-label foundation-label">🏗️ Foundation Layer</div>
                <div class="layer-label core-label">⚡ Core Operations</div>  
                <div class="layer-label infrastructure-label">🔧 Infrastructure</div>
                <div class="layer-label coordination-label">🤝 Coordination</div>
                <div class="layer-label specialized-label">🎯 Specialized</div>
                <div class="layer-label monitoring-label">📊 Monitoring</div>
            </div>
        </div>
        
        <div class="control-panel">
            <div class="metrics-card">
                <div class="metric-title">Validation Progress</div>
                <div class="metric-value">0%</div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: 0%"></div>
                </div>
            </div>
            
            <div class="metrics-card">
                <div class="metric-title">Validated Agents</div>
                <div class="metric-value">0/28</div>
            </div>
            
            <div class="metrics-card">
                <div class="metric-title">Pipeline Flows</div>
                <div class="metric-value">undefined</div>
            </div>
            
            <div class="category-legend">
                <div class="filter-title">Categories</div>
                
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #FF4444"></div>
                        <span>Critical Foundation Layer (undefined/undefined)</span>
                    </div>
                
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #FF8844"></div>
                        <span>Core Operations Layer (undefined/undefined)</span>
                    </div>
                
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #FFAA44"></div>
                        <span>Infrastructure & Engines (undefined/undefined)</span>
                    </div>
                
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #44FF44"></div>
                        <span>Coordination & Communication (undefined/undefined)</span>
                    </div>
                
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #4488FF"></div>
                        <span>Specialized Systems (undefined/undefined)</span>
                    </div>
                
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #8844FF"></div>
                        <span>Monitoring & Analysis (undefined/undefined)</span>
                    </div>
                
            </div>
            
            <div class="filter-section">
                <div class="filter-title">Filters</div>
                <div class="filter-option">
                    <input type="checkbox" id="show-validated" checked>
                    <label for="show-validated">Show Validated</label>
                </div>
                <div class="filter-option">
                    <input type="checkbox" id="show-unvalidated" checked>
                    <label for="show-unvalidated">Show Unvalidated</label>
                </div>
                <div class="filter-option">
                    <input type="checkbox" id="show-edges" checked>
                    <label for="show-edges">Show Connections</label>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Network data
        const nodes = new vis.DataSet([{"id":"TestAgent","label":"TestAgent","type":"core_agent","priority":"CRITICAL","status":"unvalidated","size":40,"fileSize":"174KB","lines":4787,"location":"Unknown","color":"#FF4444","category":"core","validationNeeds":"TestAgent: unvalidated (174KB)","dependencies":[],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":340,"y":280,"group":"core","level":5},{"id":"SecurityAgent","label":"SecurityAgent","type":"core_agent","priority":"CRITICAL","status":"unvalidated","size":30,"fileSize":"72KB","lines":2037,"location":"Unknown","color":"#FF4444","category":"core","validationNeeds":"SecurityAgent: unvalidated (72KB)","dependencies":["TestAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":520,"y":280,"group":"core","level":5},{"id":"AutonomousDevAgent","label":"AutonomousDevAgent","type":"core_agent","priority":"CRITICAL","status":"unvalidated","size":40,"fileSize":"129KB","lines":3484,"location":"Unknown","color":"#FF4444","category":"core","validationNeeds":"AutonomousDevAgent: unvalidated (129KB)","dependencies":["TestAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":700,"y":280,"group":"core","level":5},{"id":"UIAgent","label":"UIAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":30,"fileSize":"96KB","lines":2542,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"UIAgent: unvalidated (96KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":880,"y":280,"group":"core","level":5},{"id":"DevAgent","label":"DevAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":40,"fileSize":"108KB","lines":3024,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"DevAgent: unvalidated (108KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1060,"y":280,"group":"core","level":5},{"id":"ProactiveAutonomyAgent","label":"ProactiveAutonomyAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":30,"fileSize":"96KB","lines":2762,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"ProactiveAutonomyAgent: unvalidated (96KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":700,"y":400,"group":"core","level":5},{"id":"ErrorMonitorAgent","label":"ErrorMonitorAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":30,"fileSize":"81KB","lines":2311,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"ErrorMonitorAgent: unvalidated (81KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":880,"y":400,"group":"core","level":5},{"id":"ConversationalDevAgent","label":"ConversationalDevAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":30,"fileSize":"68KB","lines":2384,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"ConversationalDevAgent: unvalidated (68KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1060,"y":400,"group":"core","level":5},{"id":"PerformanceMonitoringAgent","label":"PerformanceMonitoringAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"59KB","lines":1541,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"PerformanceMonitoringAgent: unvalidated (59KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1240,"y":400,"group":"core","level":5},{"id":"WorkflowEnhancementAgent","label":"WorkflowEnhancementAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":30,"fileSize":"93KB","lines":2843,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"WorkflowEnhancementAgent: unvalidated (93KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1420,"y":400,"group":"core","level":5},{"id":"ChatResponseParserAgent","label":"ChatResponseParserAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":30,"fileSize":"80KB","lines":2238,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"ChatResponseParserAgent: unvalidated (80KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1150,"y":520,"group":"core","level":5},{"id":"UserBehaviorAgent","label":"UserBehaviorAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":30,"fileSize":"77KB","lines":2287,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"UserBehaviorAgent: unvalidated (77KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1330,"y":520,"group":"core","level":5},{"id":"FeatureDiscoveryAgent","label":"FeatureDiscoveryAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":30,"fileSize":"77KB","lines":2536,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"FeatureDiscoveryAgent: unvalidated (77KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1510,"y":520,"group":"core","level":5},{"id":"ConfigAgent","label":"ConfigAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":25,"fileSize":"57KB","lines":1519,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"ConfigAgent: unvalidated (57KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1690,"y":520,"group":"core","level":5},{"id":"OpsAgent","label":"OpsAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":25,"fileSize":"33KB","lines":1001,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"OpsAgent: unvalidated (33KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1870,"y":520,"group":"core","level":5},{"id":"LivingUIAgent","label":"LivingUIAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":20,"fileSize":"34KB","lines":918,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"LivingUIAgent: unvalidated (34KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1600,"y":640,"group":"core","level":5},{"id":"ProcessWatcherAgent","label":"ProcessWatcherAgent","type":"core_agent","priority":"LOW","status":"unvalidated","size":20,"fileSize":"25KB","lines":764,"location":"Unknown","color":"#888888","category":"core","validationNeeds":"ProcessWatcherAgent: unvalidated (25KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1780,"y":640,"group":"core","level":5},{"id":"AgentOrchestrator","label":"AgentOrchestrator","type":"orchestration","priority":"CRITICAL","status":"unvalidated","size":30,"fileSize":"92KB","lines":2775,"location":"Unknown","color":"#FF4444","category":"foundation","validationNeeds":"AgentOrchestrator: unvalidated (92KB)","dependencies":["TestAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":530,"y":100,"group":"foundation","level":6},{"id":"AdvancedAgentOrchestrator","label":"AdvancedAgentOrchestrator","type":"orchestration","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"45KB","lines":1200,"location":"Unknown","color":"#FF8844","category":"foundation","validationNeeds":"AdvancedAgentOrchestrator: unvalidated (45KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":710,"y":100,"group":"foundation","level":6},{"id":"AgentPriorityMatrix","label":"AgentPriorityMatrix","type":"orchestration","priority":"MEDIUM","status":"unvalidated","size":20,"fileSize":"28KB","lines":850,"location":"Unknown","color":"#FFAA44","category":"foundation","validationNeeds":"AgentPriorityMatrix: unvalidated (28KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":890,"y":100,"group":"foundation","level":6},{"id":"CrossAgentCommunicationEngine","label":"CrossAgentCommunicationEngine","type":"orchestration","priority":"MEDIUM","status":"unvalidated","size":20,"fileSize":"35KB","lines":980,"location":"Unknown","color":"#FFAA44","category":"foundation","validationNeeds":"CrossAgentCommunicationEngine: unvalidated (35KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1070,"y":100,"group":"foundation","level":6},{"id":"AdvancedDecisionEngine","label":"AdvancedDecisionEngine","type":"engine","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"52KB","lines":1450,"location":"Unknown","color":"#FF8844","category":"infrastructure","validationNeeds":"AdvancedDecisionEngine: unvalidated (52KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":250,"y":460,"group":"infrastructure","level":4},{"id":"AdvancedSelfModificationEngine","label":"AdvancedSelfModificationEngine","type":"engine","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"65KB","lines":1800,"location":"Unknown","color":"#FF8844","category":"infrastructure","validationNeeds":"AdvancedSelfModificationEngine: unvalidated (65KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":430,"y":460,"group":"infrastructure","level":4},{"id":"SelfImprovementEngine","label":"SelfImprovementEngine","type":"engine","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"48KB","lines":1350,"location":"Unknown","color":"#FF8844","category":"infrastructure","validationNeeds":"SelfImprovementEngine: unvalidated (48KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":610,"y":460,"group":"infrastructure","level":4},{"id":"ResourceOptimizationEngine","label":"ResourceOptimizationEngine","type":"engine","priority":"MEDIUM","status":"unvalidated","size":25,"fileSize":"38KB","lines":1100,"location":"Unknown","color":"#FFAA44","category":"infrastructure","validationNeeds":"ResourceOptimizationEngine: unvalidated (38KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":790,"y":460,"group":"infrastructure","level":4},{"id":"ResourceEconomicsEngine","label":"ResourceEconomicsEngine","type":"engine","priority":"MEDIUM","status":"unvalidated","size":20,"fileSize":"32KB","lines":920,"location":"Unknown","color":"#FFAA44","category":"infrastructure","validationNeeds":"ResourceEconomicsEngine: unvalidated (32KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":970,"y":460,"group":"infrastructure","level":4},{"id":"StrategicGovernanceEngine","label":"StrategicGovernanceEngine","type":"engine","priority":"MEDIUM","status":"unvalidated","size":25,"fileSize":"42KB","lines":1250,"location":"Unknown","color":"#FFAA44","category":"infrastructure","validationNeeds":"StrategicGovernanceEngine: unvalidated (42KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1150,"y":460,"group":"infrastructure","level":4},{"id":"QuantumLivingUIAgent","label":"QuantumLivingUIAgent","type":"unified","priority":"LOW","status":"unvalidated","size":20,"fileSize":"29KB","lines":820,"location":"Unknown","color":"#888888","category":"infrastructure","validationNeeds":"QuantumLivingUIAgent: unvalidated (29KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":610,"y":580,"group":"infrastructure","level":4}]);
        const edges = new vis.DataSet([{"id":"connection_0_SecurityAgent","from":"TestAgent","to":"SecurityAgent","type":"foundation_dependency","label":"TestAgent validates SecurityAgent functionality","color":"#FF4444","status":"inactive","weight":4,"dashArray":null},{"id":"connection_1_AutonomousDevAgent","from":"TestAgent","to":"AutonomousDevAgent","type":"foundation_dependency","label":"TestAgent validates AutonomousDevAgent safety","color":"#FF4444","status":"inactive","weight":4,"dashArray":null},{"id":"connection_2_AgentOrchestrator","from":"TestAgent","to":"AgentOrchestrator","type":"foundation_dependency","label":"TestAgent validates orchestration logic","color":"#FF4444","status":"inactive","weight":4,"dashArray":null},{"id":"connection_3_UIAgent","from":"AgentOrchestrator","to":"UIAgent","type":"orchestration","label":"Orchestrator manages UI agent execution","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_4_DevAgent","from":"AgentOrchestrator","to":"DevAgent","type":"orchestration","label":"Orchestrator manages development agent tasks","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_5_ProactiveAutonomyAgent","from":"AgentOrchestrator","to":"ProactiveAutonomyAgent","type":"orchestration","label":"Orchestrator manages autonomous behaviors","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_6_ErrorMonitorAgent","from":"AgentOrchestrator","to":"ErrorMonitorAgent","type":"orchestration","label":"Orchestrator coordinates error monitoring","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_7_PerformanceMonitoringAgent","from":"AgentOrchestrator","to":"PerformanceMonitoringAgent","type":"orchestration","label":"Orchestrator manages performance monitoring","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_8_AutonomousDevAgent","from":"SecurityAgent","to":"AutonomousDevAgent","type":"security_validation","label":"Security validation for autonomous development actions","color":"#FF8844","status":"inactive","weight":3,"dashArray":"5,5"},{"id":"connection_9_ProactiveAutonomyAgent","from":"SecurityAgent","to":"ProactiveAutonomyAgent","type":"security_validation","label":"Security validation for proactive autonomous actions","color":"#FF8844","status":"inactive","weight":3,"dashArray":"5,5"},{"id":"connection_10_AgentOrchestrator","from":"SecurityAgent","to":"AgentOrchestrator","type":"security_monitoring","label":"Security monitoring of orchestration activities","color":"#FF6644","status":"inactive","weight":1,"dashArray":null},{"id":"connection_11_ConversationalDevAgent","from":"DevAgent","to":"ConversationalDevAgent","type":"development_collaboration","label":"Development agents collaborate on tasks","color":"#4488FF","status":"inactive","weight":2,"dashArray":null},{"id":"connection_12_UIAgent","from":"DevAgent","to":"UIAgent","type":"development_coordination","label":"Development coordination for UI components","color":"#6688FF","status":"inactive","weight":1,"dashArray":null},{"id":"connection_13_ChatResponseParserAgent","from":"ConversationalDevAgent","to":"ChatResponseParserAgent","type":"conversation_processing","label":"Conversation processing and parsing","color":"#8844FF","status":"inactive","weight":1,"dashArray":null},{"id":"connection_14_DevAgent","from":"ErrorMonitorAgent","to":"DevAgent","type":"error_feedback","label":"Error feedback to development agent","color":"#FF44AA","status":"inactive","weight":2,"dashArray":"3,3"},{"id":"connection_15_AutonomousDevAgent","from":"ErrorMonitorAgent","to":"AutonomousDevAgent","type":"error_feedback","label":"Error feedback to autonomous development","color":"#FF44AA","status":"inactive","weight":2,"dashArray":"3,3"},{"id":"connection_16_ResourceOptimizationEngine","from":"PerformanceMonitoringAgent","to":"ResourceOptimizationEngine","type":"performance_feedback","label":"Performance data for optimization","color":"#44FFAA","status":"inactive","weight":1,"dashArray":"8,3"},{"id":"connection_17_UIAgent","from":"UserBehaviorAgent","to":"UIAgent","type":"behavior_feedback","label":"User behavior insights for UI improvements","color":"#AAFF44","status":"inactive","weight":1,"dashArray":null},{"id":"connection_18_AutonomousDevAgent","from":"AdvancedDecisionEngine","to":"AutonomousDevAgent","type":"decision_support","label":"Decision-making support for autonomous actions","color":"#AA44FF","status":"inactive","weight":2,"dashArray":null},{"id":"connection_19_ProactiveAutonomyAgent","from":"AdvancedDecisionEngine","to":"ProactiveAutonomyAgent","type":"decision_support","label":"Decision-making support for proactive autonomy","color":"#AA44FF","status":"inactive","weight":2,"dashArray":null},{"id":"connection_20_AgentOrchestrator","from":"ResourceOptimizationEngine","to":"AgentOrchestrator","type":"resource_optimization","label":"Resource optimization for orchestration","color":"#44AAFF","status":"inactive","weight":1,"dashArray":null},{"id":"connection_21_AdvancedSelfModificationEngine","from":"SelfImprovementEngine","to":"AdvancedSelfModificationEngine","type":"improvement_chain","label":"Self-improvement chain integration","color":"#FFAA44","status":"inactive","weight":1,"dashArray":null},{"id":"connection_22_DevAgent","from":"WorkflowEnhancementAgent","to":"DevAgent","type":"workflow_enhancement","label":"Workflow enhancements for development","color":"#44FFFF","status":"inactive","weight":1,"dashArray":"2,2"},{"id":"connection_23_UIAgent","from":"WorkflowEnhancementAgent","to":"UIAgent","type":"workflow_enhancement","label":"Workflow enhancements for UI development","color":"#44FFFF","status":"inactive","weight":1,"dashArray":"2,2"},{"id":"connection_24_UIAgent","from":"FeatureDiscoveryAgent","to":"UIAgent","type":"feature_discovery","label":"Feature discovery for UI improvements","color":"#FFFF44","status":"inactive","weight":1,"dashArray":null},{"id":"connection_25_AgentOrchestrator","from":"ConfigAgent","to":"AgentOrchestrator","type":"configuration","label":"Configuration management for orchestration","color":"#FF44FF","status":"inactive","weight":1,"dashArray":null}]);
        
        // Network options - simplified to ensure it works
        const options = {
            physics: false,  // Keep physics disabled
            nodes: {
                shape: 'dot',
                font: { 
                    color: '#ffffff', 
                    size: 14, 
                    face: 'Arial' 
                },
                borderWidth: 2,
                shadow: { 
                    enabled: true, 
                    color: 'rgba(0,0,0,0.5)' 
                },
                chosen: true
            },
            edges: {
                width: 2,
                color: { 
                    color: '#888888', 
                    highlight: '#ffffff' 
                },
                arrows: { 
                    to: { enabled: true, scaleFactor: 1 } 
                },
                font: { 
                    color: '#ffffff', 
                    size: 12 
                },
                smooth: false  // Disable smooth edges for better performance
            },
            interaction: {
                hover: true,
                selectConnectedEdges: false,
                tooltipDelay: 300,
                dragNodes: true,
                dragView: true,
                zoomView: true
            }
        };
        
        // Initialize network
        const container = document.getElementById('network-visualization');
        console.log('🎯 Container found:', container);
        console.log('📊 Nodes count:', nodes.length);
        console.log('🔗 Edges count:', edges.length);
        
        const data = { nodes: nodes, edges: edges };
        const network = new vis.Network(container, data, options);
        
        // Force fit to ensure nodes are visible - this was missing!
        setTimeout(() => {
            network.fit({
                animation: {
                    duration: 500,
                    easingFunction: 'easeInOutQuad'
                }
            });
            console.log('🎯 Network fitted to viewport');
        }, 1000);
        
        // Add stabilization progress
        network.on('stabilizationProgress', function(params) {
            console.log('🔄 Stabilizing: ' + Math.round(params.iterations/params.total*100) + '%');
        });
        
        network.on('stabilized', function() {
            console.log('✅ Network stabilized!');
            const positions = network.getPositions();
            console.log('📍 Node positions:', Object.keys(positions).length + ' nodes positioned');
        });
        
        // Log any errors
        network.on('_dataChanged', function() {
            console.log('📈 Data changed event');
        });
        
        // Filter functionality
        function updateFilters() {
            const showValidated = document.getElementById('show-validated').checked;
            const showUnvalidated = document.getElementById('show-unvalidated').checked;
            const showEdges = document.getElementById('show-edges').checked;
            
            const filteredNodes = [{"id":"TestAgent","label":"TestAgent","type":"core_agent","priority":"CRITICAL","status":"unvalidated","size":40,"fileSize":"174KB","lines":4787,"location":"Unknown","color":"#FF4444","category":"core","validationNeeds":"TestAgent: unvalidated (174KB)","dependencies":[],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":340,"y":280,"group":"core","level":5},{"id":"SecurityAgent","label":"SecurityAgent","type":"core_agent","priority":"CRITICAL","status":"unvalidated","size":30,"fileSize":"72KB","lines":2037,"location":"Unknown","color":"#FF4444","category":"core","validationNeeds":"SecurityAgent: unvalidated (72KB)","dependencies":["TestAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":520,"y":280,"group":"core","level":5},{"id":"AutonomousDevAgent","label":"AutonomousDevAgent","type":"core_agent","priority":"CRITICAL","status":"unvalidated","size":40,"fileSize":"129KB","lines":3484,"location":"Unknown","color":"#FF4444","category":"core","validationNeeds":"AutonomousDevAgent: unvalidated (129KB)","dependencies":["TestAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":700,"y":280,"group":"core","level":5},{"id":"UIAgent","label":"UIAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":30,"fileSize":"96KB","lines":2542,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"UIAgent: unvalidated (96KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":880,"y":280,"group":"core","level":5},{"id":"DevAgent","label":"DevAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":40,"fileSize":"108KB","lines":3024,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"DevAgent: unvalidated (108KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1060,"y":280,"group":"core","level":5},{"id":"ProactiveAutonomyAgent","label":"ProactiveAutonomyAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":30,"fileSize":"96KB","lines":2762,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"ProactiveAutonomyAgent: unvalidated (96KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":700,"y":400,"group":"core","level":5},{"id":"ErrorMonitorAgent","label":"ErrorMonitorAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":30,"fileSize":"81KB","lines":2311,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"ErrorMonitorAgent: unvalidated (81KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":880,"y":400,"group":"core","level":5},{"id":"ConversationalDevAgent","label":"ConversationalDevAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":30,"fileSize":"68KB","lines":2384,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"ConversationalDevAgent: unvalidated (68KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1060,"y":400,"group":"core","level":5},{"id":"PerformanceMonitoringAgent","label":"PerformanceMonitoringAgent","type":"core_agent","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"59KB","lines":1541,"location":"Unknown","color":"#FF8844","category":"core","validationNeeds":"PerformanceMonitoringAgent: unvalidated (59KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1240,"y":400,"group":"core","level":5},{"id":"WorkflowEnhancementAgent","label":"WorkflowEnhancementAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":30,"fileSize":"93KB","lines":2843,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"WorkflowEnhancementAgent: unvalidated (93KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1420,"y":400,"group":"core","level":5},{"id":"ChatResponseParserAgent","label":"ChatResponseParserAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":30,"fileSize":"80KB","lines":2238,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"ChatResponseParserAgent: unvalidated (80KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1150,"y":520,"group":"core","level":5},{"id":"UserBehaviorAgent","label":"UserBehaviorAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":30,"fileSize":"77KB","lines":2287,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"UserBehaviorAgent: unvalidated (77KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1330,"y":520,"group":"core","level":5},{"id":"FeatureDiscoveryAgent","label":"FeatureDiscoveryAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":30,"fileSize":"77KB","lines":2536,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"FeatureDiscoveryAgent: unvalidated (77KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1510,"y":520,"group":"core","level":5},{"id":"ConfigAgent","label":"ConfigAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":25,"fileSize":"57KB","lines":1519,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"ConfigAgent: unvalidated (57KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1690,"y":520,"group":"core","level":5},{"id":"OpsAgent","label":"OpsAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":25,"fileSize":"33KB","lines":1001,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"OpsAgent: unvalidated (33KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1870,"y":520,"group":"core","level":5},{"id":"LivingUIAgent","label":"LivingUIAgent","type":"core_agent","priority":"MEDIUM","status":"unvalidated","size":20,"fileSize":"34KB","lines":918,"location":"Unknown","color":"#FFAA44","category":"core","validationNeeds":"LivingUIAgent: unvalidated (34KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1600,"y":640,"group":"core","level":5},{"id":"ProcessWatcherAgent","label":"ProcessWatcherAgent","type":"core_agent","priority":"LOW","status":"unvalidated","size":20,"fileSize":"25KB","lines":764,"location":"Unknown","color":"#888888","category":"core","validationNeeds":"ProcessWatcherAgent: unvalidated (25KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1780,"y":640,"group":"core","level":5},{"id":"AgentOrchestrator","label":"AgentOrchestrator","type":"orchestration","priority":"CRITICAL","status":"unvalidated","size":30,"fileSize":"92KB","lines":2775,"location":"Unknown","color":"#FF4444","category":"foundation","validationNeeds":"AgentOrchestrator: unvalidated (92KB)","dependencies":["TestAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":530,"y":100,"group":"foundation","level":6},{"id":"AdvancedAgentOrchestrator","label":"AdvancedAgentOrchestrator","type":"orchestration","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"45KB","lines":1200,"location":"Unknown","color":"#FF8844","category":"foundation","validationNeeds":"AdvancedAgentOrchestrator: unvalidated (45KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":710,"y":100,"group":"foundation","level":6},{"id":"AgentPriorityMatrix","label":"AgentPriorityMatrix","type":"orchestration","priority":"MEDIUM","status":"unvalidated","size":20,"fileSize":"28KB","lines":850,"location":"Unknown","color":"#FFAA44","category":"foundation","validationNeeds":"AgentPriorityMatrix: unvalidated (28KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":890,"y":100,"group":"foundation","level":6},{"id":"CrossAgentCommunicationEngine","label":"CrossAgentCommunicationEngine","type":"orchestration","priority":"MEDIUM","status":"unvalidated","size":20,"fileSize":"35KB","lines":980,"location":"Unknown","color":"#FFAA44","category":"foundation","validationNeeds":"CrossAgentCommunicationEngine: unvalidated (35KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1070,"y":100,"group":"foundation","level":6},{"id":"AdvancedDecisionEngine","label":"AdvancedDecisionEngine","type":"engine","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"52KB","lines":1450,"location":"Unknown","color":"#FF8844","category":"infrastructure","validationNeeds":"AdvancedDecisionEngine: unvalidated (52KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":250,"y":460,"group":"infrastructure","level":4},{"id":"AdvancedSelfModificationEngine","label":"AdvancedSelfModificationEngine","type":"engine","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"65KB","lines":1800,"location":"Unknown","color":"#FF8844","category":"infrastructure","validationNeeds":"AdvancedSelfModificationEngine: unvalidated (65KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":430,"y":460,"group":"infrastructure","level":4},{"id":"SelfImprovementEngine","label":"SelfImprovementEngine","type":"engine","priority":"HIGH","status":"unvalidated","size":25,"fileSize":"48KB","lines":1350,"location":"Unknown","color":"#FF8844","category":"infrastructure","validationNeeds":"SelfImprovementEngine: unvalidated (48KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":610,"y":460,"group":"infrastructure","level":4},{"id":"ResourceOptimizationEngine","label":"ResourceOptimizationEngine","type":"engine","priority":"MEDIUM","status":"unvalidated","size":25,"fileSize":"38KB","lines":1100,"location":"Unknown","color":"#FFAA44","category":"infrastructure","validationNeeds":"ResourceOptimizationEngine: unvalidated (38KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":790,"y":460,"group":"infrastructure","level":4},{"id":"ResourceEconomicsEngine","label":"ResourceEconomicsEngine","type":"engine","priority":"MEDIUM","status":"unvalidated","size":20,"fileSize":"32KB","lines":920,"location":"Unknown","color":"#FFAA44","category":"infrastructure","validationNeeds":"ResourceEconomicsEngine: unvalidated (32KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":970,"y":460,"group":"infrastructure","level":4},{"id":"StrategicGovernanceEngine","label":"StrategicGovernanceEngine","type":"engine","priority":"MEDIUM","status":"unvalidated","size":25,"fileSize":"42KB","lines":1250,"location":"Unknown","color":"#FFAA44","category":"infrastructure","validationNeeds":"StrategicGovernanceEngine: unvalidated (42KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":1150,"y":460,"group":"infrastructure","level":4},{"id":"QuantumLivingUIAgent","label":"QuantumLivingUIAgent","type":"unified","priority":"LOW","status":"unvalidated","size":20,"fileSize":"29KB","lines":820,"location":"Unknown","color":"#888888","category":"infrastructure","validationNeeds":"QuantumLivingUIAgent: unvalidated (29KB)","dependencies":["TestAgent","SecurityAgent"],"borderWidth":2,"font":{"color":"#ffffff","size":12},"x":610,"y":580,"group":"infrastructure","level":4}].filter(node => {
                if (node.status === 'validated' && !showValidated) return false;
                if (node.status !== 'validated' && !showUnvalidated) return false;
                return true;
            });
            
            nodes.update(filteredNodes);
            
            if (showEdges) {
                edges.update([{"id":"connection_0_SecurityAgent","from":"TestAgent","to":"SecurityAgent","type":"foundation_dependency","label":"TestAgent validates SecurityAgent functionality","color":"#FF4444","status":"inactive","weight":4,"dashArray":null},{"id":"connection_1_AutonomousDevAgent","from":"TestAgent","to":"AutonomousDevAgent","type":"foundation_dependency","label":"TestAgent validates AutonomousDevAgent safety","color":"#FF4444","status":"inactive","weight":4,"dashArray":null},{"id":"connection_2_AgentOrchestrator","from":"TestAgent","to":"AgentOrchestrator","type":"foundation_dependency","label":"TestAgent validates orchestration logic","color":"#FF4444","status":"inactive","weight":4,"dashArray":null},{"id":"connection_3_UIAgent","from":"AgentOrchestrator","to":"UIAgent","type":"orchestration","label":"Orchestrator manages UI agent execution","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_4_DevAgent","from":"AgentOrchestrator","to":"DevAgent","type":"orchestration","label":"Orchestrator manages development agent tasks","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_5_ProactiveAutonomyAgent","from":"AgentOrchestrator","to":"ProactiveAutonomyAgent","type":"orchestration","label":"Orchestrator manages autonomous behaviors","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_6_ErrorMonitorAgent","from":"AgentOrchestrator","to":"ErrorMonitorAgent","type":"orchestration","label":"Orchestrator coordinates error monitoring","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_7_PerformanceMonitoringAgent","from":"AgentOrchestrator","to":"PerformanceMonitoringAgent","type":"orchestration","label":"Orchestrator manages performance monitoring","color":"#44FF44","status":"inactive","weight":3,"dashArray":null},{"id":"connection_8_AutonomousDevAgent","from":"SecurityAgent","to":"AutonomousDevAgent","type":"security_validation","label":"Security validation for autonomous development actions","color":"#FF8844","status":"inactive","weight":3,"dashArray":"5,5"},{"id":"connection_9_ProactiveAutonomyAgent","from":"SecurityAgent","to":"ProactiveAutonomyAgent","type":"security_validation","label":"Security validation for proactive autonomous actions","color":"#FF8844","status":"inactive","weight":3,"dashArray":"5,5"},{"id":"connection_10_AgentOrchestrator","from":"SecurityAgent","to":"AgentOrchestrator","type":"security_monitoring","label":"Security monitoring of orchestration activities","color":"#FF6644","status":"inactive","weight":1,"dashArray":null},{"id":"connection_11_ConversationalDevAgent","from":"DevAgent","to":"ConversationalDevAgent","type":"development_collaboration","label":"Development agents collaborate on tasks","color":"#4488FF","status":"inactive","weight":2,"dashArray":null},{"id":"connection_12_UIAgent","from":"DevAgent","to":"UIAgent","type":"development_coordination","label":"Development coordination for UI components","color":"#6688FF","status":"inactive","weight":1,"dashArray":null},{"id":"connection_13_ChatResponseParserAgent","from":"ConversationalDevAgent","to":"ChatResponseParserAgent","type":"conversation_processing","label":"Conversation processing and parsing","color":"#8844FF","status":"inactive","weight":1,"dashArray":null},{"id":"connection_14_DevAgent","from":"ErrorMonitorAgent","to":"DevAgent","type":"error_feedback","label":"Error feedback to development agent","color":"#FF44AA","status":"inactive","weight":2,"dashArray":"3,3"},{"id":"connection_15_AutonomousDevAgent","from":"ErrorMonitorAgent","to":"AutonomousDevAgent","type":"error_feedback","label":"Error feedback to autonomous development","color":"#FF44AA","status":"inactive","weight":2,"dashArray":"3,3"},{"id":"connection_16_ResourceOptimizationEngine","from":"PerformanceMonitoringAgent","to":"ResourceOptimizationEngine","type":"performance_feedback","label":"Performance data for optimization","color":"#44FFAA","status":"inactive","weight":1,"dashArray":"8,3"},{"id":"connection_17_UIAgent","from":"UserBehaviorAgent","to":"UIAgent","type":"behavior_feedback","label":"User behavior insights for UI improvements","color":"#AAFF44","status":"inactive","weight":1,"dashArray":null},{"id":"connection_18_AutonomousDevAgent","from":"AdvancedDecisionEngine","to":"AutonomousDevAgent","type":"decision_support","label":"Decision-making support for autonomous actions","color":"#AA44FF","status":"inactive","weight":2,"dashArray":null},{"id":"connection_19_ProactiveAutonomyAgent","from":"AdvancedDecisionEngine","to":"ProactiveAutonomyAgent","type":"decision_support","label":"Decision-making support for proactive autonomy","color":"#AA44FF","status":"inactive","weight":2,"dashArray":null},{"id":"connection_20_AgentOrchestrator","from":"ResourceOptimizationEngine","to":"AgentOrchestrator","type":"resource_optimization","label":"Resource optimization for orchestration","color":"#44AAFF","status":"inactive","weight":1,"dashArray":null},{"id":"connection_21_AdvancedSelfModificationEngine","from":"SelfImprovementEngine","to":"AdvancedSelfModificationEngine","type":"improvement_chain","label":"Self-improvement chain integration","color":"#FFAA44","status":"inactive","weight":1,"dashArray":null},{"id":"connection_22_DevAgent","from":"WorkflowEnhancementAgent","to":"DevAgent","type":"workflow_enhancement","label":"Workflow enhancements for development","color":"#44FFFF","status":"inactive","weight":1,"dashArray":"2,2"},{"id":"connection_23_UIAgent","from":"WorkflowEnhancementAgent","to":"UIAgent","type":"workflow_enhancement","label":"Workflow enhancements for UI development","color":"#44FFFF","status":"inactive","weight":1,"dashArray":"2,2"},{"id":"connection_24_UIAgent","from":"FeatureDiscoveryAgent","to":"UIAgent","type":"feature_discovery","label":"Feature discovery for UI improvements","color":"#FFFF44","status":"inactive","weight":1,"dashArray":null},{"id":"connection_25_AgentOrchestrator","from":"ConfigAgent","to":"AgentOrchestrator","type":"configuration","label":"Configuration management for orchestration","color":"#FF44FF","status":"inactive","weight":1,"dashArray":null}]);
            } else {
                edges.clear();
            }
        }
        
        // Add event listeners
        document.getElementById('show-validated').addEventListener('change', updateFilters);
        document.getElementById('show-unvalidated').addEventListener('change', updateFilters);
        document.getElementById('show-edges').addEventListener('change', updateFilters);
        
        // Node click handler
        network.on('click', function (params) {
            if (params.nodes.length > 0) {
                const nodeId = params.nodes[0];
                const node = nodes.get(nodeId);
                alert(`Agent: ${node.label}\nCategory: ${node.category}\nStatus: ${node.status}\nValidation Progress: ${node.validationProgress}%`);
            }
        });
        
        console.log('🗺️ Agent Ecosystem Visual Map initialized successfully!');
    </script>
</body>
</html>