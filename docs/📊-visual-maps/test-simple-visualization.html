<!DOCTYPE html>
<html>
<head>
    <title>Simple Agent Test</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        #mynetworkid {
            width: 800px;
            height: 600px;
            border: 1px solid lightgray;
            background: #222;
        }
    </style>
</head>
<body>
    <h1>Simple Agent Network Test</h1>
    <div id="mynetworkid"></div>

    <script>
        // Simple test data
        const nodes = new vis.DataSet([
            {id: 1, label: 'TestAgent', color: '#FF4444', size: 30},
            {id: 2, label: 'SecurityAgent', color: '#FF8844', size: 25},
            {id: 3, label: 'UIAgent', color: '#44FF44', size: 20}
        ]);

        const edges = new vis.DataSet([
            {from: 1, to: 2, color: '#FF4444'},
            {from: 1, to: 3, color: '#44FF44'}
        ]);

        const container = document.getElementById('mynetworkid');
        const data = {
            nodes: nodes,
            edges: edges
        };
        
        const options = {
            nodes: {
                font: {color: 'white'}
            }
        };

        const network = new vis.Network(container, data, options);
        
        console.log('Simple test loaded!');
        console.log('Nodes:', nodes.get());
        console.log('Edges:', edges.get());
    </script>
</body>
</html> 