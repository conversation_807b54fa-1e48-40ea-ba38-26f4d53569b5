<!DOCTYPE html>
<html>
<head>
    <title>Fixed Layout Agent Test</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body { 
            background: #000; 
            color: white; 
            font-family: Arial;
            margin: 0;
            padding: 20px;
        }
        
        .container {
            display: flex;
            gap: 20px;
            height: 80vh;
        }
        
        .visualization {
            flex: 1;
            border: 2px solid #6E7AFF;
            background: #111;
            border-radius: 10px;
        }
        
        .sidebar {
            width: 300px;
            background: #222;
            border-radius: 10px;
            padding: 20px;
            overflow-y: auto;
        }
        
        #network {
            width: 100%;
            height: 100%;
        }
        
        .metric {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 Fixed Layout Agent Visualization</h1>
    
    <div class="container">
        <div class="visualization">
            <div id="network"></div>
        </div>
        
        <div class="sidebar">
            <h3>📊 Metrics</h3>
            <div class="metric">
                <strong>Total Agents:</strong> <span id="total-agents">Loading...</span>
            </div>
            <div class="metric">
                <strong>Status:</strong> <span id="status">Initializing...</span>
            </div>
            <div class="metric" id="debug-info">
                Debug info will appear here...
            </div>
        </div>
    </div>
    
    <script>
        console.log('🔧 Starting fixed layout test...');
        
        const debugDiv = document.getElementById('debug-info');
        const statusSpan = document.getElementById('status');
        const totalSpan = document.getElementById('total-agents');
        
        function updateDebug(message) {
            console.log(message);
            debugDiv.innerHTML += '<br>' + message;
        }
        
        function updateStatus(status) {
            statusSpan.textContent = status;
        }
        
        // Real agent data from our actual system
        const agentNodes = [
            {id: "TestAgent", label: "TestAgent", color: "#FF4444", size: 50, x: 100, y: 100},
            {id: "SecurityAgent", label: "SecurityAgent", color: "#FF4444", size: 40, x: 300, y: 100},
            {id: "UIAgent", label: "UIAgent", color: "#FF8844", size: 35, x: 500, y: 100},
            {id: "DevAgent", label: "DevAgent", color: "#FF8844", size: 45, x: 100, y: 300},
            {id: "AutonomousDevAgent", label: "AutonomousDevAgent", color: "#FF4444", size: 45, x: 300, y: 300},
            {id: "ProactiveAutonomyAgent", label: "ProactiveAutonomyAgent", color: "#FF8844", size: 35, x: 500, y: 300},
            {id: "ErrorMonitorAgent", label: "ErrorMonitorAgent", color: "#FF8844", size: 35, x: 100, y: 500},
            {id: "ConversationalDevAgent", label: "ConversationalDevAgent", color: "#FF8844", size: 35, x: 300, y: 500}
        ];
        
        const agentEdges = [
            {from: "TestAgent", to: "SecurityAgent", color: "#FF4444", width: 3},
            {from: "TestAgent", to: "AutonomousDevAgent", color: "#FF4444", width: 3},
            {from: "SecurityAgent", to: "AutonomousDevAgent", color: "#FF8844", width: 2},
            {from: "UIAgent", to: "DevAgent", color: "#4488FF", width: 2},
            {from: "DevAgent", to: "ConversationalDevAgent", color: "#44FF44", width: 2}
        ];
        
        totalSpan.textContent = agentNodes.length;
        updateDebug(`📊 Loaded ${agentNodes.length} agents and ${agentEdges.length} connections`);
        
        const nodes = new vis.DataSet(agentNodes);
        const edges = new vis.DataSet(agentEdges);
        
        const container = document.getElementById('network');
        updateDebug(`📦 Container found: ${container.offsetWidth}x${container.offsetHeight}`);
        
        const options = {
            physics: false,
            nodes: {
                font: { color: 'white', size: 16, face: 'Arial' },
                borderWidth: 3,
                shape: 'dot',
                shadow: { enabled: true, color: 'rgba(0,0,0,0.5)' }
            },
            edges: {
                color: { color: '#888', highlight: '#fff' },
                arrows: { to: { enabled: true, scaleFactor: 1 } },
                smooth: false
            },
            interaction: {
                hover: true,
                selectConnectedEdges: false
            }
        };
        
        try {
            updateStatus('Creating network...');
            updateDebug('🔧 Initializing vis.js network...');
            
            const network = new vis.Network(container, { nodes: nodes, edges: edges }, options);
            
            updateStatus('Network created!');
            updateDebug('✅ Network successfully created');
            
            // Force fit after a delay
            setTimeout(() => {
                network.fit({
                    animation: {
                        duration: 500,
                        easingFunction: 'easeInOutQuad'
                    }
                });
                updateDebug('🎯 Network fitted to viewport');
                updateStatus('Ready - try clicking nodes!');
            }, 1000);
            
            // Event handlers
            network.on('click', function(params) {
                if (params.nodes.length > 0) {
                    const nodeId = params.nodes[0];
                    const node = nodes.get(nodeId);
                    updateDebug(`🖱️ Clicked: ${node.label}`);
                    alert(`Agent: ${node.label}\\nColor: ${node.color}\\nSize: ${node.size}`);
                }
            });
            
            network.on('hoverNode', function(params) {
                updateDebug(`👆 Hovering: ${params.node}`);
            });
            
        } catch (error) {
            updateStatus('ERROR!');
            updateDebug('❌ ERROR: ' + error.message);
            console.error('Network creation error:', error);
        }
    </script>
</body>
</html> 