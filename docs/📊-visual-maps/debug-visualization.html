<!DOCTYPE html>
<html>
<head>
    <title>Debug Agent Visualization</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body { 
            background: #222; 
            color: white; 
            font-family: Arial;
        }
        #network { 
            width: 100%; 
            height: 600px; 
            border: 1px solid white; 
            background: #333;
        }
        #debug {
            margin: 20px 0;
            padding: 10px;
            background: #444;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🔍 Debug Agent Visualization</h1>
    
    <div id="debug">
        <h3>Debug Info:</h3>
        <div id="debug-output"></div>
    </div>
    
    <div id="network"></div>

    <script>
        console.log('🔍 Starting debug visualization...');
        
        // Simple test nodes
        const testNodes = [
            {id: 'TestAgent', label: 'TestAgent', color: '#FF4444', size: 40},
            {id: 'SecurityAgent', label: 'SecurityAgent', color: '#FF8844', size: 30},
            {id: 'UIAgent', label: 'UIAgent', color: '#44FF44', size: 25}
        ];
        
        const testEdges = [
            {from: 'TestAgent', to: 'SecurityAgent', color: '#FF4444'},
            {from: 'TestAgent', to: 'UIAgent', color: '#44FF44'}
        ];
        
        console.log('📊 Test nodes:', testNodes);
        console.log('🔗 Test edges:', testEdges);
        
        const nodes = new vis.DataSet(testNodes);
        const edges = new vis.DataSet(testEdges);
        
        console.log('📋 DataSet nodes count:', nodes.length);
        console.log('📋 DataSet edges count:', edges.length);
        
        const container = document.getElementById('network');
        
        if (!container) {
            console.error('❌ Container not found!');
        } else {
            console.log('✅ Container found:', container);
        }
        
        const data = { nodes: nodes, edges: edges };
        
        const options = {
            nodes: {
                font: { color: 'white', size: 14 },
                borderWidth: 2,
                shadow: true
            },
            edges: {
                font: { color: 'white' },
                arrows: { to: true }
            },
            physics: {
                enabled: true,
                stabilization: { enabled: true, iterations: 100 }
            }
        };
        
        console.log('🎛️ Creating network with options:', options);
        
        try {
            const network = new vis.Network(container, data, options);
            console.log('✅ Network created successfully!');
            
            network.on('stabilizationProgress', function(params) {
                console.log('🔄 Stabilizing...', params.iterations + '/' + params.total);
            });
            
            network.on('stabilized', function() {
                console.log('✅ Network stabilized!');
                
                // Get current positions
                const positions = network.getPositions();
                console.log('📍 Node positions:', positions);
                
                // Update debug output
                const debugOutput = document.getElementById('debug-output');
                debugOutput.innerHTML = `
                    <p>✅ Network loaded successfully!</p>
                    <p>📊 Nodes: ${nodes.length}</p>
                    <p>🔗 Edges: ${edges.length}</p>
                    <p>📍 Positions: ${Object.keys(positions).length}</p>
                    <p>🎯 Test: Try clicking and dragging nodes</p>
                `;
            });
            
            network.on('click', function(params) {
                console.log('🖱️ Click:', params);
                if (params.nodes.length > 0) {
                    alert('Clicked node: ' + params.nodes[0]);
                }
            });
            
        } catch (error) {
            console.error('❌ Error creating network:', error);
            document.getElementById('debug-output').innerHTML = `<p style="color: red;">❌ Error: ${error.message}</p>`;
        }
        
        // Also try to load vis library info
        if (typeof vis !== 'undefined') {
            console.log('✅ vis.js loaded successfully');
            console.log('📦 vis version info:', vis);
        } else {
            console.error('❌ vis.js not loaded!');
        }
    </script>
</body>
</html> 