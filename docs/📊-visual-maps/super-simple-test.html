<!DOCTYPE html>
<html>
<head>
    <title>Super Simple Agent Test</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body { 
            background: #000; 
            color: white; 
            font-family: Arial;
            margin: 0;
            padding: 20px;
        }
        #network { 
            width: 100vw; 
            height: 80vh; 
            border: 3px solid #ff0000; 
            background: #222;
        }
        .info {
            background: #333;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🔬 Super Simple Agent Test</h1>
    
    <div class="info" id="info">
        Loading agent data...
    </div>
    
    <div id="network"></div>
    
    <script>
        console.log('🔬 Starting super simple test...');
        
        const infoDiv = document.getElementById('info');
        
        function log(message) {
            console.log(message);
            infoDiv.innerHTML += '<br>' + message;
        }
        
        // Use actual agent data (first 5 agents)
        const nodes = new vis.DataSet([
            {id: "TestAgent", label: "TestAgent", color: "#FF4444", size: 40, x: 400, y: 300},
            {id: "SecurityAgent", label: "SecurityAgent", color: "#FF4444", size: 30, x: 200, y: 200},
            {id: "UIAgent", label: "UIAgent", color: "#FF8844", size: 30, x: 600, y: 200},
            {id: "DevAgent", label: "DevAgent", color: "#FF8844", size: 40, x: 200, y: 400},
            {id: "AutonomousDevAgent", label: "AutonomousDevAgent", color: "#FF4444", size: 40, x: 600, y: 400}
        ]);
        
        const edges = new vis.DataSet([
            {from: "TestAgent", to: "SecurityAgent", color: "#FF4444"},
            {from: "TestAgent", to: "UIAgent", color: "#44FF44"},
            {from: "SecurityAgent", to: "AutonomousDevAgent", color: "#FF8844"},
            {from: "UIAgent", to: "DevAgent", color: "#4488FF"}
        ]);
        
        log(`📊 Created ${nodes.length} nodes and ${edges.length} edges`);
        
        // Container
        const container = document.getElementById('network');
        log(`📦 Container size: ${container.offsetWidth}x${container.offsetHeight}`);
        
        // Super simple options
        const options = {
            physics: false,
            nodes: {
                font: { color: 'white', size: 14 },
                borderWidth: 2,
                shape: 'dot'
            },
            edges: {
                color: 'white',
                width: 2,
                arrows: { to: true }
            },
            interaction: {
                dragNodes: true,
                dragView: true,
                zoomView: true
            }
        };
        
        try {
            log('🔧 Creating network...');
            const network = new vis.Network(container, { nodes: nodes, edges: edges }, options);
            log('✅ Network created!');
            
            // Manual fit
            setTimeout(() => {
                network.fit();
                log('🎯 Network fitted to view');
            }, 500);
            
            // Click handler
            network.on('click', function(params) {
                log('🖱️ Click detected: ' + JSON.stringify(params.nodes));
            });
            
        } catch (error) {
            log('❌ ERROR: ' + error.message);
            console.error('Full error:', error);
        }
        
        // Also test container visibility
        log('📐 Container computed style:');
        const computedStyle = window.getComputedStyle(container);
        log(`Display: ${computedStyle.display}, Visibility: ${computedStyle.visibility}`);
        log(`Position: ${computedStyle.position}, Overflow: ${computedStyle.overflow}`);
    </script>
</body>
</html> 