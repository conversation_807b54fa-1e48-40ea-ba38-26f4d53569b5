<!DOCTYPE html>
<html>
<head>
    <title>Simple Diagnostic</title>
    <script src="https://unpkg.com/vis-network/standalone/umd/vis-network.min.js"></script>
    <style>
        body { 
            background: #000; 
            color: white; 
            font-family: Arial;
            margin: 20px;
        }
        #network { 
            width: 800px; 
            height: 600px; 
            border: 2px solid white; 
            background: #333;
            margin: 20px 0;
        }
        .status {
            background: #444;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🔧 Simple Diagnostic Test</h1>
    
    <div class="status" id="status">
        Loading...
    </div>
    
    <div id="network"></div>
    
    <script>
        console.log('🔧 Starting diagnostic...');
        
        const statusDiv = document.getElementById('status');
        
        function updateStatus(message) {
            console.log(message);
            statusDiv.innerHTML += '<br>' + message;
        }
        
        // Check if vis.js loaded
        if (typeof vis === 'undefined') {
            updateStatus('❌ vis.js library not loaded!');
        } else {
            updateStatus('✅ vis.js library loaded successfully');
        }
        
        // Create extremely simple data
        const nodes = new vis.DataSet([
            {id: 1, label: 'Node 1', color: '#ff0000', size: 50, x: 100, y: 100},
            {id: 2, label: 'Node 2', color: '#00ff00', size: 50, x: 200, y: 200},
            {id: 3, label: 'Node 3', color: '#0000ff', size: 50, x: 300, y: 100}
        ]);
        
        const edges = new vis.DataSet([
            {from: 1, to: 2, color: '#ffffff'},
            {from: 2, to: 3, color: '#ffffff'}
        ]);
        
        updateStatus(`📊 Created ${nodes.length} nodes and ${edges.length} edges`);
        
        // Get container
        const container = document.getElementById('network');
        if (!container) {
            updateStatus('❌ Container not found!');
        } else {
            updateStatus('✅ Container found: ' + container.offsetWidth + 'x' + container.offsetHeight);
        }
        
        // Simple options
        const options = {
            physics: false, // Disable physics to prevent movement issues
            nodes: {
                font: { color: 'white', size: 16 },
                borderWidth: 2
            },
            edges: {
                color: 'white',
                width: 2
            }
        };
        
        try {
            updateStatus('🔧 Creating network...');
            const network = new vis.Network(container, { nodes: nodes, edges: edges }, options);
            updateStatus('✅ Network created successfully!');
            
            // Add click handler
            network.on('click', function(params) {
                updateStatus('🖱️ Clicked! Nodes: ' + params.nodes.length);
            });
            
            // Force redraw
            setTimeout(() => {
                network.redraw();
                updateStatus('🔄 Network redrawn');
            }, 1000);
            
        } catch (error) {
            updateStatus('❌ Error creating network: ' + error.message);
            console.error('Error details:', error);
        }
    </script>
</body>
</html> 