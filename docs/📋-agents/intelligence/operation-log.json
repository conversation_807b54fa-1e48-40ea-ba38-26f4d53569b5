[{"type": "analyzed", "path": "docs/.DS_Store", "hash": "44ee8557a7d53ea069f92908cb723e5a936746a71b2c873d657675613acc30ea", "timestamp": "2025-06-06T16:09:12.022Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/AutonomousGoalSetting-Development-Session-3.md", "hash": "01ebff590458b6d664af21f72984e3eb95c065a94d4ad31796554dfc45c50d92", "timestamp": "2025-06-06T16:09:12.022Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/AutonomousNotificationSystem-Development-Session-10.md", "hash": "af8e1aa4afa646aa9858ddb9b5ce56280ac942d89634049df85d8681fe64ea32", "timestamp": "2025-06-06T16:09:12.022Z"}, {"type": "analyzed", "path": "docs/ai-identified-component-consolidation-opportunities.md", "hash": "d71df0abc42e59db1ac73aa48e222af2cf6385a8a2d496f32d86f09966b066e8", "timestamp": "2025-06-06T16:09:12.023Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/ErrorMonitorAgent-Development-Session-1.md", "hash": "52487c0cb5883ad286565e16acb453cd8b3974aaaa73b3ca73a9c7e554f0b52a", "timestamp": "2025-06-06T16:09:12.023Z"}, {"type": "analyzed", "path": "docs/completed-features/AdvancedCanvasLayerManagementCompletion.md", "hash": "36a9e7f1c95b82ffb99743e0c5c4ce95d83c9a430aac59f84ef3cbfab6145068", "timestamp": "2025-06-06T16:09:12.023Z"}, {"type": "analyzed", "path": "docs/examples/env.example", "hash": "c13f5394523f768a7464d5128afd74e4a2a16aae87b8c42b245e0cd3f4f93084", "timestamp": "2025-06-06T16:09:12.024Z"}, {"type": "analyzed", "path": "docs/organization/comprehensive-organization-report.json", "hash": "ede07ea4e97b7e565cf62abdc678eb425044e23f4e4d37a77f86ec86337af92b", "timestamp": "2025-06-06T16:09:12.024Z"}, {"type": "analyzed", "path": "docs/organization/operation-log.json", "hash": "38b4a1e7e2483f2b00f0d8978b589789ae901b3c67cbf598e5363ba27dbc1371", "timestamp": "2025-06-06T16:09:12.024Z"}, {"type": "analyzed", "path": "docs/organization/structure/AUTOMATIC_FILE_ORGANIZATION.md", "hash": "8326acaf4e058ff47f20f29b533729fda2f22b2b135b5908336cdfe169c38434", "timestamp": "2025-06-06T16:09:12.025Z"}, {"type": "analyzed", "path": "docs/organization/structure/comprehensive-organization-report.json", "hash": "1b0f600307f2f7f8028b9358b00e76570e3d37bee1a8940c1590420b52266c90", "timestamp": "2025-06-06T16:09:12.026Z"}, {"type": "analyzed", "path": "docs/organization/structure/PROJECT_ORGANIZATION.md", "hash": "e8bc1ef198f5e0daa62ec4b40b8ef5d06e3562e1c76ad3a1f5b7bc9cc48e01e6", "timestamp": "2025-06-06T16:09:12.026Z"}, {"type": "analyzed", "path": "docs/security/policies/security-headers.js", "hash": "d0ec17f1ce29cbf01d4ae986824ea353825e617828b2c003d9711c683b3c3f63", "timestamp": "2025-06-06T16:09:12.026Z"}, {"type": "analyzed", "path": "docs/organization/structure/script-cleanup-completion-summary.md", "hash": "09ca13706f6f466379ed22f7bd6f3442cb10742be70fc110926b76deb79d5555", "timestamp": "2025-06-06T16:09:12.026Z"}, {"type": "analyzed", "path": "docs/security/policies/README.md", "hash": "78ff65d1c73f89f97ec1bf3d7eccd5f5ab016a1511d02d5a504ce0d787f29852", "timestamp": "2025-06-06T16:09:12.027Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/AdvancedDrawingToolsCompletion.md", "hash": "0ac1c9ef04de09fb240b8d7f45dde93fed23013ba05c793027371cab816ca7be", "timestamp": "2025-06-06T16:09:12.027Z"}, {"type": "analyzed", "path": "docs/security/policies/security-report.json", "hash": "ec2db362e3fec4aacafea0fb457aeb587ccc3185f47080d055ea45738cc906fa", "timestamp": "2025-06-06T16:09:12.028Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/CONTRIBUTING.md", "hash": "a2e4ae298bcd7c2b82bf9e9e7ed6c7ac86dd9babb1a66ac112e96e904505bd50", "timestamp": "2025-06-06T16:09:12.028Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/CURSOR_DAILY_WORKFLOW.md", "hash": "24dfc89018e61b5cb9c8298593156ccc44d7ff60dbfc684a7a80f6e96c301dc2", "timestamp": "2025-06-06T16:09:12.030Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/CURSOR_MEMORY_BANK_BACKUP.md", "hash": "ceb957568bbc433d91dba684354abc540e5ce9af2363e3435c38dba3b8b795b4", "timestamp": "2025-06-06T16:09:12.030Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/ConfigAgent-Development-Session-7.md", "hash": "99df6adf5c59b1cdeb4088706f84c9c14abbcf1427703dc68c09b1632b8a94b1", "timestamp": "2025-06-06T16:09:12.030Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/ConflictPreventionMechanisms.md", "hash": "40cf3c5a2edcccdc3b133764a489922b8732a752910529d2514ffe4fc9c490a2", "timestamp": "2025-06-06T16:09:12.031Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/ExtendedAgentConflictValidation.md", "hash": "209dbf8b40e58dbd2af93397cbddd673d2990dfdb5b76e80c38965de2e738ade", "timestamp": "2025-06-06T16:09:12.031Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/EnhancedAIIntegrationCompletion.md", "hash": "64a38bc34e353383fb744a42c4c0b17c777151bd2da3b726442e87f5fd7ffc19", "timestamp": "2025-06-06T16:09:12.031Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/OpsAgent-Development-Session-4.md", "hash": "6ec8563bdd162772aadb4c5373f24d643720b7deefd46d3d972890435224bcd5", "timestamp": "2025-06-06T16:09:12.031Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/OpsAgent-Interface.md", "hash": "c84dd75f34ac87ab6374effd226bd57b3dd98272a1ac70f0ee944162d0940504", "timestamp": "2025-06-06T16:09:12.031Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/LivingAgentDeploymentGuide.md", "hash": "0023dcc0a7f3acfcd47c4cd84855fb0917a221015d04a2dac39cf31954e91d56", "timestamp": "2025-06-06T16:09:12.032Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/Phase-1-Agent-Mapping-FINDINGS.md", "hash": "f15207eef593a6f89f17965c11dfc8914fec03c4e337e92dc82bf3b309bb4273", "timestamp": "2025-06-06T16:09:12.032Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/ProactiveAutonomyAgent-Development-Session-12.md", "hash": "b25be17fe527d7cf927d7996f286237981a1f6fb530f2cc7f0ea04515a9f75d2", "timestamp": "2025-06-06T16:09:12.032Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/Phase4-Standardized-Protocols-Demo.json", "hash": "****************************************************************", "timestamp": "2025-06-06T16:09:12.032Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/SECURITY_CHECKLIST.md", "hash": "ec425948e4ff1395c5d79399bbf6788fc335dde0ff9ed44cd2540f6601367cc8", "timestamp": "2025-06-06T16:09:12.033Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/SECURITY_STATUS.md", "hash": "0c91b0b5994b459670fcde0537546a9f0bae94ea8ef6bd697c2b59a2eed97d5b", "timestamp": "2025-06-06T16:09:12.033Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/SecurityAgent-Development-Session-11.md", "hash": "184328d4fd0cf851714a6c020266818174a42c54671e01acb7bbf9281ecd109e", "timestamp": "2025-06-06T16:09:12.033Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/SecurityAgent-Interface.md", "hash": "add4b7ab27c8d08bd705fbcca5c3bf6a89f8e2e24422d8a9169b5d418b8a9e4d", "timestamp": "2025-06-06T16:09:12.034Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/UIAgent-Development-Session-2.md", "hash": "e5ef2db8bdcd69a1d2282b13147a9204f686212c77119676995cdc5fc9c69251", "timestamp": "2025-06-06T16:09:12.034Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/UIAgent-Interface.md", "hash": "e7d4a513ea5d3cbb4d799e02d2d21132f39fb1ed6a7a718d581e12b070f29452", "timestamp": "2025-06-06T16:09:12.034Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/VectorMemory-Development-Session-9.md", "hash": "7f72d3fe46bef41d0325d78caa87ee50d7a11e3db1b6e003e22cb387d624cd4b", "timestamp": "2025-06-06T16:09:12.035Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/agent-ecosystem-analysis.json", "hash": "44b1f5ca2632e3dc39317a555ddf5b64341e51567cf87eda142e0685f22db24b", "timestamp": "2025-06-06T16:09:12.035Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/agent-ecosystem-map.json", "hash": "dba518f5a8d932cb6b1169985e593cdea8c25592ef4ea280d510df01261c0ad7", "timestamp": "2025-06-06T16:09:12.035Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/agent-system-analysis-and-ml-capabilities.md", "hash": "56cc78f84f3918101d93cd21c801f8c6ed85a309c7fe450f920ee47db17fdde2", "timestamp": "2025-06-06T16:09:12.036Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/approved-recommendations.json", "hash": "bba7778177cb353c5abf12461405b9d06e76661496a4833f601b77b1909900b8", "timestamp": "2025-06-06T16:09:12.036Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/any-types-report.md", "hash": "5118b99b5c2004ffdf61e4f9cd0c201dcb985ec5bfdfed3f4dad4129cab1861f", "timestamp": "2025-06-06T16:09:12.036Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/build-analysis.log", "hash": "16d1768cc52294e17a9ec2544276e62ed98cc6ef8a4b645107183f4e2bc07b88", "timestamp": "2025-06-06T16:09:12.036Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/check-status.js", "hash": "426976795d1ca8ea1ff28d65e972fab12d0d485a39f7b79471b9bbebdfd04686", "timestamp": "2025-06-06T16:09:12.036Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/continuous-progress.json", "hash": "5427dd9577bf874d33844070229b83fa8be5a3e6f83baf007ae98e46d33d9bf7", "timestamp": "2025-06-06T16:09:12.037Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/hook-dependency-report.md", "hash": "00f4f3a15e17961fbd524d7c89d58bda25fd9186996ecd7adb2e979f12237e94", "timestamp": "2025-06-06T16:09:12.037Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/design-system-update.json", "hash": "1092c197109d5f64811155405b65e9b06416e442ca7b6f33eb15ec79eec94dfc", "timestamp": "2025-06-06T16:09:12.037Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/img-to-image-report.md", "hash": "513c48117e23bd86dc80a33cae6d71051f03bbdf0957ff49c6b19dce1487c498", "timestamp": "2025-06-06T16:09:12.037Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/immediate-agent-validation-checklist.md", "hash": "0180753af5d384d6792d9bae82d72f569bc933174847c06c437c4184e3e71494", "timestamp": "2025-06-06T16:09:12.038Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/operation-log.json", "hash": "024afbe118b071b18310e9c188f599f369d50f64b994a6d309dcfd4e01b274eb", "timestamp": "2025-06-06T16:09:12.038Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/performance-report.json", "hash": "1ce4745494848f652df40e8c0466bc08adeb81aaee92021c22b37bc8be582922", "timestamp": "2025-06-06T16:09:12.038Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/progress-report.json", "hash": "dd463f76bca73ecef274e27bbba486733349ba5e024f18ddd0df880c02174ba5", "timestamp": "2025-06-06T16:09:12.039Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/performancemonitoringagent-refactoring-report.json", "hash": "1d3e6fcbc744b83d1c333a3c10d28f79a80ec177f58ed8d7dbd08ca1bf4e9319", "timestamp": "2025-06-06T16:09:12.039Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/phase5-performance-optimization-demo-report.json", "hash": "7e9b6b5d9288cb3014314be719cbf617c4e01fcb33f53ec054cbc6ae9459b53a", "timestamp": "2025-06-06T16:09:12.039Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/r1-collaborative-analysis-package.md", "hash": "2587716285f663322908987605d7f585940c14207a5d0ea5841c404028efa6f1", "timestamp": "2025-06-06T16:09:12.040Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/real-time-status.json", "hash": "75da95e0794131d24d17780c3f8cb1a59ec717d3a897eadd6290e608ce9a66fa", "timestamp": "2025-06-06T16:09:12.040Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/r1-direct-command.md", "hash": "c7550c552d466381e2ba10b4441ce5dc7bb4e9b964e1ee6ebe5832d0c0cc9339", "timestamp": "2025-06-06T16:09:12.040Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/test-intelligent-resource-manager.mjs", "hash": "bff2ba79688ab6d8816df0c0425dbc3ea8b40aa48dbad4e424c5c081132c587a", "timestamp": "2025-06-06T16:09:12.040Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/simple-ai-test.js", "hash": "a8306cf2f4726fd9bd7dba596034fba9b7dd81778f5814bf25e4f85c821f8dc8", "timestamp": "2025-06-06T16:09:12.040Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/tsconfig.production.tsbuildinfo", "hash": "91682d9c0008d57b0aceafd21706337268197c41b7df08d1e629e10ecb7d1794", "timestamp": "2025-06-06T16:09:12.040Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/tsconfig.test.tsbuildinfo", "hash": "84f3b9816d48091509dba853ee91ca9fe7095d8aa08d5883546b83f8fa1cadcc", "timestamp": "2025-06-06T16:09:12.041Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/two-agent-ai-workflow.md", "hash": "08abd8f89ed74e25101d03597a7466ce0b76e9667241b9ab6bd0d8ee1c7fc7a0", "timestamp": "2025-06-06T16:09:12.041Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/workflow-optimization-analysis.md", "hash": "d4de57a63fb0aa0954671d20ee6aeb28f8853f498b1bf90e4e54f6a1a6ecf461", "timestamp": "2025-06-06T16:09:12.041Z"}, {"type": "analyzed", "path": "docs/📊-reports/performance/design-system-update.json", "hash": "4ada60cdab6e41b1ef7bb34dabb43c9b8d929de8837e36a21bb8bbdc49e998f5", "timestamp": "2025-06-06T16:09:12.042Z"}, {"type": "analyzed", "path": "docs/📊-reports/summaries/design-consistency-report.json", "hash": "964523298f1b477c4b4e5a5f619af07c9b3fa0f9d834d538d1949e7eeb81dc5a", "timestamp": "2025-06-06T16:09:12.042Z"}, {"type": "analyzed", "path": "docs/📊-reports/summaries/progress-summary.md", "hash": "63e38342f38596d601af283110b94e405d33427d0c9139e2fb94c8f9c47eac29", "timestamp": "2025-06-06T16:09:12.042Z"}, {"type": "analyzed", "path": "docs/📊-reports/verification/agent-fix-verification.js", "hash": "23c663822b0cd25cdd06c3f76a4c53240cb2e581f77b92d06001f001c18a2a25", "timestamp": "2025-06-06T16:09:12.042Z"}, {"type": "analyzed", "path": "docs/📊-reports/summaries/r1-conflict-resolution-summary.md", "hash": "b86e9317510ac5565d001c33bfc81d5dcdefab74518868706d4f5b01e6578cd4", "timestamp": "2025-06-06T16:09:12.042Z"}, {"type": "analyzed", "path": "docs/📊-reports/verification/PredictiveGoalForecasting-Development-Session-8.md", "hash": "9d980dd32a3856ab0db8153b958acff6d114eb674b33fd20613349c6e35a62ef", "timestamp": "2025-06-06T16:09:12.042Z"}, {"type": "analyzed", "path": "docs/📊-visual-maps/agent-ecosystem-visualization.html", "hash": "377d14598a9512f9ef98d57ba3482097ff86907e24c75e834181076a7f4cbcf9", "timestamp": "2025-06-06T16:09:12.043Z"}, {"type": "analyzed", "path": "docs/📊-visual-maps/aco-agent-ecosystem.html", "hash": "7dcd84a71740b0703d199df307a286bc68d3966e5ebdd43afcae323834205cbc", "timestamp": "2025-06-06T16:09:12.043Z"}, {"type": "analyzed", "path": "docs/📊-visual-maps/debug-visualization.html", "hash": "4dbc2605b3d4a77fa31a6ec7346bd3d53931cd171119e9d9b975dbdf8bde7168", "timestamp": "2025-06-06T16:09:12.043Z"}, {"type": "analyzed", "path": "docs/📊-visual-maps/fixed-layout-test.html", "hash": "c732f388ad68f4e3e082ef9623d1e6c946e9092b4a61f274c08e00b7107b7ea9", "timestamp": "2025-06-06T16:09:12.043Z"}, {"type": "analyzed", "path": "docs/📊-visual-maps/simple-diagnostic.html", "hash": "675aa349bc68be07ed93a9820ec00aaa0ca99542a9e4b3e1dbf062f31edad03a", "timestamp": "2025-06-06T16:09:12.043Z"}, {"type": "analyzed", "path": "docs/📊-visual-maps/super-simple-test.html", "hash": "cffe8038199516c1345d0bb075432e3c75829fb9d9e5ff4717326e7ada2809d0", "timestamp": "2025-06-06T16:09:12.043Z"}, {"type": "analyzed", "path": "docs/📊-visual-maps/test-simple-visualization.html", "hash": "27e6fc051a46590e9acb9add4ee2a95b61bc6fe2620b9a832582e7dd549d410f", "timestamp": "2025-06-06T16:09:12.043Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/AGENT_ECOSYSTEM_UPDATE_SUMMARY.md", "hash": "3c00e730019169445e8199432d1164dc105562555b4d662e1c84a3d6f154fd40", "timestamp": "2025-06-06T16:09:12.044Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/AGENT_COMMUNICATION_STATUS.md", "hash": "6ba7296f7da2257c925b74fe85665d3522e61305f048f6d25439ab8f5f74273c", "timestamp": "2025-06-06T16:09:12.044Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/AGENT_INTELLIGENCE_DEVELOPMENT_GUIDE.md", "hash": "3b6880b34fe2e104b7a5a05eca5a13cc9aeefb12bd282ca482389f6967bbcb1a", "timestamp": "2025-06-06T16:09:12.044Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/AGENT_MIGRATION_COMPLETION_REPORT.md", "hash": "ba2bd8f6a3e538ab0f2e13c8f85b17974407875f0c9993ca86ebe105c8544b7c", "timestamp": "2025-06-06T16:09:12.044Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/AGENT_MIGRATION_PLAN.md", "hash": "2b214eec752453ca8368b0d8a5c5db45df0c95c31047bf754c3b56bfb57baa12", "timestamp": "2025-06-06T16:09:12.044Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/AGENT_TASKS.md", "hash": "937df64486a5cd112f05f30f2efe65e47b4406b5a2c143fa81182bc24e16cc6d", "timestamp": "2025-06-06T16:09:12.045Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/AGENT_WORKFLOW_INTEGRATION.md", "hash": "58cef87794b03c55c70db44b2c40dd6fb9ff4484103c8d8f31d67f54fded6700", "timestamp": "2025-06-06T16:09:12.045Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/AgentCodeTransformation.md", "hash": "628a4ac6bb78cc1d30307179cc07c69f80986015d4d48949a5a67b84794af849", "timestamp": "2025-06-06T16:09:12.045Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/FRONTEND_AGENT_ECOSYSTEM_VERIFICATION.md", "hash": "b5da49c10ffab8e70e6309dcdbf640da85a53a6acdbf38b3afb2694f7d48ae73", "timestamp": "2025-06-06T16:09:12.045Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/COMPREHENSIVE_FRONTEND_AGENT_ANALYSIS.md", "hash": "6fa002e664dc3cf6d7350dedf294e9abc6984e71485e5c97932a3a2f5b5ed0a9", "timestamp": "2025-06-06T16:09:12.045Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/FUNCTIONALITY_PRESERVATION_ANALYSIS.md", "hash": "5e0458d5425626f8118352b3e75226b684315126fe0f5c458fa21ed4eac9a2e1", "timestamp": "2025-06-06T16:09:12.045Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/docs-update-verification.md", "hash": "7bd714d253f44359537ca07977ce70f16f03b6733ab36c20aeed78b4f15562c4", "timestamp": "2025-06-06T16:09:12.046Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/docs-organization-system-update-summary.md", "hash": "5d85a4a8f256bd4507e256b1f17b188a1c2e42cc9443f7ab2b88bb3b5f0e6e1d", "timestamp": "2025-06-06T16:09:12.046Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/ROADMAP.md", "hash": "2ea56b72717add3a5ab248e63af621eadd468e1a2a71895f69c93302409e88c7", "timestamp": "2025-06-06T16:09:12.046Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/update-completion-summary.md", "hash": "a3d7dbea596c3bdf0c5a3362698ee113312da8e19dfbe608f836d2d2e4201052", "timestamp": "2025-06-06T16:09:12.047Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/operation-log.json", "hash": "f71dfaf6484171c47aa2de225ab4ff940efc212df6b2e4a73f90267ff58652a2", "timestamp": "2025-06-06T16:09:12.047Z"}, {"type": "analyzed", "path": "docs/📋-agents/intelligence/tasks.md", "hash": "52f159f07fcf0517672c4a62fd99e02a8b0cfebd567db6680f047d65c69e54f5", "timestamp": "2025-06-06T16:09:12.047Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/AUTOMATED_DOCUMENTATION_VERIFICATION_SOLUTION.md", "hash": "d07693ad9b9edbfa8f3debd8ca421a55336a66e25fc6e711a1d9a346afd3aec8", "timestamp": "2025-06-06T16:09:12.047Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/28-agent-ecosystem-final-report.json", "hash": "ed86a85f397c71f3501c3a5cc15f6953e6522121cb69347add8ea4b0cdd61d14", "timestamp": "2025-06-06T16:09:12.047Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/AgentOrchestrator-Development-Session-6.md", "hash": "5bae46feb6d9d907e2d96531251b0ab42defc3cf7d94111182eea7b0324a6ec7", "timestamp": "2025-06-06T16:09:12.047Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/AgentRoleDefinitions.md", "hash": "85ba8f87545388a8d98b5328e1271e0eec503abe6c54bb2d321610227bd7c911", "timestamp": "2025-06-06T16:09:12.048Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/Complete-Agent-Ecosystem-Analysis.md", "hash": "0d0185c2cee7a7674eb98ea56cc9cb997e0b490b9c4164dabfebb7a016ef8d8d", "timestamp": "2025-06-06T16:09:12.048Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/CANVAS_INTUITIVE_INNOVATIVE_UPGRADE.md", "hash": "f62a10c9a2bb504dbaf56c7e2efc237a9c350033403ab7687769df149b416699", "timestamp": "2025-06-06T16:09:12.048Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/DevAgent-Interface.md", "hash": "8232a603d0dad9918c4ff00e243ae57d5a801f7ddb98f116471e1a6758339b77", "timestamp": "2025-06-06T16:09:12.049Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/CreAItive_Agent_Build_Guide.md", "hash": "eb73e2aaa79ce011bdd2b58a983edb7b55e6aa77506cbffde00acd2a330090ae", "timestamp": "2025-06-06T16:09:12.049Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/DOCS_CLEANUP_SUMMARY.md", "hash": "325650351ab04270cd976254c232673ebb9ac7d4132dfb37d45a5a9a9da7c75d", "timestamp": "2025-06-06T16:09:12.049Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/FINAL_ACHIEVEMENT_SUMMARY.md", "hash": "6da7709ec4b98f64f3dce0c4e1052508e48487b6a52e9c36fab07fd03578e5cb", "timestamp": "2025-06-06T16:09:12.049Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/ENHANCED-INFORMATION-CAPTURE-SYSTEM.md", "hash": "659885cd1444a125626e1561871d7a5f970cbedc0c6e9d0f01f1cf19b7f75827", "timestamp": "2025-06-06T16:09:12.049Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/FINAL_CANVAS_TOOL_TEST.md", "hash": "ca290a6d14fd993e3e0d7cd167f7a911a2b8020519668b81d882915d1fc89dc3", "timestamp": "2025-06-06T16:09:12.049Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/FRONTEND_AUDIT_SUMMARY.md", "hash": "5299e7bb85e9793df697bd7b28d5b262c5a8843c84a2d3fd6e5a3c31dfd1c5e0", "timestamp": "2025-06-06T16:09:12.050Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/FRONTEND_TASK_MANAGER.md", "hash": "46e16e5a90c61a510dab40ada1a3a6fabdd09ef353e8293651766eb3c5bfebfe", "timestamp": "2025-06-06T16:09:12.050Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/FeatureDiscoveryAgent-Development-Session-14.md", "hash": "ce96bbd3a11e7f3aa8eb5c240e4d16794f7889d57fb546006cf1c22fc64c3bf2", "timestamp": "2025-06-06T16:09:12.050Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/INFORMATION-CAPTURE-SYSTEM.md", "hash": "4923727ad4f8b4f0baaa013a1fac966ca1700dcc3d71ba626cbe9ff1eabe0ee8", "timestamp": "2025-06-06T16:09:12.051Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/Phase-8.3.2-ML-Enhancement-Completion.md", "hash": "bbbf01f4b860de1adb622dc640f78a984178cda223b702c04a6d642383cd471a", "timestamp": "2025-06-06T16:09:12.051Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/LivingAgentImplementationChecklist.md", "hash": "72b9a3b42c65cb58dc708e71619b6b3bc0317e7ca29a1c9072e87114adb2fb56", "timestamp": "2025-06-06T16:09:12.051Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/Phase-9-Strategic-Roadmap.md", "hash": "2a7906a84e90ca6c63e222c5ddf8a27f16d40f0ba7088da785266ad61843a500", "timestamp": "2025-06-06T16:09:12.051Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/Phase-8.3.3-ML-Enhancement-Completion.md", "hash": "dc24d65a9b934300a57405d585c612896fec6bd5d517bc0723866092cf013c65", "timestamp": "2025-06-06T16:09:12.051Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/R1-Devstral-Agent-Ecosystem-Consensus.md", "hash": "3be8fae7ad11bbccb7710cd15cfea36c0285b3dbed7e25947641cba6ea16defa", "timestamp": "2025-06-06T16:09:12.051Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/RoleConflictAnalysis.md", "hash": "fce30b18472e215a89d02b7b78901bee543ceb9cc71c76f9a6b68dded01fc53e", "timestamp": "2025-06-06T16:09:12.052Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/README.md", "hash": "bb1080cb7a8d33476349f1810510d07dd3d034dbfabc8f42fcfe99ffe7a63dba", "timestamp": "2025-06-06T16:09:12.052Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/R1-Phase2-Implementation-Plan.md", "hash": "7de2f635703d35a59eb741e1859dd3c261359ad861720e5aa0c5ba9f62e829b9", "timestamp": "2025-06-06T16:09:12.052Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/SECURITY.md", "hash": "30ec85805944352fbc427cbef4ed38c14086c2116a6f72bce9635ced5d22d3aa", "timestamp": "2025-06-06T16:09:12.052Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/SCRIPT_CLEANUP_SUMMARY.md", "hash": "fabebca355c8d681ac7c8853d2114ab5f729ea5f72a67bd61ddcf322daddbbb2", "timestamp": "2025-06-06T16:09:12.053Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/STRATEGIC_IMPLEMENTATION_STATUS_REPORT.md", "hash": "4dc1d644f8ad9bc6fca8572e8d446a4071625867f6977f65b1f8183395a8570c", "timestamp": "2025-06-06T16:09:12.053Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/TASK-MANAGER-Total-System-Architecture.md", "hash": "e8124f9c4d99b2986b22c7cecf1a0654db1aea9a9bee3f4fc337d3343a20ced2", "timestamp": "2025-06-06T16:09:12.053Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/UNIFIED_WORKFLOW_SYSTEM.md", "hash": "e330808ce5b578f46acf62742dbd99bd95b01b7273786485e8bc502868dd9703", "timestamp": "2025-06-06T16:09:12.053Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/adr-0001-nextjs-frontend-framework.md", "hash": "b349711c3df6a0058e106207478345f35bb496dec757673297445ff4a17671d9", "timestamp": "2025-06-06T16:09:12.053Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/agent-development-tracking-report.json", "hash": "6c0cedfb3cfb19ea5ff3def2f46c0ef9c155d7ae2143a5e3323eb40bc3830df5", "timestamp": "2025-06-06T16:09:12.054Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/agent-development-command-center.md", "hash": "0a3e8cc1b040c7c11c2c15f89b4a5c05e509db293824eb49245572e4166ddcf7", "timestamp": "2025-06-06T16:09:12.054Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/agent-architecture-analysis-report.json", "hash": "4cd9e8920c33b4f185c4fa555c5f4f70c8fe3f002c2c116f56f8fa7ab9d949f1", "timestamp": "2025-06-06T16:09:12.054Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/agent-orchestration-roadmap.md", "hash": "0689201d68aa41e20f4060605da750ca45a64db3350e9bcc3695271abc69df88", "timestamp": "2025-06-06T16:09:12.055Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/ai-collaboration-protocol.md", "hash": "a8724faec25044f61917d2e948c682af24acca96580858d3d5e5d41cd7338a4a", "timestamp": "2025-06-06T16:09:12.055Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/ai-blockchain-proof-of-concept.md", "hash": "684927cf30b0d2532f54393a54a2b25f8b11fd4b782e139957e4f4657aee9ce1", "timestamp": "2025-06-06T16:09:12.055Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/ai-timeout-fix-resolution.md", "hash": "fc5cea6cdf67a488151bc5aecdbd612e797058a99dbc74580c56aa1d2df6b6a6", "timestamp": "2025-06-06T16:09:12.055Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/ai-powered-dual-phase-success.md", "hash": "9d51634796f3a576a7874926b604ea083a6e9608380bd86faca1b036d1a87017", "timestamp": "2025-06-06T16:09:12.055Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/autonomous-self-improvement-architecture.md", "hash": "0d313298704a1625657c584ef9d2d9da77b36e57b61f5b642f57a6a2921ecd68", "timestamp": "2025-06-06T16:09:12.055Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/blockchain-readiness-checkpoints.md", "hash": "2079e3a92756802b4c501a22b2c26d9949d6426d44e5d2d854c22db286df99bb", "timestamp": "2025-06-06T16:09:12.056Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/completion-results.json", "hash": "80f5ae4145316d58e273601a90c5eb28103b1e6e5675021a3e166d889d823cf3", "timestamp": "2025-06-06T16:09:12.056Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/comprehensive-agent-development-integration.md", "hash": "688bcc4979096aeaced131740f07c42476c61397d93bf94a6a331df82ce67915", "timestamp": "2025-06-06T16:09:12.056Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/consciousness-evolution-roadmap.md", "hash": "6a64c50a9885946bd93392a45d963b62174e3104fed56b3a5524d92b97c76108", "timestamp": "2025-06-06T16:09:12.057Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/comprehensive-agent-status-analysis.md", "hash": "787a3f25573da56d547a17a0af70900b7c5e27948bef6b31047985f99d23a195", "timestamp": "2025-06-06T16:09:12.057Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/comprehensive-docs-organization.md", "hash": "6781db2fec7dcdae8d69755b3204b7436dd285d49c4b5fc598a34ef194720e27", "timestamp": "2025-06-06T16:09:12.057Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/creative-canvas.md", "hash": "a363244588372b87f6938d5ff0b1a6bcbde8937d45bdf499f30bd0a8b250a4a3", "timestamp": "2025-06-06T16:09:12.058Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/consolidate-duplicate-button-components.md", "hash": "dfb2baa2e342afbdea7c29ee5b05c7b2c5bf7565a0938336d3d908b6acf1f4b6", "timestamp": "2025-06-06T16:09:12.058Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/consciousness-research-analysis.md", "hash": "fce24524fe6436a6ad7b50c7b26dcb96eb093026d9b1e1c4858bd44497f0baac", "timestamp": "2025-06-06T16:09:12.058Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/deep-compliance-audit-report.json", "hash": "44b8fc777e8694c45fa0c518d17d0e170ca9b314fdc679d8e17faa60a57d1844", "timestamp": "2025-06-06T16:09:12.058Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/critical-mcp-impact-analysis.md", "hash": "5ac7ebd9c92cb3302edd2885cff7ee51d9676442dbf83c41590978c04ca14771", "timestamp": "2025-06-06T16:09:12.058Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/decentralized-ai-resource-economy.md", "hash": "11768b78b8d07a14067feb802e6854537458ea01464077ba784847f57881e486", "timestamp": "2025-06-06T16:09:12.058Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/documentation-verification-1748947347613.json", "hash": "a44e599472d47b8870c84cf4dc12828519b7afe053c52ae165fd6d9622878813", "timestamp": "2025-06-06T16:09:12.059Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/documentation-verification-1748947364058.json", "hash": "6a4132b0a73d3ef5b4af1e4595eacf3c87be07f3af536b4d0ccdadbf5ffe1013", "timestamp": "2025-06-06T16:09:12.059Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/documentation-verification-1748947369921.json", "hash": "0a408cd048f0bb30c825db38bff6398acc21fba55f2586901ef93a653c3e4e0f", "timestamp": "2025-06-06T16:09:12.059Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/documentation-verification-1748947406714.json", "hash": "aa1c882703a6c7554cf9e91b52922248ae33fc2e508165d594438a268d98fc82", "timestamp": "2025-06-06T16:09:12.060Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/file-management-strategy.md", "hash": "bd578d7d52952ff2cf00ba0791a865dd4eb858b486496752ece00ea845021c40", "timestamp": "2025-06-06T16:09:12.060Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/file-organization-enhancement.md", "hash": "a6a6ed30e24a3a4ccdddf0d88fb7ae6d5bff4854085b323a2e6cfb5c2d4bdabb", "timestamp": "2025-06-06T16:09:12.060Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/frontend-backend-integration-status.md", "hash": "719204460f3830e5b17099a47c0357b688eea18872da7fd1a9ff6eae5c05832a", "timestamp": "2025-06-06T16:09:12.060Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/implementation-monitor.md", "hash": "38031701cc15d2322eb27e282050dfe7bb55ad1817bf5fd104b3ea66cb025290", "timestamp": "2025-06-06T16:09:12.060Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/implementation-helpers.md", "hash": "ccf3020d28be2ba7c9083aefc3490a5d01d3b369ac16b8c72a9f0e4021d2021f", "timestamp": "2025-06-06T16:09:12.060Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/master-architecture-plan.md", "hash": "ea54445a84a255add5c0fdb5ed7ea5feb965e9f35221ecdaa1f97002a1915d7e", "timestamp": "2025-06-06T16:09:12.061Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/intelligent-agent-workflow-analysis.md", "hash": "75e3a0b1c23cb0bc69d937e816b4d03c7afb2073d9a31c632a5a30a235693eab", "timestamp": "2025-06-06T16:09:12.061Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/master-implementation-strategy.md", "hash": "bcb5b9bc5c1d55b316c2561846051a12ec3e57788f0ee350c96b51b6d2d482f2", "timestamp": "2025-06-06T16:09:12.061Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/mcp-implementation-complete.md", "hash": "e24dfc717aff50bedaf45f600be198d716386d51e547f2e9608e694ca4ab4455", "timestamp": "2025-06-06T16:09:12.062Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/model-comparison-results.md", "hash": "7400b880c804def657ffb04ca72b17e7f4e81a698a7dfb5802ab7ef608b22ff1", "timestamp": "2025-06-06T16:09:12.062Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/mcp-integration-implementation-guide.md", "hash": "6c7c98fe7ad886104d523120b071fc7b806be58e8b070f9d2b895f60bd48992d", "timestamp": "2025-06-06T16:09:12.062Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/phase6-integration-validation-demo-report.json", "hash": "23366fc3289f0e4b89002ff707b6ffeec63925b5818efecc0c3354b59acdad2d", "timestamp": "2025-06-06T16:09:12.062Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/phase1-implementation-guide.md", "hash": "92a3da3afac97de6f550dd4adf85260abd66405ce8cb25c6d43cd6b735ca69c4", "timestamp": "2025-06-06T16:09:12.063Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/pheromone-trails.json", "hash": "be2f12e0127e052d7544c368ea2244a478f8e4a5fa09cd9064a672d623ddcee4", "timestamp": "2025-06-06T16:09:12.063Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/quantum-agents-bulletproof-implementation-guide.md", "hash": "7442a0f3785163587193169319219bb08b001726b4d0fde1464acb02590a7fb8", "timestamp": "2025-06-06T16:09:12.063Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/quantum-agents-implementation-guide.md", "hash": "5a69b3f6de3e9f21e02f3fcc238e4c36f26aa3691b69f789e1a13dc187b28035", "timestamp": "2025-06-06T16:09:12.063Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/quantum-core-implementation.md", "hash": "c08865007489b3cacc421e34866908b7540fff5fe1a1dc68ccea5c3d37bf2f75", "timestamp": "2025-06-06T16:09:12.063Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/quantum-quick-start-guide.md", "hash": "b7d49f37743777f3912033ccbf688d9d871228a981deff87d6e80b1494f660bb", "timestamp": "2025-06-06T16:09:12.064Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/revolutionary-ai-blockchain-architecture.md", "hash": "cc0b1d9f47255900fdcbb76391e5feba4281080b172c70a9d84f606bed2d2712", "timestamp": "2025-06-06T16:09:12.064Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/r1-devstral-consensus-plan.md", "hash": "f0e58e7b6c17144a633da59d50624f0787985cc2a133f4182c1e1909a565a6d3", "timestamp": "2025-06-06T16:09:12.064Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/task-management-completion-day-16.md", "hash": "b08e6d746c4af73fce4aab5b50714658dffacbb75ccba6c5a1e390f4c61670cc", "timestamp": "2025-06-06T16:09:12.065Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/testing-command-center.md", "hash": "793827752633c198bf23886ea8ede36b5209d1943bffb411616927cd8e37baa3", "timestamp": "2025-06-06T16:09:12.065Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/tasks.md", "hash": "449e70875ab9769f6032444bea2faf2b89a0583132718e66dd7dcf9decaef663", "timestamp": "2025-06-06T16:09:12.065Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/comprehensive-ai-test.js", "hash": "a554ea9ba7ce495d0c30fa342063168696780d02fe993701c2727cae8f986d41", "timestamp": "2025-06-06T16:09:12.065Z"}, {"type": "analyzed", "path": "docs/📋-architecture/decisions/typescript-revolution-quick-reference.md", "hash": "1aa2b5849092ca7cf938f54f1445c1090f7061f614471c79b46d17c30e9fe7c9", "timestamp": "2025-06-06T16:09:12.066Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/AgentResponsibilityMatrices.md", "hash": "4ccd5663d10b76f918968c425ce390a610692fe24b0c49111737f0a3f4fae141", "timestamp": "2025-06-06T16:09:12.066Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/comprehensive-success-test.js", "hash": "e05653c94db1222af466f2c1905729b369a178b8774253d9b9c6e9a75e82c7fa", "timestamp": "2025-06-06T16:09:12.066Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/force-agents-use-real-ai.js", "hash": "e50fe23d9a108b50382bfeabff261817e07eb491ea1051ad5810702a33239d3b", "timestamp": "2025-06-06T16:09:12.066Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/monitor-ai-progress.js", "hash": "b5c430d6af29f758bad873493c42390b1a9bc276d380c126fc90b17688614575", "timestamp": "2025-06-06T16:09:12.066Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/optimized-ai-test.js", "hash": "1a5788b0ef5f6cc64f7fa43b2373e1081236cd708885266fe07f59f995ff8f08", "timestamp": "2025-06-06T16:09:12.067Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-all-real-agents.js", "hash": "414199689dc8f72cae399a00500d7f6d8afb8b8595c07517e99057964d488fb4", "timestamp": "2025-06-06T16:09:12.067Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-direct-r1-ai.js", "hash": "bcabfdebbdb40707038e9adb7a1d0a7466fb6e94bd55287416551b8319bdce79", "timestamp": "2025-06-06T16:09:12.067Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-living-agent-intelligence.js", "hash": "b88768c9059f39d0b72a7426e13a8cbd7467be64982b7b52254749e2331bea69", "timestamp": "2025-06-06T16:09:12.067Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-r1-ai-fix.js", "hash": "430258ba358628c5c82c74e374d64cf41e2b2a32828bf24afae460fdc9870d62", "timestamp": "2025-06-06T16:09:12.067Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-real-ai-agents.js", "hash": "0c983171ab490c7b67da74b2f22dcf2521fc7c9ab9ae2896e57d06aa7aa807f5", "timestamp": "2025-06-06T16:09:12.067Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-real-ai-processing.js", "hash": "7627cba03811ca0d5dcc046b98734d25c80e98a6bf65519b00f80c151e69233c", "timestamp": "2025-06-06T16:09:12.067Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-timeout-fix.js", "hash": "858da337733df110a13faf13ce44188db57c687deefaecb9eb39aa56659dbc5b", "timestamp": "2025-06-06T16:09:12.067Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-real-test-agent.js", "hash": "47b2203f2aec599f7a07b2cbe2bf734b1de98d073b91d57d4457b6253c68d9ca", "timestamp": "2025-06-06T16:09:12.067Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/trigger-real-ai.js", "hash": "1c6870917ef478a9349e1063415e4bbe49178ce644dd718e6cfab73a31b97264", "timestamp": "2025-06-06T16:09:12.068Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/DOCUMENTATION_WORKFLOW.md", "hash": "a854af7d2240dc95df2d2078ef0608fbb90f9cfd918887b81fb12d33779c7224", "timestamp": "2025-06-06T16:09:12.068Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/LivingAgentUtilizationPlan.md", "hash": "09c9aba8a31aa0883b7486567a290fbf05c57df667e8a58f27a45d6572f8c4b9", "timestamp": "2025-06-06T16:09:12.068Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/ORGANIZATION_ENHANCEMENT_SUMMARY.md", "hash": "5eada5d090256fae9d80281195372effcc13541fe649fdbd7be09b176e67079e", "timestamp": "2025-06-06T16:09:12.068Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/LivingAgentUtilizationSummary.md", "hash": "bc3f881d7aa71a20bc9be76d8f4731c89fff77be2ab696c75d9ff39b1ef914b3", "timestamp": "2025-06-06T16:09:12.068Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/PROJECT_ORGANIZATION_ENHANCED.md", "hash": "6b41ef8b9296c95723032f1ff8ab053a743ded70aaf8b155574dcb3d6da04b83", "timestamp": "2025-06-06T16:09:12.068Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/WorkflowEnhancementAgent-Development-Session-13.md", "hash": "31347e3efde7ede51e824d4e0371d72e645c3e56a080c9f8b0082dd901d7f882", "timestamp": "2025-06-06T16:09:12.069Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/R1-Implementation-Success.md", "hash": "fd3ac28aeeb9e902775ca46812badc45dea5fac99e886ecb2c40bca7524e421f", "timestamp": "2025-06-06T16:09:12.069Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/ROADMAP.md", "hash": "752b3a138af2154d5c48c487aa1d3ab1e5ec60048e2da451d70a9c9206d0ee93", "timestamp": "2025-06-06T16:09:12.069Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/agent-refactoring-verification-report.json", "hash": "b68d2bb7b5c7b65a4002884ca368cfbe966bd443bce309d6a9dc1f85eb36ec7f", "timestamp": "2025-06-06T16:09:12.070Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/agentmesh-refactoring-report.json", "hash": "6d4c71f7e3b79d7e218c34815d47731e967ce02bc7599917f16b04713344a4d1", "timestamp": "2025-06-06T16:09:12.070Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/autonomousdevagent-refactoring-report.json", "hash": "c687551b10932aec22c6604c41be94ac6f6df3fd8cfdbe79991ef3cc9c98db18", "timestamp": "2025-06-06T16:09:12.070Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/chatresponseparseragent-refactoring-report.json", "hash": "c39f7282601b22592324d8abc6df2d8f766baa334dbad0aa39a5b72835fcd31a", "timestamp": "2025-06-06T16:09:12.070Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/configagent-refactoring-report.json", "hash": "67a0f7f69fe29218a1549d3980914e1151e532df6dcfd6d791ac9e1ada323d66", "timestamp": "2025-06-06T16:09:12.070Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/conversationaldevagent-refactoring-report.json", "hash": "078bf4b7fad2a446d1e5c1f690d3e03f2a76ff3cebda449cd04f95e8a1ecb0ec", "timestamp": "2025-06-06T16:09:12.070Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/crossagentcommunicationengine-refactoring-report.json", "hash": "56c215a02c13754362214a594f6a7fcf0bdb02ec5b910a4f04daf3e121239bd7", "timestamp": "2025-06-06T16:09:12.071Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/development-workflow-strategy.md", "hash": "8c68acebd30854b0799e57e08fc93ada44c9ead940151ee51fcc81f826f051c3", "timestamp": "2025-06-06T16:09:12.071Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/devagent-refactoring-report.json", "hash": "ef21ea66f9ee3e1b6952ce18f3af834f84f72eed771bbbecd3e7f209380e6208", "timestamp": "2025-06-06T16:09:12.071Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/featurediscoveryagent-refactoring-report.json", "hash": "51637fa40330379ac3fc7dead93f9253a2945e71d75746591475de9756cf43e2", "timestamp": "2025-06-06T16:09:12.072Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/livinguiagent-refactoring-report.json", "hash": "09f9f52a5a527b30184b26867d7a932279c4e520d7ac310faf8a57369eaa9977", "timestamp": "2025-06-06T16:09:12.072Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/errormonitoragent-refactoring-report.json", "hash": "64233cbe3b7e111fda89046c5d19b657abdd89f226c06ee9d025ee325ae4fe9d", "timestamp": "2025-06-06T16:09:12.072Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/opsagent-refactoring-report.json", "hash": "52ab1616f1437cf4f61c8f0e09991be00d8e876e82af0716ee92dee7732de691", "timestamp": "2025-06-06T16:09:12.072Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/securityagent-refactoring-report.json", "hash": "a51f24358118a407c6f66ee814e672f1d988a617dc80ea52e5c8077d2fab0b54", "timestamp": "2025-06-06T16:09:12.072Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/operation-log.json", "hash": "a53353ea678c995a3ef2ea379436241c203980cbe7f5d2eb5688324e5c5abd2b", "timestamp": "2025-06-06T16:09:12.072Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/ultimatetranscendenceorchestrator-refactoring-report.json", "hash": "a83f9a19c02f93694990c0019d817516791a3a15346bead18cf58139b38802d2", "timestamp": "2025-06-06T16:09:12.073Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/uiagent-refactoring-report.json", "hash": "9761c397a552d95a2ffae6364b061609798ee80f6a460d2d1fa6d96d75ceed41", "timestamp": "2025-06-06T16:09:12.073Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/success-patterns-guide.json", "hash": "142cf135a40d055b1634607b1c26a8e4f9a7f37f002b77c322b0c2815ceb581e", "timestamp": "2025-06-06T16:09:12.077Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/userbehavioragent-refactoring-report.json", "hash": "a26af39c9268630ccc1659313286cab6d8c5d40907aefbf2e76b3dc0520a913c", "timestamp": "2025-06-06T16:09:12.078Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/unified-integration-solution.md", "hash": "c44a976bc5ea9d29dc1d84bf9611d8cf43e9080bb9b179b9e06ff13b8fb5b202", "timestamp": "2025-06-06T16:09:12.078Z"}, {"type": "analyzed", "path": "docs/📋-methodologies/revolutions/workflowenhancementagent-refactoring-report.json", "hash": "fc88eba2fb0243cce7d1be8db87118dea8d4039994b118b1bee845009f2f43c9", "timestamp": "2025-06-06T16:09:12.078Z"}, {"type": "analyzed", "path": "docs/📋-project/tasks/Complete-Agent-System-Analysis.md", "hash": "544a0ffb3ed176793eab88d2008b0f3620ed91408c29a9e824a7d2e4d4af8779", "timestamp": "2025-06-06T16:09:12.079Z"}, {"type": "analyzed", "path": "docs/📋-project/tasks/PHASE1-CONSOLIDATION-REPORT.md", "hash": "9c62bfb0a1945de62502c1d86057c0647eb19aad95dc937c814034531558e237", "timestamp": "2025-06-06T16:09:12.079Z"}, {"type": "analyzed", "path": "docs/📋-project/tasks/Devstral-Agent-Architecture-Analysis.md", "hash": "a68a23f9c912d599df3ddbae7c6bea86f143ea7ea8704a253a886ec07f5ae0fd", "timestamp": "2025-06-06T16:09:12.079Z"}, {"type": "analyzed", "path": "docs/📝-technical/solutions/enhanced-keyboard-navigation-system.md", "hash": "bc546a5c6a883ca741d642bfeda80d5975b027e469fa48aa0dd29764bec05074", "timestamp": "2025-06-06T16:09:12.079Z"}, {"type": "analyzed", "path": "docs/📝-technical/quantum-integration-examples.md", "hash": "4375aefea20948461478e80a64755f47cd9dc098d0a2fa183b5f399a89a77efe", "timestamp": "2025-06-06T16:09:12.079Z"}, {"type": "analyzed", "path": "docs/📝-technical/solutions/README.md", "hash": "d3c634c797a315cfc19e36772f8c02d432b397452ca7e5d017e6ebaae5e321a3", "timestamp": "2025-06-06T16:09:12.079Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build-optimized.log", "hash": "db8bc8afd219b7de451f745d3280c9f0b34c3b5b3076795fd49986e2092c63d3", "timestamp": "2025-06-06T16:09:12.080Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build-phase6-final.log", "hash": "f99c4750448e2e56adb65d0f94b872c99f4100fdd4d95edd1de395b1dc92667f", "timestamp": "2025-06-06T16:09:12.080Z"}, {"type": "analyzed", "path": "docs/📝-technical/solutions/interface-conflict-prevention-system.md", "hash": "172a59764b6bd57315d3abfe5b113e09a675ca29841cb37012386d683ebcbd2c", "timestamp": "2025-06-06T16:09:12.080Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build-phase6-optimized.log", "hash": "4c7023b6fb2ec505ac14a196e685b1501193364cc7cce7d52ac7feaa0172dd7a", "timestamp": "2025-06-06T16:09:12.080Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build-task6.2-complete.log", "hash": "eab7b5b61dcc85510d552991e75d7d5cc337e88986d1d00e4b388821d20e29f7", "timestamp": "2025-06-06T16:09:12.080Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build.log", "hash": "073d21e3897f69f889e0363f65190d76812f4252c219f8cb2b334d6e4eba7f23", "timestamp": "2025-06-06T16:09:12.080Z"}, {"type": "analyzed", "path": "docs/🔧-utilities/scripts/fix-common-errors.js", "hash": "afa08b1ffc91b69da30b2b6a136cc974f7dd46caa83486ebdc4b333721d5295f", "timestamp": "2025-06-06T16:09:12.081Z"}, {"type": "analyzed", "path": "docs/🔧-utilities/scripts/optimization-report.json", "hash": "aa87c3e9637bc259ed88107891df9ac8f62e2d774273522375c2c65039f7e8d6", "timestamp": "2025-06-06T16:09:12.081Z"}, {"type": "analyzed", "path": "docs/🔍-logs/dev-server.log", "hash": "4e0f982bcc401ff5bb07d2bc9cca4e417a20010ede9c121027384529b70524b8", "timestamp": "2025-06-06T16:09:12.095Z"}, {"type": "analyzed", "path": "docs/🔧-utilities/scripts/start-dev.sh", "hash": "806024bf83a1a1f3cbb1c9e4d9e0c4bf6555c36fbbe7db0896571b4b44e1f7d9", "timestamp": "2025-06-06T16:09:12.095Z"}, {"type": "analyzed", "path": "docs/🔧-utilities/servers/server.js", "hash": "1a6d34b00ef8a2d7972b2a00a5438bdec8a572fe23fd457f396388a31697a037", "timestamp": "2025-06-06T16:09:12.095Z"}, {"type": "analyzed", "path": "docs/🔧-utilities/scripts/start-dev-clean.sh", "hash": "a768f5590258d9b5ad53b3fe6412a9f23f471dfe90f999944923d11f6ea5e5bf", "timestamp": "2025-06-06T16:09:12.095Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/AI_ENHANCEMENT_TESTING_GUIDE.md", "hash": "5fb50bb8ba1abd2f0f8d6fe32e00f5cf08db6e723b38898d7c19c77cfeedd103", "timestamp": "2025-06-06T16:09:12.096Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/Agent-Migration-Integration-Report.json", "hash": "70b2919390e98df2397ab1412322246f68a687b4570ad351a7750422b9716a4d", "timestamp": "2025-06-06T16:09:12.096Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/CANVAS_TEST_REPORT.md", "hash": "0e43575b2809e7b5c0bbb652407230dbbd4ecdc27a563dc51684e24baf9cd0a7", "timestamp": "2025-06-06T16:09:12.097Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/CreAItive_All_Combined.md", "hash": "4d0da6a34606764c872ab3dd3f550e58c49953041441d2f953ca6b1ce6bae73f", "timestamp": "2025-06-06T16:09:12.097Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/HYBRID_INTELLIGENCE_STRATEGY.md", "hash": "08b0c3da3b20e3975be16da94001be088d253d2fbbe968885a3010014b936297", "timestamp": "2025-06-06T16:09:12.097Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/STRATEGIC_REAL_IMPLEMENTATION_APPROACH.md", "hash": "816d336e8de02f51d9cac9f85c3b9e5560c6332ad8a4e50451c768c8bfcdc03e", "timestamp": "2025-06-06T16:09:12.097Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/SUCCESS-PATTERNS-GUIDE.md", "hash": "b40c71851e81b47b59afb021b98a67438fb4c63acc0a135ca4fb5eab502b61da", "timestamp": "2025-06-06T16:09:12.098Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/TestAgent-Interface.md", "hash": "edeacf59d7c9a879ce32f614af47130f9d9fe349f6ad96d992964705a18fa434", "timestamp": "2025-06-06T16:09:12.098Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/TestAgent-Development-Session-5.md", "hash": "86b2bce34c154006c09df5801deebf26404b0157652b9c9d93ed73d273baf691", "timestamp": "2025-06-06T16:09:12.098Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/ai-enhancement-test-report.json", "hash": "18fe96113ff55004d508904522e4b084c01cf1602e1b8461c2b438db9b53f62a", "timestamp": "2025-06-06T16:09:12.099Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/canvas-test-results.json", "hash": "cb51adb7041904573f5d82f7355132da4e8a2071b8aa4ec48b80bd30a36329af", "timestamp": "2025-06-06T16:09:12.099Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/automated-visual-regression-testing.md", "hash": "c13527ce04957faaa1ced532b3c1de1c549d2b1d88e55f6ed097c67f414b926a", "timestamp": "2025-06-06T16:09:12.099Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/comprehensive-docs-organization-final-report.md", "hash": "066af57c63453462f6616e4db22ef0a45a24f925f28b2c52bcd6aa7c29111282", "timestamp": "2025-06-06T16:09:12.100Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/enhanced-phase-strategy-summary.md", "hash": "4fd090a957a6a6e759b2bf85eb9e2d519c39462a3448761492246246395c6568", "timestamp": "2025-06-06T16:09:12.100Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/extraction-report.json", "hash": "8772b168e24a029c9529a572e6224d87eac888ac17ad1ea7df8c9f6ebadc92b9", "timestamp": "2025-06-06T16:09:12.100Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/mvp-specification.md", "hash": "b95e0c7534cbfd323a43e69b2e118e15c866407d39cc2513a10da15b21aad2e3", "timestamp": "2025-06-06T16:09:12.101Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/model-optimization-results.md", "hash": "9790a6ea14645fe9e723f9256c8463bc32469d428a70b399663690eccfe27f49", "timestamp": "2025-06-06T16:09:12.101Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/log-management-solution.md", "hash": "26be3ea0fb6715389f9670b58c9bf4ed2b5c642786098f0ab68842a0ed32b912", "timestamp": "2025-06-06T16:09:12.101Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/test-canvas-functions.js", "hash": "d7ab61ca9c59d693bfcedff39cfe01ab56cab878260770ae8a65dc0694a7de56", "timestamp": "2025-06-06T16:09:12.101Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/test-canvas-interactions.html", "hash": "0c01176689830c21554d044f77e099fa2c31de6caf84e7dd0dda61626b0a458b", "timestamp": "2025-06-06T16:09:12.101Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/optimization-analysis-results.md", "hash": "6b8330447d55b0a88aee01784bb3bcf19bc9a11ff777a5f22f9e7073111d8d1c", "timestamp": "2025-06-06T16:09:12.101Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/test-demo.js", "hash": "ebd64057faf01f340b63c22b0a93fa7db01f685e49ac9e7110bf09d7259fb72d", "timestamp": "2025-06-06T16:09:12.101Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/testagent-refactoring-report.json", "hash": "8939b5fac8a90ce680c0b64837adf6cba0429c7f45b80b1919d872f6acd591b2", "timestamp": "2025-06-06T16:09:12.104Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/test-results.json", "hash": "4a5edf490cdb491e8e7271f5d175db685c20bc54b5467990c39e5d0bf719f1c0", "timestamp": "2025-06-06T16:09:12.106Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/tsconfig.test.json", "hash": "80b1d331c29fe435071d628afaf858b45bd6df46cb9028a5f9c407dbc9a55870", "timestamp": "2025-06-06T16:09:12.107Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/typescript-error-resolution-revolution.md", "hash": "ddf063d3c6aa1d274c93edb8171200fb7c35cf7e14ef96ebb2c7f514ca58672e", "timestamp": "2025-06-06T16:09:12.107Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/tsconfig.production.json", "hash": "bb72656822cd7cb469b2ba6070d0dd9c580c677d8fca082f79d83214b890e588", "timestamp": "2025-06-06T16:09:12.107Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/unified-system-command-center.md", "hash": "3ed43bc162b1d9726dab5202955d1def685b29a71ff306d6aa875dd8a3e56498", "timestamp": "2025-06-06T16:09:12.107Z"}, {"type": "moved", "source": "docs/organization/structure/AUTOMATIC_FILE_ORGANIZATION.md", "target": "docs/organization/structure/AUTOMATIC_FILE_ORGANIZATION.md", "category": "organization", "confidence": 0.65, "sourceHash": "8326acaf4e058ff47f20f29b533729fda2f22b2b135b5908336cdfe169c38434", "targetHash": "8326acaf4e058ff47f20f29b533729fda2f22b2b135b5908336cdfe169c38434", "timestamp": "2025-06-06T16:09:12.110Z"}, {"type": "moved", "source": "docs/organization/comprehensive-organization-report.json", "target": "docs/organization/structure/comprehensive-organization-report.json", "category": "organization", "confidence": 0.65, "sourceHash": "ede07ea4e97b7e565cf62abdc678eb425044e23f4e4d37a77f86ec86337af92b", "targetHash": "ede07ea4e97b7e565cf62abdc678eb425044e23f4e4d37a77f86ec86337af92b", "timestamp": "2025-06-06T16:09:12.110Z"}, {"type": "moved", "source": "docs/organization/structure/comprehensive-organization-report.json", "target": "docs/organization/structure/comprehensive-organization-report.json", "category": "organization", "confidence": 0.65, "sourceHash": "1b0f600307f2f7f8028b9358b00e76570e3d37bee1a8940c1590420b52266c90", "targetHash": "1b0f600307f2f7f8028b9358b00e76570e3d37bee1a8940c1590420b52266c90", "timestamp": "2025-06-06T16:09:12.110Z"}, {"type": "moved", "source": "docs/organization/structure/script-cleanup-completion-summary.md", "target": "docs/organization/structure/script-cleanup-completion-summary.md", "category": "organization", "confidence": 0.65, "sourceHash": "09ca13706f6f466379ed22f7bd6f3442cb10742be70fc110926b76deb79d5555", "targetHash": "09ca13706f6f466379ed22f7bd6f3442cb10742be70fc110926b76deb79d5555", "timestamp": "2025-06-06T16:09:12.111Z"}, {"type": "moved", "source": "docs/organization/structure/PROJECT_ORGANIZATION.md", "target": "docs/organization/structure/PROJECT_ORGANIZATION.md", "category": "organization", "confidence": 0.65, "sourceHash": "e8bc1ef198f5e0daa62ec4b40b8ef5d06e3562e1c76ad3a1f5b7bc9cc48e01e6", "targetHash": "e8bc1ef198f5e0daa62ec4b40b8ef5d06e3562e1c76ad3a1f5b7bc9cc48e01e6", "timestamp": "2025-06-06T16:09:12.111Z"}, {"type": "moved", "source": "docs/organization/operation-log.json", "target": "docs/📋-agents/intelligence/operation-log.json", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "38b4a1e7e2483f2b00f0d8978b589789ae901b3c67cbf598e5363ba27dbc1371", "targetHash": "38b4a1e7e2483f2b00f0d8978b589789ae901b3c67cbf598e5363ba27dbc1371", "timestamp": "2025-06-06T16:09:12.111Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/AGENT_ECOSYSTEM_UPDATE_SUMMARY.md", "target": "docs/📋-agents/intelligence/AGENT_ECOSYSTEM_UPDATE_SUMMARY.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "3c00e730019169445e8199432d1164dc105562555b4d662e1c84a3d6f154fd40", "targetHash": "3c00e730019169445e8199432d1164dc105562555b4d662e1c84a3d6f154fd40", "timestamp": "2025-06-06T16:09:12.112Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/AGENT_COMMUNICATION_STATUS.md", "target": "docs/📋-agents/intelligence/AGENT_COMMUNICATION_STATUS.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "6ba7296f7da2257c925b74fe85665d3522e61305f048f6d25439ab8f5f74273c", "targetHash": "6ba7296f7da2257c925b74fe85665d3522e61305f048f6d25439ab8f5f74273c", "timestamp": "2025-06-06T16:09:12.112Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/AGENT_INTELLIGENCE_DEVELOPMENT_GUIDE.md", "target": "docs/📋-agents/intelligence/AGENT_INTELLIGENCE_DEVELOPMENT_GUIDE.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "3b6880b34fe2e104b7a5a05eca5a13cc9aeefb12bd282ca482389f6967bbcb1a", "targetHash": "3b6880b34fe2e104b7a5a05eca5a13cc9aeefb12bd282ca482389f6967bbcb1a", "timestamp": "2025-06-06T16:09:12.112Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/AGENT_MIGRATION_COMPLETION_REPORT.md", "target": "docs/📋-agents/intelligence/AGENT_MIGRATION_COMPLETION_REPORT.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "ba2bd8f6a3e538ab0f2e13c8f85b17974407875f0c9993ca86ebe105c8544b7c", "targetHash": "ba2bd8f6a3e538ab0f2e13c8f85b17974407875f0c9993ca86ebe105c8544b7c", "timestamp": "2025-06-06T16:09:12.113Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/AGENT_MIGRATION_PLAN.md", "target": "docs/📋-agents/intelligence/AGENT_MIGRATION_PLAN.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "2b214eec752453ca8368b0d8a5c5db45df0c95c31047bf754c3b56bfb57baa12", "targetHash": "2b214eec752453ca8368b0d8a5c5db45df0c95c31047bf754c3b56bfb57baa12", "timestamp": "2025-06-06T16:09:12.113Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/AGENT_TASKS.md", "target": "docs/📋-agents/intelligence/AGENT_TASKS.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "937df64486a5cd112f05f30f2efe65e47b4406b5a2c143fa81182bc24e16cc6d", "targetHash": "937df64486a5cd112f05f30f2efe65e47b4406b5a2c143fa81182bc24e16cc6d", "timestamp": "2025-06-06T16:09:12.113Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/AGENT_WORKFLOW_INTEGRATION.md", "target": "docs/📋-agents/intelligence/AGENT_WORKFLOW_INTEGRATION.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "58cef87794b03c55c70db44b2c40dd6fb9ff4484103c8d8f31d67f54fded6700", "targetHash": "58cef87794b03c55c70db44b2c40dd6fb9ff4484103c8d8f31d67f54fded6700", "timestamp": "2025-06-06T16:09:12.114Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/COMPREHENSIVE_FRONTEND_AGENT_ANALYSIS.md", "target": "docs/📋-agents/intelligence/COMPREHENSIVE_FRONTEND_AGENT_ANALYSIS.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "6fa002e664dc3cf6d7350dedf294e9abc6984e71485e5c97932a3a2f5b5ed0a9", "targetHash": "6fa002e664dc3cf6d7350dedf294e9abc6984e71485e5c97932a3a2f5b5ed0a9", "timestamp": "2025-06-06T16:09:12.114Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/AgentCodeTransformation.md", "target": "docs/📋-agents/intelligence/AgentCodeTransformation.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "628a4ac6bb78cc1d30307179cc07c69f80986015d4d48949a5a67b84794af849", "targetHash": "628a4ac6bb78cc1d30307179cc07c69f80986015d4d48949a5a67b84794af849", "timestamp": "2025-06-06T16:09:12.114Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/docs-organization-system-update-summary.md", "target": "docs/📋-agents/intelligence/docs-organization-system-update-summary.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "5d85a4a8f256bd4507e256b1f17b188a1c2e42cc9443f7ab2b88bb3b5f0e6e1d", "targetHash": "5d85a4a8f256bd4507e256b1f17b188a1c2e42cc9443f7ab2b88bb3b5f0e6e1d", "timestamp": "2025-06-06T16:09:12.115Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/FUNCTIONALITY_PRESERVATION_ANALYSIS.md", "target": "docs/📋-agents/intelligence/FUNCTIONALITY_PRESERVATION_ANALYSIS.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "5e0458d5425626f8118352b3e75226b684315126fe0f5c458fa21ed4eac9a2e1", "targetHash": "5e0458d5425626f8118352b3e75226b684315126fe0f5c458fa21ed4eac9a2e1", "timestamp": "2025-06-06T16:09:12.115Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/FRONTEND_AGENT_ECOSYSTEM_VERIFICATION.md", "target": "docs/📋-agents/intelligence/FRONTEND_AGENT_ECOSYSTEM_VERIFICATION.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "b5da49c10ffab8e70e6309dcdbf640da85a53a6acdbf38b3afb2694f7d48ae73", "targetHash": "b5da49c10ffab8e70e6309dcdbf640da85a53a6acdbf38b3afb2694f7d48ae73", "timestamp": "2025-06-06T16:09:12.115Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/docs-update-verification.md", "target": "docs/📋-agents/intelligence/docs-update-verification.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "7bd714d253f44359537ca07977ce70f16f03b6733ab36c20aeed78b4f15562c4", "targetHash": "7bd714d253f44359537ca07977ce70f16f03b6733ab36c20aeed78b4f15562c4", "timestamp": "2025-06-06T16:09:12.116Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/ROADMAP.md", "target": "docs/📋-agents/intelligence/ROADMAP.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "2ea56b72717add3a5ab248e63af621eadd468e1a2a71895f69c93302409e88c7", "targetHash": "2ea56b72717add3a5ab248e63af621eadd468e1a2a71895f69c93302409e88c7", "timestamp": "2025-06-06T16:09:12.116Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/tasks.md", "target": "docs/📋-agents/intelligence/tasks.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "52f159f07fcf0517672c4a62fd99e02a8b0cfebd567db6680f047d65c69e54f5", "targetHash": "52f159f07fcf0517672c4a62fd99e02a8b0cfebd567db6680f047d65c69e54f5", "timestamp": "2025-06-06T16:09:12.116Z"}, {"type": "moved", "source": "docs/security/policies/security-headers.js", "target": "docs/security/policies/security-headers.js", "category": "security-utilities", "confidence": 0.6, "sourceHash": "d0ec17f1ce29cbf01d4ae986824ea353825e617828b2c003d9711c683b3c3f63", "targetHash": "d0ec17f1ce29cbf01d4ae986824ea353825e617828b2c003d9711c683b3c3f63", "timestamp": "2025-06-06T16:09:12.117Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/update-completion-summary.md", "target": "docs/📋-agents/intelligence/update-completion-summary.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "a3d7dbea596c3bdf0c5a3362698ee113312da8e19dfbe608f836d2d2e4201052", "targetHash": "a3d7dbea596c3bdf0c5a3362698ee113312da8e19dfbe608f836d2d2e4201052", "timestamp": "2025-06-06T16:09:12.118Z"}, {"type": "moved", "source": "docs/📋-agents/intelligence/operation-log.json", "target": "docs/📋-agents/intelligence/operation-log.json", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "38b4a1e7e2483f2b00f0d8978b589789ae901b3c67cbf598e5363ba27dbc1371", "targetHash": "38b4a1e7e2483f2b00f0d8978b589789ae901b3c67cbf598e5363ba27dbc1371", "timestamp": "2025-06-06T16:09:12.118Z"}, {"type": "moved", "source": "docs/security/policies/security-report.json", "target": "docs/security/policies/security-report.json", "category": "security-utilities", "confidence": 0.6, "sourceHash": "ec2db362e3fec4aacafea0fb457aeb587ccc3185f47080d055ea45738cc906fa", "targetHash": "ec2db362e3fec4aacafea0fb457aeb587ccc3185f47080d055ea45738cc906fa", "timestamp": "2025-06-06T16:09:12.118Z"}, {"type": "moved", "source": "docs/security/policies/README.md", "target": "docs/security/policies/README.md", "category": "security-utilities", "confidence": 0.6, "sourceHash": "78ff65d1c73f89f97ec1bf3d7eccd5f5ab016a1511d02d5a504ce0d787f29852", "targetHash": "78ff65d1c73f89f97ec1bf3d7eccd5f5ab016a1511d02d5a504ce0d787f29852", "timestamp": "2025-06-06T16:09:12.118Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/AdvancedDrawingToolsCompletion.md", "target": "docs/📊-reports/analysis/AdvancedDrawingToolsCompletion.md", "category": "reports", "confidence": 0.75, "sourceHash": "0ac1c9ef04de09fb240b8d7f45dde93fed23013ba05c793027371cab816ca7be", "targetHash": "0ac1c9ef04de09fb240b8d7f45dde93fed23013ba05c793027371cab816ca7be", "timestamp": "2025-06-06T16:09:12.118Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/CURSOR_DAILY_WORKFLOW.md", "target": "docs/📊-reports/analysis/CURSOR_DAILY_WORKFLOW.md", "category": "reports", "confidence": 0.75, "sourceHash": "24dfc89018e61b5cb9c8298593156ccc44d7ff60dbfc684a7a80f6e96c301dc2", "targetHash": "24dfc89018e61b5cb9c8298593156ccc44d7ff60dbfc684a7a80f6e96c301dc2", "timestamp": "2025-06-06T16:09:12.119Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/CURSOR_MEMORY_BANK_BACKUP.md", "target": "docs/📊-reports/analysis/CURSOR_MEMORY_BANK_BACKUP.md", "category": "reports", "confidence": 0.75, "sourceHash": "ceb957568bbc433d91dba684354abc540e5ce9af2363e3435c38dba3b8b795b4", "targetHash": "ceb957568bbc433d91dba684354abc540e5ce9af2363e3435c38dba3b8b795b4", "timestamp": "2025-06-06T16:09:12.119Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/CONTRIBUTING.md", "target": "docs/📊-reports/analysis/CONTRIBUTING.md", "category": "reports", "confidence": 0.75, "sourceHash": "a2e4ae298bcd7c2b82bf9e9e7ed6c7ac86dd9babb1a66ac112e96e904505bd50", "targetHash": "a2e4ae298bcd7c2b82bf9e9e7ed6c7ac86dd9babb1a66ac112e96e904505bd50", "timestamp": "2025-06-06T16:09:12.119Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/ConfigAgent-Development-Session-7.md", "target": "docs/📊-reports/analysis/ConfigAgent-Development-Session-7.md", "category": "reports", "confidence": 0.75, "sourceHash": "99df6adf5c59b1cdeb4088706f84c9c14abbcf1427703dc68c09b1632b8a94b1", "targetHash": "99df6adf5c59b1cdeb4088706f84c9c14abbcf1427703dc68c09b1632b8a94b1", "timestamp": "2025-06-06T16:09:12.120Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/ExtendedAgentConflictValidation.md", "target": "docs/📊-reports/analysis/ExtendedAgentConflictValidation.md", "category": "reports", "confidence": 0.75, "sourceHash": "209dbf8b40e58dbd2af93397cbddd673d2990dfdb5b76e80c38965de2e738ade", "targetHash": "209dbf8b40e58dbd2af93397cbddd673d2990dfdb5b76e80c38965de2e738ade", "timestamp": "2025-06-06T16:09:12.120Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/ConflictPreventionMechanisms.md", "target": "docs/📊-reports/analysis/ConflictPreventionMechanisms.md", "category": "reports", "confidence": 0.75, "sourceHash": "40cf3c5a2edcccdc3b133764a489922b8732a752910529d2514ffe4fc9c490a2", "targetHash": "40cf3c5a2edcccdc3b133764a489922b8732a752910529d2514ffe4fc9c490a2", "timestamp": "2025-06-06T16:09:12.121Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/EnhancedAIIntegrationCompletion.md", "target": "docs/📊-reports/analysis/EnhancedAIIntegrationCompletion.md", "category": "reports", "confidence": 0.75, "sourceHash": "64a38bc34e353383fb744a42c4c0b17c777151bd2da3b726442e87f5fd7ffc19", "targetHash": "64a38bc34e353383fb744a42c4c0b17c777151bd2da3b726442e87f5fd7ffc19", "timestamp": "2025-06-06T16:09:12.121Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/OpsAgent-Development-Session-4.md", "target": "docs/📊-reports/analysis/OpsAgent-Development-Session-4.md", "category": "reports", "confidence": 0.75, "sourceHash": "6ec8563bdd162772aadb4c5373f24d643720b7deefd46d3d972890435224bcd5", "targetHash": "6ec8563bdd162772aadb4c5373f24d643720b7deefd46d3d972890435224bcd5", "timestamp": "2025-06-06T16:09:12.122Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/LivingAgentDeploymentGuide.md", "target": "docs/📊-reports/analysis/LivingAgentDeploymentGuide.md", "category": "reports", "confidence": 0.75, "sourceHash": "0023dcc0a7f3acfcd47c4cd84855fb0917a221015d04a2dac39cf31954e91d56", "targetHash": "0023dcc0a7f3acfcd47c4cd84855fb0917a221015d04a2dac39cf31954e91d56", "timestamp": "2025-06-06T16:09:12.122Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/OpsAgent-Interface.md", "target": "docs/📊-reports/analysis/OpsAgent-Interface.md", "category": "reports", "confidence": 0.75, "sourceHash": "c84dd75f34ac87ab6374effd226bd57b3dd98272a1ac70f0ee944162d0940504", "targetHash": "c84dd75f34ac87ab6374effd226bd57b3dd98272a1ac70f0ee944162d0940504", "timestamp": "2025-06-06T16:09:12.123Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/Phase-1-Agent-Mapping-FINDINGS.md", "target": "docs/📊-reports/analysis/Phase-1-Agent-Mapping-FINDINGS.md", "category": "reports", "confidence": 0.75, "sourceHash": "f15207eef593a6f89f17965c11dfc8914fec03c4e337e92dc82bf3b309bb4273", "targetHash": "f15207eef593a6f89f17965c11dfc8914fec03c4e337e92dc82bf3b309bb4273", "timestamp": "2025-06-06T16:09:12.123Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/ProactiveAutonomyAgent-Development-Session-12.md", "target": "docs/📊-reports/analysis/ProactiveAutonomyAgent-Development-Session-12.md", "category": "reports", "confidence": 0.75, "sourceHash": "b25be17fe527d7cf927d7996f286237981a1f6fb530f2cc7f0ea04515a9f75d2", "targetHash": "b25be17fe527d7cf927d7996f286237981a1f6fb530f2cc7f0ea04515a9f75d2", "timestamp": "2025-06-06T16:09:12.123Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/Phase4-Standardized-Protocols-Demo.json", "target": "docs/📊-reports/analysis/Phase4-Standardized-Protocols-Demo.json", "category": "reports", "confidence": 0.75, "sourceHash": "****************************************************************", "targetHash": "****************************************************************", "timestamp": "2025-06-06T16:09:12.124Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/SECURITY_STATUS.md", "target": "docs/📊-reports/analysis/SECURITY_STATUS.md", "category": "reports", "confidence": 0.75, "sourceHash": "0c91b0b5994b459670fcde0537546a9f0bae94ea8ef6bd697c2b59a2eed97d5b", "targetHash": "0c91b0b5994b459670fcde0537546a9f0bae94ea8ef6bd697c2b59a2eed97d5b", "timestamp": "2025-06-06T16:09:12.124Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/SECURITY_CHECKLIST.md", "target": "docs/📊-reports/analysis/SECURITY_CHECKLIST.md", "category": "reports", "confidence": 0.75, "sourceHash": "ec425948e4ff1395c5d79399bbf6788fc335dde0ff9ed44cd2540f6601367cc8", "targetHash": "ec425948e4ff1395c5d79399bbf6788fc335dde0ff9ed44cd2540f6601367cc8", "timestamp": "2025-06-06T16:09:12.124Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/SecurityAgent-Development-Session-11.md", "target": "docs/📊-reports/analysis/SecurityAgent-Development-Session-11.md", "category": "reports", "confidence": 0.75, "sourceHash": "184328d4fd0cf851714a6c020266818174a42c54671e01acb7bbf9281ecd109e", "targetHash": "184328d4fd0cf851714a6c020266818174a42c54671e01acb7bbf9281ecd109e", "timestamp": "2025-06-06T16:09:12.125Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/SecurityAgent-Interface.md", "target": "docs/📊-reports/analysis/SecurityAgent-Interface.md", "category": "reports", "confidence": 0.75, "sourceHash": "add4b7ab27c8d08bd705fbcca5c3bf6a89f8e2e24422d8a9169b5d418b8a9e4d", "targetHash": "add4b7ab27c8d08bd705fbcca5c3bf6a89f8e2e24422d8a9169b5d418b8a9e4d", "timestamp": "2025-06-06T16:09:12.125Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/UIAgent-Development-Session-2.md", "target": "docs/📊-reports/analysis/UIAgent-Development-Session-2.md", "category": "reports", "confidence": 0.75, "sourceHash": "e5ef2db8bdcd69a1d2282b13147a9204f686212c77119676995cdc5fc9c69251", "targetHash": "e5ef2db8bdcd69a1d2282b13147a9204f686212c77119676995cdc5fc9c69251", "timestamp": "2025-06-06T16:09:12.125Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/UIAgent-Interface.md", "target": "docs/📊-reports/analysis/UIAgent-Interface.md", "category": "reports", "confidence": 0.75, "sourceHash": "e7d4a513ea5d3cbb4d799e02d2d21132f39fb1ed6a7a718d581e12b070f29452", "targetHash": "e7d4a513ea5d3cbb4d799e02d2d21132f39fb1ed6a7a718d581e12b070f29452", "timestamp": "2025-06-06T16:09:12.126Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/agent-ecosystem-analysis.json", "target": "docs/📊-reports/analysis/agent-ecosystem-analysis.json", "category": "reports", "confidence": 0.75, "sourceHash": "44b1f5ca2632e3dc39317a555ddf5b64341e51567cf87eda142e0685f22db24b", "targetHash": "44b1f5ca2632e3dc39317a555ddf5b64341e51567cf87eda142e0685f22db24b", "timestamp": "2025-06-06T16:09:12.126Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/VectorMemory-Development-Session-9.md", "target": "docs/📊-reports/analysis/VectorMemory-Development-Session-9.md", "category": "reports", "confidence": 0.75, "sourceHash": "7f72d3fe46bef41d0325d78caa87ee50d7a11e3db1b6e003e22cb387d624cd4b", "targetHash": "7f72d3fe46bef41d0325d78caa87ee50d7a11e3db1b6e003e22cb387d624cd4b", "timestamp": "2025-06-06T16:09:12.126Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/agent-ecosystem-map.json", "target": "docs/📊-reports/analysis/agent-ecosystem-map.json", "category": "reports", "confidence": 0.75, "sourceHash": "dba518f5a8d932cb6b1169985e593cdea8c25592ef4ea280d510df01261c0ad7", "targetHash": "dba518f5a8d932cb6b1169985e593cdea8c25592ef4ea280d510df01261c0ad7", "timestamp": "2025-06-06T16:09:12.126Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/any-types-report.md", "target": "docs/📊-reports/analysis/any-types-report.md", "category": "reports", "confidence": 0.75, "sourceHash": "5118b99b5c2004ffdf61e4f9cd0c201dcb985ec5bfdfed3f4dad4129cab1861f", "targetHash": "5118b99b5c2004ffdf61e4f9cd0c201dcb985ec5bfdfed3f4dad4129cab1861f", "timestamp": "2025-06-06T16:09:12.126Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/agent-system-analysis-and-ml-capabilities.md", "target": "docs/📊-reports/analysis/agent-system-analysis-and-ml-capabilities.md", "category": "reports", "confidence": 0.75, "sourceHash": "56cc78f84f3918101d93cd21c801f8c6ed85a309c7fe450f920ee47db17fdde2", "targetHash": "56cc78f84f3918101d93cd21c801f8c6ed85a309c7fe450f920ee47db17fdde2", "timestamp": "2025-06-06T16:09:12.126Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/check-status.js", "target": "docs/📊-reports/analysis/check-status.js", "category": "reports", "confidence": 0.75, "sourceHash": "426976795d1ca8ea1ff28d65e972fab12d0d485a39f7b79471b9bbebdfd04686", "targetHash": "426976795d1ca8ea1ff28d65e972fab12d0d485a39f7b79471b9bbebdfd04686", "timestamp": "2025-06-06T16:09:12.127Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/build-analysis.log", "target": "docs/📊-reports/analysis/build-analysis.log", "category": "reports", "confidence": 0.75, "sourceHash": "16d1768cc52294e17a9ec2544276e62ed98cc6ef8a4b645107183f4e2bc07b88", "targetHash": "16d1768cc52294e17a9ec2544276e62ed98cc6ef8a4b645107183f4e2bc07b88", "timestamp": "2025-06-06T16:09:12.127Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/approved-recommendations.json", "target": "docs/📊-reports/analysis/approved-recommendations.json", "category": "reports", "confidence": 0.75, "sourceHash": "bba7778177cb353c5abf12461405b9d06e76661496a4833f601b77b1909900b8", "targetHash": "bba7778177cb353c5abf12461405b9d06e76661496a4833f601b77b1909900b8", "timestamp": "2025-06-06T16:09:12.127Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/img-to-image-report.md", "target": "docs/📊-reports/analysis/img-to-image-report.md", "category": "reports", "confidence": 0.75, "sourceHash": "513c48117e23bd86dc80a33cae6d71051f03bbdf0957ff49c6b19dce1487c498", "targetHash": "513c48117e23bd86dc80a33cae6d71051f03bbdf0957ff49c6b19dce1487c498", "timestamp": "2025-06-06T16:09:12.128Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/continuous-progress.json", "target": "docs/📊-reports/analysis/continuous-progress.json", "category": "reports", "confidence": 0.75, "sourceHash": "5427dd9577bf874d33844070229b83fa8be5a3e6f83baf007ae98e46d33d9bf7", "targetHash": "5427dd9577bf874d33844070229b83fa8be5a3e6f83baf007ae98e46d33d9bf7", "timestamp": "2025-06-06T16:09:12.128Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/immediate-agent-validation-checklist.md", "target": "docs/📊-reports/analysis/immediate-agent-validation-checklist.md", "category": "reports", "confidence": 0.75, "sourceHash": "0180753af5d384d6792d9bae82d72f569bc933174847c06c437c4184e3e71494", "targetHash": "0180753af5d384d6792d9bae82d72f569bc933174847c06c437c4184e3e71494", "timestamp": "2025-06-06T16:09:12.128Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/performancemonitoringagent-refactoring-report.json", "target": "docs/📊-reports/analysis/performancemonitoringagent-refactoring-report.json", "category": "reports", "confidence": 0.75, "sourceHash": "1d3e6fcbc744b83d1c333a3c10d28f79a80ec177f58ed8d7dbd08ca1bf4e9319", "targetHash": "1d3e6fcbc744b83d1c333a3c10d28f79a80ec177f58ed8d7dbd08ca1bf4e9319", "timestamp": "2025-06-06T16:09:12.129Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/operation-log.json", "target": "docs/📊-reports/analysis/operation-log.json", "category": "reports", "confidence": 0.75, "sourceHash": "024afbe118b071b18310e9c188f599f369d50f64b994a6d309dcfd4e01b274eb", "targetHash": "024afbe118b071b18310e9c188f599f369d50f64b994a6d309dcfd4e01b274eb", "timestamp": "2025-06-06T16:09:12.129Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/performance-report.json", "target": "docs/📊-reports/analysis/performance-report.json", "category": "reports", "confidence": 0.75, "sourceHash": "1ce4745494848f652df40e8c0466bc08adeb81aaee92021c22b37bc8be582922", "targetHash": "1ce4745494848f652df40e8c0466bc08adeb81aaee92021c22b37bc8be582922", "timestamp": "2025-06-06T16:09:12.129Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/r1-collaborative-analysis-package.md", "target": "docs/📊-reports/analysis/r1-collaborative-analysis-package.md", "category": "reports", "confidence": 0.75, "sourceHash": "2587716285f663322908987605d7f585940c14207a5d0ea5841c404028efa6f1", "targetHash": "2587716285f663322908987605d7f585940c14207a5d0ea5841c404028efa6f1", "timestamp": "2025-06-06T16:09:12.130Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/phase5-performance-optimization-demo-report.json", "target": "docs/📊-reports/analysis/phase5-performance-optimization-demo-report.json", "category": "reports", "confidence": 0.75, "sourceHash": "7e9b6b5d9288cb3014314be719cbf617c4e01fcb33f53ec054cbc6ae9459b53a", "targetHash": "7e9b6b5d9288cb3014314be719cbf617c4e01fcb33f53ec054cbc6ae9459b53a", "timestamp": "2025-06-06T16:09:12.130Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/progress-report.json", "target": "docs/📊-reports/analysis/progress-report.json", "category": "reports", "confidence": 0.75, "sourceHash": "dd463f76bca73ecef274e27bbba486733349ba5e024f18ddd0df880c02174ba5", "targetHash": "dd463f76bca73ecef274e27bbba486733349ba5e024f18ddd0df880c02174ba5", "timestamp": "2025-06-06T16:09:12.130Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/real-time-status.json", "target": "docs/📊-reports/analysis/real-time-status.json", "category": "reports", "confidence": 0.75, "sourceHash": "75da95e0794131d24d17780c3f8cb1a59ec717d3a897eadd6290e608ce9a66fa", "targetHash": "75da95e0794131d24d17780c3f8cb1a59ec717d3a897eadd6290e608ce9a66fa", "timestamp": "2025-06-06T16:09:12.130Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/r1-direct-command.md", "target": "docs/📊-reports/analysis/r1-direct-command.md", "category": "reports", "confidence": 0.75, "sourceHash": "c7550c552d466381e2ba10b4441ce5dc7bb4e9b964e1ee6ebe5832d0c0cc9339", "targetHash": "c7550c552d466381e2ba10b4441ce5dc7bb4e9b964e1ee6ebe5832d0c0cc9339", "timestamp": "2025-06-06T16:09:12.130Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/two-agent-ai-workflow.md", "target": "docs/📊-reports/analysis/two-agent-ai-workflow.md", "category": "reports", "confidence": 0.75, "sourceHash": "08abd8f89ed74e25101d03597a7466ce0b76e9667241b9ab6bd0d8ee1c7fc7a0", "targetHash": "08abd8f89ed74e25101d03597a7466ce0b76e9667241b9ab6bd0d8ee1c7fc7a0", "timestamp": "2025-06-06T16:09:12.130Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/workflow-optimization-analysis.md", "target": "docs/📊-reports/analysis/workflow-optimization-analysis.md", "category": "reports", "confidence": 0.75, "sourceHash": "d4de57a63fb0aa0954671d20ee6aeb28f8853f498b1bf90e4e54f6a1a6ecf461", "targetHash": "d4de57a63fb0aa0954671d20ee6aeb28f8853f498b1bf90e4e54f6a1a6ecf461", "timestamp": "2025-06-06T16:09:12.131Z"}, {"type": "moved", "source": "docs/📊-reports/summaries/design-consistency-report.json", "target": "docs/📊-reports/summaries/design-consistency-report.json", "category": "project-summaries", "confidence": 0.95, "sourceHash": "964523298f1b477c4b4e5a5f619af07c9b3fa0f9d834d538d1949e7eeb81dc5a", "targetHash": "964523298f1b477c4b4e5a5f619af07c9b3fa0f9d834d538d1949e7eeb81dc5a", "timestamp": "2025-06-06T16:09:12.131Z"}, {"type": "moved", "source": "docs/📊-reports/summaries/progress-summary.md", "target": "docs/📊-reports/summaries/progress-summary.md", "category": "project-summaries", "confidence": 0.95, "sourceHash": "63e38342f38596d601af283110b94e405d33427d0c9139e2fb94c8f9c47eac29", "targetHash": "63e38342f38596d601af283110b94e405d33427d0c9139e2fb94c8f9c47eac29", "timestamp": "2025-06-06T16:09:12.131Z"}, {"type": "moved", "source": "docs/📊-reports/summaries/r1-conflict-resolution-summary.md", "target": "docs/📊-reports/summaries/r1-conflict-resolution-summary.md", "category": "project-summaries", "confidence": 0.95, "sourceHash": "b86e9317510ac5565d001c33bfc81d5dcdefab74518868706d4f5b01e6578cd4", "targetHash": "b86e9317510ac5565d001c33bfc81d5dcdefab74518868706d4f5b01e6578cd4", "timestamp": "2025-06-06T16:09:12.132Z"}, {"type": "moved", "source": "docs/📊-reports/verification/agent-fix-verification.js", "target": "docs/📊-reports/verification/agent-fix-verification.js", "category": "verification", "confidence": 0.85, "sourceHash": "23c663822b0cd25cdd06c3f76a4c53240cb2e581f77b92d06001f001c18a2a25", "targetHash": "23c663822b0cd25cdd06c3f76a4c53240cb2e581f77b92d06001f001c18a2a25", "timestamp": "2025-06-06T16:09:12.132Z"}, {"type": "moved", "source": "docs/📊-reports/verification/PredictiveGoalForecasting-Development-Session-8.md", "target": "docs/📊-reports/verification/PredictiveGoalForecasting-Development-Session-8.md", "category": "verification", "confidence": 0.85, "sourceHash": "9d980dd32a3856ab0db8153b958acff6d114eb674b33fd20613349c6e35a62ef", "targetHash": "9d980dd32a3856ab0db8153b958acff6d114eb674b33fd20613349c6e35a62ef", "timestamp": "2025-06-06T16:09:12.132Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/AUTOMATED_DOCUMENTATION_VERIFICATION_SOLUTION.md", "target": "docs/📋-architecture/decisions/AUTOMATED_DOCUMENTATION_VERIFICATION_SOLUTION.md", "category": "architecture", "confidence": 0.9, "sourceHash": "d07693ad9b9edbfa8f3debd8ca421a55336a66e25fc6e711a1d9a346afd3aec8", "targetHash": "d07693ad9b9edbfa8f3debd8ca421a55336a66e25fc6e711a1d9a346afd3aec8", "timestamp": "2025-06-06T16:09:12.132Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/28-agent-ecosystem-final-report.json", "target": "docs/📋-architecture/decisions/28-agent-ecosystem-final-report.json", "category": "architecture", "confidence": 0.9, "sourceHash": "ed86a85f397c71f3501c3a5cc15f6953e6522121cb69347add8ea4b0cdd61d14", "targetHash": "ed86a85f397c71f3501c3a5cc15f6953e6522121cb69347add8ea4b0cdd61d14", "timestamp": "2025-06-06T16:09:12.133Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/AgentOrchestrator-Development-Session-6.md", "target": "docs/📋-architecture/decisions/AgentOrchestrator-Development-Session-6.md", "category": "architecture", "confidence": 0.9, "sourceHash": "5bae46feb6d9d907e2d96531251b0ab42defc3cf7d94111182eea7b0324a6ec7", "targetHash": "5bae46feb6d9d907e2d96531251b0ab42defc3cf7d94111182eea7b0324a6ec7", "timestamp": "2025-06-06T16:09:12.133Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/CANVAS_INTUITIVE_INNOVATIVE_UPGRADE.md", "target": "docs/📋-architecture/decisions/CANVAS_INTUITIVE_INNOVATIVE_UPGRADE.md", "category": "architecture", "confidence": 0.9, "sourceHash": "f62a10c9a2bb504dbaf56c7e2efc237a9c350033403ab7687769df149b416699", "targetHash": "f62a10c9a2bb504dbaf56c7e2efc237a9c350033403ab7687769df149b416699", "timestamp": "2025-06-06T16:09:12.133Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/AgentRoleDefinitions.md", "target": "docs/📋-architecture/decisions/AgentRoleDefinitions.md", "category": "architecture", "confidence": 0.9, "sourceHash": "85ba8f87545388a8d98b5328e1271e0eec503abe6c54bb2d321610227bd7c911", "targetHash": "85ba8f87545388a8d98b5328e1271e0eec503abe6c54bb2d321610227bd7c911", "timestamp": "2025-06-06T16:09:12.133Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/Complete-Agent-Ecosystem-Analysis.md", "target": "docs/📋-architecture/decisions/Complete-Agent-Ecosystem-Analysis.md", "category": "architecture", "confidence": 0.9, "sourceHash": "0d0185c2cee7a7674eb98ea56cc9cb997e0b490b9c4164dabfebb7a016ef8d8d", "targetHash": "0d0185c2cee7a7674eb98ea56cc9cb997e0b490b9c4164dabfebb7a016ef8d8d", "timestamp": "2025-06-06T16:09:12.133Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/DOCS_CLEANUP_SUMMARY.md", "target": "docs/📋-architecture/decisions/DOCS_CLEANUP_SUMMARY.md", "category": "architecture", "confidence": 0.9, "sourceHash": "325650351ab04270cd976254c232673ebb9ac7d4132dfb37d45a5a9a9da7c75d", "targetHash": "325650351ab04270cd976254c232673ebb9ac7d4132dfb37d45a5a9a9da7c75d", "timestamp": "2025-06-06T16:09:12.134Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/DevAgent-Interface.md", "target": "docs/📋-architecture/decisions/DevAgent-Interface.md", "category": "architecture", "confidence": 0.9, "sourceHash": "8232a603d0dad9918c4ff00e243ae57d5a801f7ddb98f116471e1a6758339b77", "targetHash": "8232a603d0dad9918c4ff00e243ae57d5a801f7ddb98f116471e1a6758339b77", "timestamp": "2025-06-06T16:09:12.134Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/CreAItive_Agent_Build_Guide.md", "target": "docs/📋-architecture/decisions/CreAItive_Agent_Build_Guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "eb73e2aaa79ce011bdd2b58a983edb7b55e6aa77506cbffde00acd2a330090ae", "targetHash": "eb73e2aaa79ce011bdd2b58a983edb7b55e6aa77506cbffde00acd2a330090ae", "timestamp": "2025-06-06T16:09:12.134Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/FINAL_CANVAS_TOOL_TEST.md", "target": "docs/📋-architecture/decisions/FINAL_CANVAS_TOOL_TEST.md", "category": "architecture", "confidence": 0.9, "sourceHash": "ca290a6d14fd993e3e0d7cd167f7a911a2b8020519668b81d882915d1fc89dc3", "targetHash": "ca290a6d14fd993e3e0d7cd167f7a911a2b8020519668b81d882915d1fc89dc3", "timestamp": "2025-06-06T16:09:12.135Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/ENHANCED-INFORMATION-CAPTURE-SYSTEM.md", "target": "docs/📋-architecture/decisions/ENHANCED-INFORMATION-CAPTURE-SYSTEM.md", "category": "architecture", "confidence": 0.9, "sourceHash": "659885cd1444a125626e1561871d7a5f970cbedc0c6e9d0f01f1cf19b7f75827", "targetHash": "659885cd1444a125626e1561871d7a5f970cbedc0c6e9d0f01f1cf19b7f75827", "timestamp": "2025-06-06T16:09:12.135Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/FINAL_ACHIEVEMENT_SUMMARY.md", "target": "docs/📋-architecture/decisions/FINAL_ACHIEVEMENT_SUMMARY.md", "category": "architecture", "confidence": 0.9, "sourceHash": "6da7709ec4b98f64f3dce0c4e1052508e48487b6a52e9c36fab07fd03578e5cb", "targetHash": "6da7709ec4b98f64f3dce0c4e1052508e48487b6a52e9c36fab07fd03578e5cb", "timestamp": "2025-06-06T16:09:12.135Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/FeatureDiscoveryAgent-Development-Session-14.md", "target": "docs/📋-architecture/decisions/FeatureDiscoveryAgent-Development-Session-14.md", "category": "architecture", "confidence": 0.9, "sourceHash": "ce96bbd3a11e7f3aa8eb5c240e4d16794f7889d57fb546006cf1c22fc64c3bf2", "targetHash": "ce96bbd3a11e7f3aa8eb5c240e4d16794f7889d57fb546006cf1c22fc64c3bf2", "timestamp": "2025-06-06T16:09:12.135Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/FRONTEND_AUDIT_SUMMARY.md", "target": "docs/📋-architecture/decisions/FRONTEND_AUDIT_SUMMARY.md", "category": "architecture", "confidence": 0.9, "sourceHash": "5299e7bb85e9793df697bd7b28d5b262c5a8843c84a2d3fd6e5a3c31dfd1c5e0", "targetHash": "5299e7bb85e9793df697bd7b28d5b262c5a8843c84a2d3fd6e5a3c31dfd1c5e0", "timestamp": "2025-06-06T16:09:12.136Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/FRONTEND_TASK_MANAGER.md", "target": "docs/📋-architecture/decisions/FRONTEND_TASK_MANAGER.md", "category": "architecture", "confidence": 0.9, "sourceHash": "46e16e5a90c61a510dab40ada1a3a6fabdd09ef353e8293651766eb3c5bfebfe", "targetHash": "46e16e5a90c61a510dab40ada1a3a6fabdd09ef353e8293651766eb3c5bfebfe", "timestamp": "2025-06-06T16:09:12.136Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/INFORMATION-CAPTURE-SYSTEM.md", "target": "docs/📋-architecture/decisions/INFORMATION-CAPTURE-SYSTEM.md", "category": "architecture", "confidence": 0.9, "sourceHash": "4923727ad4f8b4f0baaa013a1fac966ca1700dcc3d71ba626cbe9ff1eabe0ee8", "targetHash": "4923727ad4f8b4f0baaa013a1fac966ca1700dcc3d71ba626cbe9ff1eabe0ee8", "timestamp": "2025-06-06T16:09:12.136Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/LivingAgentImplementationChecklist.md", "target": "docs/📋-architecture/decisions/LivingAgentImplementationChecklist.md", "category": "architecture", "confidence": 0.9, "sourceHash": "72b9a3b42c65cb58dc708e71619b6b3bc0317e7ca29a1c9072e87114adb2fb56", "targetHash": "72b9a3b42c65cb58dc708e71619b6b3bc0317e7ca29a1c9072e87114adb2fb56", "timestamp": "2025-06-06T16:09:12.136Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/Phase-8.3.2-ML-Enhancement-Completion.md", "target": "docs/📋-architecture/decisions/Phase-8.3.2-ML-Enhancement-Completion.md", "category": "architecture", "confidence": 0.9, "sourceHash": "bbbf01f4b860de1adb622dc640f78a984178cda223b702c04a6d642383cd471a", "targetHash": "bbbf01f4b860de1adb622dc640f78a984178cda223b702c04a6d642383cd471a", "timestamp": "2025-06-06T16:09:12.136Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/Phase-8.3.3-ML-Enhancement-Completion.md", "target": "docs/📋-architecture/decisions/Phase-8.3.3-ML-Enhancement-Completion.md", "category": "architecture", "confidence": 0.9, "sourceHash": "dc24d65a9b934300a57405d585c612896fec6bd5d517bc0723866092cf013c65", "targetHash": "dc24d65a9b934300a57405d585c612896fec6bd5d517bc0723866092cf013c65", "timestamp": "2025-06-06T16:09:12.137Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/Phase-9-Strategic-Roadmap.md", "target": "docs/📋-architecture/decisions/Phase-9-Strategic-Roadmap.md", "category": "architecture", "confidence": 0.9, "sourceHash": "2a7906a84e90ca6c63e222c5ddf8a27f16d40f0ba7088da785266ad61843a500", "targetHash": "2a7906a84e90ca6c63e222c5ddf8a27f16d40f0ba7088da785266ad61843a500", "timestamp": "2025-06-06T16:09:12.137Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/R1-Devstral-Agent-Ecosystem-Consensus.md", "target": "docs/📋-architecture/decisions/R1-Devstral-Agent-Ecosystem-Consensus.md", "category": "architecture", "confidence": 0.9, "sourceHash": "3be8fae7ad11bbccb7710cd15cfea36c0285b3dbed7e25947641cba6ea16defa", "targetHash": "3be8fae7ad11bbccb7710cd15cfea36c0285b3dbed7e25947641cba6ea16defa", "timestamp": "2025-06-06T16:09:12.137Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/README.md", "target": "docs/📋-architecture/decisions/README.md", "category": "architecture", "confidence": 0.9, "sourceHash": "bb1080cb7a8d33476349f1810510d07dd3d034dbfabc8f42fcfe99ffe7a63dba", "targetHash": "bb1080cb7a8d33476349f1810510d07dd3d034dbfabc8f42fcfe99ffe7a63dba", "timestamp": "2025-06-06T16:09:12.138Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/R1-Phase2-Implementation-Plan.md", "target": "docs/📋-architecture/decisions/R1-Phase2-Implementation-Plan.md", "category": "architecture", "confidence": 0.9, "sourceHash": "7de2f635703d35a59eb741e1859dd3c261359ad861720e5aa0c5ba9f62e829b9", "targetHash": "7de2f635703d35a59eb741e1859dd3c261359ad861720e5aa0c5ba9f62e829b9", "timestamp": "2025-06-06T16:09:12.138Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/RoleConflictAnalysis.md", "target": "docs/📋-architecture/decisions/RoleConflictAnalysis.md", "category": "architecture", "confidence": 0.9, "sourceHash": "fce30b18472e215a89d02b7b78901bee543ceb9cc71c76f9a6b68dded01fc53e", "targetHash": "fce30b18472e215a89d02b7b78901bee543ceb9cc71c76f9a6b68dded01fc53e", "timestamp": "2025-06-06T16:09:12.138Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/SECURITY.md", "target": "docs/📋-architecture/decisions/SECURITY.md", "category": "architecture", "confidence": 0.9, "sourceHash": "30ec85805944352fbc427cbef4ed38c14086c2116a6f72bce9635ced5d22d3aa", "targetHash": "30ec85805944352fbc427cbef4ed38c14086c2116a6f72bce9635ced5d22d3aa", "timestamp": "2025-06-06T16:09:12.138Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/STRATEGIC_IMPLEMENTATION_STATUS_REPORT.md", "target": "docs/📋-architecture/decisions/STRATEGIC_IMPLEMENTATION_STATUS_REPORT.md", "category": "architecture", "confidence": 0.9, "sourceHash": "4dc1d644f8ad9bc6fca8572e8d446a4071625867f6977f65b1f8183395a8570c", "targetHash": "4dc1d644f8ad9bc6fca8572e8d446a4071625867f6977f65b1f8183395a8570c", "timestamp": "2025-06-06T16:09:12.138Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/SCRIPT_CLEANUP_SUMMARY.md", "target": "docs/📋-architecture/decisions/SCRIPT_CLEANUP_SUMMARY.md", "category": "architecture", "confidence": 0.9, "sourceHash": "fabebca355c8d681ac7c8853d2114ab5f729ea5f72a67bd61ddcf322daddbbb2", "targetHash": "fabebca355c8d681ac7c8853d2114ab5f729ea5f72a67bd61ddcf322daddbbb2", "timestamp": "2025-06-06T16:09:12.138Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/adr-0001-nextjs-frontend-framework.md", "target": "docs/📋-architecture/decisions/adr-0001-nextjs-frontend-framework.md", "category": "architecture", "confidence": 0.9, "sourceHash": "b349711c3df6a0058e106207478345f35bb496dec757673297445ff4a17671d9", "targetHash": "b349711c3df6a0058e106207478345f35bb496dec757673297445ff4a17671d9", "timestamp": "2025-06-06T16:09:12.139Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/UNIFIED_WORKFLOW_SYSTEM.md", "target": "docs/📋-architecture/decisions/UNIFIED_WORKFLOW_SYSTEM.md", "category": "architecture", "confidence": 0.9, "sourceHash": "e330808ce5b578f46acf62742dbd99bd95b01b7273786485e8bc502868dd9703", "targetHash": "e330808ce5b578f46acf62742dbd99bd95b01b7273786485e8bc502868dd9703", "timestamp": "2025-06-06T16:09:12.139Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/TASK-MANAGER-Total-System-Architecture.md", "target": "docs/📋-architecture/decisions/TASK-MANAGER-Total-System-Architecture.md", "category": "architecture", "confidence": 0.9, "sourceHash": "e8124f9c4d99b2986b22c7cecf1a0654db1aea9a9bee3f4fc337d3343a20ced2", "targetHash": "e8124f9c4d99b2986b22c7cecf1a0654db1aea9a9bee3f4fc337d3343a20ced2", "timestamp": "2025-06-06T16:09:12.139Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/agent-development-command-center.md", "target": "docs/📋-architecture/decisions/agent-development-command-center.md", "category": "architecture", "confidence": 0.9, "sourceHash": "0a3e8cc1b040c7c11c2c15f89b4a5c05e509db293824eb49245572e4166ddcf7", "targetHash": "0a3e8cc1b040c7c11c2c15f89b4a5c05e509db293824eb49245572e4166ddcf7", "timestamp": "2025-06-06T16:09:12.140Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/agent-development-tracking-report.json", "target": "docs/📋-architecture/decisions/agent-development-tracking-report.json", "category": "architecture", "confidence": 0.9, "sourceHash": "6c0cedfb3cfb19ea5ff3def2f46c0ef9c155d7ae2143a5e3323eb40bc3830df5", "targetHash": "6c0cedfb3cfb19ea5ff3def2f46c0ef9c155d7ae2143a5e3323eb40bc3830df5", "timestamp": "2025-06-06T16:09:12.140Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/agent-architecture-analysis-report.json", "target": "docs/📋-architecture/decisions/agent-architecture-analysis-report.json", "category": "architecture", "confidence": 0.9, "sourceHash": "4cd9e8920c33b4f185c4fa555c5f4f70c8fe3f002c2c116f56f8fa7ab9d949f1", "targetHash": "4cd9e8920c33b4f185c4fa555c5f4f70c8fe3f002c2c116f56f8fa7ab9d949f1", "timestamp": "2025-06-06T16:09:12.140Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/agent-orchestration-roadmap.md", "target": "docs/📋-architecture/decisions/agent-orchestration-roadmap.md", "category": "architecture", "confidence": 0.9, "sourceHash": "0689201d68aa41e20f4060605da750ca45a64db3350e9bcc3695271abc69df88", "targetHash": "0689201d68aa41e20f4060605da750ca45a64db3350e9bcc3695271abc69df88", "timestamp": "2025-06-06T16:09:12.141Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/ai-blockchain-proof-of-concept.md", "target": "docs/📋-architecture/decisions/ai-blockchain-proof-of-concept.md", "category": "architecture", "confidence": 0.9, "sourceHash": "684927cf30b0d2532f54393a54a2b25f8b11fd4b782e139957e4f4657aee9ce1", "targetHash": "684927cf30b0d2532f54393a54a2b25f8b11fd4b782e139957e4f4657aee9ce1", "timestamp": "2025-06-06T16:09:12.141Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/ai-collaboration-protocol.md", "target": "docs/📋-architecture/decisions/ai-collaboration-protocol.md", "category": "architecture", "confidence": 0.9, "sourceHash": "a8724faec25044f61917d2e948c682af24acca96580858d3d5e5d41cd7338a4a", "targetHash": "a8724faec25044f61917d2e948c682af24acca96580858d3d5e5d41cd7338a4a", "timestamp": "2025-06-06T16:09:12.141Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/ai-timeout-fix-resolution.md", "target": "docs/📋-architecture/decisions/ai-timeout-fix-resolution.md", "category": "architecture", "confidence": 0.9, "sourceHash": "fc5cea6cdf67a488151bc5aecdbd612e797058a99dbc74580c56aa1d2df6b6a6", "targetHash": "fc5cea6cdf67a488151bc5aecdbd612e797058a99dbc74580c56aa1d2df6b6a6", "timestamp": "2025-06-06T16:09:12.142Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/ai-powered-dual-phase-success.md", "target": "docs/📋-architecture/decisions/ai-powered-dual-phase-success.md", "category": "architecture", "confidence": 0.9, "sourceHash": "9d51634796f3a576a7874926b604ea083a6e9608380bd86faca1b036d1a87017", "targetHash": "9d51634796f3a576a7874926b604ea083a6e9608380bd86faca1b036d1a87017", "timestamp": "2025-06-06T16:09:12.142Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/autonomous-self-improvement-architecture.md", "target": "docs/📋-architecture/decisions/autonomous-self-improvement-architecture.md", "category": "architecture", "confidence": 0.9, "sourceHash": "0d313298704a1625657c584ef9d2d9da77b36e57b61f5b642f57a6a2921ecd68", "targetHash": "0d313298704a1625657c584ef9d2d9da77b36e57b61f5b642f57a6a2921ecd68", "timestamp": "2025-06-06T16:09:12.142Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/completion-results.json", "target": "docs/📋-architecture/decisions/completion-results.json", "category": "architecture", "confidence": 0.9, "sourceHash": "80f5ae4145316d58e273601a90c5eb28103b1e6e5675021a3e166d889d823cf3", "targetHash": "80f5ae4145316d58e273601a90c5eb28103b1e6e5675021a3e166d889d823cf3", "timestamp": "2025-06-06T16:09:12.142Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/blockchain-readiness-checkpoints.md", "target": "docs/📋-architecture/decisions/blockchain-readiness-checkpoints.md", "category": "architecture", "confidence": 0.9, "sourceHash": "2079e3a92756802b4c501a22b2c26d9949d6426d44e5d2d854c22db286df99bb", "targetHash": "2079e3a92756802b4c501a22b2c26d9949d6426d44e5d2d854c22db286df99bb", "timestamp": "2025-06-06T16:09:12.142Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/comprehensive-agent-development-integration.md", "target": "docs/📋-architecture/decisions/comprehensive-agent-development-integration.md", "category": "architecture", "confidence": 0.9, "sourceHash": "688bcc4979096aeaced131740f07c42476c61397d93bf94a6a331df82ce67915", "targetHash": "688bcc4979096aeaced131740f07c42476c61397d93bf94a6a331df82ce67915", "timestamp": "2025-06-06T16:09:12.142Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/comprehensive-agent-status-analysis.md", "target": "docs/📋-architecture/decisions/comprehensive-agent-status-analysis.md", "category": "architecture", "confidence": 0.9, "sourceHash": "787a3f25573da56d547a17a0af70900b7c5e27948bef6b31047985f99d23a195", "targetHash": "787a3f25573da56d547a17a0af70900b7c5e27948bef6b31047985f99d23a195", "timestamp": "2025-06-06T16:09:12.143Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/comprehensive-docs-organization.md", "target": "docs/📋-architecture/decisions/comprehensive-docs-organization.md", "category": "architecture", "confidence": 0.9, "sourceHash": "6781db2fec7dcdae8d69755b3204b7436dd285d49c4b5fc598a34ef194720e27", "targetHash": "6781db2fec7dcdae8d69755b3204b7436dd285d49c4b5fc598a34ef194720e27", "timestamp": "2025-06-06T16:09:12.143Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/consciousness-evolution-roadmap.md", "target": "docs/📋-architecture/decisions/consciousness-evolution-roadmap.md", "category": "architecture", "confidence": 0.9, "sourceHash": "6a64c50a9885946bd93392a45d963b62174e3104fed56b3a5524d92b97c76108", "targetHash": "6a64c50a9885946bd93392a45d963b62174e3104fed56b3a5524d92b97c76108", "timestamp": "2025-06-06T16:09:12.143Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/creative-canvas.md", "target": "docs/📋-architecture/decisions/creative-canvas.md", "category": "architecture", "confidence": 0.9, "sourceHash": "a363244588372b87f6938d5ff0b1a6bcbde8937d45bdf499f30bd0a8b250a4a3", "targetHash": "a363244588372b87f6938d5ff0b1a6bcbde8937d45bdf499f30bd0a8b250a4a3", "timestamp": "2025-06-06T16:09:12.144Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/consolidate-duplicate-button-components.md", "target": "docs/📋-architecture/decisions/consolidate-duplicate-button-components.md", "category": "architecture", "confidence": 0.9, "sourceHash": "dfb2baa2e342afbdea7c29ee5b05c7b2c5bf7565a0938336d3d908b6acf1f4b6", "targetHash": "dfb2baa2e342afbdea7c29ee5b05c7b2c5bf7565a0938336d3d908b6acf1f4b6", "timestamp": "2025-06-06T16:09:12.144Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/consciousness-research-analysis.md", "target": "docs/📋-architecture/decisions/consciousness-research-analysis.md", "category": "architecture", "confidence": 0.9, "sourceHash": "fce24524fe6436a6ad7b50c7b26dcb96eb093026d9b1e1c4858bd44497f0baac", "targetHash": "fce24524fe6436a6ad7b50c7b26dcb96eb093026d9b1e1c4858bd44497f0baac", "timestamp": "2025-06-06T16:09:12.144Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/critical-mcp-impact-analysis.md", "target": "docs/📋-architecture/decisions/critical-mcp-impact-analysis.md", "category": "architecture", "confidence": 0.9, "sourceHash": "5ac7ebd9c92cb3302edd2885cff7ee51d9676442dbf83c41590978c04ca14771", "targetHash": "5ac7ebd9c92cb3302edd2885cff7ee51d9676442dbf83c41590978c04ca14771", "timestamp": "2025-06-06T16:09:12.145Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/decentralized-ai-resource-economy.md", "target": "docs/📋-architecture/decisions/decentralized-ai-resource-economy.md", "category": "architecture", "confidence": 0.9, "sourceHash": "11768b78b8d07a14067feb802e6854537458ea01464077ba784847f57881e486", "targetHash": "11768b78b8d07a14067feb802e6854537458ea01464077ba784847f57881e486", "timestamp": "2025-06-06T16:09:12.145Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/deep-compliance-audit-report.json", "target": "docs/📋-architecture/decisions/deep-compliance-audit-report.json", "category": "architecture", "confidence": 0.9, "sourceHash": "44b8fc777e8694c45fa0c518d17d0e170ca9b314fdc679d8e17faa60a57d1844", "targetHash": "44b8fc777e8694c45fa0c518d17d0e170ca9b314fdc679d8e17faa60a57d1844", "timestamp": "2025-06-06T16:09:12.145Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/documentation-verification-1748947364058.json", "target": "docs/📋-architecture/decisions/documentation-verification-1748947364058.json", "category": "architecture", "confidence": 0.9, "sourceHash": "6a4132b0a73d3ef5b4af1e4595eacf3c87be07f3af536b4d0ccdadbf5ffe1013", "targetHash": "6a4132b0a73d3ef5b4af1e4595eacf3c87be07f3af536b4d0ccdadbf5ffe1013", "timestamp": "2025-06-06T16:09:12.145Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/documentation-verification-1748947369921.json", "target": "docs/📋-architecture/decisions/documentation-verification-1748947369921.json", "category": "architecture", "confidence": 0.9, "sourceHash": "0a408cd048f0bb30c825db38bff6398acc21fba55f2586901ef93a653c3e4e0f", "targetHash": "0a408cd048f0bb30c825db38bff6398acc21fba55f2586901ef93a653c3e4e0f", "timestamp": "2025-06-06T16:09:12.145Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/documentation-verification-1748947347613.json", "target": "docs/📋-architecture/decisions/documentation-verification-1748947347613.json", "category": "architecture", "confidence": 0.9, "sourceHash": "a44e599472d47b8870c84cf4dc12828519b7afe053c52ae165fd6d9622878813", "targetHash": "a44e599472d47b8870c84cf4dc12828519b7afe053c52ae165fd6d9622878813", "timestamp": "2025-06-06T16:09:12.145Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/file-organization-enhancement.md", "target": "docs/📋-architecture/decisions/file-organization-enhancement.md", "category": "architecture", "confidence": 0.9, "sourceHash": "a6a6ed30e24a3a4ccdddf0d88fb7ae6d5bff4854085b323a2e6cfb5c2d4bdabb", "targetHash": "a6a6ed30e24a3a4ccdddf0d88fb7ae6d5bff4854085b323a2e6cfb5c2d4bdabb", "timestamp": "2025-06-06T16:09:12.146Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/file-management-strategy.md", "target": "docs/📋-architecture/decisions/file-management-strategy.md", "category": "architecture", "confidence": 0.9, "sourceHash": "bd578d7d52952ff2cf00ba0791a865dd4eb858b486496752ece00ea845021c40", "targetHash": "bd578d7d52952ff2cf00ba0791a865dd4eb858b486496752ece00ea845021c40", "timestamp": "2025-06-06T16:09:12.146Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/documentation-verification-1748947406714.json", "target": "docs/📋-architecture/decisions/documentation-verification-1748947406714.json", "category": "architecture", "confidence": 0.9, "sourceHash": "aa1c882703a6c7554cf9e91b52922248ae33fc2e508165d594438a268d98fc82", "targetHash": "aa1c882703a6c7554cf9e91b52922248ae33fc2e508165d594438a268d98fc82", "timestamp": "2025-06-06T16:09:12.146Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/frontend-backend-integration-status.md", "target": "docs/📋-architecture/decisions/frontend-backend-integration-status.md", "category": "architecture", "confidence": 0.9, "sourceHash": "719204460f3830e5b17099a47c0357b688eea18872da7fd1a9ff6eae5c05832a", "targetHash": "719204460f3830e5b17099a47c0357b688eea18872da7fd1a9ff6eae5c05832a", "timestamp": "2025-06-06T16:09:12.147Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/implementation-monitor.md", "target": "docs/📋-architecture/decisions/implementation-monitor.md", "category": "architecture", "confidence": 0.9, "sourceHash": "38031701cc15d2322eb27e282050dfe7bb55ad1817bf5fd104b3ea66cb025290", "targetHash": "38031701cc15d2322eb27e282050dfe7bb55ad1817bf5fd104b3ea66cb025290", "timestamp": "2025-06-06T16:09:12.147Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/implementation-helpers.md", "target": "docs/📋-architecture/decisions/implementation-helpers.md", "category": "architecture", "confidence": 0.9, "sourceHash": "ccf3020d28be2ba7c9083aefc3490a5d01d3b369ac16b8c72a9f0e4021d2021f", "targetHash": "ccf3020d28be2ba7c9083aefc3490a5d01d3b369ac16b8c72a9f0e4021d2021f", "timestamp": "2025-06-06T16:09:12.147Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/intelligent-agent-workflow-analysis.md", "target": "docs/📋-architecture/decisions/intelligent-agent-workflow-analysis.md", "category": "architecture", "confidence": 0.9, "sourceHash": "75e3a0b1c23cb0bc69d937e816b4d03c7afb2073d9a31c632a5a30a235693eab", "targetHash": "75e3a0b1c23cb0bc69d937e816b4d03c7afb2073d9a31c632a5a30a235693eab", "timestamp": "2025-06-06T16:09:12.148Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/master-implementation-strategy.md", "target": "docs/📋-architecture/decisions/master-implementation-strategy.md", "category": "architecture", "confidence": 0.9, "sourceHash": "bcb5b9bc5c1d55b316c2561846051a12ec3e57788f0ee350c96b51b6d2d482f2", "targetHash": "bcb5b9bc5c1d55b316c2561846051a12ec3e57788f0ee350c96b51b6d2d482f2", "timestamp": "2025-06-06T16:09:12.148Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/master-architecture-plan.md", "target": "docs/📋-architecture/decisions/master-architecture-plan.md", "category": "architecture", "confidence": 0.9, "sourceHash": "ea54445a84a255add5c0fdb5ed7ea5feb965e9f35221ecdaa1f97002a1915d7e", "targetHash": "ea54445a84a255add5c0fdb5ed7ea5feb965e9f35221ecdaa1f97002a1915d7e", "timestamp": "2025-06-06T16:09:12.148Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/mcp-integration-implementation-guide.md", "target": "docs/📋-architecture/decisions/mcp-integration-implementation-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "6c7c98fe7ad886104d523120b071fc7b806be58e8b070f9d2b895f60bd48992d", "targetHash": "6c7c98fe7ad886104d523120b071fc7b806be58e8b070f9d2b895f60bd48992d", "timestamp": "2025-06-06T16:09:12.149Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/model-comparison-results.md", "target": "docs/📋-architecture/decisions/model-comparison-results.md", "category": "architecture", "confidence": 0.9, "sourceHash": "7400b880c804def657ffb04ca72b17e7f4e81a698a7dfb5802ab7ef608b22ff1", "targetHash": "7400b880c804def657ffb04ca72b17e7f4e81a698a7dfb5802ab7ef608b22ff1", "timestamp": "2025-06-06T16:09:12.149Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/mcp-implementation-complete.md", "target": "docs/📋-architecture/decisions/mcp-implementation-complete.md", "category": "architecture", "confidence": 0.9, "sourceHash": "e24dfc717aff50bedaf45f600be198d716386d51e547f2e9608e694ca4ab4455", "targetHash": "e24dfc717aff50bedaf45f600be198d716386d51e547f2e9608e694ca4ab4455", "timestamp": "2025-06-06T16:09:12.149Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/phase6-integration-validation-demo-report.json", "target": "docs/📋-architecture/decisions/phase6-integration-validation-demo-report.json", "category": "architecture", "confidence": 0.9, "sourceHash": "23366fc3289f0e4b89002ff707b6ffeec63925b5818efecc0c3354b59acdad2d", "targetHash": "23366fc3289f0e4b89002ff707b6ffeec63925b5818efecc0c3354b59acdad2d", "timestamp": "2025-06-06T16:09:12.149Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/pheromone-trails.json", "target": "docs/📋-architecture/decisions/pheromone-trails.json", "category": "architecture", "confidence": 0.9, "sourceHash": "be2f12e0127e052d7544c368ea2244a478f8e4a5fa09cd9064a672d623ddcee4", "targetHash": "be2f12e0127e052d7544c368ea2244a478f8e4a5fa09cd9064a672d623ddcee4", "timestamp": "2025-06-06T16:09:12.149Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/phase1-implementation-guide.md", "target": "docs/📋-architecture/decisions/phase1-implementation-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "92a3da3afac97de6f550dd4adf85260abd66405ce8cb25c6d43cd6b735ca69c4", "targetHash": "92a3da3afac97de6f550dd4adf85260abd66405ce8cb25c6d43cd6b735ca69c4", "timestamp": "2025-06-06T16:09:12.149Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/quantum-agents-implementation-guide.md", "target": "docs/📋-architecture/decisions/quantum-agents-implementation-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "5a69b3f6de3e9f21e02f3fcc238e4c36f26aa3691b69f789e1a13dc187b28035", "targetHash": "5a69b3f6de3e9f21e02f3fcc238e4c36f26aa3691b69f789e1a13dc187b28035", "timestamp": "2025-06-06T16:09:12.150Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/quantum-core-implementation.md", "target": "docs/📋-architecture/decisions/quantum-core-implementation.md", "category": "architecture", "confidence": 0.9, "sourceHash": "c08865007489b3cacc421e34866908b7540fff5fe1a1dc68ccea5c3d37bf2f75", "targetHash": "c08865007489b3cacc421e34866908b7540fff5fe1a1dc68ccea5c3d37bf2f75", "timestamp": "2025-06-06T16:09:12.150Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/quantum-agents-bulletproof-implementation-guide.md", "target": "docs/📋-architecture/decisions/quantum-agents-bulletproof-implementation-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "7442a0f3785163587193169319219bb08b001726b4d0fde1464acb02590a7fb8", "targetHash": "7442a0f3785163587193169319219bb08b001726b4d0fde1464acb02590a7fb8", "timestamp": "2025-06-06T16:09:12.150Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/quantum-quick-start-guide.md", "target": "docs/📋-architecture/decisions/quantum-quick-start-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "b7d49f37743777f3912033ccbf688d9d871228a981deff87d6e80b1494f660bb", "targetHash": "b7d49f37743777f3912033ccbf688d9d871228a981deff87d6e80b1494f660bb", "timestamp": "2025-06-06T16:09:12.151Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/revolutionary-ai-blockchain-architecture.md", "target": "docs/📋-architecture/decisions/revolutionary-ai-blockchain-architecture.md", "category": "architecture", "confidence": 0.9, "sourceHash": "cc0b1d9f47255900fdcbb76391e5feba4281080b172c70a9d84f606bed2d2712", "targetHash": "cc0b1d9f47255900fdcbb76391e5feba4281080b172c70a9d84f606bed2d2712", "timestamp": "2025-06-06T16:09:12.151Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/r1-devstral-consensus-plan.md", "target": "docs/📋-architecture/decisions/r1-devstral-consensus-plan.md", "category": "architecture", "confidence": 0.9, "sourceHash": "f0e58e7b6c17144a633da59d50624f0787985cc2a133f4182c1e1909a565a6d3", "targetHash": "f0e58e7b6c17144a633da59d50624f0787985cc2a133f4182c1e1909a565a6d3", "timestamp": "2025-06-06T16:09:12.151Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/tasks.md", "target": "docs/📋-architecture/decisions/tasks.md", "category": "architecture", "confidence": 0.9, "sourceHash": "449e70875ab9769f6032444bea2faf2b89a0583132718e66dd7dcf9decaef663", "targetHash": "449e70875ab9769f6032444bea2faf2b89a0583132718e66dd7dcf9decaef663", "timestamp": "2025-06-06T16:09:12.151Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/task-management-completion-day-16.md", "target": "docs/📋-architecture/decisions/task-management-completion-day-16.md", "category": "architecture", "confidence": 0.9, "sourceHash": "b08e6d746c4af73fce4aab5b50714658dffacbb75ccba6c5a1e390f4c61670cc", "targetHash": "b08e6d746c4af73fce4aab5b50714658dffacbb75ccba6c5a1e390f4c61670cc", "timestamp": "2025-06-06T16:09:12.151Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/testing-command-center.md", "target": "docs/📋-architecture/decisions/testing-command-center.md", "category": "architecture", "confidence": 0.9, "sourceHash": "793827752633c198bf23886ea8ede36b5209d1943bffb411616927cd8e37baa3", "targetHash": "793827752633c198bf23886ea8ede36b5209d1943bffb411616927cd8e37baa3", "timestamp": "2025-06-06T16:09:12.152Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/DOCUMENTATION_WORKFLOW.md", "target": "docs/📋-methodologies/revolutions/DOCUMENTATION_WORKFLOW.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "a854af7d2240dc95df2d2078ef0608fbb90f9cfd918887b81fb12d33779c7224", "targetHash": "a854af7d2240dc95df2d2078ef0608fbb90f9cfd918887b81fb12d33779c7224", "timestamp": "2025-06-06T16:09:12.152Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/LivingAgentUtilizationPlan.md", "target": "docs/📋-methodologies/revolutions/LivingAgentUtilizationPlan.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "09c9aba8a31aa0883b7486567a290fbf05c57df667e8a58f27a45d6572f8c4b9", "targetHash": "09c9aba8a31aa0883b7486567a290fbf05c57df667e8a58f27a45d6572f8c4b9", "timestamp": "2025-06-06T16:09:12.152Z"}, {"type": "moved", "source": "docs/📋-architecture/decisions/typescript-revolution-quick-reference.md", "target": "docs/📋-architecture/decisions/typescript-revolution-quick-reference.md", "category": "architecture", "confidence": 0.9, "sourceHash": "1aa2b5849092ca7cf938f54f1445c1090f7061f614471c79b46d17c30e9fe7c9", "targetHash": "1aa2b5849092ca7cf938f54f1445c1090f7061f614471c79b46d17c30e9fe7c9", "timestamp": "2025-06-06T16:09:12.152Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/ORGANIZATION_ENHANCEMENT_SUMMARY.md", "target": "docs/📋-methodologies/revolutions/ORGANIZATION_ENHANCEMENT_SUMMARY.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "5eada5d090256fae9d80281195372effcc13541fe649fdbd7be09b176e67079e", "targetHash": "5eada5d090256fae9d80281195372effcc13541fe649fdbd7be09b176e67079e", "timestamp": "2025-06-06T16:09:12.153Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/PROJECT_ORGANIZATION_ENHANCED.md", "target": "docs/📋-methodologies/revolutions/PROJECT_ORGANIZATION_ENHANCED.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "6b41ef8b9296c95723032f1ff8ab053a743ded70aaf8b155574dcb3d6da04b83", "targetHash": "6b41ef8b9296c95723032f1ff8ab053a743ded70aaf8b155574dcb3d6da04b83", "timestamp": "2025-06-06T16:09:12.153Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/LivingAgentUtilizationSummary.md", "target": "docs/📋-methodologies/revolutions/LivingAgentUtilizationSummary.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "bc3f881d7aa71a20bc9be76d8f4731c89fff77be2ab696c75d9ff39b1ef914b3", "targetHash": "bc3f881d7aa71a20bc9be76d8f4731c89fff77be2ab696c75d9ff39b1ef914b3", "timestamp": "2025-06-06T16:09:12.153Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/R1-Implementation-Success.md", "target": "docs/📋-methodologies/revolutions/R1-Implementation-Success.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "fd3ac28aeeb9e902775ca46812badc45dea5fac99e886ecb2c40bca7524e421f", "targetHash": "fd3ac28aeeb9e902775ca46812badc45dea5fac99e886ecb2c40bca7524e421f", "timestamp": "2025-06-06T16:09:12.154Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/ROADMAP.md", "target": "docs/📋-methodologies/revolutions/ROADMAP.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "752b3a138af2154d5c48c487aa1d3ab1e5ec60048e2da451d70a9c9206d0ee93", "targetHash": "752b3a138af2154d5c48c487aa1d3ab1e5ec60048e2da451d70a9c9206d0ee93", "timestamp": "2025-06-06T16:09:12.154Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/WorkflowEnhancementAgent-Development-Session-13.md", "target": "docs/📋-methodologies/revolutions/WorkflowEnhancementAgent-Development-Session-13.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "31347e3efde7ede51e824d4e0371d72e645c3e56a080c9f8b0082dd901d7f882", "targetHash": "31347e3efde7ede51e824d4e0371d72e645c3e56a080c9f8b0082dd901d7f882", "timestamp": "2025-06-06T16:09:12.154Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/agentmesh-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/agentmesh-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "6d4c71f7e3b79d7e218c34815d47731e967ce02bc7599917f16b04713344a4d1", "targetHash": "6d4c71f7e3b79d7e218c34815d47731e967ce02bc7599917f16b04713344a4d1", "timestamp": "2025-06-06T16:09:12.154Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/agent-refactoring-verification-report.json", "target": "docs/📋-methodologies/revolutions/agent-refactoring-verification-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "b68d2bb7b5c7b65a4002884ca368cfbe966bd443bce309d6a9dc1f85eb36ec7f", "targetHash": "b68d2bb7b5c7b65a4002884ca368cfbe966bd443bce309d6a9dc1f85eb36ec7f", "timestamp": "2025-06-06T16:09:12.154Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/autonomousdevagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/autonomousdevagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "c687551b10932aec22c6604c41be94ac6f6df3fd8cfdbe79991ef3cc9c98db18", "targetHash": "c687551b10932aec22c6604c41be94ac6f6df3fd8cfdbe79991ef3cc9c98db18", "timestamp": "2025-06-06T16:09:12.154Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/configagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/configagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "67a0f7f69fe29218a1549d3980914e1151e532df6dcfd6d791ac9e1ada323d66", "targetHash": "67a0f7f69fe29218a1549d3980914e1151e532df6dcfd6d791ac9e1ada323d66", "timestamp": "2025-06-06T16:09:12.155Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/chatresponseparseragent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/chatresponseparseragent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "c39f7282601b22592324d8abc6df2d8f766baa334dbad0aa39a5b72835fcd31a", "targetHash": "c39f7282601b22592324d8abc6df2d8f766baa334dbad0aa39a5b72835fcd31a", "timestamp": "2025-06-06T16:09:12.155Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/conversationaldevagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/conversationaldevagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "078bf4b7fad2a446d1e5c1f690d3e03f2a76ff3cebda449cd04f95e8a1ecb0ec", "targetHash": "078bf4b7fad2a446d1e5c1f690d3e03f2a76ff3cebda449cd04f95e8a1ecb0ec", "timestamp": "2025-06-06T16:09:12.155Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/crossagentcommunicationengine-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/crossagentcommunicationengine-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "56c215a02c13754362214a594f6a7fcf0bdb02ec5b910a4f04daf3e121239bd7", "targetHash": "56c215a02c13754362214a594f6a7fcf0bdb02ec5b910a4f04daf3e121239bd7", "timestamp": "2025-06-06T16:09:12.155Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/devagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/devagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "ef21ea66f9ee3e1b6952ce18f3af834f84f72eed771bbbecd3e7f209380e6208", "targetHash": "ef21ea66f9ee3e1b6952ce18f3af834f84f72eed771bbbecd3e7f209380e6208", "timestamp": "2025-06-06T16:09:12.155Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/development-workflow-strategy.md", "target": "docs/📋-methodologies/revolutions/development-workflow-strategy.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "8c68acebd30854b0799e57e08fc93ada44c9ead940151ee51fcc81f826f051c3", "targetHash": "8c68acebd30854b0799e57e08fc93ada44c9ead940151ee51fcc81f826f051c3", "timestamp": "2025-06-06T16:09:12.156Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/featurediscoveryagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/featurediscoveryagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "51637fa40330379ac3fc7dead93f9253a2945e71d75746591475de9756cf43e2", "targetHash": "51637fa40330379ac3fc7dead93f9253a2945e71d75746591475de9756cf43e2", "timestamp": "2025-06-06T16:09:12.156Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/livinguiagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/livinguiagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "09f9f52a5a527b30184b26867d7a932279c4e520d7ac310faf8a57369eaa9977", "targetHash": "09f9f52a5a527b30184b26867d7a932279c4e520d7ac310faf8a57369eaa9977", "timestamp": "2025-06-06T16:09:12.156Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/errormonitoragent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/errormonitoragent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "64233cbe3b7e111fda89046c5d19b657abdd89f226c06ee9d025ee325ae4fe9d", "targetHash": "64233cbe3b7e111fda89046c5d19b657abdd89f226c06ee9d025ee325ae4fe9d", "timestamp": "2025-06-06T16:09:12.156Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/opsagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/opsagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "52ab1616f1437cf4f61c8f0e09991be00d8e876e82af0716ee92dee7732de691", "targetHash": "52ab1616f1437cf4f61c8f0e09991be00d8e876e82af0716ee92dee7732de691", "timestamp": "2025-06-06T16:09:12.157Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/operation-log.json", "target": "docs/📋-methodologies/revolutions/operation-log.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "a53353ea678c995a3ef2ea379436241c203980cbe7f5d2eb5688324e5c5abd2b", "targetHash": "a53353ea678c995a3ef2ea379436241c203980cbe7f5d2eb5688324e5c5abd2b", "timestamp": "2025-06-06T16:09:12.157Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/securityagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/securityagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "a51f24358118a407c6f66ee814e672f1d988a617dc80ea52e5c8077d2fab0b54", "targetHash": "a51f24358118a407c6f66ee814e672f1d988a617dc80ea52e5c8077d2fab0b54", "timestamp": "2025-06-06T16:09:12.157Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/uiagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/uiagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "9761c397a552d95a2ffae6364b061609798ee80f6a460d2d1fa6d96d75ceed41", "targetHash": "9761c397a552d95a2ffae6364b061609798ee80f6a460d2d1fa6d96d75ceed41", "timestamp": "2025-06-06T16:09:12.158Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/ultimatetranscendenceorchestrator-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/ultimatetranscendenceorchestrator-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "a83f9a19c02f93694990c0019d817516791a3a15346bead18cf58139b38802d2", "targetHash": "a83f9a19c02f93694990c0019d817516791a3a15346bead18cf58139b38802d2", "timestamp": "2025-06-06T16:09:12.158Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/success-patterns-guide.json", "target": "docs/📋-methodologies/revolutions/success-patterns-guide.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "142cf135a40d055b1634607b1c26a8e4f9a7f37f002b77c322b0c2815ceb581e", "targetHash": "142cf135a40d055b1634607b1c26a8e4f9a7f37f002b77c322b0c2815ceb581e", "timestamp": "2025-06-06T16:09:12.159Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/userbehavioragent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/userbehavioragent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "a26af39c9268630ccc1659313286cab6d8c5d40907aefbf2e76b3dc0520a913c", "targetHash": "a26af39c9268630ccc1659313286cab6d8c5d40907aefbf2e76b3dc0520a913c", "timestamp": "2025-06-06T16:09:12.160Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/unified-integration-solution.md", "target": "docs/📋-methodologies/revolutions/unified-integration-solution.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "c44a976bc5ea9d29dc1d84bf9611d8cf43e9080bb9b179b9e06ff13b8fb5b202", "targetHash": "c44a976bc5ea9d29dc1d84bf9611d8cf43e9080bb9b179b9e06ff13b8fb5b202", "timestamp": "2025-06-06T16:09:12.160Z"}, {"type": "moved", "source": "docs/📋-methodologies/revolutions/workflowenhancementagent-refactoring-report.json", "target": "docs/📋-methodologies/revolutions/workflowenhancementagent-refactoring-report.json", "category": "methodologies", "confidence": 0.7, "sourceHash": "fc88eba2fb0243cce7d1be8db87118dea8d4039994b118b1bee845009f2f43c9", "targetHash": "fc88eba2fb0243cce7d1be8db87118dea8d4039994b118b1bee845009f2f43c9", "timestamp": "2025-06-06T16:09:12.160Z"}, {"type": "moved", "source": "docs/📋-project/tasks/PHASE1-CONSOLIDATION-REPORT.md", "target": "docs/📋-project/tasks/PHASE1-CONSOLIDATION-REPORT.md", "category": "project-docs", "confidence": 0.9, "sourceHash": "9c62bfb0a1945de62502c1d86057c0647eb19aad95dc937c814034531558e237", "targetHash": "9c62bfb0a1945de62502c1d86057c0647eb19aad95dc937c814034531558e237", "timestamp": "2025-06-06T16:09:12.161Z"}, {"type": "moved", "source": "docs/📋-project/tasks/Complete-Agent-System-Analysis.md", "target": "docs/📋-project/tasks/Complete-Agent-System-Analysis.md", "category": "project-docs", "confidence": 0.9, "sourceHash": "544a0ffb3ed176793eab88d2008b0f3620ed91408c29a9e824a7d2e4d4af8779", "targetHash": "544a0ffb3ed176793eab88d2008b0f3620ed91408c29a9e824a7d2e4d4af8779", "timestamp": "2025-06-06T16:09:12.161Z"}, {"type": "moved", "source": "docs/📋-project/tasks/Devstral-Agent-Architecture-Analysis.md", "target": "docs/📋-project/tasks/Devstral-Agent-Architecture-Analysis.md", "category": "project-docs", "confidence": 0.9, "sourceHash": "a68a23f9c912d599df3ddbae7c6bea86f143ea7ea8704a253a886ec07f5ae0fd", "targetHash": "a68a23f9c912d599df3ddbae7c6bea86f143ea7ea8704a253a886ec07f5ae0fd", "timestamp": "2025-06-06T16:09:12.161Z"}, {"type": "moved", "source": "docs/📝-technical/solutions/enhanced-keyboard-navigation-system.md", "target": "docs/📝-technical/solutions/enhanced-keyboard-navigation-system.md", "category": "technical", "confidence": 0.85, "sourceHash": "bc546a5c6a883ca741d642bfeda80d5975b027e469fa48aa0dd29764bec05074", "targetHash": "bc546a5c6a883ca741d642bfeda80d5975b027e469fa48aa0dd29764bec05074", "timestamp": "2025-06-06T16:09:12.161Z"}, {"type": "moved", "source": "docs/📝-technical/solutions/README.md", "target": "docs/📝-technical/solutions/README.md", "category": "technical", "confidence": 0.85, "sourceHash": "d3c634c797a315cfc19e36772f8c02d432b397452ca7e5d017e6ebaae5e321a3", "targetHash": "d3c634c797a315cfc19e36772f8c02d432b397452ca7e5d017e6ebaae5e321a3", "timestamp": "2025-06-06T16:09:12.161Z"}, {"type": "moved", "source": "docs/📝-technical/solutions/interface-conflict-prevention-system.md", "target": "docs/📝-technical/solutions/interface-conflict-prevention-system.md", "category": "technical", "confidence": 0.85, "sourceHash": "172a59764b6bd57315d3abfe5b113e09a675ca29841cb37012386d683ebcbd2c", "targetHash": "172a59764b6bd57315d3abfe5b113e09a675ca29841cb37012386d683ebcbd2c", "timestamp": "2025-06-06T16:09:12.161Z"}, {"type": "moved", "source": "docs/🔧-utilities/scripts/start-dev-clean.sh", "target": "docs/🔧-utilities/scripts/start-dev-clean.sh", "category": "misc-scripts", "confidence": 0.7, "sourceHash": "a768f5590258d9b5ad53b3fe6412a9f23f471dfe90f999944923d11f6ea5e5bf", "targetHash": "a768f5590258d9b5ad53b3fe6412a9f23f471dfe90f999944923d11f6ea5e5bf", "timestamp": "2025-06-06T16:09:12.162Z"}, {"type": "moved", "source": "docs/🔧-utilities/scripts/fix-common-errors.js", "target": "docs/🔧-utilities/scripts/fix-common-errors.js", "category": "misc-scripts", "confidence": 0.7, "sourceHash": "afa08b1ffc91b69da30b2b6a136cc974f7dd46caa83486ebdc4b333721d5295f", "targetHash": "afa08b1ffc91b69da30b2b6a136cc974f7dd46caa83486ebdc4b333721d5295f", "timestamp": "2025-06-06T16:09:12.162Z"}, {"type": "moved", "source": "docs/🔧-utilities/scripts/start-dev.sh", "target": "docs/🔧-utilities/scripts/start-dev.sh", "category": "misc-scripts", "confidence": 0.7, "sourceHash": "806024bf83a1a1f3cbb1c9e4d9e0c4bf6555c36fbbe7db0896571b4b44e1f7d9", "targetHash": "806024bf83a1a1f3cbb1c9e4d9e0c4bf6555c36fbbe7db0896571b4b44e1f7d9", "timestamp": "2025-06-06T16:09:12.162Z"}, {"type": "moved", "source": "docs/🧪-testing/results/AI_ENHANCEMENT_TESTING_GUIDE.md", "target": "docs/🧪-testing/results/AI_ENHANCEMENT_TESTING_GUIDE.md", "category": "testing", "confidence": 0.8, "sourceHash": "5fb50bb8ba1abd2f0f8d6fe32e00f5cf08db6e723b38898d7c19c77cfeedd103", "targetHash": "5fb50bb8ba1abd2f0f8d6fe32e00f5cf08db6e723b38898d7c19c77cfeedd103", "timestamp": "2025-06-06T16:09:12.162Z"}, {"type": "moved", "source": "docs/🧪-testing/results/CANVAS_TEST_REPORT.md", "target": "docs/🧪-testing/results/CANVAS_TEST_REPORT.md", "category": "testing", "confidence": 0.8, "sourceHash": "0e43575b2809e7b5c0bbb652407230dbbd4ecdc27a563dc51684e24baf9cd0a7", "targetHash": "0e43575b2809e7b5c0bbb652407230dbbd4ecdc27a563dc51684e24baf9cd0a7", "timestamp": "2025-06-06T16:09:12.162Z"}, {"type": "moved", "source": "docs/🧪-testing/results/Agent-Migration-Integration-Report.json", "target": "docs/🧪-testing/results/Agent-Migration-Integration-Report.json", "category": "testing", "confidence": 0.8, "sourceHash": "70b2919390e98df2397ab1412322246f68a687b4570ad351a7750422b9716a4d", "targetHash": "70b2919390e98df2397ab1412322246f68a687b4570ad351a7750422b9716a4d", "timestamp": "2025-06-06T16:09:12.162Z"}, {"type": "moved", "source": "docs/🧪-testing/results/CreAItive_All_Combined.md", "target": "docs/🧪-testing/results/CreAItive_All_Combined.md", "category": "testing", "confidence": 0.8, "sourceHash": "4d0da6a34606764c872ab3dd3f550e58c49953041441d2f953ca6b1ce6bae73f", "targetHash": "4d0da6a34606764c872ab3dd3f550e58c49953041441d2f953ca6b1ce6bae73f", "timestamp": "2025-06-06T16:09:12.163Z"}, {"type": "moved", "source": "docs/🧪-testing/results/STRATEGIC_REAL_IMPLEMENTATION_APPROACH.md", "target": "docs/🧪-testing/results/STRATEGIC_REAL_IMPLEMENTATION_APPROACH.md", "category": "testing", "confidence": 0.8, "sourceHash": "816d336e8de02f51d9cac9f85c3b9e5560c6332ad8a4e50451c768c8bfcdc03e", "targetHash": "816d336e8de02f51d9cac9f85c3b9e5560c6332ad8a4e50451c768c8bfcdc03e", "timestamp": "2025-06-06T16:09:12.163Z"}, {"type": "moved", "source": "docs/🧪-testing/results/HYBRID_INTELLIGENCE_STRATEGY.md", "target": "docs/🧪-testing/results/HYBRID_INTELLIGENCE_STRATEGY.md", "category": "testing", "confidence": 0.8, "sourceHash": "08b0c3da3b20e3975be16da94001be088d253d2fbbe968885a3010014b936297", "targetHash": "08b0c3da3b20e3975be16da94001be088d253d2fbbe968885a3010014b936297", "timestamp": "2025-06-06T16:09:12.163Z"}, {"type": "moved", "source": "docs/🧪-testing/results/TestAgent-Development-Session-5.md", "target": "docs/🧪-testing/results/TestAgent-Development-Session-5.md", "category": "testing", "confidence": 0.8, "sourceHash": "86b2bce34c154006c09df5801deebf26404b0157652b9c9d93ed73d273baf691", "targetHash": "86b2bce34c154006c09df5801deebf26404b0157652b9c9d93ed73d273baf691", "timestamp": "2025-06-06T16:09:12.164Z"}, {"type": "moved", "source": "docs/🧪-testing/results/SUCCESS-PATTERNS-GUIDE.md", "target": "docs/🧪-testing/results/SUCCESS-PATTERNS-GUIDE.md", "category": "testing", "confidence": 0.8, "sourceHash": "b40c71851e81b47b59afb021b98a67438fb4c63acc0a135ca4fb5eab502b61da", "targetHash": "b40c71851e81b47b59afb021b98a67438fb4c63acc0a135ca4fb5eab502b61da", "timestamp": "2025-06-06T16:09:12.164Z"}, {"type": "moved", "source": "docs/🧪-testing/results/TestAgent-Interface.md", "target": "docs/🧪-testing/results/TestAgent-Interface.md", "category": "testing", "confidence": 0.8, "sourceHash": "edeacf59d7c9a879ce32f614af47130f9d9fe349f6ad96d992964705a18fa434", "targetHash": "edeacf59d7c9a879ce32f614af47130f9d9fe349f6ad96d992964705a18fa434", "timestamp": "2025-06-06T16:09:12.164Z"}, {"type": "moved", "source": "docs/🧪-testing/results/canvas-test-results.json", "target": "docs/🧪-testing/results/canvas-test-results.json", "category": "testing", "confidence": 0.8, "sourceHash": "cb51adb7041904573f5d82f7355132da4e8a2071b8aa4ec48b80bd30a36329af", "targetHash": "cb51adb7041904573f5d82f7355132da4e8a2071b8aa4ec48b80bd30a36329af", "timestamp": "2025-06-06T16:09:12.164Z"}, {"type": "moved", "source": "docs/🧪-testing/results/automated-visual-regression-testing.md", "target": "docs/🧪-testing/results/automated-visual-regression-testing.md", "category": "testing", "confidence": 0.8, "sourceHash": "c13527ce04957faaa1ced532b3c1de1c549d2b1d88e55f6ed097c67f414b926a", "targetHash": "c13527ce04957faaa1ced532b3c1de1c549d2b1d88e55f6ed097c67f414b926a", "timestamp": "2025-06-06T16:09:12.164Z"}, {"type": "moved", "source": "docs/🧪-testing/results/ai-enhancement-test-report.json", "target": "docs/🧪-testing/results/ai-enhancement-test-report.json", "category": "testing", "confidence": 0.8, "sourceHash": "18fe96113ff55004d508904522e4b084c01cf1602e1b8461c2b438db9b53f62a", "targetHash": "18fe96113ff55004d508904522e4b084c01cf1602e1b8461c2b438db9b53f62a", "timestamp": "2025-06-06T16:09:12.164Z"}, {"type": "moved", "source": "docs/🧪-testing/results/extraction-report.json", "target": "docs/🧪-testing/results/extraction-report.json", "category": "testing", "confidence": 0.8, "sourceHash": "8772b168e24a029c9529a572e6224d87eac888ac17ad1ea7df8c9f6ebadc92b9", "targetHash": "8772b168e24a029c9529a572e6224d87eac888ac17ad1ea7df8c9f6ebadc92b9", "timestamp": "2025-06-06T16:09:12.165Z"}, {"type": "moved", "source": "docs/🧪-testing/results/enhanced-phase-strategy-summary.md", "target": "docs/🧪-testing/results/enhanced-phase-strategy-summary.md", "category": "testing", "confidence": 0.8, "sourceHash": "4fd090a957a6a6e759b2bf85eb9e2d519c39462a3448761492246246395c6568", "targetHash": "4fd090a957a6a6e759b2bf85eb9e2d519c39462a3448761492246246395c6568", "timestamp": "2025-06-06T16:09:12.165Z"}, {"type": "moved", "source": "docs/🧪-testing/results/comprehensive-docs-organization-final-report.md", "target": "docs/🧪-testing/results/comprehensive-docs-organization-final-report.md", "category": "testing", "confidence": 0.8, "sourceHash": "066af57c63453462f6616e4db22ef0a45a24f925f28b2c52bcd6aa7c29111282", "targetHash": "066af57c63453462f6616e4db22ef0a45a24f925f28b2c52bcd6aa7c29111282", "timestamp": "2025-06-06T16:09:12.165Z"}, {"type": "moved", "source": "docs/🧪-testing/results/mvp-specification.md", "target": "docs/🧪-testing/results/mvp-specification.md", "category": "testing", "confidence": 0.8, "sourceHash": "b95e0c7534cbfd323a43e69b2e118e15c866407d39cc2513a10da15b21aad2e3", "targetHash": "b95e0c7534cbfd323a43e69b2e118e15c866407d39cc2513a10da15b21aad2e3", "timestamp": "2025-06-06T16:09:12.166Z"}, {"type": "moved", "source": "docs/🧪-testing/results/log-management-solution.md", "target": "docs/🧪-testing/results/log-management-solution.md", "category": "testing", "confidence": 0.8, "sourceHash": "26be3ea0fb6715389f9670b58c9bf4ed2b5c642786098f0ab68842a0ed32b912", "targetHash": "26be3ea0fb6715389f9670b58c9bf4ed2b5c642786098f0ab68842a0ed32b912", "timestamp": "2025-06-06T16:09:12.166Z"}, {"type": "moved", "source": "docs/🧪-testing/results/model-optimization-results.md", "target": "docs/🧪-testing/results/model-optimization-results.md", "category": "testing", "confidence": 0.8, "sourceHash": "9790a6ea14645fe9e723f9256c8463bc32469d428a70b399663690eccfe27f49", "targetHash": "9790a6ea14645fe9e723f9256c8463bc32469d428a70b399663690eccfe27f49", "timestamp": "2025-06-06T16:09:12.166Z"}, {"type": "moved", "source": "docs/🧪-testing/results/optimization-analysis-results.md", "target": "docs/🧪-testing/results/optimization-analysis-results.md", "category": "testing", "confidence": 0.8, "sourceHash": "6b8330447d55b0a88aee01784bb3bcf19bc9a11ff777a5f22f9e7073111d8d1c", "targetHash": "6b8330447d55b0a88aee01784bb3bcf19bc9a11ff777a5f22f9e7073111d8d1c", "timestamp": "2025-06-06T16:09:12.166Z"}, {"type": "moved", "source": "docs/🧪-testing/results/testagent-refactoring-report.json", "target": "docs/🧪-testing/results/testagent-refactoring-report.json", "category": "testing", "confidence": 0.8, "sourceHash": "8939b5fac8a90ce680c0b64837adf6cba0429c7f45b80b1919d872f6acd591b2", "targetHash": "8939b5fac8a90ce680c0b64837adf6cba0429c7f45b80b1919d872f6acd591b2", "timestamp": "2025-06-06T16:09:12.166Z"}, {"type": "moved", "source": "docs/🧪-testing/results/test-results.json", "target": "docs/🧪-testing/results/test-results.json", "category": "testing", "confidence": 0.8, "sourceHash": "4a5edf490cdb491e8e7271f5d175db685c20bc54b5467990c39e5d0bf719f1c0", "targetHash": "4a5edf490cdb491e8e7271f5d175db685c20bc54b5467990c39e5d0bf719f1c0", "timestamp": "2025-06-06T16:09:12.169Z"}, {"type": "moved", "source": "docs/🧪-testing/results/typescript-error-resolution-revolution.md", "target": "docs/🧪-testing/results/typescript-error-resolution-revolution.md", "category": "testing", "confidence": 0.8, "sourceHash": "ddf063d3c6aa1d274c93edb8171200fb7c35cf7e14ef96ebb2c7f514ca58672e", "targetHash": "ddf063d3c6aa1d274c93edb8171200fb7c35cf7e14ef96ebb2c7f514ca58672e", "timestamp": "2025-06-06T16:09:12.169Z"}, {"type": "moved", "source": "docs/🧪-testing/results/unified-system-command-center.md", "target": "docs/🧪-testing/results/unified-system-command-center.md", "category": "testing", "confidence": 0.8, "sourceHash": "3ed43bc162b1d9726dab5202955d1def685b29a71ff306d6aa875dd8a3e56498", "targetHash": "3ed43bc162b1d9726dab5202955d1def685b29a71ff306d6aa875dd8a3e56498", "timestamp": "2025-06-06T16:09:12.169Z"}, {"type": "moved", "source": "docs/🧪-testing/results/tsconfig.test.json", "target": "docs/🧪-testing/results/tsconfig.test.json", "category": "testing", "confidence": 0.8, "sourceHash": "80b1d331c29fe435071d628afaf858b45bd6df46cb9028a5f9c407dbc9a55870", "targetHash": "80b1d331c29fe435071d628afaf858b45bd6df46cb9028a5f9c407dbc9a55870", "timestamp": "2025-06-06T16:09:12.169Z"}]