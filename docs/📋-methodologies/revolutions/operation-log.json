[{"type": "analyzed", "path": "docs/.DS_Store", "hash": "e43e409aedc6b0ef315643c51035d341aeab6d3da18b710691bd0d3d2713bfd6", "timestamp": "2025-06-02T16:15:25.137Z"}, {"type": "analyzed", "path": "docs/AI_ENHANCEMENT_TESTING_GUIDE.md", "hash": "91bd83861199e18b1e5ec65182296113845549e6f24d74c4f3b53cd8522a0024", "timestamp": "2025-06-02T16:15:25.137Z"}, {"type": "analyzed", "path": "docs/CURSOR_MEMORY_BANK_BACKUP.md", "hash": "ceb957568bbc433d91dba684354abc540e5ce9af2363e3435c38dba3b8b795b4", "timestamp": "2025-06-02T16:15:25.137Z"}, {"type": "analyzed", "path": "docs/R1-Phase2-Implementation-Plan.md", "hash": "7de2f635703d35a59eb741e1859dd3c261359ad861720e5aa0c5ba9f62e829b9", "timestamp": "2025-06-02T16:15:25.138Z"}, {"type": "analyzed", "path": "docs/README.md", "hash": "857b2b916e69633bd95cf0a2fb7aa6f88275284eab9bcc93a2a338ba80955715", "timestamp": "2025-06-02T16:15:25.138Z"}, {"type": "analyzed", "path": "docs/R1-Implementation-Success.md", "hash": "8ce60e28824a6b7ff0425878638f7f9d172479e8ea13875c0a1fed62eb1c034f", "timestamp": "2025-06-02T16:15:25.138Z"}, {"type": "analyzed", "path": "docs/adr/README.md", "hash": "7a7448a1b66f2c2cb4bd7f168186d43712bf2dbe6c74cb7d0bd02618e7c77193", "timestamp": "2025-06-02T16:15:25.138Z"}, {"type": "analyzed", "path": "docs/adr/adr-0001-nextjs-frontend-framework.md", "hash": "b349711c3df6a0058e106207478345f35bb496dec757673297445ff4a17671d9", "timestamp": "2025-06-02T16:15:25.138Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/MLCoordinationLayer-Development-Session-6.md", "hash": "5baa0237b7cc55f17466c0de30610f2c81de214f043df92a9c9fce285b6531ea", "timestamp": "2025-06-02T16:15:25.139Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/AutonomousGoalSetting-Development-Session-3.md", "hash": "01ebff590458b6d664af21f72984e3eb95c065a94d4ad31796554dfc45c50d92", "timestamp": "2025-06-02T16:15:25.139Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/ConfigAgent-Development-Session-7.md", "hash": "232b6ad3491d87cd99f7a21ba549ff79509e10d4c6d9d7b9b24f6084470c42a3", "timestamp": "2025-06-02T16:15:25.139Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/AutonomousNotificationSystem-Development-Session-10.md", "hash": "c86363dd2582dcbe624761ea7c21c0570476195e06d11db587bd46a01384cbc7", "timestamp": "2025-06-02T16:15:25.139Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/ErrorMonitorAgent-Development-Session-1.md", "hash": "56b29e7096f4b53760a2058377d13388f6c0c3d03a54fed3bfe78c9d5a428678", "timestamp": "2025-06-02T16:15:25.140Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/OpsAgent-Development-Session-4.md", "hash": "6ec8563bdd162772aadb4c5373f24d643720b7deefd46d3d972890435224bcd5", "timestamp": "2025-06-02T16:15:25.140Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/FeatureDiscoveryAgent-Development-Session-14.md", "hash": "53ac5fad653fff86bcbc0071561a09131e62d4167ffbce1e759cd04d0649a38e", "timestamp": "2025-06-02T16:15:25.140Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/AutonomousIntelligenceAgent-Development-Session-12.md", "hash": "baf55d87b734de8fb26d5fdc84d7ad56442ee2d8df4b8207f17620d66699b051", "timestamp": "2025-06-02T16:15:25.141Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/PredictiveGoalForecasting-Development-Session-8.md", "hash": "30c1b0f32c5c277d71635aaf8335d3aab548eebd4feb5bb474f34bc2214eab83", "timestamp": "2025-06-02T16:15:25.141Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/SecurityAgent-Development-Session-11.md", "hash": "201a643fde0e07788e5ad59201a0f023e9fd9cc2a688d3b34d1de80e67bf2946", "timestamp": "2025-06-02T16:15:25.141Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/TestAgent-Development-Session-5.md", "hash": "0507f52cb56b8f282fc8ae30f3f7ba3f1eb82ca0d3b5ee858e38a93f67c1d512", "timestamp": "2025-06-02T16:15:25.141Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/UIAgent-Development-Session-2.md", "hash": "e5ef2db8bdcd69a1d2282b13147a9204f686212c77119676995cdc5fc9c69251", "timestamp": "2025-06-02T16:15:25.141Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/VectorMemory-Development-Session-9.md", "hash": "d614ad6fed906b5c5d58731af60d27639a8bcd12105e4f8e7332aea4245f7e27", "timestamp": "2025-06-02T16:15:25.141Z"}, {"type": "analyzed", "path": "docs/agent-roles/AgentRoleDefinitions.md", "hash": "eeb7d7a8f212fbd36fc1b056d18a4f72c602b63bd6d6f00c6d75460b5b439458", "timestamp": "2025-06-02T16:15:25.142Z"}, {"type": "analyzed", "path": "docs/agent-intelligence-sessions/WorkflowEnhancementAgent-Development-Session-13.md", "hash": "8eae8bb95196ee8237dd79f5787b74f657b3594cb4dd2bf560af0572153bd5b9", "timestamp": "2025-06-02T16:15:25.142Z"}, {"type": "analyzed", "path": "docs/agent-transformation/AgentCodeTransformation.md", "hash": "6bbda87c51f8dc193854a99465438e61a17aa9ff340485e8ea3d883da8403226", "timestamp": "2025-06-02T16:15:25.142Z"}, {"type": "analyzed", "path": "docs/ai-identified-component-consolidation-opportunities.md", "hash": "d71df0abc42e59db1ac73aa48e222af2cf6385a8a2d496f32d86f09966b066e8", "timestamp": "2025-06-02T16:15:25.143Z"}, {"type": "analyzed", "path": "docs/ai-powered-dual-phase-success.md", "hash": "30db3166dcebcc0b4e86d270211ce52e7ab1d92ab50545ee30be87c51ccf716a", "timestamp": "2025-06-02T16:15:25.143Z"}, {"type": "analyzed", "path": "docs/automated-visual-regression-testing.md", "hash": "c13527ce04957faaa1ced532b3c1de1c549d2b1d88e55f6ed097c67f414b926a", "timestamp": "2025-06-02T16:15:25.143Z"}, {"type": "analyzed", "path": "docs/completed-features/EnhancedAIIntegrationCompletion.md", "hash": "64a38bc34e353383fb744a42c4c0b17c777151bd2da3b726442e87f5fd7ffc19", "timestamp": "2025-06-02T16:15:25.143Z"}, {"type": "analyzed", "path": "docs/completed-features/AdvancedDrawingToolsCompletion.md", "hash": "0ac1c9ef04de09fb240b8d7f45dde93fed23013ba05c793027371cab816ca7be", "timestamp": "2025-06-02T16:15:25.143Z"}, {"type": "analyzed", "path": "docs/completed-features/AdvancedCanvasLayerManagementCompletion.md", "hash": "36a9e7f1c95b82ffb99743e0c5c4ce95d83c9a430aac59f84ef3cbfab6145068", "timestamp": "2025-06-02T16:15:25.143Z"}, {"type": "analyzed", "path": "docs/consolidate-duplicate-button-components.md", "hash": "dfb2baa2e342afbdea7c29ee5b05c7b2c5bf7565a0938336d3d908b6acf1f4b6", "timestamp": "2025-06-02T16:15:25.144Z"}, {"type": "analyzed", "path": "docs/comprehensive-docs-organization.md", "hash": "d989c6f65939f86c20d99040d492b873e62902287328e9fe015e956ea6ea00f2", "timestamp": "2025-06-02T16:15:25.144Z"}, {"type": "analyzed", "path": "docs/enhanced-keyboard-navigation-system.md", "hash": "bc546a5c6a883ca741d642bfeda80d5975b027e469fa48aa0dd29764bec05074", "timestamp": "2025-06-02T16:15:25.144Z"}, {"type": "analyzed", "path": "docs/examples/env.example", "hash": "c13f5394523f768a7464d5128afd74e4a2a16aae87b8c42b245e0cd3f4f93084", "timestamp": "2025-06-02T16:15:25.144Z"}, {"type": "analyzed", "path": "docs/file-organization-enhancement.md", "hash": "3c036e6d1d6d63d7decf3da921651142ea14c5aef549be66baeffce1939f6cac", "timestamp": "2025-06-02T16:15:25.144Z"}, {"type": "analyzed", "path": "docs/implementation/LivingAgentDeploymentGuide.md", "hash": "0023dcc0a7f3acfcd47c4cd84855fb0917a221015d04a2dac39cf31954e91d56", "timestamp": "2025-06-02T16:15:25.144Z"}, {"type": "analyzed", "path": "docs/implementation/LivingAgentImplementationChecklist.md", "hash": "72b9a3b42c65cb58dc708e71619b6b3bc0317e7ca29a1c9072e87114adb2fb56", "timestamp": "2025-06-02T16:15:25.144Z"}, {"type": "analyzed", "path": "docs/interface-conflict-prevention-system.md", "hash": "172a59764b6bd57315d3abfe5b113e09a675ca29841cb37012386d683ebcbd2c", "timestamp": "2025-06-02T16:15:25.144Z"}, {"type": "analyzed", "path": "docs/optimization-analysis-results.md", "hash": "6b8330447d55b0a88aee01784bb3bcf19bc9a11ff777a5f22f9e7073111d8d1c", "timestamp": "2025-06-02T16:15:25.144Z"}, {"type": "analyzed", "path": "docs/organization/DOCUMENTATION_WORKFLOW.md", "hash": "ae2db5c6c8a9469c3668afaa92cffd49d3e986194b18652477ea597c2c2bab64", "timestamp": "2025-06-02T16:15:25.145Z"}, {"type": "analyzed", "path": "docs/organization/ORGANIZATION_ENHANCEMENT_SUMMARY.md", "hash": "5eada5d090256fae9d80281195372effcc13541fe649fdbd7be09b176e67079e", "timestamp": "2025-06-02T16:15:25.145Z"}, {"type": "analyzed", "path": "docs/organization/PROJECT_ORGANIZATION.md", "hash": "e8bc1ef198f5e0daa62ec4b40b8ef5d06e3562e1c76ad3a1f5b7bc9cc48e01e6", "timestamp": "2025-06-02T16:15:25.145Z"}, {"type": "analyzed", "path": "docs/organization/PROJECT_ORGANIZATION_ENHANCED.md", "hash": "6b41ef8b9296c95723032f1ff8ab053a743ded70aaf8b155574dcb3d6da04b83", "timestamp": "2025-06-02T16:15:25.145Z"}, {"type": "analyzed", "path": "docs/quantum-agents-bulletproof-implementation-guide.md", "hash": "7442a0f3785163587193169319219bb08b001726b4d0fde1464acb02590a7fb8", "timestamp": "2025-06-02T16:15:25.145Z"}, {"type": "analyzed", "path": "docs/quantum-agents-implementation-guide.md", "hash": "5a69b3f6de3e9f21e02f3fcc238e4c36f26aa3691b69f789e1a13dc187b28035", "timestamp": "2025-06-02T16:15:25.145Z"}, {"type": "analyzed", "path": "docs/security/README.md", "hash": "bb1080cb7a8d33476349f1810510d07dd3d034dbfabc8f42fcfe99ffe7a63dba", "timestamp": "2025-06-02T16:15:25.146Z"}, {"type": "analyzed", "path": "docs/reminders/blockchain-readiness-checkpoints.md", "hash": "2079e3a92756802b4c501a22b2c26d9949d6426d44e5d2d854c22db286df99bb", "timestamp": "2025-06-02T16:15:25.146Z"}, {"type": "analyzed", "path": "docs/security/SECURITY.md", "hash": "30ec85805944352fbc427cbef4ed38c14086c2116a6f72bce9635ced5d22d3aa", "timestamp": "2025-06-02T16:15:25.146Z"}, {"type": "analyzed", "path": "docs/security/SECURITY_CHECKLIST.md", "hash": "ec425948e4ff1395c5d79399bbf6788fc335dde0ff9ed44cd2540f6601367cc8", "timestamp": "2025-06-02T16:15:25.146Z"}, {"type": "analyzed", "path": "docs/security/SECURITY_STATUS.md", "hash": "0c91b0b5994b459670fcde0537546a9f0bae94ea8ef6bd697c2b59a2eed97d5b", "timestamp": "2025-06-02T16:15:25.146Z"}, {"type": "analyzed", "path": "docs/strategic-utilization/LivingAgentUtilizationPlan.md", "hash": "09c9aba8a31aa0883b7486567a290fbf05c57df667e8a58f27a45d6572f8c4b9", "timestamp": "2025-06-02T16:15:25.146Z"}, {"type": "analyzed", "path": "docs/strategic-utilization/LivingAgentUtilizationSummary.md", "hash": "bc3f881d7aa71a20bc9be76d8f4731c89fff77be2ab696c75d9ff39b1ef914b3", "timestamp": "2025-06-02T16:15:25.147Z"}, {"type": "analyzed", "path": "docs/technical-solutions/ai-timeout-fix-resolution.md", "hash": "f406eb6a1793a54ead4e7dd2e75747e73b2baca14748e77121e888725441c73f", "timestamp": "2025-06-02T16:15:25.147Z"}, {"type": "analyzed", "path": "docs/typescript-error-resolution-revolution.md", "hash": "f4ef29f2ea7e45bef1c8296aa61d7fbb1ccd0a9978a5f545120f2c8f1126e403", "timestamp": "2025-06-02T16:15:25.147Z"}, {"type": "analyzed", "path": "docs/typescript-revolution-quick-reference.md", "hash": "1aa2b5849092ca7cf938f54f1445c1090f7061f614471c79b46d17c30e9fe7c9", "timestamp": "2025-06-02T16:15:25.147Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/agent-ecosystem-analysis.json", "hash": "d214049ea41795c4b8df96df5d1487165ea8e952e53df66a83e727cad24415fd", "timestamp": "2025-06-02T16:15:25.147Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/ai-enhancement-test-report.json", "hash": "18fe96113ff55004d508904522e4b084c01cf1602e1b8461c2b438db9b53f62a", "timestamp": "2025-06-02T16:15:25.147Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/any-types-report.md", "hash": "5118b99b5c2004ffdf61e4f9cd0c201dcb985ec5bfdfed3f4dad4129cab1861f", "timestamp": "2025-06-02T16:15:25.147Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/design-system-update.json", "hash": "1092c197109d5f64811155405b65e9b06416e442ca7b6f33eb15ec79eec94dfc", "timestamp": "2025-06-02T16:15:25.147Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/hook-dependency-report.md", "hash": "00f4f3a15e17961fbd524d7c89d58bda25fd9186996ecd7adb2e979f12237e94", "timestamp": "2025-06-02T16:15:25.147Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/img-to-image-report.md", "hash": "513c48117e23bd86dc80a33cae6d71051f03bbdf0957ff49c6b19dce1487c498", "timestamp": "2025-06-02T16:15:25.148Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/optimization-report.json", "hash": "d22a2ebf4eedcbd71849b0112824273667bdb949c626ddf10a19a2bae64b56d0", "timestamp": "2025-06-02T16:15:25.148Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/performance-report.json", "hash": "2aa525797f11ff97db8abf422f83fa7ee04b37492c7a4ac7a3e0be3ca4701743", "timestamp": "2025-06-02T16:15:25.148Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/simple-ai-test.js", "hash": "a8306cf2f4726fd9bd7dba596034fba9b7dd81778f5814bf25e4f85c821f8dc8", "timestamp": "2025-06-02T16:15:25.148Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/test-intelligent-resource-manager.mjs", "hash": "bff2ba79688ab6d8816df0c0425dbc3ea8b40aa48dbad4e424c5c081132c587a", "timestamp": "2025-06-02T16:15:25.148Z"}, {"type": "analyzed", "path": "docs/📊-reports/analysis/r1-collaborative-analysis-package.md", "hash": "83f072f8fee0b165eabaa0fa4d8c6b76d239463b784deaad16a40ca0f490eedc", "timestamp": "2025-06-02T16:15:25.149Z"}, {"type": "analyzed", "path": "docs/📊-reports/performance/design-consistency-report.json", "hash": "964523298f1b477c4b4e5a5f619af07c9b3fa0f9d834d538d1949e7eeb81dc5a", "timestamp": "2025-06-02T16:15:25.149Z"}, {"type": "analyzed", "path": "docs/📊-reports/performance/optimization-report.json", "hash": "aa87c3e9637bc259ed88107891df9ac8f62e2d774273522375c2c65039f7e8d6", "timestamp": "2025-06-02T16:15:25.149Z"}, {"type": "analyzed", "path": "docs/📊-reports/performance/design-system-update.json", "hash": "4ada60cdab6e41b1ef7bb34dabb43c9b8d929de8837e36a21bb8bbdc49e998f5", "timestamp": "2025-06-02T16:15:25.149Z"}, {"type": "analyzed", "path": "docs/📊-reports/performance/performance-report.json", "hash": "1ce4745494848f652df40e8c0466bc08adeb81aaee92021c22b37bc8be582922", "timestamp": "2025-06-02T16:15:25.150Z"}, {"type": "analyzed", "path": "docs/📊-reports/performance/security-report.json", "hash": "ec2db362e3fec4aacafea0fb457aeb587ccc3185f47080d055ea45738cc906fa", "timestamp": "2025-06-02T16:15:25.150Z"}, {"type": "analyzed", "path": "docs/📊-reports/status/AGENT_COMMUNICATION_STATUS.md", "hash": "bda4699f9c0cf0439f0a936e1a70103e0440c752b79f716327d4d14401979354", "timestamp": "2025-06-02T16:15:25.150Z"}, {"type": "analyzed", "path": "docs/📊-reports/status/STRATEGIC_IMPLEMENTATION_STATUS_REPORT.md", "hash": "4dc1d644f8ad9bc6fca8572e8d446a4071625867f6977f65b1f8183395a8570c", "timestamp": "2025-06-02T16:15:25.150Z"}, {"type": "analyzed", "path": "docs/📊-reports/status/STRATEGIC_REAL_IMPLEMENTATION_APPROACH.md", "hash": "816d336e8de02f51d9cac9f85c3b9e5560c6332ad8a4e50451c768c8bfcdc03e", "timestamp": "2025-06-02T16:15:25.150Z"}, {"type": "analyzed", "path": "docs/📊-reports/status/implementation-monitor.md", "hash": "38031701cc15d2322eb27e282050dfe7bb55ad1817bf5fd104b3ea66cb025290", "timestamp": "2025-06-02T16:15:25.150Z"}, {"type": "analyzed", "path": "docs/📋-architecture/critical-mcp-impact-analysis.md", "hash": "a65ef62d201af0e5053b6a5408f9dd87ea6d8c75690701eea081b62ee9656b03", "timestamp": "2025-06-02T16:15:25.151Z"}, {"type": "analyzed", "path": "docs/📋-architecture/consciousness-evolution-roadmap.md", "hash": "834537045a5e8fe339bb5b0c79651b52c32c36ed2ae6453738e129a3fb4f489f", "timestamp": "2025-06-02T16:15:25.151Z"}, {"type": "analyzed", "path": "docs/📋-architecture/consciousness-research-analysis.md", "hash": "fce24524fe6436a6ad7b50c7b26dcb96eb093026d9b1e1c4858bd44497f0baac", "timestamp": "2025-06-02T16:15:25.151Z"}, {"type": "analyzed", "path": "docs/📋-architecture/implementation-helpers.md", "hash": "ccf3020d28be2ba7c9083aefc3490a5d01d3b369ac16b8c72a9f0e4021d2021f", "timestamp": "2025-06-02T16:15:25.151Z"}, {"type": "analyzed", "path": "docs/📋-architecture/intelligent-agent-workflow-analysis.md", "hash": "d986235b2daf336ca15c321418512c29fd6592fa4c4dae2c70770c5534712816", "timestamp": "2025-06-02T16:15:25.151Z"}, {"type": "analyzed", "path": "docs/📋-architecture/master-architecture-plan.md", "hash": "ea54445a84a255add5c0fdb5ed7ea5feb965e9f35221ecdaa1f97002a1915d7e", "timestamp": "2025-06-02T16:15:25.151Z"}, {"type": "analyzed", "path": "docs/📋-architecture/master-implementation-strategy.md", "hash": "bcb5b9bc5c1d55b316c2561846051a12ec3e57788f0ee350c96b51b6d2d482f2", "timestamp": "2025-06-02T16:15:25.152Z"}, {"type": "analyzed", "path": "docs/📋-architecture/mcp-implementation-complete.md", "hash": "e24dfc717aff50bedaf45f600be198d716386d51e547f2e9608e694ca4ab4455", "timestamp": "2025-06-02T16:15:25.152Z"}, {"type": "analyzed", "path": "docs/📋-architecture/mcp-integration-implementation-guide.md", "hash": "c22428ab58afecf7212496c6e82d494b4a6e1bb5fa7f7d5efc82cc936ee9bd1d", "timestamp": "2025-06-02T16:15:25.152Z"}, {"type": "analyzed", "path": "docs/📋-architecture/phase1-implementation-guide.md", "hash": "92a3da3afac97de6f550dd4adf85260abd66405ce8cb25c6d43cd6b735ca69c4", "timestamp": "2025-06-02T16:15:25.152Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/AgentResponsibilityMatrices.md", "hash": "4ccd5663d10b76f918968c425ce390a610692fe24b0c49111737f0a3f4fae141", "timestamp": "2025-06-02T16:15:25.152Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/ConflictPreventionMechanisms.md", "hash": "08d498c1be359d91501e38b1ec3297d137bcb89842beed89de515b91a993a3ab", "timestamp": "2025-06-02T16:15:25.152Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/DevAgent-Interface.md", "hash": "8232a603d0dad9918c4ff00e243ae57d5a801f7ddb98f116471e1a6758339b77", "timestamp": "2025-06-02T16:15:25.153Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/ExtendedAgentConflictValidation.md", "hash": "a2546ff649e38b91cf6ecd0306e6f0d02c347e954b219afd06293321b8306f91", "timestamp": "2025-06-02T16:15:25.153Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/OpsAgent-Interface.md", "hash": "c84dd75f34ac87ab6374effd226bd57b3dd98272a1ac70f0ee944162d0940504", "timestamp": "2025-06-02T16:15:25.153Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/SecurityAgent-Interface.md", "hash": "add4b7ab27c8d08bd705fbcca5c3bf6a89f8e2e24422d8a9169b5d418b8a9e4d", "timestamp": "2025-06-02T16:15:25.153Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/RoleConflictAnalysis.md", "hash": "fce30b18472e215a89d02b7b78901bee543ceb9cc71c76f9a6b68dded01fc53e", "timestamp": "2025-06-02T16:15:25.153Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/TestAgent-Interface.md", "hash": "edeacf59d7c9a879ce32f614af47130f9d9fe349f6ad96d992964705a18fa434", "timestamp": "2025-06-02T16:15:25.153Z"}, {"type": "analyzed", "path": "docs/📋-guides/agent-interfaces/UIAgent-Interface.md", "hash": "e7d4a513ea5d3cbb4d799e02d2d21132f39fb1ed6a7a718d581e12b070f29452", "timestamp": "2025-06-02T16:15:25.154Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/AGENT_TASKS.md", "hash": "937df64486a5cd112f05f30f2efe65e47b4406b5a2c143fa81182bc24e16cc6d", "timestamp": "2025-06-02T16:15:25.154Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/AGENT_WORKFLOW_INTEGRATION.md", "hash": "b980a20efbae0d5dd697ad0a3592ec730e0c9de0e2a723291a4f846230476102", "timestamp": "2025-06-02T16:15:25.154Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/CONTRIBUTING.md", "hash": "a2e4ae298bcd7c2b82bf9e9e7ed6c7ac86dd9babb1a66ac112e96e904505bd50", "timestamp": "2025-06-02T16:15:25.154Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/AUTOMATIC_FILE_ORGANIZATION.md", "hash": "8326acaf4e058ff47f20f29b533729fda2f22b2b135b5908336cdfe169c38434", "timestamp": "2025-06-02T16:15:25.154Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/CURSOR_DAILY_WORKFLOW.md", "hash": "24dfc89018e61b5cb9c8298593156ccc44d7ff60dbfc684a7a80f6e96c301dc2", "timestamp": "2025-06-02T16:15:25.154Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/CreAItive_All_Combined.md", "hash": "4d0da6a34606764c872ab3dd3f550e58c49953041441d2f953ca6b1ce6bae73f", "timestamp": "2025-06-02T16:15:25.155Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/CreAItive_Agent_Build_Guide.md", "hash": "cd338fc51c3a0abb88c06e30c2cbca670c8763c7faa8661eadb1e534b7e4628d", "timestamp": "2025-06-02T16:15:25.155Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/FRONTEND_TASK_MANAGER.md", "hash": "4d181d46c80a71ade80c4d43665f4e533b11832f01d93de3d1fe5d8dcf7d0d36", "timestamp": "2025-06-02T16:15:25.155Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/comprehensive-ai-test.js", "hash": "a554ea9ba7ce495d0c30fa342063168696780d02fe993701c2727cae8f986d41", "timestamp": "2025-06-02T16:15:25.155Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/agent-orchestration-roadmap.md", "hash": "3ea425225243aea947303b3ffc89220a27925ae8ccdcae8b767fc0cab7d6f157", "timestamp": "2025-06-02T16:15:25.155Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/ai-collaboration-protocol.md", "hash": "050ceb855b3f94a670031ed87d814d866a9c9fa7d27876ed934abacac089a0ba", "timestamp": "2025-06-02T16:15:25.155Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/comprehensive-success-test.js", "hash": "e05653c94db1222af466f2c1905729b369a178b8774253d9b9c6e9a75e82c7fa", "timestamp": "2025-06-02T16:15:25.155Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/force-agents-use-real-ai.js", "hash": "e50fe23d9a108b50382bfeabff261817e07eb491ea1051ad5810702a33239d3b", "timestamp": "2025-06-02T16:15:25.155Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/development-workflow-strategy.md", "hash": "8c68acebd30854b0799e57e08fc93ada44c9ead940151ee51fcc81f826f051c3", "timestamp": "2025-06-02T16:15:25.155Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/monitor-ai-progress.js", "hash": "b5c430d6af29f758bad873493c42390b1a9bc276d380c126fc90b17688614575", "timestamp": "2025-06-02T16:15:25.156Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/model-optimization-results.md", "hash": "9790a6ea14645fe9e723f9256c8463bc32469d428a70b399663690eccfe27f49", "timestamp": "2025-06-02T16:15:25.156Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/model-comparison-results.md", "hash": "7400b880c804def657ffb04ca72b17e7f4e81a698a7dfb5802ab7ef608b22ff1", "timestamp": "2025-06-02T16:15:25.156Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/optimized-ai-test.js", "hash": "1a5788b0ef5f6cc64f7fa43b2373e1081236cd708885266fe07f59f995ff8f08", "timestamp": "2025-06-02T16:15:25.156Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/quick-test.js", "hash": "e77123097d25cbfcc91e3e18508c09933eadcd3092128020457db8ab1532755a", "timestamp": "2025-06-02T16:15:25.156Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/r1-conflict-resolution-summary.md", "hash": "d7fac6b14c064b7b23c9c38decbc9247ef814986a00df58210b5c9281103132a", "timestamp": "2025-06-02T16:15:25.156Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-all-real-agents.js", "hash": "414199689dc8f72cae399a00500d7f6d8afb8b8595c07517e99057964d488fb4", "timestamp": "2025-06-02T16:15:25.157Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/r1-direct-command.md", "hash": "c7550c552d466381e2ba10b4441ce5dc7bb4e9b964e1ee6ebe5832d0c0cc9339", "timestamp": "2025-06-02T16:15:25.157Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/tasks.md", "hash": "22d6c8db210ea9140506ea6466b5e7c7a659f0d3fe9359098bcaa6e9866ec1f6", "timestamp": "2025-06-02T16:15:25.157Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-living-agent-intelligence.js", "hash": "b88768c9059f39d0b72a7426e13a8cbd7467be64982b7b52254749e2331bea69", "timestamp": "2025-06-02T16:15:25.157Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-direct-r1-ai.js", "hash": "bcabfdebbdb40707038e9adb7a1d0a7466fb6e94bd55287416551b8319bdce79", "timestamp": "2025-06-02T16:15:25.157Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-r1-ai-fix.js", "hash": "430258ba358628c5c82c74e374d64cf41e2b2a32828bf24afae460fdc9870d62", "timestamp": "2025-06-02T16:15:25.157Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-real-ai-agents.js", "hash": "0c983171ab490c7b67da74b2f22dcf2521fc7c9ab9ae2896e57d06aa7aa807f5", "timestamp": "2025-06-02T16:15:25.157Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-real-test-agent.js", "hash": "47b2203f2aec599f7a07b2cbe2bf734b1de98d073b91d57d4457b6253c68d9ca", "timestamp": "2025-06-02T16:15:25.157Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-real-ai-processing.js", "hash": "7627cba03811ca0d5dcc046b98734d25c80e98a6bf65519b00f80c151e69233c", "timestamp": "2025-06-02T16:15:25.157Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/test-timeout-fix.js", "hash": "858da337733df110a13faf13ce44188db57c687deefaecb9eb39aa56659dbc5b", "timestamp": "2025-06-02T16:15:25.158Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/trigger-real-ai.js", "hash": "1c6870917ef478a9349e1063415e4bbe49178ce644dd718e6cfab73a31b97264", "timestamp": "2025-06-02T16:15:25.158Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/two-agent-ai-workflow.md", "hash": "4c3836f936ecfe4784dabe599d70fba0e527f6ed2408e34ffeafccfa5d79d40b", "timestamp": "2025-06-02T16:15:25.158Z"}, {"type": "analyzed", "path": "docs/📋-guides/development/workflow-optimization-analysis.md", "hash": "d4de57a63fb0aa0954671d20ee6aeb28f8853f498b1bf90e4e54f6a1a6ecf461", "timestamp": "2025-06-02T16:15:25.158Z"}, {"type": "analyzed", "path": "docs/📝-technical/quantum-core-implementation.md", "hash": "c08865007489b3cacc421e34866908b7540fff5fe1a1dc68ccea5c3d37bf2f75", "timestamp": "2025-06-02T16:15:25.158Z"}, {"type": "analyzed", "path": "docs/📋-guides/quantum-quick-start-guide.md", "hash": "b7d49f37743777f3912033ccbf688d9d871228a981deff87d6e80b1494f660bb", "timestamp": "2025-06-02T16:15:25.158Z"}, {"type": "analyzed", "path": "docs/📝-technical/specifications/HYBRID_INTELLIGENCE_STRATEGY.md", "hash": "08b0c3da3b20e3975be16da94001be088d253d2fbbe968885a3010014b936297", "timestamp": "2025-06-02T16:15:25.159Z"}, {"type": "analyzed", "path": "docs/📝-technical/quantum-integration-examples.md", "hash": "4375aefea20948461478e80a64755f47cd9dc098d0a2fa183b5f399a89a77efe", "timestamp": "2025-06-02T16:15:25.159Z"}, {"type": "analyzed", "path": "docs/📝-technical/specifications/AGENT_INTELLIGENCE_DEVELOPMENT_GUIDE.md", "hash": "751dd883125ed18a1a516d28a00de5cb4b55d2278f9fe194f388b37f08ca01df", "timestamp": "2025-06-02T16:15:25.159Z"}, {"type": "analyzed", "path": "docs/📝-technical/specifications/creative-canvas.md", "hash": "50fb53f844221e14cd6c9f5e3ee50050de34d94a6751c33f9e12d93f07a0af33", "timestamp": "2025-06-02T16:15:25.159Z"}, {"type": "analyzed", "path": "docs/🔍-logs/analysis/approved-recommendations.json", "hash": "bba7778177cb353c5abf12461405b9d06e76661496a4833f601b77b1909900b8", "timestamp": "2025-06-02T16:15:25.160Z"}, {"type": "analyzed", "path": "docs/📝-technical/specifications/mvp-specification.md", "hash": "c6116ff24af3ffb7885dd5e67d8adcbbf7ab7d2a15799c10e7326d5bbc26587e", "timestamp": "2025-06-02T16:15:25.160Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build-analysis.log", "hash": "16d1768cc52294e17a9ec2544276e62ed98cc6ef8a4b645107183f4e2bc07b88", "timestamp": "2025-06-02T16:15:25.160Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build-optimized.log", "hash": "db8bc8afd219b7de451f745d3280c9f0b34c3b5b3076795fd49986e2092c63d3", "timestamp": "2025-06-02T16:15:25.160Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build-phase6-final.log", "hash": "f99c4750448e2e56adb65d0f94b872c99f4100fdd4d95edd1de395b1dc92667f", "timestamp": "2025-06-02T16:15:25.160Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build-phase6-optimized.log", "hash": "4c7023b6fb2ec505ac14a196e685b1501193364cc7cce7d52ac7feaa0172dd7a", "timestamp": "2025-06-02T16:15:25.160Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build-task6.2-complete.log", "hash": "eab7b5b61dcc85510d552991e75d7d5cc337e88986d1d00e4b388821d20e29f7", "timestamp": "2025-06-02T16:15:25.161Z"}, {"type": "analyzed", "path": "docs/🔍-logs/build.log", "hash": "073d21e3897f69f889e0363f65190d76812f4252c219f8cb2b334d6e4eba7f23", "timestamp": "2025-06-02T16:15:25.161Z"}, {"type": "analyzed", "path": "docs/🔧-utilities/scripts/agent-fix-verification.js", "hash": "23c663822b0cd25cdd06c3f76a4c53240cb2e581f77b92d06001f001c18a2a25", "timestamp": "2025-06-02T16:15:25.161Z"}, {"type": "analyzed", "path": "docs/🔧-utilities/scripts/check-status.js", "hash": "426976795d1ca8ea1ff28d65e972fab12d0d485a39f7b79471b9bbebdfd04686", "timestamp": "2025-06-02T16:15:25.161Z"}, {"type": "analyzed", "path": "docs/🔍-logs/dev-server.log", "hash": "4e0f982bcc401ff5bb07d2bc9cca4e417a20010ede9c121027384529b70524b8", "timestamp": "2025-06-02T16:15:25.174Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/CANVAS_INTUITIVE_INNOVATIVE_UPGRADE.md", "hash": "f62a10c9a2bb504dbaf56c7e2efc237a9c350033403ab7687769df149b416699", "timestamp": "2025-06-02T16:15:25.174Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/FINAL_CANVAS_TOOL_TEST.md", "hash": "ca290a6d14fd993e3e0d7cd167f7a911a2b8020519668b81d882915d1fc89dc3", "timestamp": "2025-06-02T16:15:25.174Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/CANVAS_TEST_REPORT.md", "hash": "0e43575b2809e7b5c0bbb652407230dbbd4ecdc27a563dc51684e24baf9cd0a7", "timestamp": "2025-06-02T16:15:25.174Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/test-canvas-functions.js", "hash": "d7ab61ca9c59d693bfcedff39cfe01ab56cab878260770ae8a65dc0694a7de56", "timestamp": "2025-06-02T16:15:25.175Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/test-canvas-interactions.html", "hash": "0c01176689830c21554d044f77e099fa2c31de6caf84e7dd0dda61626b0a458b", "timestamp": "2025-06-02T16:15:25.175Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/canvas-test-results.json", "hash": "cb51adb7041904573f5d82f7355132da4e8a2071b8aa4ec48b80bd30a36329af", "timestamp": "2025-06-02T16:15:25.175Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/test-demo.js", "hash": "ebd64057faf01f340b63c22b0a93fa7db01f685e49ac9e7110bf09d7259fb72d", "timestamp": "2025-06-02T16:15:25.175Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/tsconfig.production.json", "hash": "bb72656822cd7cb469b2ba6070d0dd9c580c677d8fca082f79d83214b890e588", "timestamp": "2025-06-02T16:15:25.176Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/test-results.json", "hash": "4a5edf490cdb491e8e7271f5d175db685c20bc54b5467990c39e5d0bf719f1c0", "timestamp": "2025-06-02T16:15:25.178Z"}, {"type": "analyzed", "path": "docs/🧪-testing/results/tsconfig.test.json", "hash": "80b1d331c29fe435071d628afaf858b45bd6df46cb9028a5f9c407dbc9a55870", "timestamp": "2025-06-02T16:15:25.178Z"}, {"type": "moved", "source": "docs/AI_ENHANCEMENT_TESTING_GUIDE.md", "target": "docs/🧪-testing/results/AI_ENHANCEMENT_TESTING_GUIDE.md", "category": "testing", "confidence": 0.8, "sourceHash": "91bd83861199e18b1e5ec65182296113845549e6f24d74c4f3b53cd8522a0024", "targetHash": "91bd83861199e18b1e5ec65182296113845549e6f24d74c4f3b53cd8522a0024", "timestamp": "2025-06-02T16:15:25.181Z"}, {"type": "moved", "source": "docs/automated-visual-regression-testing.md", "target": "docs/🧪-testing/results/automated-visual-regression-testing.md", "category": "testing", "confidence": 0.8, "sourceHash": "c13527ce04957faaa1ced532b3c1de1c549d2b1d88e55f6ed097c67f414b926a", "targetHash": "c13527ce04957faaa1ced532b3c1de1c549d2b1d88e55f6ed097c67f414b926a", "timestamp": "2025-06-02T16:15:25.181Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/TestAgent-Development-Session-5.md", "target": "docs/🧪-testing/results/TestAgent-Development-Session-5.md", "category": "testing", "confidence": 0.8, "sourceHash": "0507f52cb56b8f282fc8ae30f3f7ba3f1eb82ca0d3b5ee858e38a93f67c1d512", "targetHash": "0507f52cb56b8f282fc8ae30f3f7ba3f1eb82ca0d3b5ee858e38a93f67c1d512", "timestamp": "2025-06-02T16:15:25.181Z"}, {"type": "moved", "source": "docs/optimization-analysis-results.md", "target": "docs/🧪-testing/results/optimization-analysis-results.md", "category": "testing", "confidence": 0.8, "sourceHash": "6b8330447d55b0a88aee01784bb3bcf19bc9a11ff777a5f22f9e7073111d8d1c", "targetHash": "6b8330447d55b0a88aee01784bb3bcf19bc9a11ff777a5f22f9e7073111d8d1c", "timestamp": "2025-06-02T16:15:25.181Z"}, {"type": "moved", "source": "docs/typescript-error-resolution-revolution.md", "target": "docs/🧪-testing/results/typescript-error-resolution-revolution.md", "category": "testing", "confidence": 0.8, "sourceHash": "f4ef29f2ea7e45bef1c8296aa61d7fbb1ccd0a9978a5f545120f2c8f1126e403", "targetHash": "f4ef29f2ea7e45bef1c8296aa61d7fbb1ccd0a9978a5f545120f2c8f1126e403", "timestamp": "2025-06-02T16:15:25.182Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/ai-enhancement-test-report.json", "target": "docs/🧪-testing/results/ai-enhancement-test-report.json", "category": "testing", "confidence": 0.8, "sourceHash": "18fe96113ff55004d508904522e4b084c01cf1602e1b8461c2b438db9b53f62a", "targetHash": "18fe96113ff55004d508904522e4b084c01cf1602e1b8461c2b438db9b53f62a", "timestamp": "2025-06-02T16:15:25.182Z"}, {"type": "moved", "source": "docs/📊-reports/status/STRATEGIC_REAL_IMPLEMENTATION_APPROACH.md", "target": "docs/🧪-testing/results/STRATEGIC_REAL_IMPLEMENTATION_APPROACH.md", "category": "testing", "confidence": 0.8, "sourceHash": "816d336e8de02f51d9cac9f85c3b9e5560c6332ad8a4e50451c768c8bfcdc03e", "targetHash": "816d336e8de02f51d9cac9f85c3b9e5560c6332ad8a4e50451c768c8bfcdc03e", "timestamp": "2025-06-02T16:15:25.182Z"}, {"type": "moved", "source": "docs/📋-guides/agent-interfaces/TestAgent-Interface.md", "target": "docs/🧪-testing/results/TestAgent-Interface.md", "category": "testing", "confidence": 0.8, "sourceHash": "edeacf59d7c9a879ce32f614af47130f9d9fe349f6ad96d992964705a18fa434", "targetHash": "edeacf59d7c9a879ce32f614af47130f9d9fe349f6ad96d992964705a18fa434", "timestamp": "2025-06-02T16:15:25.182Z"}, {"type": "moved", "source": "docs/📋-guides/development/CreAItive_All_Combined.md", "target": "docs/🧪-testing/results/CreAItive_All_Combined.md", "category": "testing", "confidence": 0.8, "sourceHash": "4d0da6a34606764c872ab3dd3f550e58c49953041441d2f953ca6b1ce6bae73f", "targetHash": "4d0da6a34606764c872ab3dd3f550e58c49953041441d2f953ca6b1ce6bae73f", "timestamp": "2025-06-02T16:15:25.182Z"}, {"type": "moved", "source": "docs/📋-guides/development/model-optimization-results.md", "target": "docs/🧪-testing/results/model-optimization-results.md", "category": "testing", "confidence": 0.8, "sourceHash": "9790a6ea14645fe9e723f9256c8463bc32469d428a70b399663690eccfe27f49", "targetHash": "9790a6ea14645fe9e723f9256c8463bc32469d428a70b399663690eccfe27f49", "timestamp": "2025-06-02T16:15:25.183Z"}, {"type": "moved", "source": "docs/📝-technical/specifications/HYBRID_INTELLIGENCE_STRATEGY.md", "target": "docs/🧪-testing/results/HYBRID_INTELLIGENCE_STRATEGY.md", "category": "testing", "confidence": 0.8, "sourceHash": "08b0c3da3b20e3975be16da94001be088d253d2fbbe968885a3010014b936297", "targetHash": "08b0c3da3b20e3975be16da94001be088d253d2fbbe968885a3010014b936297", "timestamp": "2025-06-02T16:15:25.183Z"}, {"type": "moved", "source": "docs/📝-technical/specifications/mvp-specification.md", "target": "docs/🧪-testing/results/mvp-specification.md", "category": "testing", "confidence": 0.8, "sourceHash": "c6116ff24af3ffb7885dd5e67d8adcbbf7ab7d2a15799c10e7326d5bbc26587e", "targetHash": "c6116ff24af3ffb7885dd5e67d8adcbbf7ab7d2a15799c10e7326d5bbc26587e", "timestamp": "2025-06-02T16:15:25.184Z"}, {"type": "moved", "source": "docs/🧪-testing/results/CANVAS_TEST_REPORT.md", "target": "docs/🧪-testing/results/CANVAS_TEST_REPORT.md", "category": "testing", "confidence": 0.8, "sourceHash": "0e43575b2809e7b5c0bbb652407230dbbd4ecdc27a563dc51684e24baf9cd0a7", "targetHash": "0e43575b2809e7b5c0bbb652407230dbbd4ecdc27a563dc51684e24baf9cd0a7", "timestamp": "2025-06-02T16:15:25.184Z"}, {"type": "moved", "source": "docs/🧪-testing/results/canvas-test-results.json", "target": "docs/🧪-testing/results/canvas-test-results.json", "category": "testing", "confidence": 0.8, "sourceHash": "cb51adb7041904573f5d82f7355132da4e8a2071b8aa4ec48b80bd30a36329af", "targetHash": "cb51adb7041904573f5d82f7355132da4e8a2071b8aa4ec48b80bd30a36329af", "timestamp": "2025-06-02T16:15:25.184Z"}, {"type": "moved", "source": "docs/🧪-testing/results/test-results.json", "target": "docs/🧪-testing/results/test-results.json", "category": "testing", "confidence": 0.8, "sourceHash": "4a5edf490cdb491e8e7271f5d175db685c20bc54b5467990c39e5d0bf719f1c0", "targetHash": "4a5edf490cdb491e8e7271f5d175db685c20bc54b5467990c39e5d0bf719f1c0", "timestamp": "2025-06-02T16:15:25.186Z"}, {"type": "moved", "source": "docs/🧪-testing/results/tsconfig.test.json", "target": "docs/🧪-testing/results/tsconfig.test.json", "category": "testing", "confidence": 0.8, "sourceHash": "80b1d331c29fe435071d628afaf858b45bd6df46cb9028a5f9c407dbc9a55870", "targetHash": "80b1d331c29fe435071d628afaf858b45bd6df46cb9028a5f9c407dbc9a55870", "timestamp": "2025-06-02T16:15:25.187Z"}, {"type": "moved", "source": "docs/CURSOR_MEMORY_BANK_BACKUP.md", "target": "docs/📊-reports/analysis/CURSOR_MEMORY_BANK_BACKUP.md", "category": "reports", "confidence": 0.75, "sourceHash": "ceb957568bbc433d91dba684354abc540e5ce9af2363e3435c38dba3b8b795b4", "targetHash": "ceb957568bbc433d91dba684354abc540e5ce9af2363e3435c38dba3b8b795b4", "timestamp": "2025-06-02T16:15:25.187Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/MLCoordinationLayer-Development-Session-6.md", "target": "docs/📊-reports/analysis/MLCoordinationLayer-Development-Session-6.md", "category": "reports", "confidence": 0.75, "sourceHash": "5baa0237b7cc55f17466c0de30610f2c81de214f043df92a9c9fce285b6531ea", "targetHash": "5baa0237b7cc55f17466c0de30610f2c81de214f043df92a9c9fce285b6531ea", "timestamp": "2025-06-02T16:15:25.187Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/ConfigAgent-Development-Session-7.md", "target": "docs/📊-reports/analysis/ConfigAgent-Development-Session-7.md", "category": "reports", "confidence": 0.75, "sourceHash": "232b6ad3491d87cd99f7a21ba549ff79509e10d4c6d9d7b9b24f6084470c42a3", "targetHash": "232b6ad3491d87cd99f7a21ba549ff79509e10d4c6d9d7b9b24f6084470c42a3", "timestamp": "2025-06-02T16:15:25.187Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/AutonomousIntelligenceAgent-Development-Session-12.md", "target": "docs/📊-reports/analysis/AutonomousIntelligenceAgent-Development-Session-12.md", "category": "reports", "confidence": 0.75, "sourceHash": "baf55d87b734de8fb26d5fdc84d7ad56442ee2d8df4b8207f17620d66699b051", "targetHash": "baf55d87b734de8fb26d5fdc84d7ad56442ee2d8df4b8207f17620d66699b051", "timestamp": "2025-06-02T16:15:25.187Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/OpsAgent-Development-Session-4.md", "target": "docs/📊-reports/analysis/OpsAgent-Development-Session-4.md", "category": "reports", "confidence": 0.75, "sourceHash": "6ec8563bdd162772aadb4c5373f24d643720b7deefd46d3d972890435224bcd5", "targetHash": "6ec8563bdd162772aadb4c5373f24d643720b7deefd46d3d972890435224bcd5", "timestamp": "2025-06-02T16:15:25.187Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/UIAgent-Development-Session-2.md", "target": "docs/📊-reports/analysis/UIAgent-Development-Session-2.md", "category": "reports", "confidence": 0.75, "sourceHash": "e5ef2db8bdcd69a1d2282b13147a9204f686212c77119676995cdc5fc9c69251", "targetHash": "e5ef2db8bdcd69a1d2282b13147a9204f686212c77119676995cdc5fc9c69251", "timestamp": "2025-06-02T16:15:25.188Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/SecurityAgent-Development-Session-11.md", "target": "docs/📊-reports/analysis/SecurityAgent-Development-Session-11.md", "category": "reports", "confidence": 0.75, "sourceHash": "201a643fde0e07788e5ad59201a0f023e9fd9cc2a688d3b34d1de80e67bf2946", "targetHash": "201a643fde0e07788e5ad59201a0f023e9fd9cc2a688d3b34d1de80e67bf2946", "timestamp": "2025-06-02T16:15:25.188Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/VectorMemory-Development-Session-9.md", "target": "docs/📊-reports/analysis/VectorMemory-Development-Session-9.md", "category": "reports", "confidence": 0.75, "sourceHash": "d614ad6fed906b5c5d58731af60d27639a8bcd12105e4f8e7332aea4245f7e27", "targetHash": "d614ad6fed906b5c5d58731af60d27639a8bcd12105e4f8e7332aea4245f7e27", "timestamp": "2025-06-02T16:15:25.188Z"}, {"type": "moved", "source": "docs/completed-features/AdvancedDrawingToolsCompletion.md", "target": "docs/📊-reports/analysis/AdvancedDrawingToolsCompletion.md", "category": "reports", "confidence": 0.75, "sourceHash": "0ac1c9ef04de09fb240b8d7f45dde93fed23013ba05c793027371cab816ca7be", "targetHash": "0ac1c9ef04de09fb240b8d7f45dde93fed23013ba05c793027371cab816ca7be", "timestamp": "2025-06-02T16:15:25.189Z"}, {"type": "moved", "source": "docs/comprehensive-docs-organization.md", "target": "docs/📊-reports/analysis/comprehensive-docs-organization.md", "category": "reports", "confidence": 0.75, "sourceHash": "d989c6f65939f86c20d99040d492b873e62902287328e9fe015e956ea6ea00f2", "targetHash": "d989c6f65939f86c20d99040d492b873e62902287328e9fe015e956ea6ea00f2", "timestamp": "2025-06-02T16:15:25.189Z"}, {"type": "moved", "source": "docs/completed-features/EnhancedAIIntegrationCompletion.md", "target": "docs/📊-reports/analysis/EnhancedAIIntegrationCompletion.md", "category": "reports", "confidence": 0.75, "sourceHash": "64a38bc34e353383fb744a42c4c0b17c777151bd2da3b726442e87f5fd7ffc19", "targetHash": "64a38bc34e353383fb744a42c4c0b17c777151bd2da3b726442e87f5fd7ffc19", "timestamp": "2025-06-02T16:15:25.189Z"}, {"type": "moved", "source": "docs/security/SECURITY_CHECKLIST.md", "target": "docs/📊-reports/analysis/SECURITY_CHECKLIST.md", "category": "reports", "confidence": 0.75, "sourceHash": "ec425948e4ff1395c5d79399bbf6788fc335dde0ff9ed44cd2540f6601367cc8", "targetHash": "ec425948e4ff1395c5d79399bbf6788fc335dde0ff9ed44cd2540f6601367cc8", "timestamp": "2025-06-02T16:15:25.190Z"}, {"type": "moved", "source": "docs/implementation/LivingAgentDeploymentGuide.md", "target": "docs/📊-reports/analysis/LivingAgentDeploymentGuide.md", "category": "reports", "confidence": 0.75, "sourceHash": "0023dcc0a7f3acfcd47c4cd84855fb0917a221015d04a2dac39cf31954e91d56", "targetHash": "0023dcc0a7f3acfcd47c4cd84855fb0917a221015d04a2dac39cf31954e91d56", "timestamp": "2025-06-02T16:15:25.190Z"}, {"type": "moved", "source": "docs/file-organization-enhancement.md", "target": "docs/📊-reports/analysis/file-organization-enhancement.md", "category": "reports", "confidence": 0.75, "sourceHash": "3c036e6d1d6d63d7decf3da921651142ea14c5aef549be66baeffce1939f6cac", "targetHash": "3c036e6d1d6d63d7decf3da921651142ea14c5aef549be66baeffce1939f6cac", "timestamp": "2025-06-02T16:15:25.190Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/agent-ecosystem-analysis.json", "target": "docs/📊-reports/analysis/agent-ecosystem-analysis.json", "category": "reports", "confidence": 0.75, "sourceHash": "d214049ea41795c4b8df96df5d1487165ea8e952e53df66a83e727cad24415fd", "targetHash": "d214049ea41795c4b8df96df5d1487165ea8e952e53df66a83e727cad24415fd", "timestamp": "2025-06-02T16:15:25.190Z"}, {"type": "moved", "source": "docs/security/SECURITY_STATUS.md", "target": "docs/📊-reports/analysis/SECURITY_STATUS.md", "category": "reports", "confidence": 0.75, "sourceHash": "0c91b0b5994b459670fcde0537546a9f0bae94ea8ef6bd697c2b59a2eed97d5b", "targetHash": "0c91b0b5994b459670fcde0537546a9f0bae94ea8ef6bd697c2b59a2eed97d5b", "timestamp": "2025-06-02T16:15:25.190Z"}, {"type": "moved", "source": "docs/typescript-revolution-quick-reference.md", "target": "docs/📊-reports/analysis/typescript-revolution-quick-reference.md", "category": "reports", "confidence": 0.75, "sourceHash": "1aa2b5849092ca7cf938f54f1445c1090f7061f614471c79b46d17c30e9fe7c9", "targetHash": "1aa2b5849092ca7cf938f54f1445c1090f7061f614471c79b46d17c30e9fe7c9", "timestamp": "2025-06-02T16:15:25.190Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/any-types-report.md", "target": "docs/📊-reports/analysis/any-types-report.md", "category": "reports", "confidence": 0.75, "sourceHash": "5118b99b5c2004ffdf61e4f9cd0c201dcb985ec5bfdfed3f4dad4129cab1861f", "targetHash": "5118b99b5c2004ffdf61e4f9cd0c201dcb985ec5bfdfed3f4dad4129cab1861f", "timestamp": "2025-06-02T16:15:25.191Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/img-to-image-report.md", "target": "docs/📊-reports/analysis/img-to-image-report.md", "category": "reports", "confidence": 0.75, "sourceHash": "513c48117e23bd86dc80a33cae6d71051f03bbdf0957ff49c6b19dce1487c498", "targetHash": "513c48117e23bd86dc80a33cae6d71051f03bbdf0957ff49c6b19dce1487c498", "timestamp": "2025-06-02T16:15:25.191Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/performance-report.json", "target": "docs/📊-reports/analysis/performance-report.json", "category": "reports", "confidence": 0.75, "sourceHash": "2aa525797f11ff97db8abf422f83fa7ee04b37492c7a4ac7a3e0be3ca4701743", "targetHash": "2aa525797f11ff97db8abf422f83fa7ee04b37492c7a4ac7a3e0be3ca4701743", "timestamp": "2025-06-02T16:15:25.191Z"}, {"type": "moved", "source": "docs/📊-reports/analysis/r1-collaborative-analysis-package.md", "target": "docs/📊-reports/analysis/r1-collaborative-analysis-package.md", "category": "reports", "confidence": 0.75, "sourceHash": "83f072f8fee0b165eabaa0fa4d8c6b76d239463b784deaad16a40ca0f490eedc", "targetHash": "83f072f8fee0b165eabaa0fa4d8c6b76d239463b784deaad16a40ca0f490eedc", "timestamp": "2025-06-02T16:15:25.192Z"}, {"type": "moved", "source": "docs/📋-architecture/intelligent-agent-workflow-analysis.md", "target": "docs/📊-reports/analysis/intelligent-agent-workflow-analysis.md", "category": "reports", "confidence": 0.75, "sourceHash": "d986235b2daf336ca15c321418512c29fd6592fa4c4dae2c70770c5534712816", "targetHash": "d986235b2daf336ca15c321418512c29fd6592fa4c4dae2c70770c5534712816", "timestamp": "2025-06-02T16:15:25.192Z"}, {"type": "moved", "source": "docs/📊-reports/performance/performance-report.json", "target": "docs/📊-reports/analysis/performance-report.json", "category": "reports", "confidence": 0.75, "sourceHash": "1ce4745494848f652df40e8c0466bc08adeb81aaee92021c22b37bc8be582922", "targetHash": "1ce4745494848f652df40e8c0466bc08adeb81aaee92021c22b37bc8be582922", "timestamp": "2025-06-02T16:15:25.192Z"}, {"type": "moved", "source": "docs/📋-guides/agent-interfaces/ConflictPreventionMechanisms.md", "target": "docs/📊-reports/analysis/ConflictPreventionMechanisms.md", "category": "reports", "confidence": 0.75, "sourceHash": "08d498c1be359d91501e38b1ec3297d137bcb89842beed89de515b91a993a3ab", "targetHash": "08d498c1be359d91501e38b1ec3297d137bcb89842beed89de515b91a993a3ab", "timestamp": "2025-06-02T16:15:25.193Z"}, {"type": "moved", "source": "docs/📋-guides/agent-interfaces/OpsAgent-Interface.md", "target": "docs/📊-reports/analysis/OpsAgent-Interface.md", "category": "reports", "confidence": 0.75, "sourceHash": "c84dd75f34ac87ab6374effd226bd57b3dd98272a1ac70f0ee944162d0940504", "targetHash": "c84dd75f34ac87ab6374effd226bd57b3dd98272a1ac70f0ee944162d0940504", "timestamp": "2025-06-02T16:15:25.193Z"}, {"type": "moved", "source": "docs/📋-guides/agent-interfaces/ExtendedAgentConflictValidation.md", "target": "docs/📊-reports/analysis/ExtendedAgentConflictValidation.md", "category": "reports", "confidence": 0.75, "sourceHash": "a2546ff649e38b91cf6ecd0306e6f0d02c347e954b219afd06293321b8306f91", "targetHash": "a2546ff649e38b91cf6ecd0306e6f0d02c347e954b219afd06293321b8306f91", "timestamp": "2025-06-02T16:15:25.193Z"}, {"type": "moved", "source": "docs/📋-guides/agent-interfaces/UIAgent-Interface.md", "target": "docs/📊-reports/analysis/UIAgent-Interface.md", "category": "reports", "confidence": 0.75, "sourceHash": "e7d4a513ea5d3cbb4d799e02d2d21132f39fb1ed6a7a718d581e12b070f29452", "targetHash": "e7d4a513ea5d3cbb4d799e02d2d21132f39fb1ed6a7a718d581e12b070f29452", "timestamp": "2025-06-02T16:15:25.194Z"}, {"type": "moved", "source": "docs/📋-guides/agent-interfaces/SecurityAgent-Interface.md", "target": "docs/📊-reports/analysis/SecurityAgent-Interface.md", "category": "reports", "confidence": 0.75, "sourceHash": "add4b7ab27c8d08bd705fbcca5c3bf6a89f8e2e24422d8a9169b5d418b8a9e4d", "targetHash": "add4b7ab27c8d08bd705fbcca5c3bf6a89f8e2e24422d8a9169b5d418b8a9e4d", "timestamp": "2025-06-02T16:15:25.194Z"}, {"type": "moved", "source": "docs/📋-guides/agent-interfaces/RoleConflictAnalysis.md", "target": "docs/📊-reports/analysis/RoleConflictAnalysis.md", "category": "reports", "confidence": 0.75, "sourceHash": "fce30b18472e215a89d02b7b78901bee543ceb9cc71c76f9a6b68dded01fc53e", "targetHash": "fce30b18472e215a89d02b7b78901bee543ceb9cc71c76f9a6b68dded01fc53e", "timestamp": "2025-06-02T16:15:25.194Z"}, {"type": "moved", "source": "docs/📋-guides/development/CONTRIBUTING.md", "target": "docs/📊-reports/analysis/CONTRIBUTING.md", "category": "reports", "confidence": 0.75, "sourceHash": "a2e4ae298bcd7c2b82bf9e9e7ed6c7ac86dd9babb1a66ac112e96e904505bd50", "targetHash": "a2e4ae298bcd7c2b82bf9e9e7ed6c7ac86dd9babb1a66ac112e96e904505bd50", "timestamp": "2025-06-02T16:15:25.194Z"}, {"type": "moved", "source": "docs/📋-guides/development/AGENT_TASKS.md", "target": "docs/📊-reports/analysis/AGENT_TASKS.md", "category": "reports", "confidence": 0.75, "sourceHash": "937df64486a5cd112f05f30f2efe65e47b4406b5a2c143fa81182bc24e16cc6d", "targetHash": "937df64486a5cd112f05f30f2efe65e47b4406b5a2c143fa81182bc24e16cc6d", "timestamp": "2025-06-02T16:15:25.194Z"}, {"type": "moved", "source": "docs/📋-guides/development/CURSOR_DAILY_WORKFLOW.md", "target": "docs/📊-reports/analysis/CURSOR_DAILY_WORKFLOW.md", "category": "reports", "confidence": 0.75, "sourceHash": "24dfc89018e61b5cb9c8298593156ccc44d7ff60dbfc684a7a80f6e96c301dc2", "targetHash": "24dfc89018e61b5cb9c8298593156ccc44d7ff60dbfc684a7a80f6e96c301dc2", "timestamp": "2025-06-02T16:15:25.194Z"}, {"type": "moved", "source": "docs/📋-guides/development/r1-direct-command.md", "target": "docs/📊-reports/analysis/r1-direct-command.md", "category": "reports", "confidence": 0.75, "sourceHash": "c7550c552d466381e2ba10b4441ce5dc7bb4e9b964e1ee6ebe5832d0c0cc9339", "targetHash": "c7550c552d466381e2ba10b4441ce5dc7bb4e9b964e1ee6ebe5832d0c0cc9339", "timestamp": "2025-06-02T16:15:25.195Z"}, {"type": "moved", "source": "docs/📋-guides/development/workflow-optimization-analysis.md", "target": "docs/📊-reports/analysis/workflow-optimization-analysis.md", "category": "reports", "confidence": 0.75, "sourceHash": "d4de57a63fb0aa0954671d20ee6aeb28f8853f498b1bf90e4e54f6a1a6ecf461", "targetHash": "d4de57a63fb0aa0954671d20ee6aeb28f8853f498b1bf90e4e54f6a1a6ecf461", "timestamp": "2025-06-02T16:15:25.195Z"}, {"type": "moved", "source": "docs/📋-guides/development/two-agent-ai-workflow.md", "target": "docs/📊-reports/analysis/two-agent-ai-workflow.md", "category": "reports", "confidence": 0.75, "sourceHash": "4c3836f936ecfe4784dabe599d70fba0e527f6ed2408e34ffeafccfa5d79d40b", "targetHash": "4c3836f936ecfe4784dabe599d70fba0e527f6ed2408e34ffeafccfa5d79d40b", "timestamp": "2025-06-02T16:15:25.195Z"}, {"type": "moved", "source": "docs/🔍-logs/analysis/approved-recommendations.json", "target": "docs/📊-reports/analysis/approved-recommendations.json", "category": "reports", "confidence": 0.75, "sourceHash": "bba7778177cb353c5abf12461405b9d06e76661496a4833f601b77b1909900b8", "targetHash": "bba7778177cb353c5abf12461405b9d06e76661496a4833f601b77b1909900b8", "timestamp": "2025-06-02T16:15:25.196Z"}, {"type": "moved", "source": "docs/🔧-utilities/scripts/check-status.js", "target": "docs/📊-reports/analysis/check-status.js", "category": "reports", "confidence": 0.75, "sourceHash": "426976795d1ca8ea1ff28d65e972fab12d0d485a39f7b79471b9bbebdfd04686", "targetHash": "426976795d1ca8ea1ff28d65e972fab12d0d485a39f7b79471b9bbebdfd04686", "timestamp": "2025-06-02T16:15:25.196Z"}, {"type": "moved", "source": "docs/🔍-logs/build-analysis.log", "target": "docs/📊-reports/analysis/build-analysis.log", "category": "reports", "confidence": 0.75, "sourceHash": "16d1768cc52294e17a9ec2544276e62ed98cc6ef8a4b645107183f4e2bc07b88", "targetHash": "16d1768cc52294e17a9ec2544276e62ed98cc6ef8a4b645107183f4e2bc07b88", "timestamp": "2025-06-02T16:15:25.196Z"}, {"type": "moved", "source": "docs/adr/README.md", "target": "docs/📋-architecture/decisions/README.md", "category": "architecture", "confidence": 0.9, "sourceHash": "7a7448a1b66f2c2cb4bd7f168186d43712bf2dbe6c74cb7d0bd02618e7c77193", "targetHash": "7a7448a1b66f2c2cb4bd7f168186d43712bf2dbe6c74cb7d0bd02618e7c77193", "timestamp": "2025-06-02T16:15:25.196Z"}, {"type": "moved", "source": "docs/R1-Phase2-Implementation-Plan.md", "target": "docs/📋-architecture/decisions/R1-Phase2-Implementation-Plan.md", "category": "architecture", "confidence": 0.9, "sourceHash": "7de2f635703d35a59eb741e1859dd3c261359ad861720e5aa0c5ba9f62e829b9", "targetHash": "7de2f635703d35a59eb741e1859dd3c261359ad861720e5aa0c5ba9f62e829b9", "timestamp": "2025-06-02T16:15:25.196Z"}, {"type": "moved", "source": "docs/adr/adr-0001-nextjs-frontend-framework.md", "target": "docs/📋-architecture/decisions/adr-0001-nextjs-frontend-framework.md", "category": "architecture", "confidence": 0.9, "sourceHash": "b349711c3df6a0058e106207478345f35bb496dec757673297445ff4a17671d9", "targetHash": "b349711c3df6a0058e106207478345f35bb496dec757673297445ff4a17671d9", "timestamp": "2025-06-02T16:15:25.196Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/FeatureDiscoveryAgent-Development-Session-14.md", "target": "docs/📋-architecture/decisions/FeatureDiscoveryAgent-Development-Session-14.md", "category": "architecture", "confidence": 0.9, "sourceHash": "53ac5fad653fff86bcbc0071561a09131e62d4167ffbce1e759cd04d0649a38e", "targetHash": "53ac5fad653fff86bcbc0071561a09131e62d4167ffbce1e759cd04d0649a38e", "timestamp": "2025-06-02T16:15:25.197Z"}, {"type": "moved", "source": "docs/consolidate-duplicate-button-components.md", "target": "docs/📋-architecture/decisions/consolidate-duplicate-button-components.md", "category": "architecture", "confidence": 0.9, "sourceHash": "dfb2baa2e342afbdea7c29ee5b05c7b2c5bf7565a0938336d3d908b6acf1f4b6", "targetHash": "dfb2baa2e342afbdea7c29ee5b05c7b2c5bf7565a0938336d3d908b6acf1f4b6", "timestamp": "2025-06-02T16:15:25.197Z"}, {"type": "moved", "source": "docs/implementation/LivingAgentImplementationChecklist.md", "target": "docs/📋-architecture/decisions/LivingAgentImplementationChecklist.md", "category": "architecture", "confidence": 0.9, "sourceHash": "72b9a3b42c65cb58dc708e71619b6b3bc0317e7ca29a1c9072e87114adb2fb56", "targetHash": "72b9a3b42c65cb58dc708e71619b6b3bc0317e7ca29a1c9072e87114adb2fb56", "timestamp": "2025-06-02T16:15:25.197Z"}, {"type": "moved", "source": "docs/quantum-agents-implementation-guide.md", "target": "docs/📋-architecture/decisions/quantum-agents-implementation-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "5a69b3f6de3e9f21e02f3fcc238e4c36f26aa3691b69f789e1a13dc187b28035", "targetHash": "5a69b3f6de3e9f21e02f3fcc238e4c36f26aa3691b69f789e1a13dc187b28035", "timestamp": "2025-06-02T16:15:25.198Z"}, {"type": "moved", "source": "docs/security/README.md", "target": "docs/📋-architecture/decisions/README.md", "category": "architecture", "confidence": 0.9, "sourceHash": "bb1080cb7a8d33476349f1810510d07dd3d034dbfabc8f42fcfe99ffe7a63dba", "targetHash": "bb1080cb7a8d33476349f1810510d07dd3d034dbfabc8f42fcfe99ffe7a63dba", "timestamp": "2025-06-02T16:15:25.198Z"}, {"type": "moved", "source": "docs/quantum-agents-bulletproof-implementation-guide.md", "target": "docs/📋-architecture/decisions/quantum-agents-bulletproof-implementation-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "7442a0f3785163587193169319219bb08b001726b4d0fde1464acb02590a7fb8", "targetHash": "7442a0f3785163587193169319219bb08b001726b4d0fde1464acb02590a7fb8", "timestamp": "2025-06-02T16:15:25.198Z"}, {"type": "moved", "source": "docs/technical-solutions/ai-timeout-fix-resolution.md", "target": "docs/📋-architecture/decisions/ai-timeout-fix-resolution.md", "category": "architecture", "confidence": 0.9, "sourceHash": "f406eb6a1793a54ead4e7dd2e75747e73b2baca14748e77121e888725441c73f", "targetHash": "f406eb6a1793a54ead4e7dd2e75747e73b2baca14748e77121e888725441c73f", "timestamp": "2025-06-02T16:15:25.199Z"}, {"type": "moved", "source": "docs/security/SECURITY.md", "target": "docs/📋-architecture/decisions/SECURITY.md", "category": "architecture", "confidence": 0.9, "sourceHash": "30ec85805944352fbc427cbef4ed38c14086c2116a6f72bce9635ced5d22d3aa", "targetHash": "30ec85805944352fbc427cbef4ed38c14086c2116a6f72bce9635ced5d22d3aa", "timestamp": "2025-06-02T16:15:25.199Z"}, {"type": "moved", "source": "docs/strategic-utilization/LivingAgentUtilizationPlan.md", "target": "docs/📋-architecture/decisions/LivingAgentUtilizationPlan.md", "category": "architecture", "confidence": 0.9, "sourceHash": "09c9aba8a31aa0883b7486567a290fbf05c57df667e8a58f27a45d6572f8c4b9", "targetHash": "09c9aba8a31aa0883b7486567a290fbf05c57df667e8a58f27a45d6572f8c4b9", "timestamp": "2025-06-02T16:15:25.199Z"}, {"type": "moved", "source": "docs/📊-reports/status/implementation-monitor.md", "target": "docs/📋-architecture/decisions/implementation-monitor.md", "category": "architecture", "confidence": 0.9, "sourceHash": "38031701cc15d2322eb27e282050dfe7bb55ad1817bf5fd104b3ea66cb025290", "targetHash": "38031701cc15d2322eb27e282050dfe7bb55ad1817bf5fd104b3ea66cb025290", "timestamp": "2025-06-02T16:15:25.200Z"}, {"type": "moved", "source": "docs/📊-reports/status/STRATEGIC_IMPLEMENTATION_STATUS_REPORT.md", "target": "docs/📋-architecture/decisions/STRATEGIC_IMPLEMENTATION_STATUS_REPORT.md", "category": "architecture", "confidence": 0.9, "sourceHash": "4dc1d644f8ad9bc6fca8572e8d446a4071625867f6977f65b1f8183395a8570c", "targetHash": "4dc1d644f8ad9bc6fca8572e8d446a4071625867f6977f65b1f8183395a8570c", "timestamp": "2025-06-02T16:15:25.200Z"}, {"type": "moved", "source": "docs/📋-architecture/consciousness-evolution-roadmap.md", "target": "docs/📋-architecture/decisions/consciousness-evolution-roadmap.md", "category": "architecture", "confidence": 0.9, "sourceHash": "834537045a5e8fe339bb5b0c79651b52c32c36ed2ae6453738e129a3fb4f489f", "targetHash": "834537045a5e8fe339bb5b0c79651b52c32c36ed2ae6453738e129a3fb4f489f", "timestamp": "2025-06-02T16:15:25.200Z"}, {"type": "moved", "source": "docs/📋-architecture/consciousness-research-analysis.md", "target": "docs/📋-architecture/decisions/consciousness-research-analysis.md", "category": "architecture", "confidence": 0.9, "sourceHash": "fce24524fe6436a6ad7b50c7b26dcb96eb093026d9b1e1c4858bd44497f0baac", "targetHash": "fce24524fe6436a6ad7b50c7b26dcb96eb093026d9b1e1c4858bd44497f0baac", "timestamp": "2025-06-02T16:15:25.200Z"}, {"type": "moved", "source": "docs/📋-architecture/implementation-helpers.md", "target": "docs/📋-architecture/decisions/implementation-helpers.md", "category": "architecture", "confidence": 0.9, "sourceHash": "ccf3020d28be2ba7c9083aefc3490a5d01d3b369ac16b8c72a9f0e4021d2021f", "targetHash": "ccf3020d28be2ba7c9083aefc3490a5d01d3b369ac16b8c72a9f0e4021d2021f", "timestamp": "2025-06-02T16:15:25.200Z"}, {"type": "moved", "source": "docs/📋-architecture/critical-mcp-impact-analysis.md", "target": "docs/📋-architecture/decisions/critical-mcp-impact-analysis.md", "category": "architecture", "confidence": 0.9, "sourceHash": "a65ef62d201af0e5053b6a5408f9dd87ea6d8c75690701eea081b62ee9656b03", "targetHash": "a65ef62d201af0e5053b6a5408f9dd87ea6d8c75690701eea081b62ee9656b03", "timestamp": "2025-06-02T16:15:25.200Z"}, {"type": "moved", "source": "docs/📋-architecture/master-architecture-plan.md", "target": "docs/📋-architecture/decisions/master-architecture-plan.md", "category": "architecture", "confidence": 0.9, "sourceHash": "ea54445a84a255add5c0fdb5ed7ea5feb965e9f35221ecdaa1f97002a1915d7e", "targetHash": "ea54445a84a255add5c0fdb5ed7ea5feb965e9f35221ecdaa1f97002a1915d7e", "timestamp": "2025-06-02T16:15:25.201Z"}, {"type": "moved", "source": "docs/📋-architecture/master-implementation-strategy.md", "target": "docs/📋-architecture/decisions/master-implementation-strategy.md", "category": "architecture", "confidence": 0.9, "sourceHash": "bcb5b9bc5c1d55b316c2561846051a12ec3e57788f0ee350c96b51b6d2d482f2", "targetHash": "bcb5b9bc5c1d55b316c2561846051a12ec3e57788f0ee350c96b51b6d2d482f2", "timestamp": "2025-06-02T16:15:25.201Z"}, {"type": "moved", "source": "docs/📋-architecture/mcp-implementation-complete.md", "target": "docs/📋-architecture/decisions/mcp-implementation-complete.md", "category": "architecture", "confidence": 0.9, "sourceHash": "e24dfc717aff50bedaf45f600be198d716386d51e547f2e9608e694ca4ab4455", "targetHash": "e24dfc717aff50bedaf45f600be198d716386d51e547f2e9608e694ca4ab4455", "timestamp": "2025-06-02T16:15:25.201Z"}, {"type": "moved", "source": "docs/📋-guides/agent-interfaces/DevAgent-Interface.md", "target": "docs/📋-architecture/decisions/DevAgent-Interface.md", "category": "architecture", "confidence": 0.9, "sourceHash": "8232a603d0dad9918c4ff00e243ae57d5a801f7ddb98f116471e1a6758339b77", "targetHash": "8232a603d0dad9918c4ff00e243ae57d5a801f7ddb98f116471e1a6758339b77", "timestamp": "2025-06-02T16:15:25.202Z"}, {"type": "moved", "source": "docs/📋-architecture/phase1-implementation-guide.md", "target": "docs/📋-architecture/decisions/phase1-implementation-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "92a3da3afac97de6f550dd4adf85260abd66405ce8cb25c6d43cd6b735ca69c4", "targetHash": "92a3da3afac97de6f550dd4adf85260abd66405ce8cb25c6d43cd6b735ca69c4", "timestamp": "2025-06-02T16:15:25.202Z"}, {"type": "moved", "source": "docs/📋-architecture/mcp-integration-implementation-guide.md", "target": "docs/📋-architecture/decisions/mcp-integration-implementation-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "c22428ab58afecf7212496c6e82d494b4a6e1bb5fa7f7d5efc82cc936ee9bd1d", "targetHash": "c22428ab58afecf7212496c6e82d494b4a6e1bb5fa7f7d5efc82cc936ee9bd1d", "timestamp": "2025-06-02T16:15:25.202Z"}, {"type": "moved", "source": "docs/📋-guides/development/model-comparison-results.md", "target": "docs/📋-architecture/decisions/model-comparison-results.md", "category": "architecture", "confidence": 0.9, "sourceHash": "7400b880c804def657ffb04ca72b17e7f4e81a698a7dfb5802ab7ef608b22ff1", "targetHash": "7400b880c804def657ffb04ca72b17e7f4e81a698a7dfb5802ab7ef608b22ff1", "timestamp": "2025-06-02T16:15:25.203Z"}, {"type": "moved", "source": "docs/📋-guides/development/ai-collaboration-protocol.md", "target": "docs/📋-architecture/decisions/ai-collaboration-protocol.md", "category": "architecture", "confidence": 0.9, "sourceHash": "050ceb855b3f94a670031ed87d814d866a9c9fa7d27876ed934abacac089a0ba", "targetHash": "050ceb855b3f94a670031ed87d814d866a9c9fa7d27876ed934abacac089a0ba", "timestamp": "2025-06-02T16:15:25.203Z"}, {"type": "moved", "source": "docs/📋-guides/development/agent-orchestration-roadmap.md", "target": "docs/📋-architecture/decisions/agent-orchestration-roadmap.md", "category": "architecture", "confidence": 0.9, "sourceHash": "3ea425225243aea947303b3ffc89220a27925ae8ccdcae8b767fc0cab7d6f157", "targetHash": "3ea425225243aea947303b3ffc89220a27925ae8ccdcae8b767fc0cab7d6f157", "timestamp": "2025-06-02T16:15:25.203Z"}, {"type": "moved", "source": "docs/📝-technical/quantum-core-implementation.md", "target": "docs/📋-architecture/decisions/quantum-core-implementation.md", "category": "architecture", "confidence": 0.9, "sourceHash": "c08865007489b3cacc421e34866908b7540fff5fe1a1dc68ccea5c3d37bf2f75", "targetHash": "c08865007489b3cacc421e34866908b7540fff5fe1a1dc68ccea5c3d37bf2f75", "timestamp": "2025-06-02T16:15:25.203Z"}, {"type": "moved", "source": "docs/📋-guides/development/tasks.md", "target": "docs/📋-architecture/decisions/tasks.md", "category": "architecture", "confidence": 0.9, "sourceHash": "22d6c8db210ea9140506ea6466b5e7c7a659f0d3fe9359098bcaa6e9866ec1f6", "targetHash": "22d6c8db210ea9140506ea6466b5e7c7a659f0d3fe9359098bcaa6e9866ec1f6", "timestamp": "2025-06-02T16:15:25.203Z"}, {"type": "moved", "source": "docs/📋-guides/quantum-quick-start-guide.md", "target": "docs/📋-architecture/decisions/quantum-quick-start-guide.md", "category": "architecture", "confidence": 0.9, "sourceHash": "b7d49f37743777f3912033ccbf688d9d871228a981deff87d6e80b1494f660bb", "targetHash": "b7d49f37743777f3912033ccbf688d9d871228a981deff87d6e80b1494f660bb", "timestamp": "2025-06-02T16:15:25.203Z"}, {"type": "moved", "source": "docs/📝-technical/specifications/AGENT_INTELLIGENCE_DEVELOPMENT_GUIDE.md", "target": "docs/📋-architecture/decisions/AGENT_INTELLIGENCE_DEVELOPMENT_GUIDE.md", "category": "architecture", "confidence": 0.9, "sourceHash": "751dd883125ed18a1a516d28a00de5cb4b55d2278f9fe194f388b37f08ca01df", "targetHash": "751dd883125ed18a1a516d28a00de5cb4b55d2278f9fe194f388b37f08ca01df", "timestamp": "2025-06-02T16:15:25.204Z"}, {"type": "moved", "source": "docs/🧪-testing/results/FINAL_CANVAS_TOOL_TEST.md", "target": "docs/📋-architecture/decisions/FINAL_CANVAS_TOOL_TEST.md", "category": "architecture", "confidence": 0.9, "sourceHash": "ca290a6d14fd993e3e0d7cd167f7a911a2b8020519668b81d882915d1fc89dc3", "targetHash": "ca290a6d14fd993e3e0d7cd167f7a911a2b8020519668b81d882915d1fc89dc3", "timestamp": "2025-06-02T16:15:25.204Z"}, {"type": "moved", "source": "docs/🧪-testing/results/CANVAS_INTUITIVE_INNOVATIVE_UPGRADE.md", "target": "docs/📋-architecture/decisions/CANVAS_INTUITIVE_INNOVATIVE_UPGRADE.md", "category": "architecture", "confidence": 0.9, "sourceHash": "f62a10c9a2bb504dbaf56c7e2efc237a9c350033403ab7687769df149b416699", "targetHash": "f62a10c9a2bb504dbaf56c7e2efc237a9c350033403ab7687769df149b416699", "timestamp": "2025-06-02T16:15:25.204Z"}, {"type": "moved", "source": "docs/enhanced-keyboard-navigation-system.md", "target": "docs/📝-technical/solutions/enhanced-keyboard-navigation-system.md", "category": "technical", "confidence": 0.85, "sourceHash": "bc546a5c6a883ca741d642bfeda80d5975b027e469fa48aa0dd29764bec05074", "targetHash": "bc546a5c6a883ca741d642bfeda80d5975b027e469fa48aa0dd29764bec05074", "timestamp": "2025-06-02T16:15:25.205Z"}, {"type": "moved", "source": "docs/README.md", "target": "docs/📝-technical/solutions/README.md", "category": "technical", "confidence": 0.85, "sourceHash": "857b2b916e69633bd95cf0a2fb7aa6f88275284eab9bcc93a2a338ba80955715", "targetHash": "857b2b916e69633bd95cf0a2fb7aa6f88275284eab9bcc93a2a338ba80955715", "timestamp": "2025-06-02T16:15:25.205Z"}, {"type": "moved", "source": "docs/interface-conflict-prevention-system.md", "target": "docs/📝-technical/solutions/interface-conflict-prevention-system.md", "category": "technical", "confidence": 0.85, "sourceHash": "172a59764b6bd57315d3abfe5b113e09a675ca29841cb37012386d683ebcbd2c", "targetHash": "172a59764b6bd57315d3abfe5b113e09a675ca29841cb37012386d683ebcbd2c", "timestamp": "2025-06-02T16:15:25.205Z"}, {"type": "moved", "source": "docs/R1-Implementation-Success.md", "target": "docs/📋-methodologies/revolutions/R1-Implementation-Success.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "8ce60e28824a6b7ff0425878638f7f9d172479e8ea13875c0a1fed62eb1c034f", "targetHash": "8ce60e28824a6b7ff0425878638f7f9d172479e8ea13875c0a1fed62eb1c034f", "timestamp": "2025-06-02T16:15:25.205Z"}, {"type": "moved", "source": "docs/organization/DOCUMENTATION_WORKFLOW.md", "target": "docs/📋-methodologies/revolutions/DOCUMENTATION_WORKFLOW.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "ae2db5c6c8a9469c3668afaa92cffd49d3e986194b18652477ea597c2c2bab64", "targetHash": "ae2db5c6c8a9469c3668afaa92cffd49d3e986194b18652477ea597c2c2bab64", "timestamp": "2025-06-02T16:15:25.205Z"}, {"type": "moved", "source": "docs/agent-intelligence-sessions/WorkflowEnhancementAgent-Development-Session-13.md", "target": "docs/📋-methodologies/revolutions/WorkflowEnhancementAgent-Development-Session-13.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "8eae8bb95196ee8237dd79f5787b74f657b3594cb4dd2bf560af0572153bd5b9", "targetHash": "8eae8bb95196ee8237dd79f5787b74f657b3594cb4dd2bf560af0572153bd5b9", "timestamp": "2025-06-02T16:15:25.206Z"}, {"type": "moved", "source": "docs/organization/PROJECT_ORGANIZATION_ENHANCED.md", "target": "docs/📋-methodologies/revolutions/PROJECT_ORGANIZATION_ENHANCED.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "6b41ef8b9296c95723032f1ff8ab053a743ded70aaf8b155574dcb3d6da04b83", "targetHash": "6b41ef8b9296c95723032f1ff8ab053a743ded70aaf8b155574dcb3d6da04b83", "timestamp": "2025-06-02T16:15:25.206Z"}, {"type": "moved", "source": "docs/strategic-utilization/LivingAgentUtilizationSummary.md", "target": "docs/📋-methodologies/revolutions/LivingAgentUtilizationSummary.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "bc3f881d7aa71a20bc9be76d8f4731c89fff77be2ab696c75d9ff39b1ef914b3", "targetHash": "bc3f881d7aa71a20bc9be76d8f4731c89fff77be2ab696c75d9ff39b1ef914b3", "timestamp": "2025-06-02T16:15:25.206Z"}, {"type": "moved", "source": "docs/organization/ORGANIZATION_ENHANCEMENT_SUMMARY.md", "target": "docs/📋-methodologies/revolutions/ORGANIZATION_ENHANCEMENT_SUMMARY.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "5eada5d090256fae9d80281195372effcc13541fe649fdbd7be09b176e67079e", "targetHash": "5eada5d090256fae9d80281195372effcc13541fe649fdbd7be09b176e67079e", "timestamp": "2025-06-02T16:15:25.206Z"}, {"type": "moved", "source": "docs/📝-technical/specifications/creative-canvas.md", "target": "docs/📋-methodologies/revolutions/creative-canvas.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "50fb53f844221e14cd6c9f5e3ee50050de34d94a6751c33f9e12d93f07a0af33", "targetHash": "50fb53f844221e14cd6c9f5e3ee50050de34d94a6751c33f9e12d93f07a0af33", "timestamp": "2025-06-02T16:15:25.207Z"}, {"type": "moved", "source": "docs/📊-reports/status/AGENT_COMMUNICATION_STATUS.md", "target": "docs/📋-methodologies/revolutions/AGENT_COMMUNICATION_STATUS.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "bda4699f9c0cf0439f0a936e1a70103e0440c752b79f716327d4d14401979354", "targetHash": "bda4699f9c0cf0439f0a936e1a70103e0440c752b79f716327d4d14401979354", "timestamp": "2025-06-02T16:15:25.207Z"}, {"type": "moved", "source": "docs/📋-guides/development/development-workflow-strategy.md", "target": "docs/📋-methodologies/revolutions/development-workflow-strategy.md", "category": "methodologies", "confidence": 0.7, "sourceHash": "8c68acebd30854b0799e57e08fc93ada44c9ead940151ee51fcc81f826f051c3", "targetHash": "8c68acebd30854b0799e57e08fc93ada44c9ead940151ee51fcc81f826f051c3", "timestamp": "2025-06-02T16:15:25.207Z"}, {"type": "moved", "source": "docs/agent-transformation/AgentCodeTransformation.md", "target": "docs/📋-agents/intelligence/AgentCodeTransformation.md", "category": "agent-intelligence", "confidence": 0.95, "sourceHash": "6bbda87c51f8dc193854a99465438e61a17aa9ff340485e8ea3d883da8403226", "targetHash": "6bbda87c51f8dc193854a99465438e61a17aa9ff340485e8ea3d883da8403226", "timestamp": "2025-06-02T16:15:25.208Z"}, {"type": "moved", "source": "docs/📋-guides/development/AUTOMATIC_FILE_ORGANIZATION.md", "target": "docs/organization/structure/AUTOMATIC_FILE_ORGANIZATION.md", "category": "organization", "confidence": 0.65, "sourceHash": "8326acaf4e058ff47f20f29b533729fda2f22b2b135b5908336cdfe169c38434", "targetHash": "8326acaf4e058ff47f20f29b533729fda2f22b2b135b5908336cdfe169c38434", "timestamp": "2025-06-02T16:15:25.208Z"}, {"type": "moved", "source": "docs/organization/PROJECT_ORGANIZATION.md", "target": "docs/organization/structure/PROJECT_ORGANIZATION.md", "category": "organization", "confidence": 0.65, "sourceHash": "e8bc1ef198f5e0daa62ec4b40b8ef5d06e3562e1c76ad3a1f5b7bc9cc48e01e6", "targetHash": "e8bc1ef198f5e0daa62ec4b40b8ef5d06e3562e1c76ad3a1f5b7bc9cc48e01e6", "timestamp": "2025-06-02T16:15:25.208Z"}, {"type": "moved", "source": "docs/📊-reports/performance/security-report.json", "target": "docs/security/policies/security-report.json", "category": "security-utilities", "confidence": 0.6, "sourceHash": "ec2db362e3fec4aacafea0fb457aeb587ccc3185f47080d055ea45738cc906fa", "targetHash": "ec2db362e3fec4aacafea0fb457aeb587ccc3185f47080d055ea45738cc906fa", "timestamp": "2025-06-02T16:15:25.209Z"}]