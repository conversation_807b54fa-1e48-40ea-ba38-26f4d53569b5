# HTML <img> Tags Report

Generated on Tue May 20 16:47:58 BST 2025

These HTML <img> tags should be replaced with Next.js <Image> components for performance optimization.

## Files with <img> Tags

### src/app/categories/[id]/page.tsx

| Line | Context |
|------|---------|

Replacement pattern:



### src/app/search/page.tsx

| Line | Context |
|------|---------|
| 396 | `<img src={result.creator.avatar} alt={result.creator.name} className="w-full h-full rounded-full" />` |

Replacement pattern:



### src/features/discovery/components/RecommendationGrid.tsx

| Line | Context |
|------|---------|
| 194 | `<img src={item.creator.avatar} alt={item.creator.name} className="w-full h-full rounded-full" />` |

Replacement pattern:



### src/features/discovery/components/TrendingGrid.tsx

| Line | Context |
|------|---------|
| 150 | `<img src={item.creator.avatar} alt={item.creator.name} className="w-full h-full rounded-full" />` |

Replacement pattern:



