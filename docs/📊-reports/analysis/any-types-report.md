# TypeScript 'any' Types Report

Generated on Tue May 20 16:47:57 BST 2025

## Files with 'any' Types

### src/app/api/analyze/route.ts

| Line | Context |
|------|---------|
| 25 | `const response: any = { analysis };` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/app/canvas/collaborate/[id]/page.tsx

| Line | Context |
|------|---------|

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/app/canvas/page.tsx

| Line | Context |
|------|---------|
| 217 | `const handleFileSelect = (file: File, cloudinaryData?: any) => {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/app/design-system/toast/page.tsx

| Line | Context |
|------|---------|
| 20 | `const options: any = { duration };` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/app/media/[id]/page.tsx

| Line | Context |
|------|---------|

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/features/canvas/components/CollaborativeCanvas.tsx

| Line | Context |
|------|---------|
| 533 | `const handleRemoteCanvasUpdate = (userId: string, data: any) => {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/features/canvas/components/InteractivePanel.tsx

| Line | Context |
|------|---------|
| 106 | `const handleStatePropertyChange = (property: string, value: any) => {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/features/canvas/components/MediaUploader.tsx

| Line | Context |
|------|---------|
| 7 | `onFileSelect: (file: File, cloudinaryData?: any) => void;` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/features/canvas/components/SharedWorkspace.tsx

| Line | Context |
|------|---------|
| 239 | `const handleRemoteInteractiveUpdate = (userId: string, data: any) => {` |
| 274 | `const handleRemoteLayerUpdate = (userId: string, data: any) => {` |
| 28 | `onSave?: (state: any) => void;` |
| 359 | `const handleRemoteAnnotationUpdate = (userId: string, data: any) => {` |
| 389 | `const handleRemoteVersionUpdate = (userId: string, data: any) => {` |
| 431 | `const handleRemotePermissionUpdate = (userId: string, data: any) => {` |
| 457 | `const handleRemoteConflict = (userId: string, data: any) => {` |
| 651 | `const handleAnnotationUpdateForSave = (data: any) => {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/features/canvas/hooks/useAnimation.ts

| Line | Context |
|------|---------|
| 255 | `const interpolate = (startValue: any, endValue: any, t: number, easingFn: EasingFunction = easingFunctions.linear) => {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/features/canvas/hooks/useInteractive.ts

| Line | Context |
|------|---------|
| 130 | `const createState = useCallback((regionId: string, name: string, properties: any): InteractiveState | null => {` |
| 14 | `createRegion: (name: string, type: 'rectangle' | 'circle' | 'polygon', shape: any) => InteractiveRegion;` |
| 17 | `createState: (regionId: string, name: string, properties: any) => InteractiveState | null;` |
| 25 | `handleInteraction: (regionId: string, eventType: 'click' | 'hover' | 'drag' | 'drop' | 'custom', value?: any) => void;` |
| 376 | `value?: any` |
| 489 | `const getRegionInState = useCallback((region: InteractiveRegion): any => {` |
| 58 | `shape: any` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/features/canvas/types.ts

| Line | Context |
|------|---------|
| 118 | `requiresValue?: any;` |
| 227 | `data?: any;` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/hooks/useAuth.ts

| Line | Context |
|------|---------|
| 103 | `} catch (error: any) {` |
| 138 | `} catch (error: any) {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/hooks/useRealtime.ts

| Line | Context |
|------|---------|
| 138 | `const handleCanvasUpdate = (data: { userId: string; data: any }) => {` |
| 201 | `const updateCanvas = useCallback((data: any) => {` |
| 36 | `onCanvasUpdate?: (userId: string, data: any) => void;` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/lib/cloudinary.ts

| Line | Context |
|------|---------|
| 45 | `export const getCloudinaryUrl = (publicId: string, options: any = {}) => {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/lib/openai.ts

| Line | Context |
|------|---------|
| 106 | `export async function generateDescription(analysis: any, contentType: string = 'text') {` |
| 143 | `export async function suggestRelatedContent(contentItem: any, contentType: string = 'text') {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/lib/socket.ts

| Line | Context |
|------|---------|
| 76 | `export const sendCanvasUpdate = (roomId: string, data: any): void => {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

### src/models/User.ts

| Line | Context |
|------|---------|
| 53 | `} catch (error: any) {` |

Suggested fixes:

1. Replace with specific types based on usage context
2. Create appropriate interfaces/types for complex objects
3. Use union types for multiple possibilities
4. Consider using  instead of  if type is truly unknown

