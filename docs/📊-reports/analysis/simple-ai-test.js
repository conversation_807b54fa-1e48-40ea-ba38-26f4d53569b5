// Simple test to verify LocalAI integration actually works
const { exec } = require('child_process');

console.log('🧪 Testing LocalAI Integration...\n');

// Test 1: Check if Ollama is available
exec('ollama list', (error, stdout, stderr) => {
  if (error) {
    console.log('❌ Ollama not available:', error.message);
    return;
  }
  
  console.log('✅ Ollama models available:');
  console.log(stdout);
  
  // Test 2: Simple AI query
  console.log('🧠 Testing AI query...');
  const testPrompt = 'Respond with exactly: {"status": "working", "confidence": 95}';
  
  exec(`ollama run devstral:latest "${testPrompt}"`, (error, stdout, stderr) => {
    if (error) {
      console.log('❌ AI query failed:', error.message);
      return;
    }
    
    console.log('✅ AI Response:', stdout.trim());
    
    try {
      const parsed = JSON.parse(stdout.trim());
      console.log('✅ JSON parsing successful - LocalAI is working!');
    } catch (e) {
      console.log('⚠️ Response not JSON but AI responded');
    }
  });
}); 