# Contributing to <PERSON>reA<PERSON>tive

Thank you for your interest in contributing to CreAItive! This document provides guidelines and instructions for contributing to the project.

**Project Timeline**: May 2025 (11-day development) | **Methodology**: Real-First Development + Stable Development Framework

## 🏆 **Development Methodology (CRITICAL)**

CreAItive follows two breakthrough methodologies proven over 11 days of rapid development. **All contributions must follow these approaches.**

### **🎯 Real-First Development**
**Zero Mock Dependencies - ALL contributions must comply:**

#### **✅ REQUIRED Patterns:**
```typescript
// ✅ ALWAYS use real data sources
const data = await fetchRealData();
if (!data) return { error: 'real_data_required', status: 'unavailable' };

// ✅ Graceful degradation without fake data
if (!realResponse) {
  return { status: 'degraded', reason: 'api_unavailable' };
}
```

#### **❌ FORBIDDEN Patterns:**
```typescript
// ❌ NEVER write these - PRs will be rejected:
const data = realData || generateMockData();
if (!realResponse) return simulateIntelligentResponse();
const devData = NODE_ENV === 'development' ? mockData : realData;
```

#### **Core Requirements:**
- **Never Write Mocks**: If you can't connect to real data, don't build the feature yet
- **API Integration First**: Connect to real APIs before building logic
- **Authentic Intelligence**: Only real AI responses, never fake ones
- **Graceful Degradation**: Clear user communication when real services unavailable

### **🛡️ Stable Development Framework**
**Non-Breaking Enhancement Methodology - ALL features must be added safely:**

#### **Required Process:**
1. **Incremental Development**: Add features without modifying existing files when possible
2. **Validation Checkpoints**: Verify build success after each step
3. **Backward Compatibility**: Enhanced components must maintain existing interfaces
4. **Rollback Capability**: Be prepared to revert if breaking changes occur

#### **Example Success Pattern:**
```bash
# Add new files incrementally
src/websocket/WebSocketManager.ts          # New file - safe
src/hooks/useWebSocket.ts                  # New file - safe  
src/components/realtime/RealTimeStatus.tsx # New file - safe

# Enhance existing components safely
interface ButtonProps {
  // ... existing props ...
  glowing?: boolean;     # Optional prop - backward compatible
  animated?: boolean;    # Optional prop - backward compatible
}
```

### **🚀 Proven Results (May 2025)**
- **WebSocket Integration**: 5 files added with zero breaking changes
- **Claude AI Integration**: 100% real responses across all agents
- **Production Stability**: 49 pages generated, sub-15s build times
- **Documentation Accuracy**: Zero fake dates across 37 files

## Code of Conduct

By participating in this project, you are expected to uphold our Code of Conduct: be respectful, considerate, and collaborative.

## How to Contribute

### Setting Up the Development Environment

1. Fork the repository
2. Clone your fork: `git clone https://github.com/yourusername/creAItive.git`
3. Install dependencies: `npm install`
4. Create a branch for your changes: `git checkout -b feature/your-feature-name`

### Development Workflow

1. Make your changes following our coding standards
2. Run tests to ensure your changes don't break existing functionality: `npm test`
3. Commit your changes with a clear commit message
4. Push to your fork: `git push origin feature/your-feature-name`
5. Submit a pull request to the main repository

## Pull Request Process

1. Update the README.md or documentation with details of changes if appropriate
2. Update the Memory Bank documentation if your changes impact architecture or user experience
3. Make sure all tests pass
4. Ensure your code follows our code style guidelines
5. Your pull request will be reviewed by maintainers

## Code Style Guidelines

### General

- Use TypeScript for all code
- Follow the project's ESLint and Prettier configuration
- Write self-documenting code with clear variable and function names
- Comment complex logic or non-obvious decisions

### React Components

- Follow the component folder structure defined in `memory-bank/systemPatterns.md`
- Use functional components with hooks
- Implement proper prop types with TypeScript interfaces
- Follow accessibility best practices

### API Development

- Follow RESTful or GraphQL conventions as appropriate
- Document all endpoints with JSDoc or similar
- Implement proper error handling and validation
- Write tests for all API endpoints

## Commit Message Guidelines

Format: `type(scope): subject`

Types:
- feat: A new feature
- fix: A bug fix
- docs: Documentation only changes
- style: Changes that do not affect the meaning of the code
- refactor: A code change that neither fixes a bug nor adds a feature
- perf: A code change that improves performance
- test: Adding missing tests
- chore: Changes to the build process or auxiliary tools

Example: `feat(canvas): add image manipulation tools`

## Branch Naming Convention

Format: `type/description`

Types:
- feature: New functionality
- bugfix: Fixing issues
- hotfix: Critical fixes for production
- docs: Documentation changes
- refactor: Code improvements without changing functionality

Example: `feature/user-authentication`

## Testing Guidelines

- Write unit tests for all business logic
- Create component tests for UI components
- Add integration tests for feature flows
- Ensure tests are isolated and don't depend on external services
- Aim for high test coverage, especially for critical paths

## Documentation

- Update documentation for any feature or behavior changes
- Follow Markdown formatting guidelines
- Keep technical documentation up-to-date with code changes
- Document architectural decisions and their rationales

## Review Process

1. Code review by at least one maintainer
2. Automated checks (tests, linting, build)
3. Possible revisions based on feedback
4. Final approval and merge

## Questions?

If you have any questions about contributing, please open an issue or contact the maintainers.

---

Thank you for contributing to CreAItive! Together, we're building a platform that empowers creators through technology that evolves with their needs. 