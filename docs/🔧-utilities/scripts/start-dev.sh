#!/bin/bash

# Check if .env.local exists
if [ ! -f .env.local ]; then
  echo "Creating default .env.local file..."
  cat > .env.local << EOL
# Authentication
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=ThisIsATemporarySecretForLocalDevelopment

# URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001
SOCKET_PORT=3001

# API Keys (use actual keys in production)
OPENAI_API_KEY=sk-dummy-key-for-development
CLOUDINARY_CLOUD_NAME=demo
CLOUDINARY_API_KEY=dummy-key
CLOUDINARY_API_SECRET=dummy-secret
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=demo
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=creAItive
EOL
  echo "Created .env.local with default values."
fi

# Check if concurrently is installed
if ! npm list -g concurrently > /dev/null 2>&1; then
  echo "Installing concurrently globally..."
  npm install -g concurrently
fi

# Start both servers
echo "Starting Next.js and WebSocket servers..."
npm run dev:all 