const http = require('http');
const { Server } = require('socket.io');

// Create HTTP server
const server = http.createServer();

// Initialize Socket.IO with CORS configuration
const io = new Server(server, {
  cors: {
    origin: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    methods: ['GET', 'POST'],
    credentials: true,
  }
});

// Store active users in each room
const rooms = new Map();

// Store cursor positions
const cursorPositions = new Map();

// Handle socket connections
io.on('connection', (socket) => {
  const userId = socket.handshake.auth.userId;
  console.log(`User connected: ${userId}, Socket ID: ${socket.id}`);
  
  // Join a room
  socket.on('join-room', (roomId) => {
    console.log(`User ${userId} joining room: ${roomId}`);
    socket.join(roomId);
    
    // Track users in the room
    if (!rooms.has(roomId)) {
      rooms.set(roomId, new Set());
    }
    rooms.get(roomId).add(userId);
    
    // Notify room about new user
    io.to(roomId).emit('user-joined', {
      userId,
      users: Array.from(rooms.get(roomId))
    });
    
    // Send current canvas state to new user
    socket.to(roomId).emit('request-canvas-state', { userId });
  });
  
  // Leave a room
  socket.on('leave-room', (roomId) => {
    console.log(`User ${userId} leaving room: ${roomId}`);
    socket.leave(roomId);
    
    // Remove user from room tracking
    if (rooms.has(roomId)) {
      rooms.get(roomId).delete(userId);
      
      // If room is empty, clean up
      if (rooms.get(roomId).size === 0) {
        rooms.delete(roomId);
      } else {
        // Notify room about user leaving
        io.to(roomId).emit('user-left', {
          userId,
          users: Array.from(rooms.get(roomId))
        });
      }
    }
    
    // Clean up cursor position
    if (cursorPositions.has(roomId)) {
      const roomCursors = cursorPositions.get(roomId);
      if (roomCursors.has(userId)) {
        roomCursors.delete(userId);
        
        // Broadcast cursor removal to room
        socket.to(roomId).emit('cursor-removed', { userId });
      }
    }
  });
  
  // Handle canvas updates
  socket.on('canvas-update', ({ roomId, data }) => {
    // Broadcast to everyone in the room except the sender
    socket.to(roomId).emit('canvas-update', {
      userId,
      data
    });
  });
  
  // Handle cursor position updates
  socket.on('cursor-position', ({ roomId, position }) => {
    // Store cursor position
    if (!cursorPositions.has(roomId)) {
      cursorPositions.set(roomId, new Map());
    }
    cursorPositions.get(roomId).set(userId, position);
    
    // Broadcast to everyone in the room except the sender
    socket.to(roomId).emit('cursor-position', {
      userId,
      position
    });
  });
  
  // Handle chat messages
  socket.on('chat-message', ({ roomId, message }) => {
    const timestamp = new Date().toISOString();
    
    // Broadcast to everyone in the room including the sender
    io.to(roomId).emit('chat-message', {
      userId,
      message,
      timestamp
    });
  });
  
  // Handle canvas state responses
  socket.on('canvas-state-response', ({ roomId, targetUserId, state }) => {
    // Send canvas state only to the user who requested it
    io.to(roomId).emit('canvas-state', {
      fromUserId: userId,
      state
    });
  });
  
  // Handle disconnection
  socket.on('disconnect', () => {
    console.log(`User disconnected: ${userId}`);
    
    // Find all rooms the user was in
    for (const [roomId, users] of rooms.entries()) {
      if (users.has(userId)) {
        // Remove user from room
        users.delete(userId);
        
        // If room is empty, clean up
        if (users.size === 0) {
          rooms.delete(roomId);
        } else {
          // Notify room about user leaving
          io.to(roomId).emit('user-left', {
            userId,
            users: Array.from(users)
          });
        }
        
        // Clean up cursor position
        if (cursorPositions.has(roomId)) {
          const roomCursors = cursorPositions.get(roomId);
          if (roomCursors.has(userId)) {
            roomCursors.delete(userId);
            
            // Broadcast cursor removal to room
            io.to(roomId).emit('cursor-removed', { userId });
          }
        }
      }
    }
  });
});

// Start the server
const PORT = process.env.SOCKET_PORT || 3001;
server.listen(PORT, () => {
  console.log(`WebSocket server running on port ${PORT}`);
}); 