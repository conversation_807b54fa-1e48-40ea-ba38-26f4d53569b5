apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: creAItive-production
  labels:
    app: creAItive
    environment: production
    track: "5"
    day: "4"

resources:
- ../../base

namespace: production

commonLabels:
  environment: production
  deployment.track: "5"
  security.level: maximum

commonAnnotations:
  environment.name: "production"
  environment.purpose: "live-production-workload"
  deployment.strategy: "canary"
  security.compliance: "enterprise-grade"

# Production-specific ConfigMap
configMapGenerator:
- name: creAItive-env-config
  literals:
  - ENVIRONMENT=production
  - LOG_LEVEL=warn
  - DEBUG_MODE=false
  - API_TIMEOUT=15s
  - MAX_CONNECTIONS=500
  - CACHE_TTL=1800
  - FEATURE_FLAGS=production-stable
  - MONITORING_ENABLED=true
  - METRICS_INTERVAL=10s
  - AGENT_CONCURRENCY=20
  - DATABASE_POOL_SIZE=50
  - RATE_LIMITING=true
  - SECURITY_SCANNING=true
  - PERFORMANCE_OPTIMIZATION=true
  - AUTO_SCALING=true
  - BACKUP_ENABLED=true
  - DISASTER_RECOVERY=true

# Production image tags (stable versions)
images:
- name: ghcr.io/creAItive/app
  newTag: v1.0.0
- name: prom/prometheus
  newTag: v2.48.0
- name: grafana/grafana
  newTag: 10.2.2

# Production-specific patches
patches:
- target:
    kind: Deployment
    name: creAItive-app
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 3
    - op: add
      path: /spec/template/spec/containers/0/env/-
      value:
        name: NODE_ENV
        value: production
    - op: add
      path: /spec/template/spec/containers/0/env/-
      value:
        name: PRODUCTION_OPTIMIZATIONS
        value: "true"
    - op: add
      path: /spec/template/spec/containers/0/env/-
      value:
        name: SECURITY_HARDENING
        value: "maximum"
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: 500m
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 1Gi
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/cpu
      value: 2000m
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: 4Gi
    - op: add
      path: /spec/template/spec/securityContext
      value:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
        seccompProfile:
          type: RuntimeDefault

- target:
    kind: Deployment
    name: prometheus
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 2
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: 500m
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 1Gi
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: 4Gi

- target:
    kind: Deployment
    name: grafana
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 2
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: 250m
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 512Mi
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: 2Gi

# Production-specific strategic merge patches
patchesStrategicMerge:
- production-hpa.yaml
- production-network-policy.yaml
- production-pod-disruption-budget.yaml

# Production HPA configuration
replicas:
- name: creAItive-app
  count: 3 