apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: creAItive-app-pdb
  labels:
    app: creAItive
    environment: production
    component: availability
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: creAItive
      environment: production

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: prometheus-pdb
  labels:
    app: prometheus
    environment: production
    component: monitoring-availability
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: prometheus
      environment: production

---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: grafana-pdb
  labels:
    app: grafana
    environment: production
    component: visualization-availability
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: grafana
      environment: production 