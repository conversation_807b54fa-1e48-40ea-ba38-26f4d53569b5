apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: creAItive-development
  labels:
    app: creAItive
    environment: development
    track: "5"
    day: "4"

resources:
- ../../base

namespace: development

namePrefix: dev-

commonLabels:
  environment: development
  deployment.track: "5"

commonAnnotations:
  environment.name: "development"
  environment.purpose: "feature-development"
  deployment.strategy: "rolling-update"

# Development-specific ConfigMap
configMapGenerator:
- name: creAItive-env-config
  literals:
  - ENVIRONMENT=development
  - LOG_LEVEL=debug
  - DEBUG_MODE=true
  - API_TIMEOUT=60s
  - MAX_CONNECTIONS=50
  - CACHE_TTL=300
  - FEATURE_FLAGS=all
  - MONITORING_ENABLED=true
  - METRICS_INTERVAL=30s
  - AGENT_CONCURRENCY=5
  - DATABASE_POOL_SIZE=10

# Development image tags
images:
- name: ghcr.io/creAItive/app
  newTag: develop
- name: prom/prometheus
  newTag: v2.48.0-dev
- name: grafana/grafana
  newTag: 10.2.2-dev

# Development-specific patches
patches:
- target:
    kind: Deployment
    name: creAItive-app
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 1
    - op: add
      path: /spec/template/spec/containers/0/env/-
      value:
        name: NODE_ENV
        value: development
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: 100m
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 256Mi
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/cpu
      value: 500m
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: 1Gi

- target:
    kind: Deployment
    name: prometheus
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 1
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: 100m
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 512Mi

- target:
    kind: Deployment
    name: grafana
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 1
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: 50m
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 256Mi

# Development-specific HPA (disabled)
replicas:
- name: creAItive-app
  count: 1 