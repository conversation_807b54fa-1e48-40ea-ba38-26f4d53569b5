apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: creAItive-staging
  labels:
    app: creAItive
    environment: staging
    track: "5"
    day: "4"

resources:
- ../../base

namespace: staging

namePrefix: staging-

commonLabels:
  environment: staging
  deployment.track: "5"

commonAnnotations:
  environment.name: "staging"
  environment.purpose: "pre-production-validation"
  deployment.strategy: "blue-green"

# Staging-specific ConfigMap
configMapGenerator:
- name: creAItive-env-config
  literals:
  - ENVIRONMENT=staging
  - LOG_LEVEL=info
  - DEBUG_MODE=false
  - API_TIMEOUT=30s
  - MAX_CONNECTIONS=100
  - CACHE_TTL=600
  - FEATURE_FLAGS=stable
  - MONITORING_ENABLED=true
  - METRICS_INTERVAL=15s
  - AGENT_CONCURRENCY=10
  - DATABASE_POOL_SIZE=20
  - RATE_LIMITING=true
  - SECURITY_SCANNING=true

# Staging image tags
images:
- name: ghcr.io/creAItive/app
  newTag: staging
- name: prom/prometheus
  newTag: v2.48.0-staging
- name: grafana/grafana
  newTag: 10.2.2-staging

# Staging-specific patches
patches:
- target:
    kind: Deployment
    name: creAItive-app
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 2
    - op: add
      path: /spec/template/spec/containers/0/env/-
      value:
        name: NODE_ENV
        value: staging
    - op: add
      path: /spec/template/spec/containers/0/env/-
      value:
        name: STAGE_VALIDATION
        value: "true"
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: 250m
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 512Mi
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/cpu
      value: 1000m
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: 2Gi

- target:
    kind: Deployment
    name: prometheus
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 2
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: 300m
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 768Mi

- target:
    kind: Deployment
    name: grafana
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 2
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/cpu
      value: 150m
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 384Mi

# Staging-specific services (LoadBalancer for external testing)
patchesStrategicMerge:
- staging-service-patch.yaml

# Staging HPA configuration
replicas:
- name: creAItive-app
  count: 2 