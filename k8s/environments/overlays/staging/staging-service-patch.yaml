apiVersion: v1
kind: Service
metadata:
  name: creAItive-app-service
  labels:
    environment: staging
    service-type: external-validation
  annotations:
    service.staging/external-access: "true"
    service.staging/load-balancer: "enabled"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  - name: metrics
    port: 3001
    targetPort: 3001
    protocol: TCP
  selector:
    app: creAItive 