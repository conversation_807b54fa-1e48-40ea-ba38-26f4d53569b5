apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: creAItive-base
  labels:
    app: creAItive
    component: base
    track: "5"
    day: "4"

resources:
- ../../../namespace.yaml
- ../../../deployment.yaml
- ../../../service.yaml
- ../../../configmap.yaml
- ../../monitoring/prometheus-config.yaml
- ../../monitoring/prometheus-deployment.yaml
- ../../monitoring/grafana-config.yaml
- ../../monitoring/grafana-deployment.yaml

commonLabels:
  app: creAItive
  managed-by: kustomize

commonAnnotations:
  track5.day4/multi-environment: "true"
  deployment.strategy/kustomize: "base"

# Base configuration for all environments
configMapGenerator:
- name: creAItive-base-config
  literals:
  - DEPLOYMENT_TYPE=base
  - KUSTOMIZE_VERSION=v1beta1
  - MULTI_ENVIRONMENT=true

# Image configuration (will be overridden by overlays)
images:
- name: ghcr.io/creAItive/app
  newTag: latest 