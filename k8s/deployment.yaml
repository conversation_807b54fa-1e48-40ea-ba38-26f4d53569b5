# Track 5 Day 1: Kubernetes Deployment Configuration
# Production-ready deployment for Collective Intelligence Platform
# Integrates with ProductionScalabilityMonitor from Track 4 Day 8

apiVersion: apps/v1
kind: Deployment
metadata:
  name: creAItive-collective-intelligence
  namespace: creAItive-collective-intelligence
  labels:
    app: creAItive-platform
    component: collective-intelligence
    version: "1.0.0"
    tier: backend
  annotations:
    deployment.kubernetes.io/revision: "1"
    description: "Production deployment for 5 collective intelligence components"
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
  selector:
    matchLabels:
      app: creAItive-platform
      component: collective-intelligence
  template:
    metadata:
      labels:
        app: creAItive-platform
        component: collective-intelligence
        version: "1.0.0"
        tier: backend
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/api/metrics"
    spec:
      securityContext:
        runAsNonRoot: true
        runAsUser: 1001
        fsGroup: 1001
      containers:
      - name: collective-intelligence-platform
        image: creAItive/collective-intelligence:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
          name: http
          protocol: TCP
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: HOSTNAME
          value: "0.0.0.0"
        - name: KUBERNETES_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        # Production Scalability Monitor Configuration
        - name: SCALABILITY_MONITORING_ENABLED
          value: "true"
        - name: SCALABILITY_MONITORING_INTERVAL
          value: "5000"
        - name: SCALABILITY_ALERT_THRESHOLDS_RESPONSE_TIME
          value: "1000"
        - name: SCALABILITY_ALERT_THRESHOLDS_MEMORY_USAGE
          value: "80"
        - name: SCALABILITY_ALERT_THRESHOLDS_CPU_USAGE
          value: "75"
        - name: SCALABILITY_ALERT_THRESHOLDS_ERROR_RATE
          value: "5"
        # Collective Intelligence Components Configuration
        - name: COLLECTIVE_INTELLIGENCE_COMPONENTS
          value: "CollectiveIntelligencePlatform,MultiAgentDataAggregator,RealTimeCollaborativeLearning,DistributedKnowledgeGraph,CollectiveDecisionMaking"
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 15
          timeoutSeconds: 5
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/ready
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/health
            port: 3000
            scheme: HTTP
          initialDelaySeconds: 10
          periodSeconds: 5
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 10
        volumeMounts:
        - name: app-logs
          mountPath: /app/logs
        - name: tmp
          mountPath: /tmp
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1001
      volumes:
      - name: app-logs
        emptyDir: {}
      - name: tmp
        emptyDir: {}
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - creAItive-platform
              topologyKey: kubernetes.io/hostname
---
# Horizontal Pod Autoscaler for Production Scalability
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: creAItive-collective-intelligence-hpa
  namespace: creAItive-collective-intelligence
  labels:
    app: creAItive-platform
    component: autoscaler
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: creAItive-collective-intelligence
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 25
        periodSeconds: 60 