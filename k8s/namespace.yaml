# Track 5 Day 1: Kubernetes Namespace Configuration
# Multi-environment namespace for Collective Intelligence Platform
# Implements Devstral's multi-environment deployment strategy

apiVersion: v1
kind: Namespace
metadata:
  name: creAItive-collective-intelligence
  labels:
    name: creAItive-collective-intelligence
    environment: production
    app.kubernetes.io/name: creAItive-platform
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: collective-intelligence
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    description: "Production namespace for CreAItive Collective Intelligence Platform"
    contact: "<EMAIL>"
    documentation: "https://github.com/creAItive/collective-intelligence/docs"
---
# Development Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: creAItive-dev
  labels:
    name: creAItive-dev
    environment: development
    app.kubernetes.io/name: creAItive-platform
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: collective-intelligence
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    description: "Development namespace for CreAItive Collective Intelligence Platform"
    contact: "<EMAIL>"
---
# Staging Namespace
apiVersion: v1
kind: Namespace
metadata:
  name: creAItive-staging
  labels:
    name: creAItive-staging
    environment: staging
    app.kubernetes.io/name: creAItive-platform
    app.kubernetes.io/component: namespace
    app.kubernetes.io/part-of: collective-intelligence
    app.kubernetes.io/version: "1.0.0"
    app.kubernetes.io/managed-by: kubernetes
  annotations:
    description: "Staging namespace for CreAItive Collective Intelligence Platform"
    contact: "<EMAIL>" 