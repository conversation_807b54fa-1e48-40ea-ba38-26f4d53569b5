# Track 5 Day 1: Kubernetes ConfigMap
# Production configuration for Collective Intelligence Platform
# Integrates Track 4 Day 8 ProductionScalabilityMonitor settings

apiVersion: v1
kind: ConfigMap
metadata:
  name: creAItive-collective-intelligence-config
  namespace: creAItive-collective-intelligence
  labels:
    app: creAItive-platform
    component: configuration
    version: "1.0.0"
  annotations:
    description: "Production configuration for collective intelligence components"
data:
  # Application Configuration
  NODE_ENV: "production"
  PORT: "3000"
  HOSTNAME: "0.0.0.0"
  
  # Production Scalability Monitor Configuration
  SCALABILITY_MONITORING_ENABLED: "true"
  SCALABILITY_MONITORING_INTERVAL: "5000"
  SCALABILITY_METRICS_RETENTION: "24"
  SCALABILITY_ENABLE_DETAILED_PROFILING: "true"
  SCALABILITY_ENABLE_ALERTS: "true"
  
  # Alert Thresholds Configuration
  SCALABILITY_ALERT_THRESHOLDS_RESPONSE_TIME: "1000"
  SCALABILITY_ALERT_THRESHOLDS_MEMORY_USAGE: "80"
  SCALABILITY_ALERT_THRESHOLDS_CPU_USAGE: "75"
  SCALABILITY_ALERT_THRESHOLDS_ERROR_RATE: "5"
  
  # Collective Intelligence Components
  COLLECTIVE_INTELLIGENCE_COMPONENTS: "CollectiveIntelligencePlatform,MultiAgentDataAggregator,RealTimeCollaborativeLearning,DistributedKnowledgeGraph,CollectiveDecisionMaking"
  
  # Component-Specific Configuration
  MULTI_AGENT_DATA_AGGREGATOR_MAX_JOBS: "20"
  MULTI_AGENT_DATA_AGGREGATOR_QUALITY_THRESHOLD: "85"
  REAL_TIME_COLLABORATIVE_LEARNING_MAX_SESSIONS: "50"
  REAL_TIME_COLLABORATIVE_LEARNING_SYNC_INTERVAL: "1000"
  DISTRIBUTED_KNOWLEDGE_GRAPH_MAX_NODES: "10000"
  COLLECTIVE_DECISION_MAKING_ALGORITHMS: "voting,consensus,ml-optimized"
  
  # Kubernetes Integration
  KUBERNETES_ENABLED: "true"
  KUBERNETES_SERVICE_DISCOVERY: "true"
  KUBERNETES_HEALTH_CHECK_PATH: "/api/health"
  KUBERNETES_METRICS_PATH: "/api/metrics"
  
  # Logging Configuration
  LOG_LEVEL: "info"
  LOG_FORMAT: "json"
  LOG_OUTPUT: "stdout"
  
  # Performance Configuration
  PERFORMANCE_OPTIMIZATION_ENABLED: "true"
  CONCURRENT_PROCESSING_ENABLED: "true"
  LOAD_BALANCING_STRATEGY: "round-robin"
  
  # Health Check Configuration
  HEALTH_CHECK_INTERVAL: "15000"
  HEALTH_CHECK_TIMEOUT: "5000"
  HEALTH_CHECK_RETRIES: "3"
  
  # Security Configuration
  SECURITY_HEADERS_ENABLED: "true"
  CORS_ENABLED: "true"
  RATE_LIMITING_ENABLED: "true"
  
  # Monitoring Integration
  PROMETHEUS_METRICS_ENABLED: "true"
  PROMETHEUS_METRICS_PORT: "9090"
  PROMETHEUS_METRICS_PATH: "/api/metrics"
  
  # Production Deployment Configuration
  DEPLOYMENT_ENVIRONMENT: "production"
  DEPLOYMENT_VERSION: "1.0.0"
  DEPLOYMENT_REVISION: "1"
---
# Secret Configuration (for sensitive data)
apiVersion: v1
kind: Secret
metadata:
  name: creAItive-collective-intelligence-secrets
  namespace: creAItive-collective-intelligence
  labels:
    app: creAItive-platform
    component: secrets
    version: "1.0.0"
type: Opaque
data:
  # Base64 encoded values - replace with actual secrets
  DATABASE_URL: ""
  API_SECRET_KEY: ""
  JWT_SECRET: ""
  REDIS_PASSWORD: ""
  MONITORING_API_KEY: "" 