apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: creAItive-production
  namespace: argocd
  labels:
    app: creAItive
    environment: production
    track: "5"
    day: "4"
  annotations:
    argocd.argoproj.io/sync-wave: "3"
    deployment.strategy/gitops: "strict-manual"
    security.compliance: "enterprise-grade"
spec:
  project: production
  source:
    repoURL: 'https://github.com/creAItive/CompatibleCreativeVision'
    targetRevision: main
    path: k8s/environments/overlays/production
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: production
  syncPolicy:
    automated: {}  # Explicitly disabled - manual only
    syncOptions:
    - CreateNamespace=true
    - PrunePropagationPolicy=foreground
    - PruneLast=true
    - ApplyOutOfSyncOnly=true
    - Validate=true
    - RespectIgnoreDifferences=true
    retry:
      limit: 2
      backoff:
        duration: 30s
        factor: 2
        maxDuration: 10m
  revisionHistoryLimit: 50
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  - group: autoscaling
    kind: HorizontalPodAutoscaler
    jsonPointers:
    - /spec/minReplicas
    - /spec/maxReplicas
  - group: policy
    kind: PodDisruptionBudget
    jsonPointers:
    - /spec/minAvailable
  operation:
    sync:
      syncStrategy:
        apply:
          force: false
        hook:
          force: false
      prune: false 