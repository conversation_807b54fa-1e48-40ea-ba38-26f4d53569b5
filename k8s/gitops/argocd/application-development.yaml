apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: creAItive-development
  namespace: argocd
  labels:
    app: creAItive
    environment: development
    track: "5"
    day: "4"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
    deployment.strategy/gitops: "continuous"
spec:
  project: default
  source:
    repoURL: 'https://github.com/creAItive/CompatibleCreativeVision'
    targetRevision: develop
    path: k8s/environments/overlays/development
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: development
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
      allowEmpty: false
    syncOptions:
    - CreateNamespace=true
    - PrunePropagationPolicy=foreground
    - PruneLast=true
    retry:
      limit: 5
      backoff:
        duration: 5s
        factor: 2
        maxDuration: 3m
  revisionHistoryLimit: 10
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  - group: ""
    kind: ConfigMap
    jsonPointers:
    - /data/timestamp 