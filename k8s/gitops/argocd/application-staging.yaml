apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: creAItive-staging
  namespace: argocd
  labels:
    app: creAItive
    environment: staging
    track: "5"
    day: "4"
  annotations:
    argocd.argoproj.io/sync-wave: "2"
    deployment.strategy/gitops: "manual-approval"
spec:
  project: default
  source:
    repoURL: 'https://github.com/creAItive/CompatibleCreativeVision'
    targetRevision: staging
    path: k8s/environments/overlays/staging
  destination:
    server: 'https://kubernetes.default.svc'
    namespace: staging
  syncPolicy:
    automated:
      prune: false
      selfHeal: false
    syncOptions:
    - CreateNamespace=true
    - PrunePropagationPolicy=foreground
    - PruneLast=true
    - ApplyOutOfSyncOnly=true
    retry:
      limit: 3
      backoff:
        duration: 10s
        factor: 2
        maxDuration: 5m
  revisionHistoryLimit: 20
  ignoreDifferences:
  - group: apps
    kind: Deployment
    jsonPointers:
    - /spec/replicas
  - group: autoscaling
    kind: HorizontalPodAutoscaler
    jsonPointers:
    - /spec/minReplicas
  operation:
    sync:
      syncStrategy:
        apply:
          force: false 