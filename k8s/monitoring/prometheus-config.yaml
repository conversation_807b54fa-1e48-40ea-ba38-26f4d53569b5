apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: production
  labels:
    app: prometheus
    component: monitoring
    track: "5"
    day: "3"
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s
      external_labels:
        cluster: 'creAItive-production'
        environment: 'production'

    rule_files:
      - "alert_rules.yml"

    scrape_configs:
      # Kubernetes API server
      - job_name: 'kubernetes-apiservers'
        kubernetes_sd_configs:
          - role: endpoints
            namespaces:
              names:
                - default
                - production
                - development
                - staging
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
            action: keep
            regex: default;kubernetes;https

      # Kubernetes nodes
      - job_name: 'kubernetes-nodes'
        kubernetes_sd_configs:
          - role: node
        scheme: https
        tls_config:
          ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
        relabel_configs:
          - action: labelmap
            regex: __meta_kubernetes_node_label_(.+)

      # Kubernetes pods
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
          - role: pod
            namespaces:
              names:
                - production
                - development
                - staging
        relabel_configs:
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
            action: keep
            regex: true
          - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
            action: replace
            target_label: __metrics_path__
            regex: (.+)
          - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
            action: replace
            regex: ([^:]+)(?::\d+)?;(\d+)
            replacement: $1:$2
            target_label: __address__
          - action: labelmap
            regex: __meta_kubernetes_pod_label_(.+)
          - source_labels: [__meta_kubernetes_namespace]
            action: replace
            target_label: kubernetes_namespace
          - source_labels: [__meta_kubernetes_pod_name]
            action: replace
            target_label: kubernetes_pod_name

      # CreAItive Application (Next.js)
      - job_name: 'creAItive-app'
        static_configs:
          - targets: ['creAItive-app-service:3000']
        metrics_path: '/api/metrics'
        scrape_interval: 30s
        scrape_timeout: 10s

      # Agent system monitoring
      - job_name: 'agent-system'
        static_configs:
          - targets: ['creAItive-app-service:3000']
        metrics_path: '/api/orchestration/metrics'
        scrape_interval: 30s
        scrape_timeout: 10s

    alerting:
      alertmanagers:
        - static_configs:
            - targets:
              - alertmanager:9093

  alert_rules.yml: |
    groups:
    - name: kubernetes.rules
      rules:
      - alert: KubernetesNodeReady
        expr: kube_node_status_condition{condition="Ready",status="true"} == 0
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: Kubernetes Node not ready (instance {{ $labels.instance }})
          description: "Node {{ $labels.node }} has been unready for a long time\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

      - alert: KubernetesMemoryPressure
        expr: kube_node_status_condition{condition="MemoryPressure",status="true"} == 1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: Kubernetes memory pressure (instance {{ $labels.instance }})
          description: "Node {{ $labels.node }} has MemoryPressure condition\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

      - alert: KubernetesPodCrashLooping
        expr: increase(kube_pod_container_status_restarts_total[1h]) > 3
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: Kubernetes pod crash looping (instance {{ $labels.instance }})
          description: "Pod {{ $labels.pod }} is crash looping\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

    - name: creAItive.rules
      rules:
      - alert: CreAItiveAppDown
        expr: up{job="creAItive-app"} == 0
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: CreAItive Application is down
          description: "CreAItive application has been down for more than 5 minutes"

      - alert: CreAItiveHighResponseTime
        expr: histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le)) > 2
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: CreAItive Application high response time
          description: "95th percentile response time is above 2s for 10 minutes"

      - alert: CreAItiveAgentSystemDown
        expr: up{job="agent-system"} == 0
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: CreAItive Agent System is down
          description: "Agent orchestration system has been down for more than 3 minutes"

      - alert: CreAItiveHighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: CreAItive Application high error rate
          description: "Error rate is above 10% for 5 minutes" 