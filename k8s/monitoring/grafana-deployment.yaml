apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: production
  labels:
    app: grafana
    component: monitoring
    track: "5"
    day: "3"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
        component: monitoring
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "3000"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: grafana-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 472
        fsGroup: 472
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - grafana
              topologyKey: kubernetes.io/hostname
      initContainers:
      - name: init-grafana
        image: busybox:1.36
        securityContext:
          runAsUser: 0
        command:
        - /bin/sh
        - -c
        - |
          chown -R 472:472 /var/lib/grafana
          chmod -R 755 /var/lib/grafana
          cp -r /etc/grafana-provisioning/* /var/lib/grafana/provisioning/
          chown -R 472:472 /var/lib/grafana/provisioning
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-config-volume
          mountPath: /etc/grafana-provisioning
      containers:
      - name: grafana
        image: grafana/grafana:10.2.2
        imagePullPolicy: IfNotPresent
        ports:
        - name: http
          containerPort: 3000
          protocol: TCP
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin-password
        - name: GF_SECURITY_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: secret-key
        - name: GRAFANA_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin-password
        - name: GRAFANA_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: secret-key
        - name: GF_PATHS_CONFIG
          value: /etc/grafana/grafana.ini
        - name: GF_PATHS_PROVISIONING
          value: /var/lib/grafana/provisioning
        - name: GF_INSTALL_PLUGINS
          value: ""
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 30
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 10
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 30
        resources:
          requests:
            cpu: 250m
            memory: 512Mi
          limits:
            cpu: 1000m
            memory: 2Gi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 472
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-config-volume
          mountPath: /etc/grafana/grafana.ini
          subPath: grafana.ini
          readOnly: true
        - name: grafana-dashboards-volume
          mountPath: /etc/grafana/provisioning/dashboards
          readOnly: true
        - name: tmp-volume
          mountPath: /tmp
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-storage-pvc
      - name: grafana-config-volume
        configMap:
          name: grafana-config
          defaultMode: 420
      - name: grafana-dashboards-volume
        configMap:
          name: grafana-dashboards
          defaultMode: 420
      - name: tmp-volume
        emptyDir:
          sizeLimit: 1Gi

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: grafana-service-account
  namespace: production
  labels:
    app: grafana
    component: monitoring

---
apiVersion: v1
kind: Secret
metadata:
  name: grafana-secrets
  namespace: production
  labels:
    app: grafana
    component: monitoring
type: Opaque
data:
  # admin-password: "admin123!" (base64 encoded)
  admin-password: YWRtaW4xMjMh
  # secret-key: "grafana-secret-key-change-me" (base64 encoded)
  secret-key: Z3JhZmFuYS1zZWNyZXQta2V5LWNoYW5nZS1tZQ==

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: grafana-storage-pvc
  namespace: production
  labels:
    app: grafana
    component: monitoring
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: standard

---
apiVersion: v1
kind: Service
metadata:
  name: grafana-service
  namespace: production
  labels:
    app: grafana
    component: monitoring
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "3000"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 3000
    targetPort: 3000
    protocol: TCP
  selector:
    app: grafana

---
apiVersion: v1
kind: Service
metadata:
  name: grafana-loadbalancer
  namespace: production
  labels:
    app: grafana
    component: monitoring
    service-type: external
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 3000
    targetPort: 3000
    protocol: TCP
  selector:
    app: grafana

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: monitoring-ingress
  namespace: production
  labels:
    app: monitoring
    component: ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - prometheus.creAItive.local
    - grafana.creAItive.local
    secretName: monitoring-tls
  rules:
  - host: prometheus.creAItive.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: prometheus-service
            port:
              number: 9090
  - host: grafana.creAItive.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: grafana-service
            port:
              number: 3000 