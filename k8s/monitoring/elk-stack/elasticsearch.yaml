# Elasticsearch Configuration for Log Aggregation
# Track 5 Day 5: Production Monitoring Integration & Validation
apiVersion: elasticsearch.k8s.elastic.co/v1
kind: Elasticsearch
metadata:
  name: elasticsearch-production
  namespace: monitoring
  labels:
    app: elasticsearch
    environment: production
spec:
  version: 8.8.0
  
  # Node configuration
  nodeSets:
    - name: master
      count: 3
      config:
        node.roles: ["master"]
        cluster.name: "production-logs"
        network.host: 0.0.0.0
        discovery.seed_hosts: ["elasticsearch-production-es-master-0.elasticsearch-production-es-master", "elasticsearch-production-es-master-1.elasticsearch-production-es-master", "elasticsearch-production-es-master-2.elasticsearch-production-es-master"]
        cluster.initial_master_nodes: ["elasticsearch-production-es-master-0", "elasticsearch-production-es-master-1", "elasticsearch-production-es-master-2"]
        xpack.security.enabled: true
        xpack.security.http.ssl.enabled: true
        xpack.security.transport.ssl.enabled: true
        xpack.monitoring.collection.enabled: true
        
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              resources:
                requests:
                  memory: 2Gi
                  cpu: 1000m
                limits:
                  memory: 4Gi
                  cpu: 2000m
              env:
                - name: ES_JAVA_OPTS
                  value: "-Xms2g -Xmx2g"
                  
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data
          spec:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 100Gi
            storageClassName: ssd
            
    - name: data
      count: 3
      config:
        node.roles: ["data", "ingest"]
        cluster.name: "production-logs"
        network.host: 0.0.0.0
        xpack.security.enabled: true
        xpack.security.http.ssl.enabled: true
        xpack.security.transport.ssl.enabled: true
        xpack.monitoring.collection.enabled: true
        
      podTemplate:
        spec:
          containers:
            - name: elasticsearch
              resources:
                requests:
                  memory: 4Gi
                  cpu: 1000m
                limits:
                  memory: 8Gi
                  cpu: 2000m
              env:
                - name: ES_JAVA_OPTS
                  value: "-Xms4g -Xmx4g"
                  
      volumeClaimTemplates:
        - metadata:
            name: elasticsearch-data
          spec:
            accessModes:
              - ReadWriteOnce
            resources:
              requests:
                storage: 500Gi
            storageClassName: ssd
            
  # HTTP configuration
  http:
    service:
      spec:
        type: LoadBalancer
        ports:
          - name: https
            port: 9200
            protocol: TCP
            targetPort: 9200
    tls:
      selfSignedCertificate:
        disabled: false
        
  # Security configuration
  auth:
    roles:
      - secretName: elasticsearch-users-secret
      
  # Monitoring integration
  monitoring:
    metrics:
      elasticsearchRefs:
        - name: elasticsearch-production
    logs:
      elasticsearchRefs:
        - name: elasticsearch-production 