apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-config
  namespace: production
  labels:
    app: grafana
    component: monitoring
    track: "5"
    day: "3"
data:
  grafana.ini: |
    [server]
    protocol = http
    http_port = 3000
    domain = grafana.creAItive.local
    root_url = %(protocol)s://%(domain)s:%(http_port)s/
    serve_from_sub_path = false

    [security]
    admin_user = admin
    admin_password = $__env{GRAFANA_ADMIN_PASSWORD}
    secret_key = $__env{GRAFANA_SECRET_KEY}
    disable_gravatar = true
    allow_embedding = false
    cookie_secure = true
    cookie_samesite = strict

    [users]
    allow_sign_up = false
    allow_org_create = false
    auto_assign_org = true
    auto_assign_org_id = 1
    auto_assign_org_role = Viewer
    verify_email_enabled = false

    [auth]
    login_remember_days = 7
    login_maximum_inactive_lifetime_duration = 24h
    login_maximum_lifetime_duration = 30d

    [auth.anonymous]
    enabled = false

    [database]
    type = sqlite3
    path = /var/lib/grafana/grafana.db

    [session]
    provider = file
    provider_config = sessions

    [analytics]
    reporting_enabled = false
    check_for_updates = false

    [log]
    mode = console
    level = info

    [log.console]
    level = info
    format = console

    [alerting]
    enabled = true
    execute_alerts = true

  datasources.yaml: |
    apiVersion: 1
    datasources:
      - name: Prometheus
        type: prometheus
        access: proxy
        url: http://prometheus-service:9090
        isDefault: true
        editable: false
        jsonData:
          httpMethod: GET
          manageAlerts: true
          prometheusType: Prometheus
          prometheusVersion: 2.48.0
          cacheLevel: 'High'
          disableMetricsLookup: false
          customQueryParameters: ''
          
      - name: Prometheus-Alerts
        type: prometheus
        access: proxy
        url: http://prometheus-service:9090
        editable: false
        jsonData:
          httpMethod: GET
          manageAlerts: false
          prometheusType: Prometheus
          prometheusVersion: 2.48.0

  dashboards.yaml: |
    apiVersion: 1
    providers:
      - name: 'default'
        orgId: 1
        folder: ''
        type: file
        disableDeletion: false
        editable: true
        allowUiUpdates: true
        options:
          path: /etc/grafana/provisioning/dashboards

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana-dashboards
  namespace: production
  labels:
    app: grafana
    component: monitoring
    track: "5"
    day: "3"
data:
  kubernetes-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Kubernetes Cluster Overview",
        "tags": ["kubernetes", "cluster"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Cluster Nodes Status",
            "type": "stat",
            "targets": [
              {
                "expr": "sum(kube_node_status_condition{condition=\"Ready\",status=\"true\"})",
                "legendFormat": "Ready Nodes"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "yellow", "value": 1},
                    {"color": "green", "value": 2}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Pod Status",
            "type": "piechart",
            "targets": [
              {
                "expr": "sum by (phase) (kube_pod_status_phase)",
                "legendFormat": "{{phase}}"
              }
            ],
            "gridPos": {"h": 8, "w": 6, "x": 6, "y": 0}
          },
          {
            "id": 3,
            "title": "CPU Usage by Namespace",
            "type": "timeseries",
            "targets": [
              {
                "expr": "sum by (namespace) (rate(container_cpu_usage_seconds_total[5m]))",
                "legendFormat": "{{namespace}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}
          },
          {
            "id": 4,
            "title": "Memory Usage by Namespace",
            "type": "timeseries",
            "targets": [
              {
                "expr": "sum by (namespace) (container_memory_usage_bytes)",
                "legendFormat": "{{namespace}}"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 5,
            "title": "Network I/O",
            "type": "timeseries",
            "targets": [
              {
                "expr": "sum(rate(container_network_receive_bytes_total[5m]))",
                "legendFormat": "Received"
              },
              {
                "expr": "sum(rate(container_network_transmit_bytes_total[5m]))",
                "legendFormat": "Transmitted"
              }
            ],
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {"from": "now-1h", "to": "now"},
        "refresh": "5s"
      }
    }

  creAItive-application.json: |
    {
      "dashboard": {
        "id": null,
        "title": "CreAItive Application Monitoring",
        "tags": ["creAItive", "application", "nextjs"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Application Status",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=\"creAItive-app\"}",
                "legendFormat": "App Status"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "green", "value": 1}
                  ]
                },
                "mappings": [
                  {"options": {"0": {"text": "DOWN"}}, "type": "value"},
                  {"options": {"1": {"text": "UP"}}, "type": "value"}
                ]
              }
            },
            "gridPos": {"h": 8, "w": 6, "x": 0, "y": 0}
          },
          {
            "id": 2,
            "title": "Request Rate",
            "type": "timeseries",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total[5m]))",
                "legendFormat": "Requests/sec"
              }
            ],
            "gridPos": {"h": 8, "w": 9, "x": 6, "y": 0}
          },
          {
            "id": 3,
            "title": "Response Time (95th percentile)",
            "type": "timeseries",
            "targets": [
              {
                "expr": "histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket[5m])) by (le))",
                "legendFormat": "95th percentile"
              }
            ],
            "gridPos": {"h": 8, "w": 9, "x": 15, "y": 0}
          },
          {
            "id": 4,
            "title": "Error Rate",
            "type": "timeseries",
            "targets": [
              {
                "expr": "sum(rate(http_requests_total{status=~\"5..\"}[5m])) / sum(rate(http_requests_total[5m]))",
                "legendFormat": "Error Rate"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "unit": "percentunit",
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "green", "value": 0},
                    {"color": "yellow", "value": 0.05},
                    {"color": "red", "value": 0.1}
                  ]
                }
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}
          },
          {
            "id": 5,
            "title": "Agent System Status",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=\"agent-system\"}",
                "legendFormat": "Agent System"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "color": {"mode": "thresholds"},
                "thresholds": {
                  "steps": [
                    {"color": "red", "value": 0},
                    {"color": "green", "value": 1}
                  ]
                },
                "mappings": [
                  {"options": {"0": {"text": "DOWN"}}, "type": "value"},
                  {"options": {"1": {"text": "UP"}}, "type": "value"}
                ]
              }
            },
            "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}
          }
        ],
        "time": {"from": "now-1h", "to": "now"},
        "refresh": "5s"
      }
    }

  alerts-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "Alerts Overview",
        "tags": ["alerts", "monitoring"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Active Alerts",
            "type": "alertlist",
            "targets": [],
            "options": {
              "showOptions": "current",
              "maxItems": 20,
              "sortOrder": 1,
              "dashboardAlerts": false,
              "alertInstanceLabelFilter": "",
              "datasource": "Prometheus-Alerts"
            },
            "gridPos": {"h": 16, "w": 24, "x": 0, "y": 0}
          }
        ],
        "time": {"from": "now-1h", "to": "now"},
        "refresh": "10s"
      }
    } 