# Grafana Configuration for Production Monitoring
# Track 5 Day 5: Production Monitoring Integration & Validation
apiVersion: integreatly.org/v1alpha1
kind: Grafana
metadata:
  name: grafana-production-monitoring
  namespace: monitoring
  labels:
    app: grafana
    environment: production
spec:
  config:
    server:
      root_url: "https://grafana.monitoring.example.com"
      serve_from_sub_path: true
      protocol: https
    
    security:
      admin_user: admin
      admin_password: ${GRAFANA_ADMIN_PASSWORD}
      secret_key: ${GRAFANA_SECRET_KEY}
      disable_gravatar: true
      
    auth:
      disable_login_form: false
      disable_signout_menu: false
      
    auth.basic:
      enabled: true
      
    auth.anonymous:
      enabled: false
      
    database:
      type: postgres
      host: postgres:5432
      name: grafana
      user: grafana
      password: ${GRAFANA_DB_PASSWORD}
      ssl_mode: require
      
    session:
      provider: postgres
      
    dataproxy:
      timeout: 30
      keep_alive_seconds: 30
      
    alerting:
      enabled: true
      execute_alerts: true
      
    unified_alerting:
      enabled: true
      
    metrics:
      enabled: true
      basic_auth_username: prometheus
      basic_auth_password: ${PROMETHEUS_PASSWORD}
      
    log:
      mode: console
      level: info
      
    datasources:
      datasources:
        - name: Prometheus
          type: prometheus
          access: proxy
          url: http://prometheus:9090
          isDefault: true
          editable: true
          jsonData:
            timeInterval: 30s
            queryTimeout: 60s
            httpMethod: POST
            
        - name: Prometheus-Dev
          type: prometheus
          access: proxy
          url: http://prometheus-dev:9090
          editable: true
          jsonData:
            timeInterval: 30s
            queryTimeout: 60s
            httpMethod: POST
            
        - name: Prometheus-Staging
          type: prometheus
          access: proxy
          url: http://prometheus-staging:9090
          editable: true
          jsonData:
            timeInterval: 30s
            queryTimeout: 60s
            httpMethod: POST
            
        - name: Elasticsearch
          type: elasticsearch
          access: proxy
          url: http://elasticsearch:9200
          database: logstash-*
          jsonData:
            interval: Daily
            timeField: "@timestamp"
            esVersion: 8
            
        - name: Loki
          type: loki
          access: proxy
          url: http://loki:3100
          editable: true
          jsonData:
            maxLines: 1000
            
    dashboards:
      dashboards.yaml:
        apiVersion: 1
        providers:
          - name: 'default'
            folder: 'General'
            type: file
            disableDeletion: false
            updateIntervalSeconds: 10
            allowUiUpdates: true
            options:
              path: /etc/grafana/provisioning/dashboards
              
          - name: 'production'
            folder: 'Production'
            type: file
            disableDeletion: false
            updateIntervalSeconds: 10
            allowUiUpdates: true
            options:
              path: /etc/grafana/provisioning/dashboards/production
              
          - name: 'performance'
            folder: 'Performance'
            type: file
            disableDeletion: false
            updateIntervalSeconds: 10
            allowUiUpdates: true
            options:
              path: /etc/grafana/provisioning/dashboards/performance
              
  deployment:
    replicas: 2
    
  service:
    type: LoadBalancer
    ports:
      - name: grafana-ui
        port: 3000
        protocol: TCP
        targetPort: 3000
        
  ingress:
    enabled: true
    hostname: grafana.monitoring.example.com
    tlsEnabled: true
    tlsSecretName: grafana-tls
    
  resources:
    requests:
      cpu: 200m
      memory: 512Mi
    limits:
      cpu: 500m
      memory: 1Gi
      
  persistence:
    enabled: true
    storageClassName: ssd
    accessModes:
      - ReadWriteOnce
    size: 10Gi
    
  securityContext:
    runAsUser: 472
    runAsGroup: 472
    fsGroup: 472
    
  env:
    - name: GF_INSTALL_PLUGINS
      value: "grafana-piechart-panel,grafana-worldmap-panel,grafana-clock-panel,redis-datasource,postgres-datasource"
      
    - name: GF_FEATURE_TOGGLES_ENABLE
      value: "ngalert,live,publicDashboards"
      
  volumeMounts:
    - name: grafana-storage
      mountPath: /var/lib/grafana
      
    - name: grafana-config
      mountPath: /etc/grafana/grafana.ini
      subPath: grafana.ini
      
    - name: grafana-dashboards
      mountPath: /etc/grafana/provisioning/dashboards
      
    - name: grafana-datasources
      mountPath: /etc/grafana/provisioning/datasources 