apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: production
  labels:
    app: prometheus
    component: monitoring
    track: "5"
    day: "3"
spec:
  replicas: 2
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 1
      maxSurge: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
        component: monitoring
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: prometheus-service-account
      securityContext:
        runAsNonRoot: true
        runAsUser: 65534
        fsGroup: 65534
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - prometheus
              topologyKey: kubernetes.io/hostname
      containers:
      - name: prometheus
        image: prom/prometheus:v2.48.0
        imagePullPolicy: IfNotPresent
        args:
          - '--config.file=/etc/prometheus/prometheus.yml'
          - '--storage.tsdb.path=/prometheus/'
          - '--web.console.libraries=/etc/prometheus/console_libraries'
          - '--web.console.templates=/etc/prometheus/consoles'
          - '--storage.tsdb.retention.time=30d'
          - '--storage.tsdb.retention.size=50GB'
          - '--web.enable-lifecycle'
          - '--web.enable-admin-api'
          - '--web.external-url=http://prometheus.creAItive.local'
          - '--log.level=info'
        ports:
        - name: http
          containerPort: 9090
          protocol: TCP
        livenessProbe:
          httpGet:
            path: /-/healthy
            port: 9090
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 30
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 30
          timeoutSeconds: 10
          periodSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /-/ready
            port: 9090
          initialDelaySeconds: 10
          timeoutSeconds: 10
          periodSeconds: 10
          failureThreshold: 30
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 65534
          capabilities:
            drop:
            - ALL
        volumeMounts:
        - name: prometheus-config-volume
          mountPath: /etc/prometheus/
          readOnly: true
        - name: prometheus-storage-volume
          mountPath: /prometheus/
        - name: tmp-volume
          mountPath: /tmp
        env:
        - name: GOMAXPROCS
          valueFrom:
            resourceFieldRef:
              resource: limits.cpu
        - name: GOMEMLIMIT
          valueFrom:
            resourceFieldRef:
              resource: limits.memory
      volumes:
      - name: prometheus-config-volume
        configMap:
          name: prometheus-config
          defaultMode: 420
      - name: prometheus-storage-volume
        persistentVolumeClaim:
          claimName: prometheus-storage-pvc
      - name: tmp-volume
        emptyDir:
          sizeLimit: 1Gi
      
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: prometheus-service-account
  namespace: production
  labels:
    app: prometheus
    component: monitoring

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: prometheus-cluster-role
  labels:
    app: prometheus
    component: monitoring
rules:
- apiGroups: [""]
  resources:
  - nodes
  - nodes/proxy
  - services
  - endpoints
  - pods
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources:
  - ingresses
  verbs: ["get", "list", "watch"]
- nonResourceURLs: ["/metrics"]
  verbs: ["get"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: prometheus-cluster-role-binding
  labels:
    app: prometheus
    component: monitoring
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: prometheus-cluster-role
subjects:
- kind: ServiceAccount
  name: prometheus-service-account
  namespace: production

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: prometheus-storage-pvc
  namespace: production
  labels:
    app: prometheus
    component: monitoring
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: standard

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-service
  namespace: production
  labels:
    app: prometheus
    component: monitoring
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9090"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 9090
    targetPort: 9090
    protocol: TCP
  selector:
    app: prometheus

---
apiVersion: v1
kind: Service
metadata:
  name: prometheus-loadbalancer
  namespace: production
  labels:
    app: prometheus
    component: monitoring
    service-type: external
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 9090
    targetPort: 9090
    protocol: TCP
  selector:
    app: prometheus 