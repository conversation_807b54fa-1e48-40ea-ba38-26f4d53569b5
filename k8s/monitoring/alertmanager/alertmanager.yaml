# AlertManager Configuration for Production Monitoring
# Track 5 Day 5: Production Monitoring Integration & Validation

global:
  smtp_smarthost: 'smtp.example.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: '${SMTP_PASSWORD}'
  smtp_require_tls: true
  resolve_timeout: 5m

# Templates for alert notifications
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# Routing configuration
route:
  group_by: ['alertname', 'environment', 'severity']
  group_wait: 30s
  group_interval: 5m
  repeat_interval: 12h
  receiver: 'default-receiver'
  routes:
    # Critical alerts route to PagerDuty immediately
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 10s
      repeat_interval: 5m
      continue: true

    # Production environment alerts
    - match:
        environment: production
      receiver: 'production-alerts'
      group_wait: 15s
      repeat_interval: 30m
      continue: true

    # Performance alerts
    - match:
        alertname: 'HighResponseTime|LowThroughput|HighErrorRate'
      receiver: 'performance-alerts'
      group_wait: 30s
      repeat_interval: 1h

    # Infrastructure alerts
    - match:
        alertname: 'InstanceDown|NodeFailure|DiskSpaceLow'
      receiver: 'infrastructure-alerts'
      group_wait: 20s
      repeat_interval: 30m

    # Security alerts
    - match:
        alertname: 'SecurityBreach|UnauthorizedAccess|SuspiciousActivity'
      receiver: 'security-alerts'
      group_wait: 5s
      repeat_interval: 15m

    # Application alerts
    - match_re:
        alertname: 'ApplicationError|DatabaseConnectionLoss|ServiceUnavailable'
      receiver: 'application-alerts'
      group_wait: 25s
      repeat_interval: 45m

# Notification receivers
receivers:
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '[{{ .GroupLabels.severity | title }}] {{ .GroupLabels.alertname }}'
        body: |
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Environment: {{ .Labels.environment }}
          Severity: {{ .Labels.severity }}
          {{ end }}

  - name: 'critical-alerts'
    pagerduty_configs:
      - service_key: '${PAGERDUTY_INTEGRATION_KEY}'
        description: 'Critical Alert: {{ .GroupLabels.alertname }}'
        details:
          firing: '{{ template "pagerduty.details" . }}'
        client: 'Kubernetes Monitoring'
        client_url: 'https://grafana.monitoring.example.com'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#critical-alerts'
        title: '🚨 CRITICAL ALERT 🚨'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Environment:* {{ .Labels.environment }}
          *Severity:* {{ .Labels.severity }}
          *Description:* {{ .Annotations.description }}
          {{ end }}
        color: 'danger'

  - name: 'production-alerts'
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#production-alerts'
        title: '⚠️ Production Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Severity:* {{ .Labels.severity }}
          *Description:* {{ .Annotations.description }}
          *Runbook:* {{ .Annotations.runbook_url }}
          {{ end }}
        color: '{{ if eq .Status "firing" }}warning{{ else }}good{{ end }}'

  - name: 'performance-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Performance] {{ .GroupLabels.alertname }}'
        body: |
          Performance alert detected:
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Environment: {{ .Labels.environment }}
          Current Value: {{ .Annotations.current_value }}
          Threshold: {{ .Annotations.threshold }}
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#performance-monitoring'
        title: '📊 Performance Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Environment:* {{ .Labels.environment }}
          *Current Value:* {{ .Annotations.current_value }}
          *Threshold:* {{ .Annotations.threshold }}
          {{ end }}

  - name: 'infrastructure-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Infrastructure] {{ .GroupLabels.alertname }}'
        body: |
          Infrastructure alert:
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Node: {{ .Labels.instance }}
          Description: {{ .Annotations.description }}
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#infrastructure'
        title: '🏗️ Infrastructure Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Node:* {{ .Labels.instance }}
          *Description:* {{ .Annotations.description }}
          {{ end }}

  - name: 'security-alerts'
    pagerduty_configs:
      - service_key: '${PAGERDUTY_SECURITY_KEY}'
        description: 'Security Alert: {{ .GroupLabels.alertname }}'
        severity: 'critical'
    email_configs:
      - to: '<EMAIL>'
        subject: '[SECURITY ALERT] {{ .GroupLabels.alertname }}'
        body: |
          SECURITY ALERT DETECTED:
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Description: {{ .Annotations.description }}
          Source: {{ .Labels.instance }}
          Time: {{ .StartsAt }}
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_SECURITY_WEBHOOK_URL}'
        channel: '#security-alerts'
        title: '🔒 SECURITY ALERT'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Description:* {{ .Annotations.description }}
          *Source:* {{ .Labels.instance }}
          *Time:* {{ .StartsAt }}
          {{ end }}
        color: 'danger'

  - name: 'application-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[Application] {{ .GroupLabels.alertname }}'
        body: |
          Application alert:
          {{ range .Alerts }}
          Alert: {{ .Annotations.summary }}
          Service: {{ .Labels.service }}
          Environment: {{ .Labels.environment }}
          Description: {{ .Annotations.description }}
          {{ end }}
    slack_configs:
      - api_url: '${SLACK_WEBHOOK_URL}'
        channel: '#application-alerts'
        title: '📱 Application Alert'
        text: |
          {{ range .Alerts }}
          *Alert:* {{ .Annotations.summary }}
          *Service:* {{ .Labels.service }}
          *Environment:* {{ .Labels.environment }}
          *Description:* {{ .Annotations.description }}
          {{ end }}

# Inhibition rules to reduce noise
inhibit_rules:
  # Inhibit any alert that has a critical alert with the same alertname and instance
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'instance']

  # Inhibit instance alerts when the entire cluster is down
  - source_match:
      alertname: 'ClusterDown'
    target_match_re:
      alertname: 'Instance.*'

  # Inhibit specific service alerts when the node is down
  - source_match:
      alertname: 'NodeDown'
    target_match_re:
      alertname: 'Service.*'
    equal: ['instance'] 