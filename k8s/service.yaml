# Track 5 Day 1: Kubernetes Service Configuration
# Load balancing and service discovery for Collective Intelligence Platform
# Implements production-ready networking with monitoring integration

apiVersion: v1
kind: Service
metadata:
  name: creAItive-collective-intelligence-service
  namespace: creAItive-collective-intelligence
  labels:
    app: creAItive-platform
    component: service
    version: "1.0.0"
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
    prometheus.io/scrape: "true"
    prometheus.io/port: "3000"
    prometheus.io/path: "/api/metrics"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: 3000
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: 3000
    protocol: TCP
  selector:
    app: creAItive-platform
    component: collective-intelligence
  sessionAffinity: None
  loadBalancerSourceRanges:
  - 0.0.0.0/0
---
# Internal Service for Inter-Component Communication
apiVersion: v1
kind: Service
metadata:
  name: creAItive-collective-intelligence-internal
  namespace: creAItive-collective-intelligence
  labels:
    app: creAItive-platform
    component: internal-service
    version: "1.0.0"
  annotations:
    description: "Internal service for collective intelligence component communication"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 3000
    targetPort: 3000
    protocol: TCP
  - name: metrics
    port: 9090
    targetPort: 3000
    protocol: TCP
  selector:
    app: creAItive-platform
    component: collective-intelligence
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 10800
---
# Headless Service for StatefulSet Components
apiVersion: v1
kind: Service
metadata:
  name: creAItive-collective-intelligence-headless
  namespace: creAItive-collective-intelligence
  labels:
    app: creAItive-platform
    component: headless-service
    version: "1.0.0"
  annotations:
    description: "Headless service for direct pod communication and service discovery"
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: http
    port: 3000
    targetPort: 3000
    protocol: TCP
  selector:
    app: creAItive-platform
    component: collective-intelligence
  publishNotReadyAddresses: true 